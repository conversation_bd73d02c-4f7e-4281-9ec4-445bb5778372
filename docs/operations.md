# Operations

This document describes the operations layer of the Immuta SRE Toolkit.

## Overview

The operations layer provides a unified interface for performing operations on Immuta resources. It abstracts away the details of whether an operation is performed through the API or web automation, providing a consistent interface for the rest of the toolkit.

## Architecture

The operations layer consists of the following components:

- **Operation**: Base class for all operations.
- **OperationResult**: Class representing the result of an operation.
- **OperationEngine**: Class for executing operations.
- **OperationRegistry**: Registry for operations.

### Operation

The `Operation` class is the base class for all operations. It provides methods for executing operations through the API or web automation, validating parameters and results, and handling errors.

```python
class Operation(ABC, Generic[P, T]):
    """Base class for all operations."""

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation."""
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Set operation name and description
        self.name = self.__class__.__name__
        self.description = self.__doc__ or "No description available"

    @abstractmethod
    def execute_api(self, params: P) -> T:
        """Execute the operation using the API."""
        pass
    
    @abstractmethod
    def execute_web(self, params: P) -> T:
        """Execute the operation using web automation."""
        pass
    
    def validate_params(self, params: P) -> Tuple[bool, Optional[str]]:
        """Validate operation parameters."""
        return True, None
    
    def validate_result(self, result: T) -> Tuple[bool, Optional[str]]:
        """Validate operation result."""
        return True, None
    
    def execute(self, params: P) -> OperationResult[T]:
        """Execute the operation with fallback."""
        # Implementation details omitted for brevity
```

### OperationResult

The `OperationResult` class represents the result of an operation. It includes the status of the operation, the result data, any error messages, and metadata about the execution.

```python
class OperationResult(BaseModel, Generic[T]):
    """Operation result."""

    status: OperationStatus = Field(..., description="Operation status")
    data: Optional[T] = Field(None, description="Operation result data")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    execution_time: float = Field(..., description="Operation execution time in seconds")
    api_used: bool = Field(..., description="Whether the API was used for the operation")
    web_used: bool = Field(..., description="Whether web automation was used")
    attempts: int = Field(1, description="Number of attempts made")
    timestamp: float = Field(..., description="Operation timestamp")
```

### OperationEngine

The `OperationEngine` class is responsible for executing operations. It provides methods for getting operations from the registry, executing operations, and handling errors.

```python
class OperationEngine:
    """Engine for executing operations."""

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation engine."""
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.registry = OperationRegistry(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
    
    def get_operation(self, operation_class: Type[Operation]) -> Operation:
        """Get an operation instance."""
        return self.registry.create_operation(operation_class.__name__)
    
    def execute_operation(self, operation_name: str, params: dict) -> Optional[OperationResult]:
        """Execute an operation by name."""
        return self.registry.execute_operation(operation_name, params)
    
    def list_operations(self) -> List[str]:
        """List all registered operations."""
        return self.registry.list_operations()
```

### OperationRegistry

The `OperationRegistry` class is a registry for operations. It provides methods for registering operations, getting operations by name, and creating operation instances.

```python
class OperationRegistry:
    """Registry for operations."""

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation registry."""
        self.operations: Dict[str, Type[Operation]] = {}
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    def register(self, operation_class: Type[T]) -> Type[T]:
        """Register an operation."""
        operation_name = operation_class.__name__
        self.operations[operation_name] = operation_class
        return operation_class
    
    def get_operation(self, operation_name: str) -> Optional[Type[Operation]]:
        """Get an operation by name."""
        return self.operations.get(operation_name)
    
    def create_operation(self, operation_name: str) -> Optional[Operation]:
        """Create an instance of an operation by name."""
        operation_class = self.get_operation(operation_name)
        if operation_class:
            return operation_class(
                api_client=self.api_client,
                web_client=self.web_client,
                use_web_fallback=self.use_web_fallback,
                max_retries=self.max_retries,
                retry_delay=self.retry_delay,
            )
        return None
    
    def list_operations(self) -> List[str]:
        """List all registered operations."""
        return list(self.operations.keys())
    
    def execute_operation(self, operation_name: str, params: dict) -> Optional[OperationResult]:
        """Execute an operation by name."""
        operation = self.create_operation(operation_name)
        if operation:
            return operation.execute(params)
        return None
```

## Available Operations

The Immuta SRE Toolkit provides operations for managing various Immuta resources:

- **User Operations**: Operations for managing users.
- **Data Source Operations**: Operations for managing data sources.
- **Policy Operations**: Operations for managing policies.
- **Project Operations**: Operations for managing projects.

Each operation follows a consistent pattern:

1. Validate the input parameters.
2. Attempt to execute the operation using the API.
3. If the API operation fails and web fallback is enabled, retry using web automation.
4. Validate the result.
5. Return the result.

## Using Operations

Operations can be used through the CLI, service layer, or directly in Python code:

```python
from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.operations.users import ListUsersOperation

# Initialize clients
api_client = ImmutaClient(
    api_key="your_api_key",
    base_url="https://your-immuta-instance.com",
)
web_client = ImmutaWebClient(
    base_url="https://your-immuta-instance.com",
    username="your_username",
    password="your_password",
)

# Initialize operation engine
engine = OperationEngine(
    api_client=api_client,
    web_client=web_client,
    use_web_fallback=True,
)

# Get and execute an operation
operation = engine.get_operation(ListUsersOperation)
result = operation.execute({"limit": 10, "offset": 0})

# Check the result
if result.status == "success":
    print(f"Found {len(result.data)} users")
else:
    print(f"Operation failed: {result.error}")
```

## Extending Operations

To add a new operation, create a new class that inherits from `Operation` and implements the `execute_api` and `execute_web` methods:

```python
from immuta_toolkit.operations.base import Operation, OperationResult

class MyOperation(Operation[Dict[str, Any], List[Dict[str, Any]]]):
    """My custom operation."""

    def execute_api(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute the operation using the API."""
        # Implementation details
        return []
    
    def execute_web(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute the operation using web automation."""
        # Implementation details
        return []
```

Then register the operation with the registry:

```python
from immuta_toolkit.operations.registry import register_operation

@register_operation
class MyOperation(Operation[Dict[str, Any], List[Dict[str, Any]]]):
    """My custom operation."""
    # Implementation details
```
