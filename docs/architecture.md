# Architecture

This document provides an overview of the Immuta SRE Toolkit architecture.

## Overview

The Immuta SRE Toolkit is designed with a modular architecture that separates concerns and provides flexibility in how the toolkit is used. The architecture consists of several layers:

1. **API Client Layer**: Provides a high-level interface for interacting with the Immuta API.
2. **Web Automation Layer**: Provides a fallback mechanism for operations that cannot be performed through the API.
3. **Operations Layer**: Unifies the API and web automation layers to provide a consistent interface for operations.
4. **Validation Layer**: Validates inputs and outputs to ensure data integrity.
5. **Reporting Layer**: Generates reports on operations and data.
6. **Storage Layer**: Provides persistence for data and configuration.
7. **CLI Layer**: Provides a command-line interface for interacting with the toolkit.
8. **Service Layer**: Provides a REST API for interacting with the toolkit.

## Components

### API Client

The API client provides a high-level interface for interacting with the Immuta API. It handles authentication, rate limiting, retries, and error handling. The API client is organized into endpoints that correspond to the Immuta API endpoints.

```
api/
├── __init__.py
├── client.py
├── auth.py
├── endpoints/
│   ├── __init__.py
│   ├── users.py
│   ├── data_sources.py
│   ├── policies.py
│   └── projects.py
└── models/
    ├── __init__.py
    ├── auth.py
    └── common.py
```

### Web Automation

The web automation layer provides a fallback mechanism for operations that cannot be performed through the API. It uses Playwright to automate the Immuta web UI. The web automation layer is organized into actions that correspond to operations that can be performed through the web UI.

```
web/
├── __init__.py
├── client.py
├── page.py
├── selectors/
│   ├── __init__.py
│   ├── registry.py
│   └── elements.py
├── actions/
│   ├── __init__.py
│   ├── base.py
│   ├── users.py
│   ├── data_sources.py
│   └── policies.py
└── page_objects/
    ├── __init__.py
    ├── base_page.py
    ├── login_page.py
    ├── users_page.py
    ├── data_sources_page.py
    └── policies_page.py
```

### Operations

The operations layer unifies the API and web automation layers to provide a consistent interface for operations. It handles fallback from API to web automation when necessary and provides a registry for operations.

```
operations/
├── __init__.py
├── base.py
├── engine.py
├── registry.py
├── users.py
├── data_sources.py
└── policies.py
```

### Validation

The validation layer validates inputs and outputs to ensure data integrity. It provides validators for different types of data and operations.

```
validation/
├── __init__.py
├── base.py
├── user_validator.py
├── data_source_validator.py
└── policy_validator.py
```

### Reporting

The reporting layer generates reports on operations and data. It provides formatters for different output formats and templates for report generation.

```
reporting/
├── __init__.py
├── report.py
├── formatter.py
├── exporters/
│   ├── __init__.py
│   ├── json_exporter.py
│   ├── csv_exporter.py
│   └── html_exporter.py
└── templates/
    ├── __init__.py
    └── html_report.html
```

### Storage

The storage layer provides persistence for data and configuration. It provides different storage providers for different use cases.

```
storage/
├── __init__.py
├── base.py
├── file.py
└── sqlite.py
```

### CLI

The CLI layer provides a command-line interface for interacting with the toolkit. It provides commands for different operations and utilities.

```
cli/
├── __init__.py
├── main.py
├── user_commands.py
├── data_source_commands.py
├── policy_commands.py
└── api_commands.py
```

### Service

The service layer provides a REST API for interacting with the toolkit. It provides endpoints for different operations and utilities.

```
service/
├── __init__.py
├── app.py
├── routes/
│   ├── __init__.py
│   ├── users.py
│   ├── data_sources.py
│   ├── policies.py
│   └── operations.py
└── middleware/
    ├── __init__.py
    ├── logging.py
    └── error_handler.py
```

## Flow

The typical flow of an operation in the Immuta SRE Toolkit is as follows:

1. The user initiates an operation through the CLI or service layer.
2. The operation is validated by the validation layer.
3. The operation is executed by the operations layer, which attempts to use the API client first.
4. If the API operation fails and web fallback is enabled, the operation is retried using the web automation layer.
5. The result of the operation is validated by the validation layer.
6. The result is returned to the user and optionally stored by the storage layer or included in a report by the reporting layer.

This architecture provides flexibility in how the toolkit is used and allows for easy extension and customization.

## Design Patterns

The Immuta SRE Toolkit uses several design patterns to ensure maintainability, extensibility, and reliability:

### Strategy Pattern

The toolkit uses the Strategy pattern to allow different implementations of the same operation. For example, an operation can be performed using either the API client or web automation, with the appropriate strategy selected at runtime based on availability and success.

### Factory Pattern

The Operations Registry acts as a factory for creating operation instances. This allows for dynamic creation of operations based on configuration or user input.

### Adapter Pattern

The API client and web automation layers act as adapters for the Immuta API and web interface, respectively. This allows the operations layer to interact with both interfaces using a consistent API.

### Command Pattern

Operations are implemented as command objects that encapsulate all the information needed to perform an action. This allows for operations to be queued, logged, and undone.

### Observer Pattern

The reporting and logging systems use the Observer pattern to monitor operations and generate reports or logs based on their outcomes.

### Singleton Pattern

The OperationEngine and ConfigService are implemented as singletons to ensure that only one instance exists throughout the application.

## Error Handling

The toolkit uses a comprehensive error handling strategy:

1. **Validation Errors**: Caught early in the process by the validation layer.
2. **API Errors**: Handled by the API client with retries and fallback to web automation.
3. **Web Automation Errors**: Handled with retries and detailed error reporting.
4. **Operation Errors**: Wrapped in OperationResult objects with detailed error information.
5. **System Errors**: Caught and logged with stack traces for debugging.

## Performance Considerations

The toolkit is designed with performance in mind:

1. **Caching**: Frequently accessed data is cached to reduce API calls.
2. **Rate Limiting**: API calls are rate-limited to prevent throttling.
3. **Connection Pooling**: HTTP connections are pooled for efficiency.
4. **Batch Operations**: Operations on multiple resources are batched when possible.
5. **Asynchronous Operations**: Long-running operations can be performed asynchronously.

## Security Considerations

Security is a top priority for the toolkit:

1. **Secrets Management**: Sensitive information is stored securely using environment variables or Azure Key Vault.
2. **Authentication**: Multiple authentication methods are supported with secure token handling.
3. **Input Validation**: All inputs are validated to prevent injection attacks.
4. **Audit Logging**: All operations are logged for audit purposes.
5. **Least Privilege**: Operations are performed with the minimum required permissions.

## Extensibility

The toolkit is designed to be easily extended:

1. **New Operations**: New operations can be added by implementing the Operation interface and registering them with the OperationRegistry.
2. **New Validators**: New validators can be added by implementing the Validator interface.
3. **New Exporters**: New report exporters can be added by implementing the Exporter interface.
4. **New Storage Providers**: New storage providers can be added by implementing the Storage interface.
5. **New CLI Commands**: New CLI commands can be added by implementing the Command interface.

## Testing Strategy

The toolkit uses a comprehensive testing strategy:

1. **Unit Tests**: Test individual components in isolation.
2. **Integration Tests**: Test interactions between components.
3. **End-to-End Tests**: Test complete workflows.
4. **Mock Tests**: Test components with mocked dependencies.
5. **Property-Based Tests**: Test properties of components rather than specific examples.
6. **Performance Tests**: Test performance under load.
7. **Security Tests**: Test for security vulnerabilities.
