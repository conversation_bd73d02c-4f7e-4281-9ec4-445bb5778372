# Testing Guide

This guide provides comprehensive information about testing the Immuta SRE Toolkit, including test types, running tests, and writing new tests.

## Test Types

The Immuta SRE Toolkit uses several types of tests to ensure code quality and functionality:

### Unit Tests

Unit tests verify individual components in isolation. These tests are located in the `tests/unit/` directory and are the most numerous. They use mocking to isolate the component being tested from its dependencies.

### Integration Tests

Integration tests verify interactions between components. These tests are located in the `tests/integration/` directory and test how different parts of the system work together.

### End-to-End Tests

End-to-End (E2E) tests verify the entire system from a user's perspective. These tests are located in the `tests/e2e/` directory and require a running Immuta instance to test against.

### Web Automation Tests

Web automation tests verify the web automation functionality using Playwright. These tests are located in the `tests/web/` directory and require a running Immuta instance to test against.

## Running Tests

### Using PDM

The recommended way to run tests is using PDM:

```bash
# Run all tests
pdm run pytest

# Run tests with verbose output
pdm run pytest -xvs

# Run tests with coverage
pdm run pytest --cov=immuta_toolkit

# Run specific tests
pdm run pytest tests/unit/test_user_service.py

# Run a specific test function
pdm run pytest tests/unit/test_user_service.py::test_create_user

# Run tests matching a pattern
pdm run pytest -k "user"

# Run tests and stop on first failure
pdm run pytest -xvs --exitfirst
```

### Using Make

You can also use the Makefile to run tests:

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run web automation tests
make test-web
```

### Using Docker

To run tests in a Docker container:

```bash
# Run all tests in Docker
make test-docker

# Run web automation tests in Docker
make test-web-docker
```

## Test Configuration

### Environment Variables

Tests can be configured using environment variables:

- `IMMUTA_API_KEY`: Immuta API key for API tests
- `IMMUTA_BASE_URL`: Immuta base URL for API tests
- `IMMUTA_USERNAME`: Username for web automation tests
- `IMMUTA_PASSWORD`: Password for web automation tests
- `IMMUTA_USE_WEB_FALLBACK`: Whether to use web automation as fallback (default: true)
- `IMMUTA_HEADLESS`: Whether to run the browser in headless mode (default: true)

### Test Configuration Files

Test configuration can also be provided using configuration files:

- `tests/config/test_config.yaml`: Default test configuration
- `tests/config/local_config.yaml`: Local test configuration (gitignored)

## Writing Tests

### Unit Tests

Unit tests should follow these guidelines:

1. Use pytest fixtures for setup and teardown
2. Use mocking to isolate the component being tested
3. Test both success and failure cases
4. Test edge cases and boundary conditions

Example:

```python
import pytest
from unittest.mock import MagicMock, patch
from immuta_toolkit.services.user_service import UserService
from immuta_toolkit.models import UserModel, UserAttributes, UserRole

@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.storage = MagicMock()
    client.notifier = MagicMock()
    return client

def test_create_user(mock_client):
    """Test creating a user."""
    # Set up mock response
    mock_client.make_request.return_value.json.return_value = {
        "id": 123,
        "email": "<EMAIL>",
        "name": "Test User",
        "attributes": {"role": "DataScientist"},
        "groups": ["Group1"]
    }

    # Create user service
    user_service = UserService(mock_client)

    # Create user model
    user = UserModel(
        email="<EMAIL>",
        name="Test User",
        attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
        groups=["Group1"]
    )

    # Test creating user
    result = user_service.create_user(user=user, backup=True)

    # Check result
    assert result["id"] == 123
    assert result["email"] == "<EMAIL>"

    # Check that request was made
    mock_client.make_request.assert_called_once()

    # Check that backup was created
    mock_client.storage.upload_file.assert_called_once()
```

### Integration Tests

Integration tests should:

1. Test interactions between components
2. Use minimal mocking
3. Focus on component interfaces

Example:

```python
import pytest
from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.services.project_service import ProjectService
from immuta_toolkit.services.user_service import UserService

@pytest.fixture
def client():
    """Create a client in local mode for testing."""
    return ImmutaClient(api_key="test", base_url="https://example.com", is_local=True)

def test_project_lifecycle(client):
    """Test the project lifecycle with users."""
    # Create services
    project_service = ProjectService(client)
    user_service = UserService(client)

    # Create a project
    project = project_service.create_project(
        name="Test Project",
        description="Test project description"
    )

    # Add a user to the project
    result = project_service.add_member_to_project(
        project_id=project["id"],
        user_id=1,
        role="MEMBER"
    )

    # Check result
    assert result["success"] is True

    # Delete the project
    delete_result = project_service.delete_project(project["id"])
    assert delete_result["success"] is True
```

### Mocking

The toolkit uses the `unittest.mock` module for mocking. Here are some common mocking patterns:

#### Mocking API Responses

```python
def test_api_response(mock_client):
    # Mock the API response
    mock_client.make_request.return_value.json.return_value = {
        "id": 123,
        "name": "Test"
    }

    # Test the function
    result = service.get_item(123)

    # Check the result
    assert result["id"] == 123
```

#### Mocking Multiple API Calls

```python
def test_multiple_api_calls(mock_client):
    # Mock multiple API responses
    mock_client.make_request.return_value.json.side_effect = [
        {"id": 1, "name": "Item 1"},
        {"id": 2, "name": "Item 2"}
    ]

    # Test the function
    result1 = service.get_item(1)
    result2 = service.get_item(2)

    # Check the results
    assert result1["name"] == "Item 1"
    assert result2["name"] == "Item 2"
```

#### Patching Functions

```python
@patch("immuta_toolkit.utils.rate_limiter.TokenBucket")
def test_rate_limiter(mock_token_bucket):
    # Mock the TokenBucket class
    mock_bucket_instance = MagicMock()
    mock_token_bucket.return_value = mock_bucket_instance
    mock_bucket_instance.consume.return_value = True

    # Test the function
    # ...
```

## Test Skipping

Tests can be skipped using pytest decorators:

```python
import pytest

@pytest.mark.skip(reason="Not implemented yet")
def test_not_implemented():
    # This test will be skipped
    pass

@pytest.mark.skipif(
    "IMMUTA_API_KEY" not in os.environ,
    reason="Immuta API key not set"
)
def test_api_integration():
    # This test will be skipped if IMMUTA_API_KEY is not set
    pass
```

### Skipped Tests

Some tests are intentionally skipped in the codebase:

1. **Snowflake Tests**: Tests in `test_client_snowflake.py` are skipped because Snowflake support is not implemented in the mock client. These tests will be enabled once Snowflake support is fully implemented.

2. **Web Automation Tests**: Tests in the `tests/web/` directory that require actual Immuta credentials are skipped when those credentials are not available.

3. **CLI-Config Integration Tests**: Some CLI tests that require specific environment setup are skipped in the test environment.

4. **Rate Limiter Tests**: Some tests in `test_rate_limiter.py` are skipped because the implementation has changed from using the `limits` library to using a custom `TokenBucket` implementation. The tests have been updated to reflect this change, but some tests specific to the old implementation are skipped.

## Test Coverage

The toolkit aims for high test coverage. You can generate a coverage report using:

```bash
pdm run pytest --cov=immuta_toolkit --cov-report=html
```

This will generate an HTML coverage report in the `htmlcov/` directory.

## Continuous Integration

Tests are automatically run in the CI/CD pipeline defined in `.github/workflows/ci.yml`. The pipeline:

1. Runs on every pull request and push to main
2. Runs tests on multiple Python versions (3.9, 3.10, 3.11, 3.12, 3.13)
3. Generates a test coverage report
4. Fails if tests fail or coverage drops below the threshold

## Troubleshooting

### Common Issues

- **Import errors**: Make sure the package is installed in development mode (`pdm install -G dev`) or add the source directory to PYTHONPATH (`export PYTHONPATH=$PYTHONPATH:$(pwd)/src`)
- **Missing dependencies**: Make sure all dependencies are installed (`pdm install -G dev`)
- **Test failures**: Check the error message and stack trace for details
- **Slow tests**: Use the `-v` flag to see which tests are running and identify slow tests

### Getting Help

If you encounter issues with tests:

1. Check the test logs for error messages
2. Check the documentation for the component being tested
3. Ask for help in the GitHub issues
