# CLI Enhancements

The Immuta SRE Toolkit CLI has been enhanced to provide a more comprehensive and user-friendly interface for managing Immuta instances. This document describes the enhancements made to the CLI.

## Key Features

- **Rich Command-Line Interface**: User-friendly CLI with rich formatting and interactive elements
- **Comprehensive Command Set**: Commands for all Immuta operations including user, policy, data source, and project management
- **Configuration Management**: Support for multiple configuration sources, profiles, and validation
- **Secrets Management**: Secure storage and retrieval of sensitive configuration values
- **Interactive Mode**: Interactive shell for executing multiple commands
- **Batch Operations**: Support for batch operations using JSON or YAML files
- **Dry Run Mode**: Simulate operations without making changes
- **Output Formatting**: Multiple output formats including tables, JSON, YAML, and CSV
- **Progress Tracking**: Visual progress bars for long-running operations
- **Error Handling**: Detailed error messages and suggestions for resolution
- **Logging**: Comprehensive logging with configurable levels
- **Autocompletion**: Command and argument autocompletion for bash, zsh, and fish shells
- **Help System**: Detailed help with examples for all commands

## Configuration Management

The CLI includes a comprehensive configuration management system that supports multiple configuration sources, validation, profiles, versioning, and secure storage of sensitive information.

### Configuration Sources

The CLI supports the following configuration sources:

- **File-based configuration**: JSON, YAML, and TOML formats
- **Environment variables**: Prefixed environment variables (e.g., `IMMUTA_API_URL`)
- **Command-line arguments**: Arguments passed to the CLI
- **Secrets managers**: Azure Key Vault, AWS Secrets Manager, and HashiCorp Vault

### Configuration Profiles

The CLI supports configuration profiles for different environments (development, testing, production) and roles (admin, operator, auditor).

### Configuration Validation

The CLI provides comprehensive configuration validation to ensure that the configuration is valid and consistent.

### Configuration Versioning

The CLI supports configuration versioning to track changes to configuration over time and enable rollback to previous versions.

## Command-line Interface

The CLI has been enhanced with a new `config` command group for managing configuration.

### Global Options

The following options are available for all commands:

```bash
# Run with web automation fallback enabled (default)
immuta --web-fallback <command>

# Run with web automation fallback disabled
immuta --no-web-fallback <command>

# Run with browser in non-headless mode (for debugging)
immuta --no-headless <command>

# Run in local mode (no API calls)
immuta --local <command>

# Use a specific configuration file
immuta --config config.yaml <command>

# Use a specific environment variable prefix
immuta --env-prefix CUSTOM_ <command>

# Use a secrets provider
immuta --secrets-provider azure --vault-url https://example.vault.azure.net <command>
immuta --secrets-provider aws --region us-east-1 --secret-name immuta-config <command>
immuta --secrets-provider hashicorp --vault-url https://vault.example.com:8200 <command>

# Use a configuration profile
immuta --profile development <command>
```

### Configuration Commands

The `config` command group provides commands for managing configuration.

#### Viewing Configuration

```bash
# View the current configuration
immuta config view

# View the current configuration in a specific format
immuta config view --format json
immuta config view --format yaml
immuta config view --format toml

# View a specific configuration value
immuta config view --key api.base_url
```

#### Setting Configuration Values

```bash
# Set a configuration value
immuta config set api.base_url https://example.com

# Set a configuration value and save to the default configuration file
immuta config set api.base_url https://example.com --save

# Set a configuration value and save to a specific file
immuta config set api.base_url https://example.com --file config.yaml
```

#### Validating Configuration

```bash
# Validate the current configuration
immuta config validate

# Validate the current configuration and show only errors
immuta config validate --level error

# Validate the current configuration and show warnings and errors
immuta config validate --level warning

# Validate the current configuration and show all validation results
immuta config validate --level info
```

#### Initializing Configuration

```bash
# Initialize a new configuration file with default values
immuta config init

# Initialize a new configuration file with a specific name and format
immuta config init --file custom-config.yaml --format yaml

# Initialize a new configuration file and overwrite existing file
immuta config init --force
```

#### Loading Configuration

```bash
# Load configuration from a file
immuta config load --file config.yaml

# Load configuration from environment variables
immuta config load --env-prefix IMMUTA_

# Load configuration from Azure Key Vault
immuta config load --secrets-provider azure --vault-url https://example.vault.azure.net

# Load configuration from AWS Secrets Manager
immuta config load --secrets-provider aws --region us-east-1 --secret-name immuta-config

# Load configuration from HashiCorp Vault
immuta config load --secrets-provider hashicorp --vault-url https://vault.example.com:8200
```

#### Managing Configuration Profiles

```bash
# List all profiles
immuta config profile list

# List profiles of a specific type
immuta config profile list --type environment
immuta config profile list --type role

# List only enabled profiles
immuta config profile list --enabled-only

# View a specific profile
immuta config profile view development

# View a specific profile in a specific format
immuta config profile view development --format json

# Create a new profile
immuta config profile create development --type environment --description "Development environment"

# Create a new profile with configuration from a file
immuta config profile create development --type environment --file development-config.json

# Update an existing profile
immuta config profile update development --description "Updated description"

# Enable a profile
immuta config profile enable development

# Disable a profile
immuta config profile disable development

# Delete a profile
immuta config profile delete development --force

# Apply a profile
immuta config profile apply development
```

## Implementation Details

The CLI enhancements were implemented using the following components:

### Configuration Management

The configuration management system was implemented using the following components:

- **ConfigManager**: Manages configuration from multiple sources
- **ConfigSource**: Base class for configuration sources
- **FileConfigSource**: Loads configuration from files
- **EnvConfigSource**: Loads configuration from environment variables
- **ArgParseConfigSource**: Loads configuration from command-line arguments
- **SecretsConfigSource**: Loads configuration from secrets managers
- **ConfigProfile**: Represents a configuration profile
- **ProfileManager**: Manages configuration profiles
- **ConfigValidator**: Validates configuration
- **ValidationResult**: Represents a validation result

### Command-line Interface

The CLI was implemented using the following components:

- **Click**: Python package for creating command-line interfaces
- **Rich**: Python package for rich text and beautiful formatting in the terminal
- **ConfigCommands**: Command group for managing configuration
- **ProfileCommands**: Command group for managing configuration profiles

## Testing

The CLI enhancements were tested using the following tests:

- **Unit tests**: Tests for individual components
- **Integration tests**: Tests for the integration between components
- **End-to-end tests**: Tests for the entire CLI

## Implemented Enhancements

The following enhancements have been implemented:

- **Interactive configuration**: Interactive mode for creating and editing configuration
- **Configuration templates**: Templates for common configuration scenarios
- **Configuration migration**: Migration of configuration between versions
- **Configuration versioning**: Versioning of configuration files
- **Configuration history**: History of configuration changes
- **Configuration backup/restore**: Backup and restore of configuration files
- **Rich formatting**: Rich text and beautiful formatting in the terminal
- **Progress tracking**: Visual progress bars for long-running operations
- **Error handling**: Detailed error messages and suggestions for resolution
- **Output formatting**: Multiple output formats including tables, JSON, YAML, and CSV
- **Autocompletion**: Command and argument autocompletion for bash, zsh, and fish shells
- **Help system**: Detailed help with examples for all commands

## Future Enhancements

The following enhancements are planned for the future:

- **Configuration sharing**: Sharing configuration between users
- **Configuration diff**: Comparison of configuration files
- **Configuration merge**: Merging of configuration files
- **Configuration export/import**: Export and import of configuration files
- **Interactive wizards**: Interactive wizards for common operations
- **Plugin system**: Plugin system for extending the CLI
- **Remote execution**: Execute commands on remote systems
- **Scheduled operations**: Schedule operations to run at specific times
- **Notification integration**: Integration with notification systems
- **Workflow automation**: Automation of common workflows
