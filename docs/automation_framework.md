# Immuta Automation Framework

The Immuta SRE Toolkit includes a comprehensive automation framework that provides a hybrid approach to managing Immuta instances using both API calls and web automation as fallback. This framework is designed for reliability, scalability, and ease of use in production environments.

## Overview

The automation framework consists of several key components:

1. **Hybrid Client**: Combines API and web automation with intelligent fallback
2. **Operation Engine**: Executes operations with support for different execution modes
3. **Validation Layer**: Ensures data integrity and business rule compliance
4. **Reporting System**: Comprehensive reporting and audit trails
5. **Metrics Collection**: Performance monitoring and analytics
6. **CLI Interface**: Command-line interface for automation workflows

## Key Features

### Hybrid Execution Strategy

The framework uses a hybrid approach that prioritizes API calls but automatically falls back to web automation when needed:

- **API-First**: Fast and efficient API operations
- **Web Fallback**: Reliable web automation when API fails
- **Configurable Strategy**: Multiple fallback strategies available
- **Circuit Breaker**: Prevents cascading failures
- **Retry Logic**: Intelligent retry with exponential backoff

### Execution Modes

The framework supports multiple execution modes for different use cases:

- **Sequential**: Operations executed one after another
- **Parallel**: Operations executed concurrently with configurable limits
- **Pipeline**: Output of one operation feeds into the next

### Validation and Safety

- **Input Validation**: Validates operation parameters before execution
- **Business Rules**: Enforces business logic and compliance requirements
- **Dry Run Mode**: Test operations without making changes
- **Backup Integration**: Automatic backups before destructive operations

### Monitoring and Observability

- **Real-time Metrics**: Performance and success rate monitoring
- **Detailed Reporting**: Comprehensive execution reports
- **Progress Tracking**: Real-time progress updates for long-running operations
- **Error Analysis**: Detailed error reporting and categorization

## Architecture

### Hybrid Client

The `ImmutaHybridClient` is the main entry point for the automation framework:

```python
from immuta_toolkit.hybrid_client import ImmutaHybridClient, FallbackStrategy

client = ImmutaHybridClient(
    api_key="your-api-key",
    base_url="https://your-immuta-instance.com",
    username="your-username",
    password="your-password",
    fallback_strategy=FallbackStrategy.RETRY_THEN_FALLBACK,
    validation_enabled=True,
    reporting_enabled=True,
    metrics_enabled=True,
)
```

### Fallback Strategies

The framework supports four fallback strategies:

1. **IMMEDIATE**: Fallback to web automation immediately on API failure
2. **RETRY_THEN_FALLBACK**: Retry API calls, then fallback (default)
3. **API_ONLY**: Never use web automation fallback
4. **WEB_ONLY**: Use only web automation

### Operation Plans

Operations are defined in JSON plan files that specify:

- **Operations**: List of operations to execute
- **Parameters**: Parameters for each operation
- **Metadata**: Additional information and configuration
- **Execution Config**: How operations should be executed

Example operation plan:

```json
{
  "metadata": {
    "title": "User Management Plan",
    "description": "Manage users in Immuta"
  },
  "operations": [
    {
      "operation": "list_users",
      "params": {"limit": 100},
      "metadata": {"description": "List all users"}
    },
    {
      "operation": "create_user",
      "params": {
        "user": {
          "email": "<EMAIL>",
          "name": "New User",
          "attributes": {"role": "DataScientist"}
        }
      },
      "metadata": {"description": "Create new user"}
    }
  ]
}
```

## CLI Usage

### Basic Commands

```bash
# Execute an operation plan
immuta-toolkit automation execute-plan user-plan.json

# Execute with parallel mode
immuta-toolkit automation execute-plan user-plan.json --mode parallel --max-concurrent 5

# Execute with custom fallback strategy
immuta-toolkit automation execute-plan user-plan.json --fallback-strategy immediate

# View metrics
immuta-toolkit automation metrics

# View client status
immuta-toolkit automation status

# Configure client settings
immuta-toolkit automation configure --fallback-strategy api_only --enable-validation
```

### Creating Operation Plans

```bash
# Create a user management plan template
immuta-toolkit automation create-plan user-plan.json --template user-management

# Create a data source setup plan
immuta-toolkit automation create-plan ds-plan.json --template data-source-setup

# Create a custom plan
immuta-toolkit automation create-plan custom-plan.json --template custom
```

### Validating Plans

```bash
# Validate a plan file
immuta-toolkit automation validate-plan user-plan.json

# Validate with verbose output
immuta-toolkit automation validate-plan user-plan.json --verbose
```

## Python API Usage

### Basic Operations

```python
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.models import UserModel, UserAttributes, UserRole

# Initialize client
client = ImmutaHybridClient(
    api_key="your-api-key",
    base_url="https://your-immuta-instance.com",
)

# Create a user
user = UserModel(
    email="<EMAIL>",
    name="New User",
    attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
    groups=["DataScientists"]
)

result = client.create_user(user=user, backup=True)
print(f"Created user: {result['id']}")

# List users
users = client.list_users(limit=100)
print(f"Found {len(users)} users")
```

### Batch Operations

```python
import asyncio
from immuta_toolkit.operations.engine import OperationPlan, ExecutionMode

# Create operation plan
plan = OperationPlan(
    operations=[
        {
            "operation": "get_user",
            "params": {"user_id": "<EMAIL>"}
        },
        {
            "operation": "get_user", 
            "params": {"user_id": "<EMAIL>"}
        }
    ],
    mode=ExecutionMode.PARALLEL,
    max_concurrent=5
)

# Execute plan
context = client.operation_engine.execute_plan(plan)

# Check results
print(f"Executed {len(context.results)} operations")
print(f"Success rate: {sum(1 for r in context.results if r.status.value == 'success') / len(context.results):.2%}")
```

### Metrics and Monitoring

```python
# Get metrics
metrics = client.get_metrics()
if metrics:
    summary = metrics["summary"]
    print(f"Total operations: {summary['total_operations']}")
    print(f"Success rate: {summary['success_rate']:.2%}")
    print(f"Fallback rate: {summary['fallback_rate']:.2%}")

# Get client status
status = client.get_client_status()
print(f"API client available: {status['api_client_available']}")
print(f"Web client available: {status['web_client_available']}")
print(f"Fallback strategy: {status['fallback_strategy']}")
```

## Configuration

### Environment Variables

The framework can be configured using environment variables:

```bash
export IMMUTA_API_KEY="your-api-key"
export IMMUTA_BASE_URL="https://your-immuta-instance.com"
export IMMUTA_USERNAME="your-username"
export IMMUTA_PASSWORD="your-password"
export IMMUTA_USE_WEB_FALLBACK="true"
export IMMUTA_HEADLESS="true"
export IMMUTA_FALLBACK_STRATEGY="retry_then_fallback"
```

### Configuration Files

Configuration can also be provided via YAML files:

```yaml
# config.yaml
api:
  key: "your-api-key"
  base_url: "https://your-immuta-instance.com"
  username: "your-username"
  password: "your-password"

automation:
  use_web_fallback: true
  headless: true
  fallback_strategy: "retry_then_fallback"
  validation_enabled: true
  reporting_enabled: true
  metrics_enabled: true
  max_retries: 3
  retry_delay: 2
```

## Best Practices

### Operation Design

1. **Idempotent Operations**: Design operations to be safely repeatable
2. **Error Handling**: Include proper error handling and recovery
3. **Validation**: Always validate inputs before execution
4. **Backups**: Create backups before destructive operations
5. **Dry Run**: Test operations in dry run mode first

### Performance Optimization

1. **Parallel Execution**: Use parallel mode for independent operations
2. **Batch Size**: Optimize batch sizes for your environment
3. **Connection Pooling**: Reuse connections when possible
4. **Caching**: Cache frequently accessed data
5. **Circuit Breakers**: Use circuit breakers to prevent cascading failures

### Security Considerations

1. **Credential Management**: Use secure credential storage
2. **Access Control**: Implement proper access controls
3. **Audit Logging**: Enable comprehensive audit logging
4. **Encryption**: Use encryption for sensitive data
5. **Network Security**: Secure network communications

## Troubleshooting

### Common Issues

1. **API Timeouts**: Increase timeout values or use web fallback
2. **Authentication Failures**: Check credentials and permissions
3. **Rate Limiting**: Implement proper rate limiting and backoff
4. **Network Issues**: Use retry logic and circuit breakers
5. **Validation Errors**: Check input data and business rules

### Debugging

1. **Enable Verbose Logging**: Use `--verbose` flag for detailed output
2. **Check Metrics**: Monitor success rates and performance metrics
3. **Review Reports**: Analyze execution reports for patterns
4. **Test in Isolation**: Test individual operations separately
5. **Use Dry Run**: Test operations without making changes

## Examples

See the `examples/automation/` directory for complete examples:

- `user-management-plan.json`: User management operations
- `data-source-setup-plan.json`: Data source configuration
- `parallel-operations-plan.json`: Parallel execution example

## Support

For support and questions:

1. Check the documentation and examples
2. Review the troubleshooting guide
3. Enable verbose logging for debugging
4. Check the GitHub issues for known problems
5. Contact the SRE team for assistance
