# Docker Support

The Immuta SRE Toolkit provides comprehensive Docker support for containerized execution in various environments. This document describes how to use Docker with the toolkit.

## Key Features

- **Multiple Container Types**: Specialized containers for production, development, testing, and API server
- **Security Hardening**: Non-root user, read-only volumes, no-new-privileges, and minimal base image
- **Resource Management**: CPU and memory limits for all containers
- **Volume Management**: Separate volumes for data, logs, reports, and configurations
- **Environment Configuration**: Support for environment variables and .env files
- **Helper Scripts**: Convenient scripts for common Docker operations
- **Makefile Integration**: Comprehensive Makefile targets for Docker operations
- **CI/CD Integration**: Ready-to-use Docker configurations for CI/CD pipelines
- **Kubernetes Support**: Kubernetes deployment manifests for production use
- **Cross-Platform Support**: Support for Linux, macOS, and Windows

## Docker Images

The toolkit provides several Docker images for different use cases:

1. **Production Image**: A lightweight image for running the toolkit in production environments.
2. **Development Image**: A full-featured image for development and testing.
3. **Test Image**: A specialized image for running tests.
4. **API Server Image**: A dedicated image for running the API server.

### Production Image

The production image is based on Python 3.13 slim and includes:

- The Immuta SRE Toolkit with all production dependencies
- Playwright for web automation
- A non-root user for security
- Health checks for monitoring
- Volume mounts for data, logs, reports, and configurations
- Resource limits for CPU and memory
- Security features like no-new-privileges

### Development Image

The development image extends the production image with:

- Development dependencies (testing, linting, documentation)
- Additional development tools (vim, nano, etc.)
- Source code mounted as a volume for live editing
- Support for running tests and building binaries
- Debugging tools and utilities
- Increased resource limits for development work

### Test Image

The test image is based on the development image and is configured for running tests:

- Includes all testing dependencies
- Configured for running unit, integration, and web automation tests
- Provides test coverage reporting
- Optimized for CI/CD pipelines

## Getting Started

### Prerequisites

- Docker 20.10.0 or higher
- Docker Compose 2.0.0 or higher (for multi-container setups)

### Building the Images

```bash
# Build the production image
make build

# Build the development image
make build-dev

# Build both images
make dev
```

### Running the Toolkit

#### Using Docker Compose

```bash
# Run the CLI container
docker-compose -f docker/docker-compose.yml up immuta-toolkit

# Run the API server
docker-compose -f docker/docker-compose.yml up api

# Run the development environment
docker-compose -f docker/docker-compose.yml up dev
```

#### Using Make

```bash
# Run the CLI container
make run

# Run the API server
make run-api

# Run the development environment
make run-dev
```

#### Using the Helper Script

```bash
# Run the CLI with default help command
./scripts/run-docker.sh

# Run a specific command
./scripts/run-docker.sh user list

# Run the API server
./scripts/run-docker.sh api

# Run the development environment
./scripts/run-docker.sh dev
```

## Container Types

### CLI Container

The CLI container is designed for running command-line operations:

```bash
# Run a command
docker run -it --rm \
    --env-file .env \
    -v ./data:/app/data:rw \
    -v ./logs:/app/logs:rw \
    -v ./reports:/app/reports:rw \
    -v ./configs:/app/configs:ro \
    --security-opt no-new-privileges:true \
    immuta-sre-toolkit:latest user list
```

### API Server Container

The API server container exposes the toolkit's API:

```bash
# Run the API server
docker run -it --rm \
    -p 8000:8000 \
    --env-file .env \
    -v ./data:/app/data:rw \
    -v ./logs:/app/logs:rw \
    -v ./reports:/app/reports:rw \
    -v ./configs:/app/configs:ro \
    --security-opt no-new-privileges:true \
    immuta-sre-toolkit:latest api start --host 0.0.0.0 --port 8000
```

### Development Container

The development container provides a full development environment:

```bash
# Run the development container
docker run -it --rm \
    -p 8000:8000 \
    -v .:/app:rw \
    --security-opt no-new-privileges:true \
    immuta-sre-toolkit-dev:latest bash
```

### Test Container

The test container is designed for running tests:

```bash
# Run all tests
docker run -it --rm \
    --env-file .env \
    -v .:/app:rw \
    --security-opt no-new-privileges:true \
    immuta-sre-toolkit-dev:latest pdm run pytest

# Run specific tests
docker run -it --rm \
    --env-file .env \
    -v .:/app:rw \
    --security-opt no-new-privileges:true \
    immuta-sre-toolkit-dev:latest pdm run pytest tests/unit/test_user_service.py
```

## Configuration

### Environment Variables

The Docker containers support the following environment variables:

- `IMMUTA_API_KEY`: API key for Immuta
- `IMMUTA_BASE_URL`: Base URL for Immuta instance
- `IMMUTA_USERNAME`: Username for Immuta (for web automation)
- `IMMUTA_PASSWORD`: Password for Immuta (for web automation)
- `IMMUTA_API_TIMEOUT`: Timeout for API requests (default: 30)
- `IMMUTA_API_RATE_LIMIT`: Rate limit for API requests (default: 10)
- `IMMUTA_LOG_LEVEL`: Log level (default: INFO)
- `IMMUTA_LOG_FILE`: Log file path (default: /app/logs/immuta-toolkit.log)
- `IMMUTA_USE_WEB_FALLBACK`: Whether to use web automation as fallback (default: true)
- `IMMUTA_HEADLESS`: Whether to run browser in headless mode (default: true)

### Environment Files

You can use an `.env` file to provide environment variables:

```bash
# Create a .env file
cat > .env << EOF
IMMUTA_API_KEY=your_api_key
IMMUTA_BASE_URL=https://your-immuta-instance.com
IMMUTA_USERNAME=your_username
IMMUTA_PASSWORD=your_password
EOF

# Run with the .env file
docker run -it --rm \
    --env-file .env \
    immuta-sre-toolkit:latest user list
```

### Volume Mounts

The Docker containers support the following volume mounts:

| Volume         | Description                                       | Mode       |
| -------------- | ------------------------------------------------- | ---------- |
| `/app/data`    | Data directory for storing data files             | Read/Write |
| `/app/logs`    | Logs directory for storing log files              | Read/Write |
| `/app/reports` | Reports directory for storing report files        | Read/Write |
| `/app/configs` | Configs directory for storing configuration files | Read-Only  |

```bash
# Mount volumes with appropriate permissions
docker run -it --rm \
    -v ./data:/app/data:rw \
    -v ./logs:/app/logs:rw \
    -v ./reports:/app/reports:rw \
    -v ./configs:/app/configs:ro \
    immuta-sre-toolkit:latest user list
```

For security reasons, configuration files are mounted as read-only to prevent accidental modification.

## Development Workflow

### Running Tests

```bash
# Run all tests in the development container
make test

# Run web automation tests in the development container
make test-web

# Run specific tests in the development container
make test-user

# Run all tests in a dedicated test container
make test-docker

# Run web automation tests in a dedicated test container
make test-web-docker
```

### Running Linting

```bash
# Run linting
make lint

# Fix linting issues
make lint-fix

# Format code
make format
```

### Building Binaries

```bash
# Build a binary (one-directory mode)
make binary

# Build a binary (one-file mode)
make binary-onefile

# Build binaries for specific platforms
make binary-windows
make binary-macos
make binary-linux

# Build binaries for all platforms
make binary-all
```

### Accessing the Development Container

```bash
# Open a shell in the development container
make shell

# Run a specific command in the development container
make exec CMD="pdm run pytest tests/unit/test_user_service.py"
```

### Docker Registry Operations

```bash
# Build and push Docker images to registry
make docker-push

# Pull Docker images from registry
make docker-pull
```

## Advanced Usage

### Container Entrypoint

The Docker containers use an entrypoint script (`docker/entrypoint.sh`) that:

1. Checks if PDM is installed and installs it if needed
2. Installs dependencies if they're not already installed
3. Creates necessary directories for data, logs, and reports
4. Sets up Playwright if web automation is needed
5. Executes the appropriate command based on the arguments

This script ensures that the container is properly initialized before running commands.

### Running Custom Scripts

```bash
# Run a custom script
make run-script SCRIPT=scripts/my_script.py
```

### Continuous Integration

The Docker images can be used in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Build Docker image
        run: docker build -t immuta-sre-toolkit:test .
      - name: Run tests
        run: docker run --rm immuta-sre-toolkit:test pdm run pytest
```

### Kubernetes Deployment

The Docker images can be deployed to Kubernetes:

```yaml
# Example Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: immuta-sre-toolkit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: immuta-sre-toolkit
  template:
    metadata:
      labels:
        app: immuta-sre-toolkit
    spec:
      containers:
      - name: immuta-sre-toolkit
        image: immuta-sre-toolkit:latest
        command: ["api", "start", "--host", "0.0.0.0", "--port", "8000"]
        ports:
        - containerPort: 8000
        env:
        - name: IMMUTA_API_KEY
          valueFrom:
            secretKeyRef:
              name: immuta-credentials
              key: api-key
        - name: IMMUTA_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: base-url
```

## Resource Limits and Security

### Resource Limits

The Docker containers have the following resource limits defined in the `docker/docker-compose.yml` file:

| Service        | CPU Limit | Memory Limit | CPU Reservation | Memory Reservation |
| -------------- | --------- | ------------ | --------------- | ------------------ |
| immuta-toolkit | 1.0       | 1G           | 0.25            | 512M               |
| api            | 1.0       | 1G           | 0.25            | 512M               |
| dev            | 2.0       | 2G           | 0.5             | 1G                 |
| test           | 2.0       | 2G           | -               | -                  |

You can adjust these limits in the `docker/docker-compose.yml` file or when running containers directly:

```bash
# Run with custom resource limits
docker run -it --rm \
    --cpus 0.5 \
    --memory 512m \
    --env-file .env \
    immuta-sre-toolkit:latest user list
```

### Security Features

The Docker containers include several security features:

1. **Non-root User**: All containers run as a non-root user (`immuta`) for better security
2. **Read-only Volumes**: Configuration files are mounted as read-only
3. **No New Privileges**: Containers are configured with `no-new-privileges:true` to prevent privilege escalation
4. **Minimal Base Image**: Containers use the slim variant of the Python image to reduce attack surface

```bash
# Run with security options
docker run -it --rm \
    --security-opt no-new-privileges:true \
    --read-only \
    --tmpfs /tmp \
    --env-file .env \
    immuta-sre-toolkit:latest user list
```

## Troubleshooting

### Common Issues

#### Container fails to start

Check the Docker logs:

```bash
docker logs immuta-toolkit
```

#### Web automation fails

Ensure Playwright dependencies are installed:

```bash
make install-playwright
```

#### Permission issues with mounted volumes

Ensure the volumes have the correct permissions:

```bash
mkdir -p data logs reports configs
chmod -R 755 data logs reports
chmod -R 644 configs
```

### Debugging

For debugging, you can run the container with additional options:

```bash
# Run with debug logging
docker run -it --rm \
    --env-file .env \
    -e IMMUTA_LOG_LEVEL=DEBUG \
    immuta-sre-toolkit:latest user list

# Run with non-headless browser
docker run -it --rm \
    --env-file .env \
    -e IMMUTA_HEADLESS=false \
    immuta-sre-toolkit:latest user list
```
