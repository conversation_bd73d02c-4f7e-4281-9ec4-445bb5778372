# Immuta SRE Toolkit Documentation

Welcome to the Immuta SRE Toolkit documentation. This documentation provides comprehensive information about the toolkit, its features, and how to use it.

## Table of Contents

### User Guide

- [CLI Usage](cli.md)
- [Configuration Management](configuration.md)
- [Docker Support](docker.md)
- [Frequently Asked Questions](faq.md)

### Developer Guide

- [Architecture](architecture.md)
- [Development Guide](development_guide.md)
- [API Client](api_client.md)
- [Web Automation](web_automation.md)

### API Reference

- [API Reference](api_reference.md)
- [API Service](api_service.md)

### Deployment

- [Deployment Guide](deployment_guide.md)
- [Kubernetes Deployment](../kubernetes/deployment.yaml)
- [Azure DevOps Integration](ado_integration.md)

### Security

- [Security Best Practices](security_best_practices.md)

### Operations

- [Operations Guide](operations.md)
- [CLI Enhancements](cli_enhancements.md)

## Getting Started

To get started with the Immuta SRE Toolkit, please refer to the [Development Guide](development_guide.md) for setup instructions and the [CLI Usage](cli.md) guide for basic usage.

## Contributing

If you'd like to contribute to the Immuta SRE Toolkit, please refer to the [Development Guide](development_guide.md) for information on how to set up your development environment and contribute to the project.
