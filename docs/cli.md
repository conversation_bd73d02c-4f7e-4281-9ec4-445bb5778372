# Command Line Interface

The Immuta SRE Toolkit provides a comprehensive command-line interface (CLI) for managing Immuta instances. This document describes the available commands and options.

## Global Options

The following options are available for all commands:

```bash
# Run with web automation fallback enabled (default)
immuta --web-fallback <command>

# Run with web automation fallback disabled
immuta --no-web-fallback <command>

# Run with browser in non-headless mode (for debugging)
immuta --no-headless <command>

# Run in local mode (no API calls)
immuta --local <command>

# Use a specific configuration file
immuta --config config.yaml <command>

# Use a specific environment variable prefix
immuta --env-prefix CUSTOM_ <command>

# Use a secrets provider
immuta --secrets-provider azure --vault-url https://example.vault.azure.net <command>
immuta --secrets-provider aws --region us-east-1 --secret-name immuta-config <command>
immuta --secrets-provider hashicorp --vault-url https://vault.example.com:8200 <command>

# Use a configuration profile
immuta --profile development <command>
```

## Configuration Management

The `config` command group provides commands for managing configuration.

### Viewing Configuration

```bash
# View the current configuration
immuta config view

# View the current configuration in a specific format
immuta config view --format json
immuta config view --format yaml
immuta config view --format toml

# View a specific configuration value
immuta config view --key api.base_url
```

### Setting Configuration Values

```bash
# Set a configuration value
immuta config set api.base_url https://example.com

# Set a configuration value and save to the default configuration file
immuta config set api.base_url https://example.com --save

# Set a configuration value and save to a specific file
immuta config set api.base_url https://example.com --file config.yaml
```

### Validating Configuration

```bash
# Validate the current configuration
immuta config validate

# Validate the current configuration and show only errors
immuta config validate --level error

# Validate the current configuration and show warnings and errors
immuta config validate --level warning

# Validate the current configuration and show all validation results
immuta config validate --level info
```

### Initializing Configuration

```bash
# Initialize a new configuration file with default values
immuta config init

# Initialize a new configuration file with a specific name and format
immuta config init --file custom-config.yaml --format yaml

# Initialize a new configuration file and overwrite existing file
immuta config init --force
```

### Loading Configuration

```bash
# Load configuration from a file
immuta config load --file config.yaml

# Load configuration from environment variables
immuta config load --env-prefix IMMUTA_

# Load configuration from Azure Key Vault
immuta config load --secrets-provider azure --vault-url https://example.vault.azure.net

# Load configuration from AWS Secrets Manager
immuta config load --secrets-provider aws --region us-east-1 --secret-name immuta-config

# Load configuration from HashiCorp Vault
immuta config load --secrets-provider hashicorp --vault-url https://vault.example.com:8200
```

### Managing Configuration Profiles

```bash
# List all profiles
immuta config profile list

# List profiles of a specific type
immuta config profile list --type environment
immuta config profile list --type role

# List only enabled profiles
immuta config profile list --enabled-only

# View a specific profile
immuta config profile view development

# View a specific profile in a specific format
immuta config profile view development --format json

# Create a new profile
immuta config profile create development --type environment --description "Development environment"

# Create a new profile with configuration from a file
immuta config profile create development --type environment --file development-config.json

# Update an existing profile
immuta config profile update development --description "Updated description"

# Enable a profile
immuta config profile enable development

# Disable a profile
immuta config profile disable development

# Delete a profile
immuta config profile delete development --force

# Apply a profile
immuta config profile apply development
```

## User Management

The `user` command group provides commands for managing users.

```bash
# List users
immuta user list

# Get user by ID
immuta user get 123

# Create user
immuta user create --email <EMAIL> --name "User Name" --role DataScientist

# Update user
immuta user update 123 --role Admin

# Delete user
immuta user delete 123
```

## Data Source Management

The `data-source` command group provides commands for managing data sources.

```bash
# List data sources
immuta data-source list

# Get data source by ID
immuta data-source get 123

# Create data source from JSON file
immuta data-source create --file data-source.json

# Update data source
immuta data-source update 123 --file data-source.json

# Delete data source
immuta data-source delete 123
```

## Backup and Restore

The `backup` and `restore` command groups provide commands for backing up and restoring Immuta instances.

```bash
# Backup all entities
immuta backup all --output-dir /path/to/backup

# Backup users
immuta backup users --output-dir /path/to/backup

# Backup data sources
immuta backup data-sources --output-dir /path/to/backup

# Backup policies
immuta backup policies --output-dir /path/to/backup

# Restore all entities
immuta restore all --backup-dir /path/to/backup

# Restore users
immuta restore users --backup-dir /path/to/backup

# Restore data sources
immuta restore data-sources --backup-dir /path/to/backup

# Restore policies
immuta restore policies --backup-dir /path/to/backup
```

## Batch Operations

The `batch` command group provides commands for batch operations.

```bash
# Import entities from a file
immuta batch import --file entities.json --type users

# Export entities to a file
immuta batch export --file entities.json --type users

# Validate entities in a file
immuta batch validate --file entities.json --type users
```

## API Server

The `api` command group provides commands for managing the API server.

```bash
# Start the API server
immuta api start --host 0.0.0.0 --port 8000

# Generate an API key
immuta api generate-key --username admin

# Add an API user
immuta api add-user --username admin --password password

# List API users
immuta api list-users

# Disable an API user
immuta api disable-user --username admin
```
