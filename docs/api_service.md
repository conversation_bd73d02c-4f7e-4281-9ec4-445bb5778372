# API Service

The Immuta SRE Toolkit includes a REST API service that provides a way to interact with Immuta programmatically. This document describes how to use the API service.

## Starting the API Service

You can start the API service using the CLI:

```bash
# Start the API service on localhost:8000
immuta api start

# Start the API service on a specific host and port
immuta api start --host 0.0.0.0 --port 8080

# Start the API service with auto-reload for development
immuta api start --reload

# Start the API service with multiple workers
immuta api start --workers 4
```

## Authentication

The API service uses JWT-based authentication. You need to obtain an access token by providing your username and password to the `/token` endpoint.

### Managing API Users

You can manage API users using the CLI:

```bash
# Add a new API user
immuta api user-add --username admin --password password

# List all API users
immuta api user-list

# Disable an API user
immuta api user-disable --username admin
```

### Generating API Keys

You can generate API keys for authentication using the CLI:

```bash
# Generate an API key for a user that expires in 30 days
immuta api generate-key --username admin --expires-days 30
```

### Authenticating with the API

To authenticate with the API, you need to obtain an access token by sending a POST request to the `/token` endpoint with your username and password:

```bash
curl -X POST http://localhost:8000/token \
  -d "username=admin&password=password" \
  -H "Content-Type: application/x-www-form-urlencoded"
```

The response will include an access token:

```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

You can then use this token to authenticate with the API by including it in the `Authorization` header:

```bash
curl -X GET http://localhost:8000/users \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## API Endpoints

The API service provides the following endpoints:

### Users

- `GET /users`: List all users
- `GET /users/{user_id}`: Get a user by ID or email
- `POST /users`: Create a new user
- `DELETE /users/{user_id}`: Delete a user

### Data Sources

- `GET /data-sources`: List all data sources
- `GET /data-sources/{data_source_id}`: Get a data source by ID or name
- `POST /data-sources`: Create a new data source
- `DELETE /data-sources/{data_source_id}`: Delete a data source

## Rate Limiting

The API service includes rate limiting to prevent abuse. By default, clients are limited to 20 requests per minute. If you exceed this limit, you will receive a `429 Too Many Requests` response.

## Caching

The API service includes caching for GET requests to improve performance. Responses are cached for 60 seconds by default.

## API Documentation

The API service includes Swagger documentation that you can access at `/docs` when the service is running:

```
http://localhost:8000/docs
```

This provides an interactive interface for exploring and testing the API.

## Using the API in Python

You can use the API in Python using the `requests` library:

```python
import requests

# Authenticate
response = requests.post(
    "http://localhost:8000/token",
    data={"username": "admin", "password": "password"},
)
token = response.json()["access_token"]

# Use the token for API requests
headers = {"Authorization": f"Bearer {token}"}

# List users
response = requests.get("http://localhost:8000/users", headers=headers)
users = response.json()
print(users)

# Create a user
user_data = {
    "email": "<EMAIL>",
    "name": "Test User",
    "attributes": {
        "role": "DATA_SCIENTIST"
    }
}
response = requests.post(
    "http://localhost:8000/users",
    json=user_data,
    headers=headers,
)
user = response.json()
print(user)
```
