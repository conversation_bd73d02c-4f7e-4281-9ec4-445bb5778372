# Immuta API Client

The Immuta API Client provides a robust interface for interacting with the Immuta API. It includes support for:

- API versioning
- Comprehensive error handling
- Rate limiting
- Circuit breaking
- Automatic retries with exponential backoff
- Mock mode for testing

## Basic Usage

```python
from immuta_toolkit.api.client import ImmutaApiClient

# Create a client
client = ImmutaApiClient(
    base_url="https://your-immuta-instance.com",
    api_key="your-api-key",
    api_version="v1",
)

# Make a GET request
users = client.get("bim/users")

# Make a POST request
new_user = client.post("bim/user", json_data={
    "email": "<EMAIL>",
    "name": "Test User",
    "attributes": {"role": "DataScientist"},
})

# Make a PUT request
updated_user = client.put(f"bim/user/{user_id}", json_data={
    "name": "Updated User",
})

# Make a DELETE request
client.delete(f"bim/user/{user_id}")

# Close the client when done
client.close()
```

## Error Handling

The client provides comprehensive error handling with specific exception types:

```python
from immuta_toolkit.api.exceptions import (
    ImmutaApiError,
    AuthenticationError,
    AuthorizationError,
    ResourceNotFoundError,
    ValidationError,
    RateLimitError,
    ServerError,
    CircuitBreakerError,
    TimeoutError,
    ConnectionError,
    ApiVersionError,
)

try:
    client.get("bim/users")
except AuthenticationError:
    print("Authentication failed")
except ResourceNotFoundError:
    print("Resource not found")
except ValidationError as e:
    print(f"Validation error: {e}")
except RateLimitError as e:
    print(f"Rate limit exceeded. Retry after {e.retry_after} seconds")
except ServerError:
    print("Server error")
except CircuitBreakerError as e:
    print(f"Circuit breaker is open. Reset in {e.reset_timeout} seconds")
except TimeoutError:
    print("Request timed out")
except ConnectionError:
    print("Connection error")
except ApiVersionError as e:
    print(f"API version error. Current: {e.current_version}, Supported: {e.supported_versions}")
except ImmutaApiError as e:
    print(f"API error: {e}")
```

## Circuit Breaker

The client includes a circuit breaker to prevent cascading failures:

```python
# Get the current state of the circuit breaker
state = client.get_circuit_breaker_state()
print(f"Circuit breaker state: {state['state']}")

# Reset the circuit breaker
client.reset_circuit_breaker()
```

## Mock Mode

The client can be run in mock mode for testing:

```python
# Create a client in mock mode
client = ImmutaApiClient(
    base_url="https://example.com",
    api_key="test_api_key",
    is_local=True,
)

# All requests will return empty responses without making actual API calls
users = client.get("bim/users")  # Returns {}
```

## Advanced Configuration

The client supports advanced configuration options:

```python
client = ImmutaApiClient(
    base_url="https://your-immuta-instance.com",
    api_key="your-api-key",
    api_version="v1",
    timeout=30,  # Request timeout in seconds
    max_retries=3,  # Maximum number of retries
    backoff_factor=0.5,  # Backoff factor for retries
    rate_limit=10,  # Maximum number of requests per second
    rate_limit_window=1,  # Window size for rate limiting in seconds
    circuit_breaker_threshold=5,  # Number of failures before opening the circuit
    circuit_breaker_timeout=60,  # Seconds to wait before attempting to close the circuit
)
```

## Request Options

Each request method supports additional options:

```python
# Make a request with custom headers
client.get("bim/users", headers={"X-Custom-Header": "value"})

# Make a request without rate limiting
client.get("bim/users", use_rate_limiter=False)

# Make a request without circuit breaking
client.get("bim/users", use_circuit_breaker=False)

# Get the raw response object
response = client.get("bim/users", raw_response=True)
```

## API Versioning

The client supports API versioning:

```python
# Create a client with a specific API version
client = ImmutaApiClient(
    base_url="https://your-immuta-instance.com",
    api_key="your-api-key",
    api_version="v1",
)

# The client will automatically add the API version to the URL
# This will make a request to https://your-immuta-instance.com/api/v1/bim/users
client.get("bim/users")

# If the endpoint already includes the API version, it will not be added again
# This will make a request to https://your-immuta-instance.com/api/v1/bim/users
client.get("api/v1/bim/users")
```

## Testing

The client includes comprehensive tests:

```bash
# Run the tests
pdm run pytest tests/api/test_client.py -v
```
