# Configuration Management

The Immuta SRE Toolkit provides a comprehensive configuration management system that supports multiple configuration sources, validation, profiles, versioning, and secure storage of sensitive information.

## Key Features

- **Multiple Configuration Sources**: Load configuration from files, environment variables, command-line arguments, and secrets managers
- **Multiple File Formats**: Support for JSON, YAML, and TOML configuration files
- **Configuration Profiles**: Define and switch between different configuration profiles for different environments or use cases
- **Configuration Versioning**: Track changes to configuration with version history and rollback capability
- **Validation**: Comprehensive validation of configuration values with detailed error messages
- **Secrets Management**: Secure storage and retrieval of sensitive configuration values
- **Migration**: Support for migrating configuration between different schema versions
- **Extensibility**: Easily extend the configuration system with custom sources and validators

## Configuration Sources

The toolkit supports the following configuration sources:

- **File-based configuration**: JSON, YAML, and TOML formats
- **Environment variables**: Prefixed environment variables (e.g., `IMMUTA_API_URL`)
- **Command-line arguments**: Arguments passed to the CLI
- **Secrets managers**: Azure Key Vault, AWS Secrets Manager, and HashiCorp Vault

### File Configuration

File-based configuration is supported in JSON, YAML, and TOML formats. The default configuration file is `config.yaml` in the current directory.

```yaml
# Example configuration file
api:
  base_url: "https://example.immuta.com"
  timeout: 30
  max_retries: 3

logging:
  level: "INFO"
  file: "logs/immuta.log"

storage:
  local_path: "data"
```

### Environment Variables

Environment variables are prefixed with `IMMUTA_` by default. The environment variable names are converted to lowercase and underscores are used as separators for nested configuration.

```bash
# Example environment variables
export IMMUTA_API_BASE_URL="https://example.immuta.com"
export IMMUTA_API_TIMEOUT=30
export IMMUTA_LOGGING_LEVEL="INFO"
```

### Command-line Arguments

Command-line arguments can be used to override configuration values. The argument names are converted to configuration keys.

```bash
# Example command-line arguments
immuta-cli --api-base-url="https://example.immuta.com" --api-timeout=30 --logging-level="INFO"
```

### Secrets Managers

The toolkit supports the following secrets managers:

#### Azure Key Vault

```yaml
# Example configuration for Azure Key Vault
secrets:
  provider: "azure"
  vault_url: "https://example.vault.azure.net"
  key_prefix: "immuta-"
```

#### AWS Secrets Manager

```yaml
# Example configuration for AWS Secrets Manager
secrets:
  provider: "aws"
  region: "us-east-1"
  secret_name: "immuta-config"  # Optional: Load a single secret
  key_prefix: "immuta-"  # Optional: Load multiple secrets with this prefix
```

#### HashiCorp Vault

```yaml
# Example configuration for HashiCorp Vault
secrets:
  provider: "hashicorp"
  vault_url: "https://vault.example.com:8200"
  auth_method: "token"  # Options: token, approle, userpass, kubernetes
  token: "s.xxxxxxxx"  # For token authentication
  # For AppRole authentication
  role_id: "xxxxxxxx"
  secret_id: "xxxxxxxx"
  # For userpass authentication
  username: "username"
  password: "password"
  # For Kubernetes authentication
  role: "immuta"
  # Other options
  engine_type: "kv"  # Options: kv, database, aws
  engine_version: 2  # For KV engine
  path: "secret/immuta"  # Path to secret
  mount_point: "secret"  # Mount point for KV engine
```

## Configuration Profiles

The toolkit supports configuration profiles for different environments (development, testing, production) and roles (admin, operator, auditor).

```yaml
# Example configuration profiles
profiles:
  development:
    type: "environment"
    enabled: true
    api:
      base_url: "https://dev.example.immuta.com"

  production:
    type: "environment"
    enabled: false
    api:
      base_url: "https://prod.example.immuta.com"

  admin:
    type: "role"
    enabled: true
    features:
      backup: true
      restore: true
      user_management: true
```

## Configuration Validation

The toolkit provides comprehensive configuration validation to ensure that the configuration is valid and consistent.

```python
# Example validation rules
validator = ConfigValidator()

# Add a custom validation rule
def validate_custom_value(value):
    if value == "invalid":
        return ValidationResult(
            key="custom.value",
            level=ValidationLevel.ERROR,
            message="Invalid value",
            value=value,
        )
    return None

validator.add_rule("custom.value", validate_custom_value)

# Validate configuration
results = validator.validate(config)
for result in results:
    print(f"{result.level}: {result.key} - {result.message}")
```

## Configuration Versioning

The toolkit supports configuration versioning to track changes to configuration over time and enable rollback to previous versions.

```python
from immuta_toolkit.config import ConfigManager

# Create configuration manager with versioning enabled
manager = ConfigManager(enable_versioning=True, config_dir="/path/to/config")

# Load configuration
config = manager.load()

# Save configuration with version
manager.save(
    path="/path/to/config/config.yaml",
    format=ConfigFormat.YAML,
    version="1.0.0",
    version_description="Initial configuration",
)

# Get version history
versions = manager.get_version_history()
for version in versions:
    print(f"{version['version']}: {version['description']} ({version['timestamp']})")

# Get current version
current = manager.get_current_version()
print(f"Current version: {current['version']}")

# Restore previous version
manager.restore_version("0.9.0")
```

## Configuration Migration

The toolkit supports configuration migration to handle changes in the configuration schema.

```python
# Example migration
migration = ConfigMigration()

def migrate_to_1_0_0(config):
    # Rename api.url to api.base_url
    if "api" in config and "url" in config["api"]:
        config["api"]["base_url"] = config["api"].pop("url")
    return config

migration.add_migration("1.0.0", migrate_to_1_0_0)

# Migrate configuration
result = migration.migrate(config, "1.0.0")
```

## Usage Examples

### Loading Configuration

```python
from immuta_toolkit.config import load_config, get_config_value

# Load configuration from various sources
load_config(
    config_file="config.yaml",
    env_prefix="IMMUTA_",
    secrets_provider="azure",
    vault_url="https://example.vault.azure.net",
)

# Get configuration values
api_url = get_config_value("api.base_url")
api_timeout = get_config_value("api.timeout")
```

### Command-line Arguments

```python
import argparse
from immuta_toolkit.config import load_args

# Create argument parser
parser = argparse.ArgumentParser()
parser.add_argument("--api-url", dest="api.base_url", type=str)
parser.add_argument("--api-timeout", dest="api.timeout", type=int)

# Parse arguments
args = parser.parse_args()

# Load configuration from arguments
load_args(parser, args)
```

### Custom Configuration Manager

```python
from immuta_toolkit.config import ConfigManager, ConfigFormat

# Create configuration manager
manager = ConfigManager()

# Load from file
manager.from_file("config.yaml", format=ConfigFormat.YAML)

# Load from environment variables
manager.from_env("IMMUTA_")

# Load from command-line arguments
manager.from_args(parser, args)

# Load from secrets manager
manager.from_secrets("azure", vault_url="https://example.vault.azure.net")

# Load configuration
config = manager.load()

# Get configuration values
api_url = manager.get("api.base_url")
api_timeout = manager.get("api.timeout")
```
