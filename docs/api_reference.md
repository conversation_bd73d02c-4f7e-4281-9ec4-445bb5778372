# Immuta SRE Toolkit API Reference

This document provides a comprehensive reference for the Immuta SRE Toolkit API.

## Core Client

### ImmutaClient

The main client for interacting with the Immuta API.

```python
from immuta_toolkit.client import ImmutaClient

# Initialize with API key
client = ImmutaClient(
    api_key="your_api_key",
    base_url="https://your-immuta-instance.com"
)

# Initialize with Azure Key Vault integration
client = ImmutaClient(
    base_url="https://your-immuta-instance.com",
    secrets_provider="azure",
    vault_url="https://your-key-vault.vault.azure.net/"
)

# Initialize in local mode (for testing)
client = ImmutaClient(
    is_local=True
)
```

## User Management

### List Users

```python
# List all users
users = client.user_service.list_users()

# List users with pagination
users = client.user_service.list_users(limit=100, offset=0)

# List users with filtering
users = client.user_service.list_users(query="search_term")
```

### Get User

```python
# Get user by ID
user = client.user_service.get_user(user_id=123)

# Get user by email
user = client.user_service.get_user_by_email("<EMAIL>")
```

### Create User

```python
from immuta_toolkit.models import UserModel, UserAttributes, UserRole

# Create a new user
user = UserModel(
    email="<EMAIL>",
    name="User Name",
    attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
    groups=["Group1", "Group2"]
)
result = client.user_service.create_user(user)
```

### Update User

```python
# Update user role
result = client.user_service.update_user(
    user_id=123,
    role=UserRole.ADMIN
)

# Update user groups
result = client.user_service.update_user(
    user_id=123,
    groups=["Group1", "Group2", "NewGroup"]
)
```

### Delete User

```python
result = client.user_service.delete_user(user_id=123)
```

## Data Source Management

### List Data Sources

```python
# List all data sources
data_sources = client.data_source_service.list_data_sources()

# List data sources with pagination
data_sources = client.data_source_service.list_data_sources(limit=100, offset=0)

# List data sources with filtering
data_sources = client.data_source_service.list_data_sources(query="search_term")
```

### Get Data Source

```python
# Get data source by ID
data_source = client.data_source_service.get_data_source(data_source_id=123)

# Get data source by name
data_source = client.data_source_service.get_data_source_by_name("Data Source Name")
```

### Create Data Source

```python
# Create a new data source
data_source = {
    "name": "New Data Source",
    "description": "Description of the data source",
    "handler": {
        "type": "Snowflake",
        "config": {
            "account": "your_account",
            "warehouse": "your_warehouse",
            "database": "your_database",
            "schema": "your_schema",
            "table": "your_table"
        }
    }
}
result = client.data_source_service.create_data_source(data_source)
```

### Update Data Source

```python
# Update data source
result = client.data_source_service.update_data_source(
    data_source_id=123,
    name="Updated Name",
    description="Updated description"
)
```

### Delete Data Source

```python
result = client.data_source_service.delete_data_source(data_source_id=123)
```

## Policy Management

### List Policies

```python
# List all policies
policies = client.policy_service.list_policies()

# List policies for a specific data source
policies = client.policy_service.list_policies(data_source_id=123)

# List policies by type
policies = client.policy_service.list_policies(policy_type="RowRedaction")
```

### Get Policy

```python
policy = client.policy_service.get_policy(policy_id=123)
```

### Create Policy

```python
# Create a row redaction policy
policy = {
    "name": "Row Redaction Policy",
    "policy_type": "RowRedaction",
    "data_source_id": 123,
    "rule": {
        "column": "sensitive_column",
        "operator": "EQUALS",
        "value": "sensitive_value"
    }
}
result = client.policy_service.create_policy(policy)
```

### Delete Policy

```python
result = client.policy_service.delete_policy(policy_id=123)
```

## Backup and Restore

### Backup Data Sources

```python
# Backup all data sources
result = client.backup_service.backup_data_sources(
    output_dir="/path/to/backup",
    include_policies=True,
    include_tags=True,
    include_metadata=True
)

# Backup specific data sources
result = client.backup_service.backup_data_sources(
    output_dir="/path/to/backup",
    data_source_ids=[1, 2, 3],
    include_policies=True,
    include_tags=True,
    include_metadata=True
)
```

### Restore Data Sources

```python
# Restore all data sources
result = client.backup_service.restore_data_sources(
    backup_dir="/path/to/backup",
    include_policies=True,
    include_tags=True,
    include_metadata=True
)

# Restore specific data sources
result = client.backup_service.restore_data_sources(
    backup_dir="/path/to/backup",
    data_source_ids=[1, 2, 3],
    include_policies=True,
    include_tags=True,
    include_metadata=True
)
```

### Backup All

```python
# Backup everything
result = client.backup_service.backup_all(
    output_dir="/path/to/backup",
    include_users=True,
    include_policies=True,
    include_data_sources=True,
    include_projects=True,
    include_purposes=True
)
```

## Error Handling

All API calls can raise exceptions. It's recommended to handle them appropriately:

```python
from requests.exceptions import RequestException

try:
    users = client.user_service.list_users()
except RequestException as e:
    print(f"API error: {e}")
except ValueError as e:
    print(f"Validation error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

## Dry Run Mode

Most operations support a dry run mode to preview changes without applying them:

```python
# Preview user creation without actually creating the user
result = client.user_service.create_user(user, dry_run=True)

# Preview data source backup without actually backing up
result = client.backup_service.backup_data_sources(
    output_dir="/path/to/backup",
    dry_run=True
)
```
