# Development Guide

This guide provides instructions for setting up a development environment and contributing to the Immuta SRE Toolkit.

## Prerequisites

- Python 3.13 (primary supported version)
- Python 3.9 or higher (3.9, 3.10, 3.11, 3.12 are also supported)
- <PERSON><PERSON> (Python Dependency Manager)
- Git
- Docker (optional, for containerized development)

## Setting Up the Development Environment

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/davidlu1001/immuta-sre-toolkit.git
cd immuta-sre-toolkit
```

### 2. Install PDM

If you don't have PDM installed, you can install it using pip:

```bash
pip install pdm
```

### 3. Initialize the Project

```bash
pdm init
```

### 4. Install Dependencies

```bash
pdm install -G dev
```

This will install all dependencies, including development dependencies.

### 5. Set Up Pre-commit Hooks

```bash
pdm run pre-commit install
```

This will install pre-commit hooks to ensure code quality before committing.

## Development Workflow

### 1. Create a Feature Branch

```bash
git checkout -b feature/your-feature-name
```

### 2. Make Changes

Make your changes to the codebase. Be sure to follow the coding standards and add tests for new functionality.

### 3. Run Tests

```bash
# Run all tests
pdm run pytest

# Run tests with coverage
pdm run pytest --cov=immuta_toolkit

# Run specific tests
pdm run pytest tests/unit/test_specific_module.py

# Run tests with verbose output
pdm run pytest -xvs
```

For more detailed information about testing, see [Testing Guide](testing.md).

### 4. Run Linting and Formatting

```bash
# Run linting
pdm run ruff check src/ tests/

# Run formatting
pdm run black src/ tests/

# Run type checking
pdm run mypy src/
```

### 5. Commit Changes

```bash
git add .
git commit -m "Add your feature description"
```

The pre-commit hooks will automatically check your code for issues before committing.

### 6. Push Changes

```bash
git push origin feature/your-feature-name
```

### 7. Create a Pull Request

Create a pull request on GitHub to merge your changes into the main branch.

## Using Docker for Development

### 1. Build the Development Container

```bash
make build-dev
```

### 2. Run the Development Container

```bash
make run-dev
```

This will start a container with all development dependencies installed and mount the current directory as a volume.

### 3. Run Commands Inside the Container

```bash
# Run tests
pdm run pytest

# Run linting
pdm run ruff check src/ tests/

# Run the CLI
pdm run immuta --help
```

## Project Structure

```
immuta-sre-toolkit/
├── src/
│   └── immuta_toolkit/
│       ├── __init__.py
│       ├── cli/
│       │   ├── __init__.py
│       │   ├── main.py
│       │   └── backup_commands.py
│       ├── models.py
│       ├── client.py
│       ├── services/
│       │   ├── __init__.py
│       │   ├── backup_service.py
│       │   ├── batch_service.py
│       │   ├── data_source_service.py
│       │   ├── policy_service.py
│       │   ├── project_service.py
│       │   ├── purpose_service.py
│       │   ├── tag_service.py
│       │   └── user_service.py
│       └── utils/
│           ├── __init__.py
│           ├── cache.py
│           ├── logging.py
│           ├── rate_limiter.py
│           ├── secrets.py
│           └── storage.py
├── tests/
│   ├── unit/
│   │   ├── test_client.py
│   │   ├── test_backup_service.py
│   │   └── ...
│   ├── integration/
│   │   └── test_project_service_integration.py
│   └── e2e/
│       └── test_web_automation.py
├── examples/
│   ├── add_users_to_group.py
│   ├── datasource_report.py
│   └── policy_batch_update.py
├── docker/
│   ├── Dockerfile.prod
│   ├── Dockerfile.dev
│   ├── docker-compose.yml
│   ├── entrypoint.sh
│   └── README.md
├── configs/
│   ├── default.yaml
│   ├── environments/
│   │   ├── dev.yaml
│   │   ├── test.yaml
│   │   └── prod.yaml
│   ├── schemas/
│   └── examples/
├── docs/
│   ├── README.md
│   ├── api_reference.md
│   ├── security_best_practices.md
│   └── development_guide.md
├── scripts/
│   ├── build_binary.py
│   ├── fix-lock.py
│   └── run-docker.sh
├── tools/
│   └── README.md
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── docker-publish.yml
├── pyproject.toml
├── pdm.lock
├── Makefile
└── README.md
```

## Adding New Features

### 1. Add a New Service

To add a new service:

1. Create a new file in `src/immuta_toolkit/services/`
2. Implement the service class
3. Add the service to the client in `src/immuta_toolkit/client.py`
4. Add tests in `tests/unit/`

Example:

```python
# src/immuta_toolkit/services/new_service.py
class NewService:
    def __init__(self, client):
        self.client = client

    def new_method(self, param1, param2):
        # Implementation
        pass
```

```python
# src/immuta_toolkit/client.py
from immuta_toolkit.services.new_service import NewService

class ImmutaClient:
    def __init__(self, ...):
        # Existing initialization

        # Add the new service
        self.new_service = NewService(self)
```

### 2. Add a New CLI Command

To add a new CLI command:

1. Create a new command group in `src/immuta_toolkit/cli/` if needed
2. Add the command to the appropriate group
3. Add tests in `tests/unit/`

Example:

```python
# src/immuta_toolkit/cli/new_commands.py
import click

@click.group()
def new_group():
    """New command group."""
    pass

@new_group.command("new-command")
@click.option("--param1", help="Parameter 1")
@click.option("--param2", help="Parameter 2")
@click.pass_context
def new_command(ctx, param1, param2):
    """New command description."""
    client = ctx.obj["client"]
    result = client.new_service.new_method(param1, param2)
    # Handle result
```

```python
# src/immuta_toolkit/cli/main.py
from immuta_toolkit.cli.new_commands import new_group

# Add the new command group
cli.add_command(new_group)
```

## Release Process

### 1. Update Version

Update the version in `pyproject.toml`:

```toml
[project]
name = "immuta-sre-toolkit"
version = "0.2.0"  # Update this
```

### 2. Create a Release Commit

```bash
git add pyproject.toml
git commit -m "Bump version to 0.2.0"
```

### 3. Create a Tag

```bash
git tag -a v0.2.0 -m "Version 0.2.0"
git push origin v0.2.0
```

### 4. Create a GitHub Release

The CI/CD pipeline will automatically:
1. Build the package
2. Build binaries for all platforms
3. Create a GitHub release
4. Publish the package to PyPI

## Troubleshooting

### PDM Lock File Issues

If you encounter issues with the PDM lock file, you can fix it using:

```bash
python scripts/fix-lock.py
```

### Import Errors in Tests

If you encounter import errors in tests, make sure the package is installed in development mode:

```bash
pdm install -G dev
```

Or add the source directory to PYTHONPATH:

```bash
export PYTHONPATH=$PYTHONPATH:$(pwd)/src
```
