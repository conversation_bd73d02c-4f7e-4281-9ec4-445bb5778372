# Azure DevOps Integration

This document describes how to integrate the Immuta SRE Toolkit with Azure DevOps (ADO).

## Overview

The Immuta SRE Toolkit can be integrated with Azure DevOps to automate Immuta operations as part of your CI/CD pipelines. This integration allows you to:

- Automate Immuta operations as part of your deployment process
- Validate Immuta configurations before deployment
- Generate reports on Immuta resources
- Backup and restore Immuta resources

## Prerequisites

Before integrating the Immuta SRE Toolkit with Azure DevOps, you need:

1. An Azure DevOps organization and project
2. A service connection to your Immuta instance
3. The Immuta SRE Toolkit installed in your build environment

## Installation

### Using Docker

The easiest way to use the Immuta SRE Toolkit in Azure DevOps is to use the Docker image:

```yaml
resources:
  containers:
    - container: immuta_toolkit
      image: your-registry/immuta-sre-toolkit:latest
      endpoint: your-container-registry-service-connection
```

### Using Python

Alternatively, you can install the Immuta SRE Toolkit using Python:

```yaml
steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: '3.10'
      addToPath: true
  
  - script: |
      python -m pip install --upgrade pip
      pip install immuta-sre-toolkit
    displayName: 'Install Immuta SRE Toolkit'
```

## Authentication

### Using Environment Variables

You can authenticate with Immuta using environment variables:

```yaml
steps:
  - task: AzureKeyVault@2
    inputs:
      azureSubscription: 'your-azure-subscription'
      KeyVaultName: 'your-key-vault'
      SecretsFilter: 'IMMUTA-API-KEY'
      RunAsPreJob: true
  
  - script: |
      export IMMUTA_API_KEY=$(IMMUTA-API-KEY)
      export IMMUTA_BASE_URL=https://your-immuta-instance.com
      immuta users list
    displayName: 'List Immuta Users'
```

### Using Azure Key Vault

You can also use Azure Key Vault directly:

```yaml
steps:
  - script: |
      immuta --secrets-provider azure --vault-url https://your-vault.vault.azure.net/ users list
    displayName: 'List Immuta Users'
```

## Pipeline Examples

### Validating Immuta Configuration

```yaml
trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: AzureKeyVault@2
    inputs:
      azureSubscription: 'your-azure-subscription'
      KeyVaultName: 'your-key-vault'
      SecretsFilter: 'IMMUTA-API-KEY'
      RunAsPreJob: true
  
  - script: |
      export IMMUTA_API_KEY=$(IMMUTA-API-KEY)
      export IMMUTA_BASE_URL=https://your-immuta-instance.com
      
      # Validate data sources
      immuta data-sources validate --config-file ./immuta/data-sources.yaml
      
      # Validate policies
      immuta policies validate --config-file ./immuta/policies.yaml
    displayName: 'Validate Immuta Configuration'
```

### Deploying Immuta Configuration

```yaml
trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: AzureKeyVault@2
    inputs:
      azureSubscription: 'your-azure-subscription'
      KeyVaultName: 'your-key-vault'
      SecretsFilter: 'IMMUTA-API-KEY'
      RunAsPreJob: true
  
  - script: |
      export IMMUTA_API_KEY=$(IMMUTA-API-KEY)
      export IMMUTA_BASE_URL=https://your-immuta-instance.com
      
      # Create backup
      immuta backup create --output-dir ./backups
      
      # Apply data sources
      immuta data-sources apply --config-file ./immuta/data-sources.yaml
      
      # Apply policies
      immuta policies apply --config-file ./immuta/policies.yaml
    displayName: 'Deploy Immuta Configuration'
```

### Generating Reports

```yaml
trigger:
  - main

pool:
  vmImage: 'ubuntu-latest'

steps:
  - task: AzureKeyVault@2
    inputs:
      azureSubscription: 'your-azure-subscription'
      KeyVaultName: 'your-key-vault'
      SecretsFilter: 'IMMUTA-API-KEY'
      RunAsPreJob: true
  
  - script: |
      export IMMUTA_API_KEY=$(IMMUTA-API-KEY)
      export IMMUTA_BASE_URL=https://your-immuta-instance.com
      
      # Generate data source report
      immuta data-sources report --output-format html --output-file ./reports/data-sources.html
      
      # Generate policy report
      immuta policies report --output-format html --output-file ./reports/policies.html
    displayName: 'Generate Immuta Reports'
  
  - task: PublishBuildArtifacts@1
    inputs:
      pathtoPublish: './reports'
      artifactName: 'immuta-reports'
    displayName: 'Publish Reports'
```

## Azure DevOps Extensions

The Immuta SRE Toolkit can be integrated with Azure DevOps extensions to provide a more seamless experience:

### Azure Pipelines Tasks

You can create custom Azure Pipelines tasks for common Immuta operations:

```yaml
steps:
  - task: ImmutaBackup@1
    inputs:
      apiKey: $(IMMUTA-API-KEY)
      baseUrl: https://your-immuta-instance.com
      outputDir: ./backups
  
  - task: ImmutaApplyDataSources@1
    inputs:
      apiKey: $(IMMUTA-API-KEY)
      baseUrl: https://your-immuta-instance.com
      configFile: ./immuta/data-sources.yaml
```

### Azure DevOps Dashboard Widgets

You can create custom Azure DevOps dashboard widgets to display Immuta metrics and reports:

- Immuta Data Source Count Widget
- Immuta Policy Distribution Widget
- Immuta User Activity Widget

## Best Practices

When integrating the Immuta SRE Toolkit with Azure DevOps, follow these best practices:

1. **Use Service Principals**: Use Azure service principals for authentication instead of user credentials.
2. **Store Secrets Securely**: Store API keys and other secrets in Azure Key Vault.
3. **Version Control Configurations**: Store Immuta configurations in version control.
4. **Validate Before Deployment**: Always validate configurations before deploying them.
5. **Create Backups**: Create backups before making changes to Immuta resources.
6. **Use Dry Run Mode**: Use dry run mode to preview changes before applying them.
7. **Generate Reports**: Generate reports to document changes and the current state of Immuta resources.
8. **Implement Approval Gates**: Implement approval gates for sensitive operations.

## Troubleshooting

### Common Issues

1. **Authentication Failures**: Ensure that the API key is correct and has the necessary permissions.
2. **Connection Timeouts**: Increase the timeout value in the configuration.
3. **Rate Limiting**: Implement rate limiting to avoid hitting API rate limits.
4. **Permission Denied**: Ensure that the service principal has the necessary permissions.

### Logging

Enable detailed logging to troubleshoot issues:

```yaml
steps:
  - script: |
      export IMMUTA_API_KEY=$(IMMUTA-API-KEY)
      export IMMUTA_BASE_URL=https://your-immuta-instance.com
      export IMMUTA_LOG_LEVEL=DEBUG
      
      immuta users list
    displayName: 'List Immuta Users with Debug Logging'
```

## Conclusion

Integrating the Immuta SRE Toolkit with Azure DevOps allows you to automate Immuta operations as part of your CI/CD pipelines. By following the best practices outlined in this document, you can ensure a smooth and secure integration.
