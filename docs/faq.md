# Frequently Asked Questions (FAQ)

This document provides answers to common questions and solutions to common issues when working with the Immuta SRE Toolkit.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [Development Environment](#development-environment)
3. [Docker and Containerization](#docker-and-containerization)
4. [API and Authentication](#api-and-authentication)
5. [Backup and Restore](#backup-and-restore)
6. [Performance and Optimization](#performance-and-optimization)
7. [Monitoring and Observability](#monitoring-and-observability)
8. [Kubernetes Deployment](#kubernetes-deployment)
9. [Common Errors](#common-errors)
10. [Contributing](#contributing)

## Installation Issues

### Q: I'm getting dependency conflicts when installing with PDM

**A:** Try using a clean virtual environment and ensure you're using a compatible Python version (3.9+):

```bash
# Create a new virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install PDM
pip install pdm

# Install dependencies
pdm install
```

### Q: How do I install the toolkit without Docker?

**A:** You can install the toolkit directly using PDM or pip:

```bash
# Using PDM
pdm install

# Using pip
pip install immuta-sre-toolkit
```

## Development Environment

### Q: How do I set up my development environment?

**A:** Use the provided setup script:

```bash
./scripts/dev-setup.sh
```

Or follow the manual setup instructions in the README.

### Q: Pre-commit hooks are failing. How do I fix this?

**A:** Run pre-commit manually to see detailed error messages:

```bash
pdm run pre-commit run --all-files
```

Then fix the issues reported by the hooks. Common issues include:
- Code formatting (run `pdm run black src/ tests/`)
- Import sorting (run `pdm run isort src/ tests/`)
- Linting errors (run `pdm run ruff check --fix src/ tests/`)

## Docker and Containerization

### Q: Docker build fails with "context deadline exceeded"

**A:** This usually happens when Docker doesn't have enough resources. Try:
1. Increase Docker's memory allocation in Docker Desktop settings
2. Restart Docker
3. Clean up unused Docker resources: `docker system prune -a`

### Q: How do I access files inside the Docker container?

**A:** Mount a volume when running the container:

```bash
docker run -v /path/on/host:/app/data immuta-sre-toolkit
```

In the Kubernetes deployment, use a PersistentVolumeClaim.

## API and Authentication

### Q: I'm getting "401 Unauthorized" when connecting to Immuta

**A:** Check the following:
1. Verify your API key is correct
2. Ensure the API key has the necessary permissions
3. Check if the API key has expired
4. Verify the base URL is correct

### Q: How do I use Azure Key Vault for secrets?

**A:** Set the following environment variables:

```bash
export SECRETS_PROVIDER=azure
export AZURE_KEY_VAULT_URL=https://your-vault.vault.azure.net/
export AZURE_CLIENT_ID=your_client_id
export AZURE_CLIENT_SECRET=your_client_secret
export AZURE_TENANT_ID=your_tenant_id
```

## Backup and Restore

### Q: Backup fails with "Permission denied"

**A:** Ensure the user running the backup has write permissions to the backup directory:

```bash
# Check permissions
ls -la /path/to/backup/dir

# Change ownership if needed
sudo chown -R your_user:your_group /path/to/backup/dir
```

### Q: How do I automate backups?

**A:** Use the provided automated backup script with a cron job:

```bash
# Edit crontab
crontab -e

# Add a daily backup at 1 AM
0 1 * * * /path/to/scripts/automated-backup.sh --backup-dir /path/to/backups --retention-days 30
```

## Performance and Optimization

### Q: The toolkit is running slowly. How can I improve performance?

**A:** Try the following:
1. Increase cache TTL: `export TAG_CACHE_TTL_MINUTES=60`
2. Use batch operations for bulk changes
3. Use asynchronous operations where possible
4. Increase API rate limit if your Immuta instance can handle it: `export IMMUTA_API_RATE_LIMIT=20`

### Q: How do I monitor performance?

**A:** Use the built-in metrics endpoint:

```bash
curl http://localhost:8000/metrics
```

And integrate with Prometheus and Grafana for visualization.

## Monitoring and Observability

### Q: How do I check if the toolkit is healthy?

**A:** Use the healthcheck endpoint:

```bash
curl http://localhost:8000/health
```

### Q: What metrics are available?

**A:** The toolkit provides the following metrics:
- API request counts and durations
- Operation durations and error counts
- Cache hit/miss rates and sizes
- Backup sizes and durations

## Kubernetes Deployment

### Q: Pods are crashing with "CrashLoopBackOff"

**A:** Check the pod logs:

```bash
kubectl logs -l app=immuta-sre-toolkit
```

Common issues include:
- Missing environment variables
- Invalid API key or base URL
- Insufficient resources

### Q: How do I update the Kubernetes deployment?

**A:** Update the image tag in the deployment:

```bash
kubectl set image deployment/immuta-sre-toolkit immuta-sre-toolkit=ghcr.io/your-org/immuta-sre-toolkit:new-version
```

Or apply the updated manifest:

```bash
kubectl apply -f kubernetes/deployment.yaml
```

## Common Errors

### Q: "ModuleNotFoundError: No module named 'immuta_toolkit'"

**A:** This usually means the package is not installed or not in the Python path. Try:

```bash
# Install the package
pdm install

# Or add the src directory to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:/path/to/immuta-sre-toolkit/src
```

### Q: "ConnectionError: Connection refused"

**A:** This usually means the Immuta API is not reachable. Check:
1. The base URL is correct
2. Network connectivity to the Immuta instance
3. Firewall rules

## Contributing

### Q: How do I contribute to the project?

**A:** Follow these steps:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

### Q: How do I report a bug?

**A:** Create a GitHub issue with the following information:
1. Description of the bug
2. Steps to reproduce
3. Expected behavior
4. Actual behavior
5. Environment details (OS, Python version, etc.)
6. Logs and error messages
