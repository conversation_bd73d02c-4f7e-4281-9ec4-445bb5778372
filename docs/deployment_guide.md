# Deployment Guide

This guide provides instructions for deploying the Immuta SRE Toolkit in various environments.

## Deployment Options

The Immuta SRE Toolkit can be deployed in several ways:

1. **Python Package**: Install as a Python package using PDM or pip
2. **Standalone Binary**: Use pre-built binaries for Windows, macOS, or Linux
3. **Docker Container**: Run as a Docker container
4. **Kubernetes**: Deploy to a Kubernetes cluster

## 1. Python Package Deployment

### Prerequisites

- Python 3.9 or higher (3.9, 3.10, 3.11, 3.12, 3.13 are supported)
- PDM (recommended) or pip

### Installation

#### Using PDM (recommended)

```bash
pdm add immuta-sre-toolkit
```

#### Using pip

```bash
pip install immuta-sre-toolkit
```

### Configuration

Create a `.env` file with your configuration:

```bash
# Required
IMMUTA_API_KEY=your_api_key
IMMUTA_BASE_URL=https://your-immuta-instance.com

# Optional
IMMUTA_API_TIMEOUT=10
IMMUTA_API_RATE_LIMIT=10
```

### Usage

```bash
# Using PDM
pdm run immuta --help

# Using pip
immuta --help
```

## 2. Standalone Binary Deployment

### Prerequisites

- None (binaries are self-contained)

### Installation

1. Download the appropriate binary for your platform from the [releases page](https://github.com/davidlu1001/immuta-sre-toolkit/releases)
2. Make the binary executable (Linux/macOS):
   ```bash
   chmod +x immuta
   ```

### Configuration

Create a `.env` file in the same directory as the binary with your configuration:

```bash
# Required
IMMUTA_API_KEY=your_api_key
IMMUTA_BASE_URL=https://your-immuta-instance.com

# Optional
IMMUTA_API_TIMEOUT=10
IMMUTA_API_RATE_LIMIT=10
```

### Usage

```bash
# Linux/macOS
./immuta --help

# Windows
immuta.exe --help
```

## 3. Docker Container Deployment

### Prerequisites

- Docker

### Installation

#### Using Pre-built Image

```bash
docker pull ghcr.io/davidlu1001/immuta-sre-toolkit:latest
```

#### Building from Source

```bash
git clone https://github.com/davidlu1001/immuta-sre-toolkit.git
cd immuta-sre-toolkit
docker build -t immuta-sre-toolkit -f docker/Dockerfile.prod .
```

### Configuration

Create a `.env` file with your configuration:

```bash
# Required
IMMUTA_API_KEY=your_api_key
IMMUTA_BASE_URL=https://your-immuta-instance.com

# Optional
IMMUTA_API_TIMEOUT=10
IMMUTA_API_RATE_LIMIT=10
```

### Usage

```bash
# Using environment file
docker run -it --rm \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  ghcr.io/davidlu1001/immuta-sre-toolkit:latest \
  --help

# Using environment variables directly
docker run -it --rm \
  -e IMMUTA_API_KEY=your_api_key \
  -e IMMUTA_BASE_URL=https://your-immuta-instance.com \
  -v $(pwd)/data:/app/data \
  ghcr.io/davidlu1001/immuta-sre-toolkit:latest \
  user list
```

### Using Docker Compose

Use the provided `docker/docker-compose.yml` file or create your own:

```yaml
version: '3'
services:
  immuta-toolkit:
    image: ghcr.io/davidlu1001/immuta-sre-toolkit:latest
    env_file: .env
    volumes:
      - ./data:/app/data
    command: --help
```

Run with Docker Compose:

```bash
docker-compose -f docker/docker-compose.yml up
```

## 4. Kubernetes Deployment

### Prerequisites

- Kubernetes cluster
- kubectl

### Installation

1. Create a Kubernetes Secret for your Immuta API key:

```bash
kubectl create secret generic immuta-credentials \
  --from-literal=IMMUTA_API_KEY=your_api_key \
  --from-literal=IMMUTA_BASE_URL=https://your-immuta-instance.com
```

2. Create a Kubernetes ConfigMap for your configuration:

```bash
kubectl create configmap immuta-config \
  --from-literal=IMMUTA_API_TIMEOUT=10 \
  --from-literal=IMMUTA_API_RATE_LIMIT=10
```

3. Apply the Kubernetes manifests:

```bash
kubectl apply -f kubernetes/deployment.yaml
kubectl apply -f kubernetes/service.yaml
kubectl apply -f kubernetes/cronjob.yaml
```

### Example Kubernetes Manifests

#### deployment.yaml

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: immuta-sre-toolkit
  labels:
    app: immuta-sre-toolkit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: immuta-sre-toolkit
  template:
    metadata:
      labels:
        app: immuta-sre-toolkit
    spec:
      containers:
      - name: immuta-sre-toolkit
        image: ghcr.io/davidlu1001/immuta-sre-toolkit:latest
        command: ["immuta", "metrics", "start", "--port", "8000"]
        ports:
        - containerPort: 8000
        envFrom:
        - secretRef:
            name: immuta-credentials
        - configMapRef:
            name: immuta-config
        volumeMounts:
        - name: data
          mountPath: /app/data
        resources:
          limits:
            cpu: "1"
            memory: "512Mi"
          requests:
            cpu: "0.5"
            memory: "256Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: immuta-sre-toolkit-data
```

#### service.yaml

```yaml
apiVersion: v1
kind: Service
metadata:
  name: immuta-sre-toolkit
  labels:
    app: immuta-sre-toolkit
spec:
  ports:
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: immuta-sre-toolkit
```

#### cronjob.yaml

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: immuta-backup
spec:
  schedule: "0 0 * * *"  # Daily at midnight
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: immuta-backup
            image: ghcr.io/davidlu1001/immuta-sre-toolkit:latest
            command: ["immuta", "backup", "all", "--output-dir", "/app/data/backup"]
            envFrom:
            - secretRef:
                name: immuta-credentials
            - configMapRef:
                name: immuta-config
            volumeMounts:
            - name: data
              mountPath: /app/data
          restartPolicy: OnFailure
          volumes:
          - name: data
            persistentVolumeClaim:
              claimName: immuta-sre-toolkit-data
```

## Production Considerations

### Security

1. **Secrets Management**:
   - Use Azure Key Vault for storing secrets in production
   - Rotate API keys regularly
   - Use the principle of least privilege for API keys

2. **Network Security**:
   - Use HTTPS for all API connections
   - Implement network policies in Kubernetes
   - Consider using a service mesh for additional security

### High Availability

1. **Redundancy**:
   - Run multiple replicas in Kubernetes
   - Use pod anti-affinity to distribute across nodes

2. **Backup and Restore**:
   - Schedule regular backups
   - Test restore procedures
   - Implement retention policies

### Monitoring and Alerting

1. **Metrics**:
   - Collect metrics using Prometheus
   - Set up alerts for critical conditions
   - Monitor API rate limits and errors

2. **Logging**:
   - Configure centralized logging
   - Set appropriate log levels
   - Implement log rotation and retention

### Resource Management

1. **Resource Limits**:
   - Set appropriate CPU and memory limits
   - Monitor resource usage
   - Scale based on demand

2. **Storage**:
   - Use persistent storage for backups
   - Implement storage quotas
   - Monitor storage usage

## Troubleshooting

### Common Issues

1. **API Connection Issues**:
   - Check network connectivity
   - Verify API key and base URL
   - Check for rate limiting

2. **Permission Issues**:
   - Verify API key permissions
   - Check file system permissions for backups
   - Ensure proper Kubernetes RBAC

3. **Resource Issues**:
   - Check for CPU or memory limits
   - Monitor for out-of-memory errors
   - Adjust resource limits as needed

### Logs and Diagnostics

1. **Viewing Logs**:
   - Docker: `docker logs <container_id>`
   - Kubernetes: `kubectl logs <pod_name>`
   - Python: Check the configured log file

2. **Diagnostic Commands**:
   - Check health: `immuta health`
   - Check version: `immuta version`
   - Run with verbose logging: `immuta --verbose <command>`
