# Web Automation Framework

The Immuta SRE Toolkit includes a web automation framework based on <PERSON><PERSON> that provides a fallback mechanism when API operations fail. This document describes how to use the web automation framework.

## Architecture

The web automation framework follows an enhanced Page Object Model (POM) pattern with a component-based architecture, which separates the page structure from the test logic and promotes reusability. This makes tests more maintainable and robust.

```
ImmutaWebClient
    |
    +-- EnhancedBasePage
    |       |
    |       +-- LoginPage
    |       |
    |       +-- UsersPage
    |       |
    |       +-- DataSourcesPage
    |       |
    |       +-- PoliciesPage
    |       |
    |       +-- ProjectsPage
    |
    +-- Components
            |
            +-- BaseComponent
                |
                +-- NavigationComponent
                |
                +-- DialogComponent
                |
                +-- (Other reusable components)
```

### Key Improvements

The enhanced web automation framework includes several improvements:

1. **Robust Error Handling**
   - Specific exception types for different error scenarios
   - Detailed error messages with context information
   - Automatic screenshot capture on failure

2. **Retry Mechanisms**
   - Automatic retry for flaky UI interactions
   - Configurable retry count and delay
   - Exponential backoff for retries

3. **Performance Monitoring**
   - Timing metrics for page loads and interactions
   - Reporting for slow operations
   - Performance analysis capabilities

4. **Component-Based Architecture**
   - Reusable UI components across multiple pages
   - Better encapsulation of UI elements
   - Improved maintainability and testability

## Selector Registry

The framework uses a centralized selector registry to manage UI selectors. This makes it easy to update selectors when the UI changes without modifying the page objects.

```python
# Example selector registry
SELECTORS = {
    "2024.02": {
        "login.username": "input[name='username']",
        "login.password": "input[name='password']",
        "login.submit": "button[type='submit']",
        # ...
    }
}
```

## Hybrid Client

The Immuta SRE Toolkit uses a hybrid client that combines API and web automation approaches. The client first tries to use the API, and if that fails, it falls back to web automation.

```python
# Example usage of the hybrid client
from immuta_toolkit.hybrid_client import ImmutaHybridClient

# Initialize the hybrid client
client = ImmutaHybridClient(
    api_key="your-api-key",
    base_url="https://your-immuta-instance.com",
    username="your-username",
    password="your-password",
    use_web_fallback=True,
)

# Create a user (will use API first, then fall back to web if needed)
user = client.create_user(user_model)
```

## Configuration

The web automation framework can be configured using environment variables:

- `IMMUTA_BASE_URL`: Base URL of the Immuta instance
- `IMMUTA_USERNAME`: Username for web authentication
- `IMMUTA_PASSWORD`: Password for web authentication
- `IMMUTA_USE_WEB_FALLBACK`: Whether to use web automation as fallback (default: true)
- `IMMUTA_HEADLESS`: Whether to run the browser in headless mode (default: true)
- `IMMUTA_VERSION`: Immuta version for selector registry (default: 2024.02)

## CLI Options

The CLI includes options for controlling web automation:

```bash
# Enable web automation fallback (default)
immuta --web-fallback user list

# Disable web automation fallback
immuta --no-web-fallback user list

# Run with browser in non-headless mode (for debugging)
immuta --no-headless user list
```

## Using the Web Client Directly

You can use the web client directly if you need to perform operations that are not supported by the API:

```python
from immuta_toolkit.web.client import ImmutaWebClient

# Initialize the web client
client = ImmutaWebClient(
    base_url="https://your-immuta-instance.com",
    username="your-username",
    password="your-password",
    headless=True,
    browser_type="chromium",  # Options: chromium, firefox, webkit
    timeout=30000,  # Timeout in milliseconds
)

# Use with context manager for automatic cleanup
with ImmutaWebClient(
    base_url="https://your-immuta-instance.com",
    username="your-username",
    password="your-password",
) as client:
    # Create a user
    user = client.create_user(user_model)

    # Get a user
    user = client.get_user("<EMAIL>")

    # Delete a user
    client.delete_user("<EMAIL>")

    # Create a data source
    data_source = client.create_data_source(data_source_model)

    # Get a data source
    data_source = client.get_data_source("My Data Source")

    # Delete a data source
    client.delete_data_source("My Data Source")

    # Create a policy
    policy = client.create_policy(policy_model)

    # Get a policy
    policy = client.get_policy("My Policy")

    # Delete a policy
    client.delete_policy("My Policy")

    # Create a project
    project = client.create_project(project_model)

    # Get a project
    project = client.get_project("My Project")

    # Delete a project
    client.delete_project("My Project")

    # Add a member to a project
    client.add_project_member("My Project", "<EMAIL>", "Owner")

# Client is automatically closed when exiting the context manager
```

### Error Handling

The enhanced web client provides comprehensive error handling:

```python
from immuta_toolkit.web.exceptions import (
    WebAutomationError,
    ElementNotFoundError,
    ElementNotVisibleError,
    ElementNotClickableError,
    NavigationError,
    TimeoutError,
    AuthenticationError,
    FormSubmissionError,
)

try:
    client.create_user(user_model)
except ElementNotFoundError as e:
    print(f"Element not found: {e}")
    print(f"Screenshot: {e.screenshot_path}")
except AuthenticationError as e:
    print(f"Authentication failed: {e}")
except WebAutomationError as e:
    print(f"Web automation error: {e}")
    print(f"Screenshot: {e.screenshot_path}")
```

### Performance Monitoring

You can access performance metrics for web operations:

```python
# Get performance metrics from the page objects
metrics = client.users_page.get_performance_metrics()

# Print metrics
for operation, stats in metrics.items():
    print(f"{operation}:")
    print(f"  Min: {stats['min']:.2f} seconds")
    print(f"  Max: {stats['max']:.2f} seconds")
    print(f"  Avg: {stats['avg']:.2f} seconds")
    print(f"  Count: {stats['count']}")
```

## Testing with Web Automation

The Immuta SRE Toolkit includes tests for web automation. You can run these tests using PDM:

```bash
# Run all web automation tests
pdm run pytest tests/web/

# Run a specific web automation test
pdm run pytest tests/web/test_login.py
```

## Troubleshooting

### Browser Installation

The web automation framework requires a browser to be installed. You can install the browser using Playwright:

```bash
# Install Playwright browsers
pdm run playwright install --with-deps chromium
```

### Debugging

If you're having trouble with web automation, you can run the browser in non-headless mode to see what's happening:

```bash
# Run with browser in non-headless mode
immuta --no-headless user list
```

### Selectors

If the selectors are not working, you may need to update them for your version of Immuta. You can do this by modifying the selector registry in `src/immuta_toolkit/web/selectors.py`.

### Screenshots

You can take screenshots during web automation to help with debugging:

```python
# Take a screenshot
client.page.screenshot(path="screenshot.png")
```

## Extending the Framework

You can extend the web automation framework by adding new page objects, components, and selectors:

### Adding New Selectors

Add new selectors to the selector registry in `src/immuta_toolkit/web/selectors.py`:

```python
# Add selectors
SELECTORS["2024.02"]["new_feature.button"] = "button.new-feature"
SELECTORS["2024.02"]["new_feature.input"] = "input.new-feature"
SELECTORS["2024.02"]["new_feature.submit"] = "button[type='submit']"
```

### Creating New Components

Create reusable UI components that can be used across multiple pages:

```python
# Create a new component
from immuta_toolkit.web.components.base_component import BaseComponent

class NewFeatureComponent(BaseComponent):
    """Component for the new feature."""

    def __init__(self, page, timeout=10000):
        super().__init__(page, "new_feature.container", timeout)

    def enter_value(self, value):
        """Enter a value in the new feature input."""
        self.click_child("new_feature.input")

        # Get the child element and fill it
        input_element = self.get_child_element("new_feature.input")
        input_element.fill(value)

    def submit(self):
        """Submit the new feature form."""
        self.click_child("new_feature.submit")

        # Wait for the form to be submitted
        self.page.wait_for_load_state("networkidle")
```

### Creating New Page Objects

Create a new page object class that extends `EnhancedBasePage`:

```python
# Create a new page object
from immuta_toolkit.web.page_objects.enhanced_base_page import EnhancedBasePage
from immuta_toolkit.web.components.common import DialogComponent
from my_components import NewFeatureComponent

class NewFeaturePage(EnhancedBasePage):
    """Page object for the new feature page."""

    def __init__(self, page, base_url, timeout=10000):
        super().__init__(page, base_url, timeout)

        # Initialize components
        self.new_feature_component = NewFeatureComponent(page, timeout)
        self.dialog = DialogComponent(page, timeout)

    def navigate_to_new_feature(self):
        """Navigate to the new feature page."""
        self.navigate("new-feature")

        # Wait for the new feature component to be visible
        self.new_feature_component.wait_for_visible()

    def use_new_feature(self, value):
        """Use the new feature with the given value."""
        # Navigate to the new feature page
        self.navigate_to_new_feature()

        # Enter the value and submit
        self.new_feature_component.enter_value(value)
        self.new_feature_component.submit()

        # Handle confirmation dialog if it appears
        if self.dialog.is_visible(timeout=5000):
            self.dialog.confirm()

        # Return performance metrics
        return self.get_performance_metrics()
```

### Adding to the Web Client

Add the new page object to the web client:

```python
# Add to web client
class ImmutaWebClient:
    # ...
    def __init__(self, base_url, username, password, **kwargs):
        # ...
        self.new_feature_page = NewFeaturePage(self.page, self.base_url, self.timeout)

    def use_new_feature(self, value):
        """Use the new feature with the given value."""
        return self.new_feature_page.use_new_feature(value)
```

### Creating Tests

Create tests for the new feature:

```python
# Create tests
import pytest
from unittest.mock import MagicMock, patch

from immuta_toolkit.web.client import ImmutaWebClient

class TestNewFeature:
    """Tests for the new feature."""

    @pytest.fixture
    def mock_client(self):
        """Create a mock web client."""
        with patch("immuta_toolkit.web.client.ImmutaWebClient") as mock_client:
            client = mock_client.return_value
            client.new_feature_page = MagicMock()
            yield client

    def test_use_new_feature(self, mock_client):
        """Test using the new feature."""
        mock_client.use_new_feature("test value")
        mock_client.new_feature_page.use_new_feature.assert_called_once_with("test value")
```
