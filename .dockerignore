# Git
.git
.gitignore
.github

# Docker
.dockerignore
Dockerfile
Dockerfile.dev
docker-compose.yml

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.mypy_cache/

# PDM
.pdm.toml
__pypackages__/

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Project specific
data/
logs/
*.log
backups/
.env*
!.env.example

# Documentation
docs/build/

# Temporary files
tmp/
temp/
