#!/usr/bin/env python
"""Setup script for pip installation fallback."""

import os
import re
from setuptools import setup, find_packages

# Read version from pyproject.toml
with open("pyproject.toml", "r") as f:
    content = f.read()
    version_match = re.search(r'version = "([^"]+)"', content)
    version = version_match.group(1) if version_match else "0.0.0"

# Read dependencies from pyproject.toml
dependencies = [
    "requests>=2.32.3",
    "pydantic>=2.0.0",
    "click>=8.2.0",
    "rich>=14.0.0",
    "tenacity>=8.1.0",
    "pyyaml>=6.0.2",
    "azure-storage-blob>=12.25.1",
    "azure-monitor-query>=1.0.3",
    "azure-identity>=1.15.0",
    "azure-keyvault-secrets>=4.7.0",
    "snowflake-connector-python>=3.15.0",
    "pandas>=1.5.3",
    "openpyxl>=3.1.0",
    "ratelimit>=2.2.1",
    "faker>=13.15.1",
    "pytest>=8.0.0",
    "email-validator>=2.2.0",
    "prometheus-client>=0.16.0",
    "cryptography>=44.0.0",
]

setup(
    name="immuta-sre-toolkit",
    version=version,
    description="Immuta SRE Toolkit",
    author="Immuta SRE Team",
    author_email="<EMAIL>",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    install_requires=dependencies,
    python_requires="==3.13.*",
    entry_points={
        "console_scripts": [
            "immuta=immuta_toolkit.cli.main:main",
        ],
    },
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.13",
    ],
)
