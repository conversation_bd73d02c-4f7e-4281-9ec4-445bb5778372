{"categories": [{"title": "## 🚀 Features", "labels": ["feature", "enhancement"]}, {"title": "## 🐛 Bug Fixes", "labels": ["fix", "bug"]}, {"title": "## 📝 Documentation", "labels": ["documentation"]}, {"title": "## 🧪 Tests", "labels": ["test"]}, {"title": "## 🔧 Maintenance", "labels": ["chore", "maintenance"]}, {"title": "## 🔒 Security", "labels": ["security"]}, {"title": "## 📦 Dependencies", "labels": ["dependencies"]}], "ignore_labels": ["ignore"], "sort": {"order": "ASC", "on_property": "mergedAt"}, "template": "${{CHANGELOG}}\n\n## 📦 Assets\n\n* [Source code](https://github.com/${{owner}}/${{repo}}/archive/v${{version}}.tar.gz)\n* [Python package](https://pypi.org/project/immuta-sre-toolkit/${{version}}/)\n* [Docker image](https://github.com/${{owner}}/${{repo}}/pkgs/container/immuta-sre-toolkit)\n* [Binary releases](https://github.com/${{owner}}/${{repo}}/releases/tag/v${{version}})", "pr_template": "- ${{TITLE}} by @${{AUTHOR}} in #${{NUMBER}}", "empty_template": "No changes", "max_pull_requests": 1000, "max_back_track_time_days": 365}