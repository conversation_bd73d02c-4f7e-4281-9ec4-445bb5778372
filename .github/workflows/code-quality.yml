name: Code Quality

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

jobs:
  ruff:
    name: Ruff
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install ruff

      - name: Run Ruff
        run: |
          ruff check --output-format=github .
          ruff format --check .

  black:
    name: Black
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black

      - name: Run Black
        run: black --check .

  mypy:
    name: MyPy
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pdm
          pdm install -G dev

      - name: Run MyPy
        run: pdm run mypy src

  pylint:
    name: Pylint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pdm
          pdm install -G dev
          pip install pylint

      - name: Run Pylint
        run: pylint src/immuta_toolkit

  complexity:
    name: Complexity Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install radon xenon

      - name: Run Radon
        run: |
          radon cc src -a -s
          radon mi src -s

      - name: Run Xenon
        run: xenon --max-absolute B --max-modules B --max-average A src

  docstring-coverage:
    name: Docstring Coverage
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install interrogate

      - name: Run Interrogate
        run: interrogate -v src

  code-quality-report:
    name: Generate Code Quality Report
    needs: [ruff, black, mypy, pylint, complexity, docstring-coverage]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - uses: actions/checkout@v3

      - name: Generate code quality summary
        run: |
          echo "# Code Quality Results" > code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## Ruff" >> code-quality-summary.md
          echo "- Status: ${{ needs.ruff.result }}" >> code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## Black" >> code-quality-summary.md
          echo "- Status: ${{ needs.black.result }}" >> code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## MyPy" >> code-quality-summary.md
          echo "- Status: ${{ needs.mypy.result }}" >> code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## Pylint" >> code-quality-summary.md
          echo "- Status: ${{ needs.pylint.result }}" >> code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## Complexity Analysis" >> code-quality-summary.md
          echo "- Status: ${{ needs.complexity.result }}" >> code-quality-summary.md
          echo "" >> code-quality-summary.md
          echo "## Docstring Coverage" >> code-quality-summary.md
          echo "- Status: ${{ needs.docstring-coverage.result }}" >> code-quality-summary.md

      - name: Upload code quality summary
        uses: actions/upload-artifact@v3
        with:
          name: code-quality-summary
          path: code-quality-summary.md

      - name: Check if any job failed
        id: check-failures
        run: |
          if [[ "${{ needs.ruff.result }}" == "failure" || \
                "${{ needs.black.result }}" == "failure" || \
                "${{ needs.mypy.result }}" == "failure" || \
                "${{ needs.pylint.result }}" == "failure" || \
                "${{ needs.complexity.result }}" == "failure" || \
                "${{ needs.docstring-coverage.result }}" == "failure" ]]; then
            echo "has_failures=true" >> $GITHUB_OUTPUT
          else
            echo "has_failures=false" >> $GITHUB_OUTPUT
          fi

      - name: Comment on PR
        if: github.event_name == 'pull_request' && steps.check-failures.outputs.has_failures == 'true'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('code-quality-summary.md', 'utf8');
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });
