name: Security Scanning

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]
  schedule:
    - cron: '0 0 * * 0'  # Run weekly on Sunday at midnight

jobs:
  dependency-check:
    name: Dependency Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install safety bandit

      - name: Run safety check
        run: safety check

      - name: Generate requirements.txt for OWASP Dependency Check
        run: |
          pip install pdm
          pdm export -f requirements > requirements.txt

      - name: OWASP Dependency Check
        uses: dependency-check/Dependency-Check_Action@main
        with:
          project: 'immuta-sre-toolkit'
          path: '.'
          format: 'HTML'
          out: 'reports'
          args: >
            --scan requirements.txt
            --enableExperimental
            --failOnCVSS 7
            --suppression .github/workflows/suppressions.xml

      - name: Upload dependency check report
        uses: actions/upload-artifact@v3
        with:
          name: dependency-check-report
          path: reports/dependency-check-report.html

  bandit:
    name: Bandit Security Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install bandit bandit-sarif-formatter

      - name: Run bandit
        run: bandit -r src/ -x tests/ -f json -o bandit-results.json

      - name: Convert bandit results to SARIF
        run: |
          cat bandit-results.json | bandit-sarif-formatter > bandit-results.sarif

      - name: Upload bandit results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: bandit-results.sarif
          category: bandit

  semgrep:
    name: Semgrep Scan
    runs-on: ubuntu-latest
    container:
      image: returntocorp/semgrep
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Semgrep
        run: semgrep ci --config=p/python --config=p/security-audit --sarif > semgrep-results.sarif
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

      - name: Upload SARIF file
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: semgrep-results.sarif
          category: semgrep

  secrets-scan:
    name: Secret Scanning
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: TruffleHog OSS
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: ${{ github.event.repository.default_branch }}
          head: HEAD
          extra_args: --debug --only-verified

  codeql:
    name: CodeQL Analysis
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: python

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

  security-report:
    name: Generate Security Report
    needs: [dependency-check, bandit, semgrep, secrets-scan, codeql]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - uses: actions/checkout@v3

      - name: Download dependency check report
        uses: actions/download-artifact@v3
        with:
          name: dependency-check-report
          path: security-reports/dependency-check

      - name: Generate security summary
        run: |
          echo "# Security Scan Results" > security-summary.md
          echo "" >> security-summary.md
          echo "## Dependency Check" >> security-summary.md
          echo "- Status: ${{ needs.dependency-check.result }}" >> security-summary.md
          echo "" >> security-summary.md
          echo "## Bandit" >> security-summary.md
          echo "- Status: ${{ needs.bandit.result }}" >> security-summary.md
          echo "" >> security-summary.md
          echo "## Semgrep" >> security-summary.md
          echo "- Status: ${{ needs.semgrep.result }}" >> security-summary.md
          echo "" >> security-summary.md
          echo "## Secrets Scan" >> security-summary.md
          echo "- Status: ${{ needs.secrets-scan.result }}" >> security-summary.md
          echo "" >> security-summary.md
          echo "## CodeQL" >> security-summary.md
          echo "- Status: ${{ needs.codeql.result }}" >> security-summary.md
          echo "" >> security-summary.md
          echo "See detailed reports in the artifacts." >> security-summary.md

      - name: Upload security summary
        uses: actions/upload-artifact@v3
        with:
          name: security-summary
          path: security-summary.md

      - name: Notify on failure
        if: ${{ needs.dependency-check.result == 'failure' || needs.bandit.result == 'failure' || needs.semgrep.result == 'failure' || needs.secrets-scan.result == 'failure' || needs.codeql.result == 'failure' }}
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: 'Security scan failed',
              body: 'Security scan failed. Please check the security report for details.',
              labels: ['security', 'bug']
            })
