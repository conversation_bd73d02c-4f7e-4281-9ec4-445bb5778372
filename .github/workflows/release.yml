name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    name: Build Package
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install PDM
        run: |
          python -m pip install --upgrade pip
          pip install pdm
          pdm --version

      - name: Install dependencies
        run: |
          pdm config python.use_venv false
          pdm install

      - name: Build package
        run: pdm build

      - name: Upload package artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/
          if-no-files-found: error

  build-binaries:
    name: Build Binaries
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            platform: linux
          - os: windows-latest
            platform: windows
          - os: macos-latest
            platform: macos
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.13"
          cache: "pip"
          check-latest: true

      - name: Install PDM
        run: |
          python -m pip install --upgrade pip
          pip install pdm
          pdm --version

      - name: Install dependencies
        run: |
          pdm config python.use_venv false
          pdm install

      - name: Install PyInstaller
        run: pdm add pyinstaller>=6.5.0

      - name: Build binary
        run: |
          pdm run python scripts/build_binary.py --platform ${{ matrix.platform }} --one-file --clean

      - name: Upload binary artifact
        uses: actions/upload-artifact@v3
        with:
          name: immuta-${{ matrix.platform }}
          path: dist/immuta*
          if-no-files-found: error

  create-release:
    name: Create GitHub Release
    needs: [build, build-binaries]
    runs-on: ubuntu-latest
    permissions:
      contents: write
      discussions: write
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Generate release notes
        id: generate_release_notes
        uses: mikepenz/release-changelog-builder-action@v3
        with:
          configuration: ".github/changelog-config.json"
          commitMode: true
          ignorePreReleases: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Download all artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          name: Release v${{ steps.get_version.outputs.VERSION }}
          draft: false
          prerelease: false
          body: ${{ steps.generate_release_notes.outputs.changelog }}
          files: |
            artifacts/dist/*.tar.gz
            artifacts/dist/*.whl
            artifacts/immuta-linux/*
            artifacts/immuta-windows/*
            artifacts/immuta-macos/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create GitHub Discussion
        uses: actions/github-script@v6
        with:
          script: |
            const version = process.env.VERSION;
            const changelog = process.env.CHANGELOG;
            
            await github.rest.discussions.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              category_id: 'DIC_kwDOLXXXXXXXXX', // Replace with your actual category ID
              title: `Release v${version}`,
              body: `# Release v${version}\n\n${changelog}\n\nSee the [release page](https://github.com/${context.repo.owner}/${context.repo.repo}/releases/tag/v${version}) for downloads.`
            });
        env:
          VERSION: ${{ steps.get_version.outputs.VERSION }}
          CHANGELOG: ${{ steps.generate_release_notes.outputs.changelog }}

  publish-pypi:
    name: Publish to PyPI
    needs: [build, create-release]
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    steps:
      - name: Download dist artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          packages-dir: dist/
          skip-existing: true

  publish-docker:
    name: Publish Docker Image
    needs: [create-release]
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/${{ github.repository }}
          tags: |
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          provenance: true

  notify:
    name: Notify Team
    needs: [publish-pypi, publish-docker]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Notify on Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          payload: |
            {
              "text": "Release v${{ steps.get_version.outputs.VERSION }} completed!",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "Release v${{ steps.get_version.outputs.VERSION }} completed!"
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "The release has been published to:\n• <https://github.com/${{ github.repository }}/releases/tag/v${{ steps.get_version.outputs.VERSION }}|GitHub Release>\n• <https://pypi.org/project/immuta-sre-toolkit/${{ steps.get_version.outputs.VERSION }}|PyPI>\n• <https://github.com/${{ github.repository }}/pkgs/container/immuta-sre-toolkit|GitHub Container Registry>"
                  }
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "Triggered by: <https://github.com/${{ github.actor }}|${{ github.actor }}>"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
