apiVersion: apps/v1
kind: Deployment
metadata:
  name: immuta-sre-toolkit
  labels:
    app: immuta-sre-toolkit
spec:
  replicas: 1
  selector:
    matchLabels:
      app: immuta-sre-toolkit
  template:
    metadata:
      labels:
        app: immuta-sre-toolkit
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: immuta-sre-toolkit
        image: ghcr.io/your-org/immuta-sre-toolkit:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: metrics
        env:
        - name: IMMUTA_API_KEY
          valueFrom:
            secretKeyRef:
              name: immuta-credentials
              key: api-key
        - name: IMMUTA_BASE_URL
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: base-url
        - name: IMMUTA_API_TIMEOUT
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: api-timeout
              optional: true
        - name: IMMUTA_API_RATE_LIMIT
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: api-rate-limit
              optional: true
        - name: IMMUTA_LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: log-level
              optional: true
        - name: TAG_CACHE_TTL_MINUTES
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: tag-cache-ttl-minutes
              optional: true
        - name: SECRETS_PROVIDER
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: secrets-provider
              optional: true
        - name: AZURE_KEY_VAULT_URL
          valueFrom:
            configMapKeyRef:
              name: immuta-config
              key: azure-key-vault-url
              optional: true
        volumeMounts:
        - name: data
          mountPath: /app/data
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: immuta-sre-toolkit-data
---
apiVersion: v1
kind: Service
metadata:
  name: immuta-sre-toolkit
  labels:
    app: immuta-sre-toolkit
spec:
  ports:
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: immuta-sre-toolkit
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: immuta-sre-toolkit-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: immuta-config
data:
  base-url: "https://your-immuta-instance.com"
  api-timeout: "30"
  api-rate-limit: "10"
  log-level: "INFO"
  tag-cache-ttl-minutes: "5"
  secrets-provider: "env"
---
apiVersion: v1
kind: Secret
metadata:
  name: immuta-credentials
type: Opaque
data:
  api-key: "YOUR_BASE64_ENCODED_API_KEY"  # Replace with: echo -n "your-api-key" | base64
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: immuta-backup
spec:
  schedule: "0 1 * * *"  # Run daily at 1:00 AM
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: ghcr.io/your-org/immuta-sre-toolkit:latest
            command:
            - immuta
            - backup
            - projects
            - --output-dir
            - /app/data/backups/$(date +%Y-%m-%d)
            env:
            - name: IMMUTA_API_KEY
              valueFrom:
                secretKeyRef:
                  name: immuta-credentials
                  key: api-key
            - name: IMMUTA_BASE_URL
              valueFrom:
                configMapKeyRef:
                  name: immuta-config
                  key: base-url
            volumeMounts:
            - name: data
              mountPath: /app/data
          restartPolicy: OnFailure
          volumes:
          - name: data
            persistentVolumeClaim:
              claimName: immuta-sre-toolkit-data
