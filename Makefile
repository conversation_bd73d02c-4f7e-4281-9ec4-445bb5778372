# Makefile for Immuta SRE Toolkit
# This Makefile provides commands for Docker-related operations and development workflow

# Variables
IMAGE_NAME := immuta-sre-toolkit
DEV_IMAGE_NAME := immuta-sre-toolkit-dev
VERSION := $(shell grep -m1 version pyproject.toml | cut -d'"' -f2)
CONTAINER_NAME := immuta-toolkit
DEV_CONTAINER_NAME := immuta-toolkit-dev
DOCKER_COMPOSE := docker-compose -f docker/docker-compose.yml

.PHONY: help build build-dev run run-dev run-api run-script shell test test-web lint clean clean-all binary binary-onefile binary-windows binary-macos binary-linux binary-all install-playwright

# Default target
help:
	@echo "Immuta SRE Toolkit Makefile"
	@echo ""
	@echo "Usage:"
	@echo "  make build         Build the production Docker image"
	@echo "  make build-dev     Build the development Docker image"
	@echo "  make run           Run the production container"
	@echo "  make run-dev       Run the development container"
	@echo "  make run-api       Run the API server container"
	@echo "  make run-script    Run a script using the Docker container"
	@echo "  make shell         Open a shell in the development container"
	@echo "  make test          Run tests in the development container"
	@echo "  make test-web      Run web automation tests in the development container"
	@echo "  make lint          Run linting in the development container"
	@echo "  make clean         Remove containers"
	@echo "  make clean-all     Remove containers and images"
	@echo "  make binary        Build a binary (one-directory mode)"
	@echo "  make binary-onefile Build a binary (one-file mode)"
	@echo "  make binary-windows Build a Windows binary"
	@echo "  make binary-macos  Build a macOS binary"
	@echo "  make binary-linux  Build a Linux binary"
	@echo "  make binary-all    Build binaries for all platforms"
	@echo "  make install-playwright Install Playwright browsers"
	@echo "  make docker-push   Build and push Docker images to registry"
	@echo "  make docker-pull   Pull Docker images from registry"
	@echo "  make test-docker   Run tests in Docker container"
	@echo "  make test-web-docker Run web automation tests in Docker"
	@echo "  make help          Show this help message"
	@echo ""
	@echo "Environment variables:"
	@echo "  VERSION            Version tag for Docker images (default: from pyproject.toml)"
	@echo "  CONTAINER_NAME     Name for the production container (default: immuta-toolkit)"
	@echo "  DEV_CONTAINER_NAME Name for the development container (default: immuta-toolkit-dev)"
	@echo ""

# Build the production Docker image
build:
	@echo "Building production Docker image $(IMAGE_NAME):$(VERSION)..."
	docker build -t $(IMAGE_NAME):$(VERSION) -t $(IMAGE_NAME):latest -f docker/Dockerfile.prod .
	@echo "Done! Run with: make run"

# Build the development Docker image
build-dev:
	@echo "Building development Docker image $(DEV_IMAGE_NAME):$(VERSION)..."
	docker build -f docker/Dockerfile.dev -t $(DEV_IMAGE_NAME):$(VERSION) -t $(DEV_IMAGE_NAME):latest .
	@echo "Done! Run with: make run-dev"

# Run the production container
run:
	@echo "Running production container $(CONTAINER_NAME)..."
	$(DOCKER_COMPOSE) up -d $(CONTAINER_NAME)
	@echo "Container is running. Use 'docker logs -f $(CONTAINER_NAME)' to view logs."

# Run the development container
run-dev:
	@echo "Running development container $(DEV_CONTAINER_NAME)..."
	$(DOCKER_COMPOSE) up -d dev
	@echo "Container is running. Use 'make shell' to access the container."

# Run the API server container
run-api:
	@echo "Running API server container..."
	$(DOCKER_COMPOSE) up -d api
	@echo "API server is running at http://localhost:8000"
	@echo "Use 'docker logs -f immuta-api' to view logs."

# Run a script using the Docker container
run-script:
	@if [ -z "$(SCRIPT)" ]; then \
		echo "Error: SCRIPT is required. Usage: make run-script SCRIPT='path/to/script.py'"; \
		exit 1; \
	fi
	@echo "Running script $(SCRIPT) in Docker container..."
	docker run --rm -it \
		--env-file .env \
		-v "$(PWD)/data:/app/data" \
		-v "$(PWD)/logs:/app/logs" \
		-v "$(PWD)/reports:/app/reports" \
		-v "$(PWD)/configs:/app/configs" \
		-v "$(PWD)/$(SCRIPT):/app/script.py" \
		$(IMAGE_NAME):latest python /app/script.py
	@echo "Script execution completed."

# Open a shell in the development container
shell:
	@echo "Opening shell in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) bash

# Run tests in the development container
test:
	@echo "Running tests in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run pytest

# Run web automation tests in the development container
test-web:
	@echo "Running web automation tests in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run pytest tests/web/

# Run specific tests in the development container
test-%:
	@echo "Running tests matching $* in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run pytest -xvs tests/**/test_$*.py

# Run tests with coverage in the development container
test-coverage:
	@echo "Running tests with coverage in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run pytest --cov=immuta_toolkit

# Run linting in the development container
lint:
	@echo "Running linting in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run ruff check src/

# Fix linting issues in the development container
lint-fix:
	@echo "Fixing linting issues in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run ruff check --fix src/

# Format code in the development container
format:
	@echo "Formatting code in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run black src/ tests/

# Install dependencies in the development container
install:
	@echo "Installing dependencies in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm install

# Update dependencies in the development container
update:
	@echo "Updating dependencies in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm update

# Stop containers
stop:
	@echo "Stopping containers..."
	$(DOCKER_COMPOSE) stop

# Remove containers
clean:
	@echo "Removing containers..."
	$(DOCKER_COMPOSE) down

# Remove containers and images
clean-all: clean
	@echo "Removing Docker images..."
	docker rmi -f $(IMAGE_NAME):$(VERSION) $(IMAGE_NAME):latest $(DEV_IMAGE_NAME):$(VERSION) $(DEV_IMAGE_NAME):latest 2>/dev/null || true
	@echo "Cleanup complete."

# Build and run the development environment
dev: build-dev run-dev
	@echo "Development environment is ready. Use 'make shell' to access the container."

# Build and run the production environment
prod: build run
	@echo "Production environment is running."

# Run a specific command in the development container
exec:
	@if [ -z "$(CMD)" ]; then \
		echo "Error: CMD is required. Usage: make exec CMD='your command'"; \
		exit 1; \
	fi
	docker exec -it $(DEV_CONTAINER_NAME) bash -c "$(CMD)"

# Generate documentation
docs:
	@echo "Generating documentation in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run sphinx-build -b html docs/source docs/build/html
	@echo "Documentation generated. Open docs/build/html/index.html in your browser."

# Create a new release
release:
	@if [ -z "$(VERSION)" ]; then \
		echo "Error: VERSION is required. Usage: make release VERSION=x.y.z"; \
		exit 1; \
	fi
	@echo "Creating release $(VERSION)..."
	@echo "1. Running tests..."
	make test
	@echo "2. Building Docker images..."
	make build
	make build-dev
	@echo "3. Tagging release..."
	git tag -a v$(VERSION) -m "Release v$(VERSION)"
	git push origin v$(VERSION)
	@echo "Release v$(VERSION) created successfully!"

# Show Docker container status
status:
	@echo "Docker container status:"
	$(DOCKER_COMPOSE) ps

# Show Docker logs
logs:
	@echo "Docker logs for $(CONTAINER_NAME):"
	docker logs -f $(CONTAINER_NAME)

logs-dev:
	@echo "Docker logs for $(DEV_CONTAINER_NAME):"
	docker logs -f $(DEV_CONTAINER_NAME)

# Build binary (one-directory mode)
binary:
	@echo "Building binary (one-directory mode)..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run pyinstaller immuta.spec
	@echo "Binary built successfully! Find it in the dist/ directory."

# Build binary (one-file mode)
binary-onefile:
	@echo "Building binary (one-file mode)..."
	docker exec -it $(DEV_CONTAINER_NAME) bash -c "ONEFILE=1 pdm run pyinstaller immuta.spec"
	@echo "Binary built successfully! Find it in the dist/ directory."

# Build Windows binary
binary-windows:
	@echo "Building Windows binary..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run python scripts/build_binary.py --platform windows --clean
	@echo "Windows binary built successfully! Find it in the dist/ directory."

# Build macOS binary
binary-macos:
	@echo "Building macOS binary..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run python scripts/build_binary.py --platform macos --clean
	@echo "macOS binary built successfully! Find it in the dist/ directory."

# Build Linux binary
binary-linux:
	@echo "Building Linux binary..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run python scripts/build_binary.py --platform linux --clean
	@echo "Linux binary built successfully! Find it in the dist/ directory."

# Build all binaries
binary-all: binary-windows binary-macos binary-linux
	@echo "All binaries built successfully! Find them in the dist/ directory."

# Install Playwright browsers
install-playwright:
	@echo "Installing Playwright browsers in development container..."
	docker exec -it $(DEV_CONTAINER_NAME) pdm run playwright install --with-deps chromium
	@echo "Playwright browsers installed successfully!"

# Build and push Docker images to registry
docker-push: build build-dev
	@echo "Pushing Docker images to registry..."
	docker push $(IMAGE_NAME):latest
	docker push $(IMAGE_NAME):$(VERSION)
	docker push $(DEV_IMAGE_NAME):latest
	docker push $(DEV_IMAGE_NAME):$(VERSION)
	@echo "Images pushed successfully."

# Pull Docker images from registry
docker-pull:
	@echo "Pulling Docker images from registry..."
	docker pull $(IMAGE_NAME):latest
	docker pull $(DEV_IMAGE_NAME):latest
	@echo "Images pulled successfully."

# Run tests in Docker container
test-docker:
	@echo "Running tests in Docker container..."
	$(DOCKER_COMPOSE) run --rm test
	@echo "Tests completed."

# Run web automation tests in Docker
test-web-docker:
	@echo "Running web automation tests in Docker container..."
	$(DOCKER_COMPOSE) run --rm test pytest tests/web/
	@echo "Web automation tests completed."
