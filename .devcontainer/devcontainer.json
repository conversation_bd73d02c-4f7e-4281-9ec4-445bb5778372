{"name": "Immuta SRE Toolkit Development", "dockerFile": "../Dockerfile.dev", "context": "..", "remoteUser": "root", "workspaceFolder": "/app", "workspaceMount": "source=${localWorkspaceFolder},target=/app,type=bind,consistency=cached", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.vscode-pylance", "ms-python.black-formatter", "ms-python.isort", "charliermarsh.ruff", "matangover.mypy", "redhat.vscode-yaml", "yzhang.markdown-all-in-one", "ms-azuretools.vscode-docker", "github.vscode-github-actions", "ms-vscode-remote.remote-containers", "ms-vscode.makefile-tools", "tamasfe.even-better-toml", "njpwerner.autodocstring", "streetsidesoftware.code-spell-checker"], "settings": {"python.linting.enabled": true, "python.linting.mypyEnabled": true, "python.linting.flake8Enabled": false, "python.linting.pylintEnabled": false, "python.formatting.provider": "black", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": true}, "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests"], "python.analysis.extraPaths": ["/app/src"], "terminal.integrated.defaultProfile.linux": "bash"}}}, "postCreateCommand": "pdm install && pdm run pre-commit install", "features": {"ghcr.io/devcontainers/features/github-cli:1": {}, "ghcr.io/devcontainers/features/docker-in-docker:2": {}}, "forwardPorts": [], "mounts": ["source=${localEnv:HOME}/.ssh,target=/root/.ssh,type=bind,consistency=cached"], "runArgs": ["--env-file", ".env"]}