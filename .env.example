# Immuta API Configuration
IMMUTA_API_KEY=your_api_key_here
IMMUTA_BASE_URL=https://your-immuta-instance.com
IMMUTA_API_TIMEOUT=30
IMMUTA_API_RATE_LIMIT=10
IMMUTA_LOG_LEVEL=INFO
IMMUTA_LOG_FILE=/path/to/log/file.log

# Azure Blob Storage Configuration (for backups)
BLOB_CONNECTION_STRING=your_azure_blob_connection_string
BLOB_CONTAINER_NAME=immuta-backups

# Tag Cache Configuration
TAG_CACHE_TTL_MINUTES=5

# Secrets Management Configuration
SECRETS_PROVIDER=env  # Options: env, azure
AZURE_KEY_VAULT_URL=https://your-vault.vault.azure.net/
AZURE_CLIENT_ID=your_client_id
AZURE_CLIENT_SECRET=your_client_secret
AZURE_TENANT_ID=your_tenant_id

# Notification Configuration
NOTIFICATION_ENABLED=true
NOTIFICATION_OPERATIONS=user_create,user_update,user_delete,data_source_create,data_source_update,data_source_delete,policy_create,policy_update,policy_delete
NOTIFICATION_ERRORS=true

# Email Notification Configuration
EMAIL_ENABLED=false
EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
SMTP_SERVER=smtp.example.com
SMTP_PORT=25
SMTP_USER=smtp_user
SMTP_PASSWORD=smtp_password
SMTP_FROM=<EMAIL>

# Slack Notification Configuration
SLACK_ENABLED=false
SLACK_WEBHOOK=https://hooks.slack.com/services/your/slack/webhook

# Microsoft Teams Notification Configuration
TEAMS_ENABLED=false
TEAMS_WEBHOOK=https://your-organization.webhook.office.com/webhookb2/your-teams-webhook

# Snowflake Configuration (for Audit Logging)
SNOWFLAKE_ACCOUNT=your_account
SNOWFLAKE_USER=your_user
SNOWFLAKE_PASSWORD=your_password
SNOWFLAKE_DATABASE=your_database
SNOWFLAKE_SCHEMA=your_schema
SNOWFLAKE_WAREHOUSE=your_warehouse
SNOWFLAKE_ROLE=your_role
SNOWFLAKE_AUDIT_TABLE=IMMUTA_AUDIT_LOGS
