# Immuta SRE Toolkit

A comprehensive toolkit for managing Immuta instances, designed for SRE teams in banking/data analytics environments. This toolkit supports daily Business-as-Usual (BAU) tasks for Immuta, emphasizing operational efficiency, automation, safety, performance, and logging.

[![CI/CD Pipeline](https://github.com/davidlu1001/immuta-sre-toolkit/actions/workflows/ci.yml/badge.svg)](https://github.com/davidlu1001/immuta-sre-toolkit/actions/workflows/ci.yml)
[![Docker Image Publish](https://github.com/davidlu1001/immuta-sre-toolkit/actions/workflows/docker-publish.yml/badge.svg)](https://github.com/davidlu1001/immuta-sre-toolkit/actions/workflows/docker-publish.yml)
[![Python Version](https://img.shields.io/badge/python-3.13%20%7C%203.9%2B-blue)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-black)](https://github.com/psf/black)

## Documentation

- [API Reference](docs/api_reference.md)
- [Security Best Practices](docs/security_best_practices.md)
- [Development Guide](docs/development_guide.md)
- [Testing Guide](docs/testing.md)
- [Deployment Guide](docs/deployment_guide.md)
- [Docker Support](docs/docker.md)
- [Configuration Management](docs/configuration.md)
- [CLI Enhancements](docs/cli_enhancements.md)

## Features

### Core Functionality
- **User Management**: Create, update, and batch update users with role assignments and group memberships
- **Policy Management**: Create and apply global data policies with masking rules based on tags
- **Data Source Management**: Create, update, and delete data sources with connection details
- **Project Management**: Create, update, and manage projects with members and data sources
- **Tag Management**: Bulk tagging of data sources and columns using YAML configuration
- **Purpose Management**: Create and manage purposes with subscribers
- **Schema Evolution**: Configure schema evolution settings with naming templates
- **Backup & Restore**: Comprehensive backup and restore functionality for all Immuta components

### Performance & Safety
- **Hybrid Client**: API-first approach with automatic web automation fallback for reliability
- **Web Automation**: Playwright-based web automation for operations not supported by the API
- **Dry Run Mode**: Simulate changes without applying them for all operations
- **Validation**: Comprehensive validation for all inputs with descriptive error messages
- **Rate Limiting**: Configurable rate limiting using TokenBucket algorithm to prevent API throttling (default: 10 req/sec)
- **Caching**: Intelligent metadata caching with configurable TTL to reduce API calls
- **Connection Pooling**: Efficient connection management
- **Batch Operations**: Optimized batch API calls for large datasets with chunking
- **Thread Safety**: Thread-safe operations for concurrent processing
- **Secrets Management**: Secure secrets management with environment variables, Azure Key Vault, AWS Secrets Manager, or HashiCorp Vault
- **Containerization**: Comprehensive Docker support with multiple container types

### Operational Features
- **REST API Service**: FastAPI-based REST API for integration with other systems
- **Monitoring & Observability**: Prometheus metrics for monitoring and alerting
- **Healthcheck Endpoint**: HTTP endpoint for health monitoring and readiness/liveness probes
- **Kubernetes Deployment**: Ready-to-use Kubernetes manifests for deployment
- **Automated Backup & Restore**: Scheduled backups with retention management
- **CI/CD Integration**: GitHub Actions workflows for continuous integration and deployment
- **Development Environment**: VSCode configuration and development container support
- **Configuration Management**: Enhanced configuration management with profiles, versioning, and validation

### Monitoring & Notifications
- **Audit Logging**: Comprehensive logging with sanitized operation details
- **Notifications**: Support for email, Slack, and Microsoft Teams notifications
- **Progress Tracking**: Visual progress bars for long-running operations
- **Error Handling**: Robust error handling with detailed error messages and recovery options

## Installation

### Using PDM (recommended)

```bash
pdm install immuta-toolkit
```

### Using pip

```bash
pip install immuta-toolkit
```

### Using Standalone Binaries

The toolkit can be compiled into standalone binaries that run without requiring Python or any dependencies to be installed. These binaries are perfect for isolated environments or for distributing to users who don't have Python installed.

```bash
# Download the appropriate binary for your platform from the latest release
# https://github.com/your-org/immuta-sre-toolkit/releases

# Make the binary executable (Linux/macOS)
chmod +x immuta

# Run the binary
./immuta --help

# On Windows
immuta.exe --help
```

The standalone binaries:
- Include all dependencies and the Python interpreter
- Run in completely isolated environments without internet access
- Are available for Windows, macOS, and Linux
- Support all the same commands and features as the Python package

### Using Docker

The toolkit provides comprehensive Docker support with multiple container types for different use cases. For detailed documentation, see [Docker Support](docs/docker.md).

#### Container Types

The toolkit provides several container types for different use cases:

1. **Production Container**: A lightweight container for running the toolkit in production
   - Based on Python 3.13 slim image
   - Includes only production dependencies
   - Runs as a non-root user for security
   - Optimized for size and performance

2. **Development Container**: A fully-featured container for development work
   - Includes all dependencies including development tools
   - Provides a complete development environment
   - Includes debugging tools and utilities
   - Mounts the source code for live editing

3. **API Server Container**: A container for running the API server
   - Exposes the REST API on port 8000
   - Includes health check and metrics endpoints
   - Configured for production use

4. **Test Container**: A container for running tests
   - Includes all testing dependencies
   - Configured for running unit, integration, and web automation tests
   - Provides test coverage reporting

#### Using the Helper Script

You can use the provided script to run the toolkit in a Docker container:

```bash
# Run the CLI with default help command
./scripts/run-docker.sh

# Run a specific command
./scripts/run-docker.sh user list

# Run the API server
./scripts/run-docker.sh api

# Run the development environment
./scripts/run-docker.sh dev

# Run tests
./scripts/run-docker.sh test

# Run tests with verbose output
pdm run python -m pytest -xvs
```

The script will:
- Check if the `.env` file exists and create it if needed
- Build the Docker image if it doesn't exist
- Create necessary directories (data, logs, reports, configs)
- Run the container with the specified command
- Mount volumes for data, logs, reports, and configs

#### Using Docker Compose

```bash
# Run the CLI container
docker-compose -f docker/docker-compose.yml up immuta-toolkit

# Run the API server
docker-compose -f docker/docker-compose.yml up api

# Run the development environment
docker-compose -f docker/docker-compose.yml up dev

# Run tests
docker-compose -f docker/docker-compose.yml run --rm test
```

#### Using Docker Commands Directly

```bash
# Build the production image
docker build -t immuta-sre-toolkit:latest -f docker/Dockerfile.prod .

# Build the development image
docker build -f docker/Dockerfile.dev -t immuta-sre-toolkit-dev:latest .

# Run the CLI container
docker run -it --rm \
  --env-file .env \
  -v ./data:/app/data:rw \
  -v ./logs:/app/logs:rw \
  -v ./reports:/app/reports:rw \
  -v ./configs:/app/configs:ro \
  immuta-sre-toolkit:latest user list

# Run the API server
docker run -it --rm \
  -p 8000:8000 \
  --env-file .env \
  -v ./data:/app/data:rw \
  -v ./logs:/app/logs:rw \
  -v ./reports:/app/reports:rw \
  -v ./configs:/app/configs:ro \
  immuta-sre-toolkit:latest api start --host 0.0.0.0 --port 8000
```

#### Security Features

The Docker containers include several security features:

- **Non-root User**: All containers run as a non-root user (`immuta`) for better security
- **Read-only Volumes**: Configuration files are mounted as read-only
- **Resource Limits**: Containers have CPU and memory limits to prevent resource exhaustion
- **No New Privileges**: Containers are configured with `no-new-privileges:true` to prevent privilege escalation
- **Minimal Base Image**: Containers use the slim variant of the Python image to reduce attack surface

### Using Makefile

The project includes a Makefile to simplify common operations:

```bash
# Docker operations
make build              # Build the production Docker image
make build-dev          # Build the development Docker image
make run                # Run the production container
make run-dev            # Run the development container
make run-api            # Run the API server container
make run-script SCRIPT=scripts/my_script.py  # Run a custom script
make shell              # Open a shell in the development container
make dev                # Build and run the development environment
make prod               # Build and run the production environment
make stop               # Stop containers
make clean              # Remove containers
make clean-all          # Remove containers and images
make status             # Show Docker container status
make logs               # Show Docker logs for production container
make logs-dev           # Show Docker logs for development container
make docker-push        # Build and push Docker images to registry
make docker-pull        # Pull Docker images from registry
make test-docker        # Run tests in Docker container
make test-web-docker    # Run web automation tests in Docker

# Development operations
make test               # Run all tests
make test-web           # Run web automation tests
make test-coverage      # Run tests with coverage
make test-verbose       # Run tests with verbose output (pdm run python -m pytest -xvs)
make lint               # Run linting
make lint-fix           # Fix linting issues
make format             # Format code
make install            # Install dependencies
make update             # Update dependencies
make docs               # Generate documentation
make exec CMD="command" # Run a command in the development container

# Binary operations
make binary             # Build a standalone binary (one-directory mode)
make binary-onefile     # Build a standalone binary (one-file mode)
make binary-windows     # Build a Windows binary
make binary-macos       # Build a macOS binary
make binary-linux       # Build a Linux binary
make binary-all         # Build binaries for all platforms

# Other operations
make install-playwright # Install Playwright browsers
make help               # Show all available commands
```

For more details on Docker support, see [Docker Support](docs/docker.md).

### Kubernetes Deployment

The toolkit can be deployed to Kubernetes using the provided manifests:

```bash
# Apply the Kubernetes manifests
kubectl apply -f kubernetes/deployment.yaml

# Check the deployment status
kubectl get pods -l app=immuta-sre-toolkit

# View the logs
kubectl logs -l app=immuta-sre-toolkit

# Port-forward to access the metrics endpoint
kubectl port-forward svc/immuta-sre-toolkit 8000:8000
```

The Kubernetes deployment includes:
- Deployment with readiness/liveness probes
- Service for metrics and healthcheck endpoints
- PersistentVolumeClaim for data storage
- ConfigMap for configuration
- Secret for credentials
- CronJob for scheduled backups

After installation, you can use the `immuta` command to interact with the toolkit.

## Configuration

The toolkit can be configured using environment variables or command-line options. You can create a `.env` file in the project root directory to set these variables (see `.env.example` for a template).

### Required Configuration

- `IMMUTA_API_KEY`: Immuta API key
- `IMMUTA_BASE_URL`: Immuta base URL

### Web Automation Configuration

- `IMMUTA_USERNAME`: Username for web authentication (required for web automation)
- `IMMUTA_PASSWORD`: Password for web authentication (required for web automation)
- `IMMUTA_USE_WEB_FALLBACK`: Whether to use web automation as fallback (default: true)
- `IMMUTA_HEADLESS`: Whether to run the browser in headless mode (default: true)
- `IMMUTA_VERSION`: Immuta version for selector registry (default: 2024.02)

### Optional Configuration

- `IMMUTA_API_TIMEOUT`: API timeout in seconds (default: 10)
- `IMMUTA_API_RATE_LIMIT`: Maximum API calls per second (default: 10)
- `TAG_CACHE_TTL_MINUTES`: Tag cache TTL in minutes (default: 5)
- `BLOB_CONNECTION_STRING`: Azure Blob Storage connection string for backups
- `BLOB_CONTAINER_NAME`: Azure Blob Storage container name (default: immuta-backups)

### Secrets Management Configuration

- `SECRETS_PROVIDER`: Secrets provider to use (default: env, options: env, azure, aws, hashicorp)
- `AZURE_KEY_VAULT_URL`: Azure Key Vault URL for secrets management
- `AZURE_CLIENT_ID`: Azure Client ID for Key Vault authentication
- `AZURE_CLIENT_SECRET`: Azure Client Secret for Key Vault authentication
- `AZURE_TENANT_ID`: Azure Tenant ID for Key Vault authentication
- `AWS_REGION`: AWS region for Secrets Manager
- `AWS_SECRET_NAME`: AWS Secrets Manager secret name
- `VAULT_URL`: HashiCorp Vault URL
- `VAULT_TOKEN`: HashiCorp Vault token
- `VAULT_MOUNT_POINT`: HashiCorp Vault mount point (default: secret)

### Snowflake Configuration (for Audit Logging)

- `SNOWFLAKE_ACCOUNT`: Snowflake account name
- `SNOWFLAKE_USER`: Snowflake username
- `SNOWFLAKE_PASSWORD`: Snowflake password
- `SNOWFLAKE_DATABASE`: Snowflake database name
- `SNOWFLAKE_SCHEMA`: Snowflake schema name
- `SNOWFLAKE_WAREHOUSE`: Snowflake warehouse name
- `SNOWFLAKE_ROLE`: Snowflake role (optional)
- `SNOWFLAKE_AUDIT_TABLE`: Snowflake audit table name (default: IMMUTA_AUDIT_LOGS)

### Notification Configuration

- `NOTIFICATION_ENABLED`: Whether notifications are enabled (default: true)
- `EMAIL_ENABLED`: Whether email notifications are enabled (default: false)
- `SLACK_ENABLED`: Whether Slack notifications are enabled (default: false)
- `TEAMS_ENABLED`: Whether Microsoft Teams notifications are enabled (default: false)
- `EMAIL_RECIPIENTS`: Comma-separated list of email recipients
- `SLACK_WEBHOOK`: Slack webhook URL
- `TEAMS_WEBHOOK`: Microsoft Teams webhook URL
- `SMTP_SERVER`: SMTP server for email notifications (default: localhost)
- `SMTP_PORT`: SMTP port for email notifications (default: 25)
- `SMTP_USER`: SMTP username for email notifications
- `SMTP_PASSWORD`: SMTP password for email notifications
- `SMTP_FROM`: From address for email notifications (default: <EMAIL>)

## Usage

### Command-Line Interface

The toolkit provides a command-line interface for common operations:

```bash
# Run with web automation fallback enabled (default)
immuta --web-fallback user list

# Run with web automation fallback disabled
immuta --no-web-fallback user list

# Run with browser in non-headless mode (for debugging)
immuta --no-headless user list

# List users
immuta user list

# Get user by ID or email
immuta user get 123
immuta <NAME_EMAIL>

# Create user
immuta user create --email <EMAIL> --name "User Name" --role DataScientist --group "Group1" --group "Group2"

# Update user
immuta user update 123 --role Admin --group "Administrators"

# Batch update users from JSON file
immuta user batch-update users.json --dry-run

# Bulk apply tags from YAML configuration
immuta tag bulk-apply tag_config.yaml --dry-run

# Create a policy from JSON file
immuta policy create policy.json --dry-run

# Configure schema evolution settings
immuta schema configure schema_config.json --dry-run

# List data sources
immuta datasource list

# Create a data source from JSON file
immuta datasource create datasource.json --dry-run

# Get data dictionary for a data source
immuta dictionary get 123

# Export data dictionary to a file
immuta dictionary get 123 --output dictionary.json --format json

# Import data dictionary from a file
immuta dictionary import 123 dictionary.json --dry-run

# Backup projects
immuta backup projects --output-dir /path/to/backup --include-members --include-data-sources --include-purposes

# Restore projects from backup
immuta restore projects --backup-dir /path/to/backup --restore-members --restore-data-sources --restore-purposes

# Verify backup integrity
immuta backup verify --backup-dir /path/to/backup

# Run automated backup script
./scripts/automated-backup.sh --backup-dir /path/to/backup --retention-days 30

# Start the API server
immuta api start --host 0.0.0.0 --port 8000

# Access the API documentation
# Open http://localhost:8000/docs in your browser

# Monitoring & Observability
immuta metrics start --port 8000  # Start the metrics server
curl http://localhost:8000/metrics  # Access metrics endpoint
curl http://localhost:8000/health   # Check health status
```

### Python API

The toolkit can also be used as a Python library:

```python
# Using the Hybrid Client (recommended)
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.models import UserModel, UserAttributes, UserRole

# Initialize hybrid client with web automation fallback
client = ImmutaHybridClient(
    api_key="your_api_key",
    base_url="https://your-immuta-instance.com",
    username="your_username",  # Required for web automation
    password="your_password",  # Required for web automation
    use_web_fallback=True,     # Enable web automation fallback
    headless=True,             # Run browser in headless mode
    snowflake_credentials={
        "account": "your_account",
        "user": "your_user",
        "password": "your_password",
        "database": "your_database",
        "schema": "your_schema",
        "warehouse": "your_warehouse",
    }
)

# Or using the API-only client
from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.models import UserModel, UserAttributes, UserRole

# Initialize API client
api_client = ImmutaClient(
    api_key="your_api_key",
    base_url="https://your-immuta-instance.com",
    snowflake_credentials={
        "account": "your_account",
        "user": "your_user",
        "password": "your_password",
        "database": "your_database",
        "schema": "your_schema",
        "warehouse": "your_warehouse",
    }
)

# List users
users = client.user_service.list_users()

# Create user
user = UserModel(
    email="<EMAIL>",
    name="User Name",
    attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
    groups=["Group1", "Group2"],
)
client.user_service.create_user(user)

# Batch update users
users_to_update = [
    {"user_id": 1, "role": "Admin"},
    {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
]
client.batch_service.batch_update_users(users_to_update)

# Bulk apply tags
client.data_source_service.tag_existing_data_sources("tag_config.yaml")

# Configure schema evolution
schema_config = {
    "datasource_name_format": "{table_name}_v{version}",
    "query_engine_table_name_format": "{table_name}_v{version}",
    "enabled": True,
}
client.config_service.configure_schema_evolution(schema_config)

# Create a policy
policy = {
    "name": "PII Masking Policy",
    "description": "Masks PII data",
    "actions": [{"type": "mask", "fields": ["email", "phone"]}],
    "rules": [{"type": "tag", "value": "pii"}],
}
client.policy_service.create_policy(policy)

# Get data dictionary
dictionary = client.dictionary_service.get_data_dictionary(data_source_id=123)

# Update data dictionary
dictionary_update = {
    "columns": [
        {
            "name": "email",
            "description": "Customer email address",
            "business_definition": "Primary contact email for the customer"
        }
    ]
}
client.dictionary_service.update_data_dictionary(
    data_source_id=123,
    dictionary=dictionary_update
)

# Batch operations
result = client.project_service.batch_add_members_to_project(
    project_id=123,
    user_ids=[1, 2, 3, 4, 5],
    role="MEMBER",
)
print(f"Added {result['added']} members to project")

# Backup and restore
backup_result = client.backup_service.backup_projects(
    output_dir="/path/to/backup",
    include_members=True,
    include_data_sources=True,
    include_purposes=True,
    retention_days=30,
)
print(f"Backed up {backup_result['project_count']} projects")

# Asynchronous operations
import asyncio

async def async_example():
    # List projects asynchronously
    projects = await client.project_service.list_projects_async()
    print(f"Found {len(projects)} projects")

    # Execute multiple operations in parallel
    operations = [
        {"type": "create_project", "params": {"project": {"name": "Project 1"}}},
        {"type": "create_project", "params": {"project": {"name": "Project 2"}}},
        {"type": "create_project", "params": {"project": {"name": "Project 3"}}},
    ]

    results = await client.project_service.batch_operation_async(operations)
    print(f"Completed {len(results)} operations")

# Run the async function
asyncio.run(async_example())

# Always close the client when done to release resources
client.close()
```

## Configuration Files

### Tag Configuration

The tag configuration file is a YAML file with the following structure:

```yaml
data_source_tags:
  customer: [pii, sensitive]
  transaction: [financial]

column_tags:
  email: [pii, email]
  address: [pii, address]
  credit_card: [pci, financial]
```

This configuration will:
- Add the tags `pii` and `sensitive` to any data source with "customer" in its name
- Add the tag `financial` to any data source with "transaction" in its name
- Add the tags `pii` and `email` to any column with "email" in its name
- Add the tags `pii` and `address` to any column with "address" in its name
- Add the tags `pci` and `financial` to any column with "credit_card" in its name

### Policy Configuration

The policy configuration file is a JSON file with the following structure:

```json
{
  "name": "PII Masking Policy",
  "description": "Masks PII data",
  "actions": [
    {
      "type": "mask",
      "fields": ["email", "phone"]
    }
  ],
  "rules": [
    {
      "type": "tag",
      "value": "pii"
    }
  ],
  "exceptions": {
    "groups": ["Administrators", "Data Owners"]
  }
}
```

### Schema Evolution Configuration

The schema evolution configuration file is a JSON file with the following structure:

```json
{
  "datasource_name_format": "{table_name}_v{version}",
  "query_engine_table_name_format": "{table_name}_v{version}",
  "enabled": true
}
```

## Software Development Lifecycle

This section provides a comprehensive guide to the full lifecycle of software management for the Immuta SRE Toolkit, from initial development to production deployment.

```
┌─────────────────────────────────────────────────────────────────────────┐
│                     Immuta SRE Toolkit Development Lifecycle            │
└─────────────────────────────────────────────────────────────────────────┘
                                     │
                                     ▼
┌─────────────────┐  ┌─────────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  1. Development  │  │    2. Testing &     │  │   3. Release    │  │  4. Deployment  │
│                  │◄─┤     Integration     │◄─┤                 │◄─┤                 │
│  • Setup env     │  │                     │  │  • Versioning   │  │  • Docker       │
│  • Code          │─►│  • Unit tests       │─►│  • Tagging      │─►│  • Kubernetes   │
│  • Review        │  │  • Integration tests│  │  • Publishing   │  │  • Monitoring   │
└─────────────────┘  └─────────────────────┘  └─────────────────┘  └─────────────────┘
        │                       │                      │                     │
        ▼                       ▼                      ▼                     ▼
┌─────────────────┐  ┌─────────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│  • Feature      │  │  • Automated tests   │  │  • Semantic     │  │  • Health checks│
│    branches     │  │  • Code coverage     │  │    versioning   │  │  • Metrics      │
│  • Pull requests│  │  • CI pipeline       │  │  • Changelogs   │  │  • Backups      │
│  • Code reviews │  │  • Quality gates     │  │  • Artifacts    │  │  • Scaling      │
└─────────────────┘  └─────────────────────┘  └─────────────────┘  └─────────────────┘
```

### 1. Development Environment Setup

#### 1.1 Initial Setup

You can use the provided setup script to quickly set up your development environment:

```bash
# Clone the repository
git clone https://github.com/your-org/immuta-sre-toolkit.git
cd immuta-sre-toolkit

# Run the setup script
./scripts/dev-setup.sh
```

The script will:
- Check if PDM is installed and install it if needed
- Check if Docker and Docker Compose are installed
- Create a `.env` file from `.env.example`
- Install dependencies
- Install pre-commit hooks
- Build Docker images

Alternatively, you can set up the environment manually:

```bash
# Clone the repository
git clone https://github.com/your-org/immuta-sre-toolkit.git
cd immuta-sre-toolkit

# Install PDM if not already installed
pip install pdm

# Install dependencies
pdm install

# Install pre-commit hooks
pdm run pre-commit install
```

#### 1.2 Development Container

For a consistent development experience, we recommend using the development container:

```bash
# Using VS Code
# 1. Install the "Remote - Containers" extension
# 2. Open the project folder in VS Code
# 3. Click on the green button in the bottom-left corner
# 4. Select "Reopen in Container"

# Using Docker directly
make dev  # Builds and runs the development container
make shell  # Opens a shell in the development container
```

The development container includes:
- All required dependencies
- Pre-configured development tools (linters, formatters)
- Git integration
- VS Code extensions for Python development

### 2. Development Workflow

#### 2.1 Feature Branch Workflow

We follow the feature branch workflow:

```bash
# Create a new feature branch
git checkout -b feature/your-feature-name

# Make your changes
# ...

# Run tests locally
make test

# Run linting
make lint

# Format code
make format

# Commit your changes
git add .
git commit -m "feat: add your feature description"

# Push your changes
git push origin feature/your-feature-name

# Create a pull request on GitHub
```

#### 2.2 Running Tests

The project uses pytest for testing. You can run tests using PDM:

```bash
# Run all tests
pdm run pytest

# Run tests with coverage
pdm run pytest --cov=immuta_toolkit

# Run specific tests
pdm run pytest tests/unit/test_project_service.py

# Run tests with verbose output
pdm run pytest -xvs

# Run tests with verbose output (alternative)
pdm run python -m pytest -xvs
```

If you encounter import errors when running tests, you can try:

```bash
# Add the src directory to PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)/src

# Run the tests
pdm run pytest
```

### 2.3 Code Quality Tools

The project uses several code quality tools that run automatically:

1. **Pre-commit hooks**: Run before each commit to ensure code quality
   ```bash
   # Run pre-commit manually
   pdm run pre-commit run --all-files
   ```

2. **Linting with Ruff**: Checks for code quality issues
   ```bash
   # Run linting
   make lint
   # or
   pdm run ruff check src/

   # Fix issues automatically
   pdm run ruff check --fix src/
   ```

3. **Formatting with Black**: Ensures consistent code formatting
   ```bash
   # Format code
   make format
   # or
   pdm run black src/ tests/
   ```

4. **Type checking with MyPy**: Verifies type annotations
   ```bash
   # Run type checking
   pdm run mypy src/
   ```

5. **Building Binaries**: Create standalone executables
   ```bash
   # Build a binary for the current platform
   pdm run python scripts/build_binary.py

   # Build a one-file binary
   pdm run python scripts/build_binary.py --one-file

   # Build for a specific platform
   pdm run python scripts/build_binary.py --platform windows
   pdm run python scripts/build_binary.py --platform macos
   pdm run python scripts/build_binary.py --platform linux

   # Or use the Makefile
   make binary-windows
   make binary-macos
   make binary-linux
   make binary-all
   ```

### 3. Testing Strategy

The project follows a comprehensive testing strategy with multiple levels:

#### 3.1 Unit Tests

Unit tests verify individual components in isolation:

```bash
# Run all unit tests
pdm run pytest tests/unit/

# Run specific unit tests
pdm run pytest tests/unit/test_cache.py

# Run tests with coverage
pdm run pytest --cov=immuta_toolkit
```

#### 3.2 Integration Tests

Integration tests verify interactions between components:

```bash
# Run all integration tests
pdm run pytest tests/integration/

# Run specific integration tests
pdm run pytest tests/integration/test_project_service_integration.py
```

#### 3.3 End-to-End Tests

End-to-end tests verify the entire system:

```bash
# Run all end-to-end tests
pdm run pytest tests/e2e/

# Run with a specific configuration
IMMUTA_API_KEY=your_key IMMUTA_BASE_URL=your_url pdm run pytest tests/e2e/
```

#### 3.4 Test Coverage

We aim for high test coverage:

```bash
# Generate coverage report
pdm run pytest --cov=immuta_toolkit --cov-report=html

# Open coverage report
open htmlcov/index.html
```

### 4. Continuous Integration

The project uses GitHub Actions for continuous integration:

1. **Pull Request Workflow**:
   - Runs on every pull request
   - Lints code with Ruff
   - Checks formatting with Black
   - Runs unit and integration tests
   - Generates test coverage report

2. **Main Branch Workflow**:
   - Runs on every push to main
   - Performs all PR checks
   - Builds Docker images
   - Publishes Docker images to GitHub Container Registry

The CI pipeline is defined in `.github/workflows/ci.yml`.

### 5. Release Process

#### 5.1 Semantic Versioning

We follow [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes
- **MINOR** version for backward-compatible functionality
- **PATCH** version for backward-compatible bug fixes

#### 5.2 Creating a Release

You can use the provided script to create a new release:

```bash
# Create a new release
./scripts/release.sh 1.0.0
```

The script will:
- Update the version in pyproject.toml
- Run tests to ensure everything is working
- Build Docker images
- Commit the version change
- Create a Git tag
- Optionally push changes and tag to the remote repository

You can also use the Makefile:

```bash
make release VERSION=1.0.0
```

#### 5.3 Release Workflow

The release process follows these steps:

1. **Prepare Release**:
   - Ensure all tests pass on the main branch
   - Update CHANGELOG.md with new features and fixes
   - Create a release branch: `release/vX.Y.Z`

2. **Create Release**:
   - Run the release script: `./scripts/release.sh X.Y.Z`
   - Push the changes and tag: `git push origin release/vX.Y.Z && git push origin vX.Y.Z`

3. **GitHub Release**:
   - The GitHub Actions workflow will create a GitHub Release
   - The workflow will build and publish Docker images
   - The workflow will build standalone binaries for Windows, macOS, and Linux
   - The workflow will publish the package to PyPI

4. **Post-Release**:
   - Merge the release branch back to main
   - Update documentation if needed

### 6. Deployment

#### 6.1 Docker Deployment

Deploy using Docker:

```bash
# Pull the image
docker pull ghcr.io/your-org/immuta-sre-toolkit:latest

# Run with environment variables
docker run -d \
  --name immuta-sre-toolkit \
  -e IMMUTA_API_KEY=your_key \
  -e IMMUTA_BASE_URL=your_url \
  -p 8000:8000 \
  -v /path/to/data:/app/data \
  ghcr.io/your-org/immuta-sre-toolkit:latest
```

#### 6.2 Kubernetes Deployment

Deploy to Kubernetes:

```bash
# Update configuration
# Edit kubernetes/deployment.yaml with your settings

# Apply the manifests
kubectl apply -f kubernetes/deployment.yaml

# Verify deployment
kubectl get pods -l app=immuta-sre-toolkit
```

#### 6.3 Continuous Deployment

For continuous deployment:

1. **Development Environment**:
   - Automatically deployed on merge to the develop branch
   - Uses the `latest` Docker tag

2. **Staging Environment**:
   - Deployed manually after testing in development
   - Uses release candidate tags: `vX.Y.Z-rc.N`

3. **Production Environment**:
   - Deployed manually after approval
   - Uses release tags: `vX.Y.Z`

### 7. Monitoring and Observability

#### 7.1 Health Checks

Monitor the application health:

```bash
# Check health status
curl http://localhost:8000/health
```

#### 7.2 Metrics

Monitor application metrics:

```bash
# Access metrics endpoint
curl http://localhost:8000/metrics
```

#### 7.3 Prometheus Integration

Configure Prometheus to scrape metrics:

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'immuta-sre-toolkit'
    scrape_interval: 15s
    static_configs:
      - targets: ['immuta-sre-toolkit:8000']
```

#### 7.4 Grafana Dashboards

Import the provided Grafana dashboards:
- `dashboards/overview.json`: General overview
- `dashboards/performance.json`: Performance metrics
- `dashboards/operations.json`: Operation metrics

### 8. Backup and Disaster Recovery

#### 8.1 Automated Backups

Schedule regular backups:

```bash
# Set up a cron job
0 1 * * * /path/to/scripts/automated-backup.sh --backup-dir /path/to/backups --retention-days 30
```

Or use the Kubernetes CronJob:

```bash
kubectl apply -f kubernetes/backup-cronjob.yaml
```

#### 8.2 Restore from Backup

Restore from a backup:

```bash
# Restore all projects
immuta restore projects --backup-dir /path/to/backup

# Restore specific projects
immuta restore projects --backup-dir /path/to/backup --project-ids 1,2,3
```

### 9. Documentation

#### 9.1 Code Documentation

Document your code using docstrings:

```python
def my_function(param1: str, param2: int) -> bool:
    """Short description of the function.

    Longer description explaining the function's purpose,
    behavior, and any side effects.

    Args:
        param1: Description of param1
        param2: Description of param2

    Returns:
        Description of return value

    Raises:
        ValueError: When param1 is empty
    """
    # Function implementation
```

#### 9.2 API Documentation

Generate API documentation:

```bash
# Generate API docs
pdm run sphinx-build -b html docs/source docs/build/html

# View documentation
open docs/build/html/index.html
```

#### 9.3 User Documentation

Update user documentation in the README.md and docs/ directory.

### 10. Troubleshooting

#### 10.1 Common Issues

- **Docker build fails**: Ensure Docker is running and you have sufficient permissions
- **Tests fail**: Check for environment variables or missing dependencies
- **Pre-commit hooks fail**: Run `pdm run pre-commit run --all-files` to see detailed errors

#### 10.2 Logs

Access logs for troubleshooting:

```bash
# Docker logs
docker logs immuta-sre-toolkit

# Kubernetes logs
kubectl logs -l app=immuta-sre-toolkit
```

#### 10.3 Getting Help

If you encounter issues:
1. Check the [FAQ](docs/faq.md)
2. Search existing GitHub issues
3. Create a new issue with detailed information

### Project Structure

```
immuta-sre-toolkit/
├── docker/                         # Docker-related files
│   ├── Dockerfile.prod             # Production Docker image
│   ├── Dockerfile.dev              # Development Docker image
│   ├── docker-compose.yml          # Docker Compose configuration
│   ├── entrypoint.sh               # Container entrypoint script
│   └── README.md                   # Docker documentation
├── configs/                        # Configuration files
│   ├── default.yaml                # Default configuration
│   ├── environments/               # Environment-specific configs
│   │   ├── dev.yaml
│   │   ├── test.yaml
│   │   └── prod.yaml
│   ├── schemas/                    # JSON schemas for validation
│   └── examples/                   # Example configurations
├── examples/                       # Example scripts and configurations
├── scripts/
│   └── immuta                      # Main entry point
├── src/immuta_toolkit/
│   ├── client.py                   # Core client implementation
│   ├── models.py                   # Data models using Pydantic
│   ├── services/
│   │   ├── backup_service.py       # Backup and restore operations
│   │   ├── batch_service.py        # Batch operations
│   │   ├── config_service.py       # Schema evolution configuration
│   │   ├── data_source_service.py  # Data source management with tagging
│   │   ├── policy_service.py       # Policy management
│   │   ├── project_service.py      # Project management
│   │   ├── purpose_service.py      # Purpose management
│   │   ├── query_service.py        # Query operations
│   │   ├── tag_service.py          # Tag management
│   │   └── user_service.py         # User management
│   └── utils/
│       ├── cache.py                # Caching utilities
│       ├── logging.py              # Logging utilities
│       ├── notifications.py        # Notification management
│       ├── rate_limiter.py         # Rate limiting
│       ├── secrets.py              # Secrets management
│       ├── snowflake.py            # Snowflake connection pool and audit logger
│       └── storage.py              # Storage management
├── tests/
│   ├── unit/
│   │   ├── test_cache.py           # Tests for the cache utility
│   │   ├── test_client.py          # Tests for the client
│   │   ├── test_client_snowflake.py # Tests for Snowflake integration
│   │   ├── test_secrets_manager.py # Tests for secrets management
│   │   ├── test_snowflake.py       # Tests for Snowflake connection pool
│   │   └── test_snowflake_audit.py # Tests for Snowflake audit logger
│   ├── integration/
│   │   └── test_project_service_integration.py # Integration tests for project service
│   └── e2e/
│       └── test_web_automation.py  # End-to-end tests for web automation
├── docs/
│   ├── README.md                   # Documentation index
│   ├── api_reference.md            # API reference documentation
│   ├── security_best_practices.md  # Security best practices
│   └── development_guide.md        # Development guide
└── tools/                          # Development and maintenance tools
```

## License

MIT
