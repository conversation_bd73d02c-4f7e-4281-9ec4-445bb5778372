#!/bin/bash
# Run Immuta SRE Toolkit in a Docker container
# This script helps run the toolkit with proper environment variables

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}    Immuta SRE Toolkit Docker Runner     ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""

# Check if .env file exists
if [ ! -f .env ]; then
    echo -e "${YELLOW}Warning: .env file not found. Creating from .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}.env file created. Please update it with your credentials.${NC}"
    echo -e "${RED}Please edit the .env file and run this script again.${NC}"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed. Please install Docker first.${NC}"
    echo "Visit https://docs.docker.com/get-docker/ for installation instructions."
    exit 1
fi

# Check if the image exists
if ! docker image inspect immuta-sre-toolkit:latest &> /dev/null; then
    echo -e "${YELLOW}Docker image not found. Building image...${NC}"
    docker build -t immuta-sre-toolkit:latest -f docker/Dockerfile.prod .
    echo -e "${GREEN}Docker image built successfully!${NC}"
fi

# Create necessary directories if they don't exist
mkdir -p data logs reports configs

# Parse command line arguments
MODE="cli"
COMMAND="--help"

# Check for special modes
if [ "$1" == "api" ]; then
    MODE="api"
    shift
    COMMAND="api start --host 0.0.0.0 --port 8000"
    if [ $# -gt 0 ]; then
        COMMAND="api $@"
    fi
elif [ "$1" == "dev" ]; then
    MODE="dev"
    shift
    COMMAND="bash"
    if [ $# -gt 0 ]; then
        COMMAND="$@"
    fi
elif [ "$1" == "test" ]; then
    MODE="test"
    shift
    COMMAND="pytest"
    if [ $# -gt 0 ]; then
        COMMAND="pytest $@"
    fi
elif [ $# -gt 0 ]; then
    COMMAND="$@"
fi

# Run the container
echo -e "${YELLOW}Running Immuta SRE Toolkit in Docker container (Mode: $MODE)...${NC}"
echo -e "${YELLOW}Command: $COMMAND${NC}"
echo ""

if [ "$MODE" == "api" ]; then
    # Run API server with port mapping
    docker run -it --rm \
        --name immuta-api \
        --env-file .env \
        -p 8000:8000 \
        -v "$(pwd)/data:/app/data:rw" \
        -v "$(pwd)/logs:/app/logs:rw" \
        -v "$(pwd)/reports:/app/reports:rw" \
        -v "$(pwd)/configs:/app/configs:ro" \
        --security-opt no-new-privileges:true \
        immuta-sre-toolkit:latest $COMMAND
elif [ "$MODE" == "dev" ]; then
    # Run development container with source code mounted
    docker run -it --rm \
        --name immuta-dev \
        --env-file .env \
        -p 8000:8000 \
        -v "$(pwd):/app:rw" \
        --security-opt no-new-privileges:true \
        immuta-sre-toolkit-dev:latest $COMMAND
elif [ "$MODE" == "test" ]; then
    # Run test container
    docker run -it --rm \
        --name immuta-test \
        --env-file .env \
        -v "$(pwd):/app:rw" \
        --security-opt no-new-privileges:true \
        immuta-sre-toolkit-dev:latest $COMMAND
else
    # Run CLI container
    docker run -it --rm \
        --name immuta-cli \
        --env-file .env \
        -v "$(pwd)/data:/app/data:rw" \
        -v "$(pwd)/logs:/app/logs:rw" \
        -v "$(pwd)/reports:/app/reports:rw" \
        -v "$(pwd)/configs:/app/configs:ro" \
        --security-opt no-new-privileges:true \
        immuta-sre-toolkit:latest $COMMAND
fi

echo ""
echo -e "${GREEN}Command completed!${NC}"
