#!/bin/bash
# Automated backup script for Immuta SRE Toolkit
# This script performs automated backups and manages retention

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Default values
BACKUP_DIR="/app/data/backups"
RETENTION_DAYS=30
BACKUP_TYPES="projects,users,policies,data_sources,purposes"
NOTIFICATION_EMAIL=""
NOTIFICATION_SLACK=""
NOTIFICATION_TEAMS=""
BACKUP_LOG="/app/data/logs/backup.log"

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}    Immuta SRE Toolkit Backup Script     ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --backup-dir)
            BACKUP_DIR="$2"
            shift
            shift
            ;;
        --retention-days)
            RETENTION_DAYS="$2"
            shift
            shift
            ;;
        --backup-types)
            BACKUP_TYPES="$2"
            shift
            shift
            ;;
        --notification-email)
            NOTIFICATION_EMAIL="$2"
            shift
            shift
            ;;
        --notification-slack)
            NOTIFICATION_SLACK="$2"
            shift
            shift
            ;;
        --notification-teams)
            NOTIFICATION_TEAMS="$2"
            shift
            shift
            ;;
        --log)
            BACKUP_LOG="$2"
            shift
            shift
            ;;
        *)
            echo -e "${RED}Unknown option: $key${NC}"
            exit 1
            ;;
    esac
done

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"
mkdir -p "$(dirname "$BACKUP_LOG")"

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$BACKUP_LOG"
}

# Send notification
send_notification() {
    local subject="$1"
    local message="$2"
    local status="$3"  # success, warning, error
    
    # Log the notification
    log "$subject: $message"
    
    # Send email notification
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        echo "$message" | mail -s "$subject" "$NOTIFICATION_EMAIL"
    fi
    
    # Send Slack notification
    if [ -n "$NOTIFICATION_SLACK" ]; then
        local color="good"
        if [ "$status" == "warning" ]; then
            color="warning"
        elif [ "$status" == "error" ]; then
            color="danger"
        fi
        
        curl -s -X POST -H 'Content-type: application/json' --data "{
            \"attachments\": [
                {
                    \"color\": \"$color\",
                    \"title\": \"$subject\",
                    \"text\": \"$message\",
                    \"footer\": \"Immuta SRE Toolkit\",
                    \"ts\": $(date +%s)
                }
            ]
        }" "$NOTIFICATION_SLACK"
    fi
    
    # Send Teams notification
    if [ -n "$NOTIFICATION_TEAMS" ]; then
        local color="00FF00"
        if [ "$status" == "warning" ]; then
            color="FFA500"
        elif [ "$status" == "error" ]; then
            color="FF0000"
        fi
        
        curl -s -X POST -H 'Content-Type: application/json' --data "{
            \"@type\": \"MessageCard\",
            \"@context\": \"http://schema.org/extensions\",
            \"themeColor\": \"$color\",
            \"summary\": \"$subject\",
            \"sections\": [
                {
                    \"activityTitle\": \"$subject\",
                    \"activitySubtitle\": \"Immuta SRE Toolkit\",
                    \"text\": \"$message\",
                    \"facts\": [
                        {
                            \"name\": \"Time\",
                            \"value\": \"$(date '+%Y-%m-%d %H:%M:%S')\"
                        }
                    ]
                }
            ]
        }" "$NOTIFICATION_TEAMS"
    fi
}

# Create backup timestamp
TIMESTAMP=$(date +%Y-%m-%d_%H-%M-%S)
BACKUP_PATH="$BACKUP_DIR/$TIMESTAMP"

# Start backup
log "Starting backup to $BACKUP_PATH"
mkdir -p "$BACKUP_PATH"

# Backup each type
IFS=',' read -ra TYPES <<< "$BACKUP_TYPES"
for TYPE in "${TYPES[@]}"; do
    log "Backing up $TYPE..."
    
    case $TYPE in
        projects)
            immuta backup projects --output-dir "$BACKUP_PATH/projects" --retention-days "$RETENTION_DAYS"
            ;;
        users)
            immuta backup users --output-dir "$BACKUP_PATH/users" --retention-days "$RETENTION_DAYS"
            ;;
        policies)
            immuta backup policies --output-dir "$BACKUP_PATH/policies" --retention-days "$RETENTION_DAYS"
            ;;
        data_sources)
            immuta backup data-sources --output-dir "$BACKUP_PATH/data_sources" --retention-days "$RETENTION_DAYS"
            ;;
        purposes)
            immuta backup purposes --output-dir "$BACKUP_PATH/purposes" --retention-days "$RETENTION_DAYS"
            ;;
        *)
            log "Unknown backup type: $TYPE"
            ;;
    esac
    
    if [ $? -ne 0 ]; then
        send_notification "Backup Failed" "Failed to backup $TYPE" "error"
    else
        log "Successfully backed up $TYPE"
    fi
done

# Verify backup
log "Verifying backup..."
immuta backup verify --backup-dir "$BACKUP_PATH"

if [ $? -ne 0 ]; then
    send_notification "Backup Verification Failed" "Failed to verify backup at $BACKUP_PATH" "error"
    exit 1
fi

# Calculate backup size
BACKUP_SIZE=$(du -sh "$BACKUP_PATH" | cut -f1)

# Clean up old backups
log "Cleaning up backups older than $RETENTION_DAYS days..."
find "$BACKUP_DIR" -type d -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true

# Count remaining backups
BACKUP_COUNT=$(find "$BACKUP_DIR" -maxdepth 1 -type d | wc -l)
BACKUP_COUNT=$((BACKUP_COUNT - 1))  # Subtract 1 for the backup directory itself

# Send success notification
send_notification "Backup Completed" "Successfully created backup at $BACKUP_PATH\nSize: $BACKUP_SIZE\nRetained backups: $BACKUP_COUNT" "success"

log "Backup completed successfully"
