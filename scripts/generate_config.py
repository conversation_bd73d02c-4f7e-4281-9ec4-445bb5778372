#!/usr/bin/env python3
"""Script to generate a configuration template for the Immuta SRE Toolkit."""

import os
import sys
import argparse
import json
import yaml
import toml
from typing import Dict, Any, Optional

from immuta_toolkit.config import Config<PERSON>anager, ConfigFormat
from immuta_toolkit.config.schema import ConfigSchema
from immuta_toolkit.config.validation import ConfigValidator


def parse_args():
    """Parse command line arguments.

    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="Generate a configuration template for the Immuta SRE Toolkit"
    )

    parser.add_argument(
        "--output",
        "-o",
        default="config.json",
        help="Output file path (default: config.json)",
    )

    parser.add_argument(
        "--format",
        "-f",
        choices=["json", "yaml", "toml"],
        help="Output file format (default: determined from file extension)",
    )

    parser.add_argument(
        "--environment",
        "-e",
        choices=["local", "dev", "test", "prod"],
        default="dev",
        help="Environment type (default: dev)",
    )

    parser.add_argument(
        "--api-url",
        help="Immuta API base URL",
    )

    parser.add_argument(
        "--web-url",
        help="Immuta web UI base URL",
    )

    parser.add_argument(
        "--username",
        help="Immuta username",
    )

    parser.add_argument(
        "--password",
        help="Immuta password",
    )

    parser.add_argument(
        "--api-key",
        help="Immuta API key",
    )

    parser.add_argument(
        "--secrets-provider",
        choices=["env", "azure", "aws", "hashicorp"],
        help="Secrets provider",
    )

    parser.add_argument(
        "--vault-url",
        help="Secrets vault URL",
    )

    parser.add_argument(
        "--storage-provider",
        choices=["file", "azure", "aws", "sqlite"],
        help="Storage provider",
    )

    parser.add_argument(
        "--storage-path",
        help="Local storage path",
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        help="Logging level",
    )

    parser.add_argument(
        "--log-file",
        help="Log file path",
    )

    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate the configuration",
    )

    parser.add_argument(
        "--force",
        "-y",
        action="store_true",
        help="Overwrite existing file without confirmation",
    )

    return parser.parse_args()


def get_format(output_path: str, format_arg: Optional[str] = None) -> ConfigFormat:
    """Get the configuration format.

    Args:
        output_path: Output file path.
        format_arg: Format argument.

    Returns:
        Configuration format.

    Raises:
        ValueError: If the format cannot be determined.
    """
    # Use format argument if provided
    if format_arg:
        if format_arg.lower() == "json":
            return ConfigFormat.JSON
        elif format_arg.lower() == "yaml":
            return ConfigFormat.YAML
        elif format_arg.lower() == "toml":
            return ConfigFormat.TOML
        else:
            raise ValueError(f"Unsupported format: {format_arg}")

    # Determine format from file extension
    ext = os.path.splitext(output_path)[1].lower()
    if ext == ".json":
        return ConfigFormat.JSON
    elif ext in (".yaml", ".yml"):
        return ConfigFormat.YAML
    elif ext == ".toml":
        return ConfigFormat.TOML
    else:
        raise ValueError(f"Cannot determine format from file extension: {ext}")


def generate_config(args) -> Dict[str, Any]:
    """Generate a configuration dictionary.

    Args:
        args: Command-line arguments.

    Returns:
        Configuration dictionary.
    """
    # Create configuration manager
    manager = ConfigManager()

    # Load default configuration
    config = manager.config.model_dump()

    # Set environment
    config["environment"] = args.environment

    # Set API configuration
    if args.api_url:
        config["api"]["base_url"] = args.api_url
    if args.api_key:
        config["api"]["api_key"] = args.api_key

    # Set web configuration
    if args.web_url:
        config["web"]["base_url"] = args.web_url
    if args.username:
        config["web"]["username"] = args.username
    if args.password:
        config["web"]["password"] = args.password

    # Set secrets configuration
    if args.secrets_provider:
        config["secrets"]["provider"] = args.secrets_provider
    if args.vault_url:
        config["secrets"]["vault_url"] = args.vault_url

    # Set storage configuration
    if args.storage_provider:
        config["storage"]["provider"] = args.storage_provider
    if args.storage_path:
        config["storage"]["local_path"] = args.storage_path

    # Set logging configuration
    if args.log_level:
        config["logging"]["level"] = args.log_level
    if args.log_file:
        config["logging"]["file"] = args.log_file

    # Set version
    config["version"] = "1.0.0"

    return config


def validate_config(config: Dict[str, Any]) -> bool:
    """Validate a configuration.

    Args:
        config: Configuration dictionary.

    Returns:
        True if the configuration is valid, False otherwise.
    """
    # Create validator
    validator = ConfigValidator()

    # Validate configuration
    results = validator.validate(config)

    # Print validation results
    if results:
        print("Validation results:")
        for result in results:
            level = result.level.upper()
            if level == "ERROR":
                print(f"  ERROR: {result.key}: {result.message}")
            elif level == "WARNING":
                print(f"  WARNING: {result.key}: {result.message}")
            else:
                print(f"  INFO: {result.key}: {result.message}")

        # Check if there are any errors
        errors = [r for r in results if r.level.upper() == "ERROR"]
        if errors:
            return False

    return True


def save_config(config: Dict[str, Any], output_path: str, format: ConfigFormat) -> None:
    """Save a configuration to a file.

    Args:
        config: Configuration dictionary.
        output_path: Output file path.
        format: Configuration format.
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    # Save configuration to file
    with open(output_path, "w") as f:
        if format == ConfigFormat.JSON:
            json.dump(config, f, indent=2)
        elif format == ConfigFormat.YAML:
            yaml.dump(config, f, default_flow_style=False)
        elif format == ConfigFormat.TOML:
            toml.dump(config, f)
        else:
            raise ValueError(f"Unsupported format: {format}")

    print(f"Configuration saved to {output_path}")


def main():
    """Main entry point."""
    # Parse command-line arguments
    args = parse_args()

    # Get configuration format
    try:
        format = get_format(args.output, args.format)
    except ValueError as e:
        print(f"Error: {e}")
        return 1

    # Check if output file exists
    if os.path.exists(args.output) and not args.force:
        response = input(f"File {args.output} already exists. Overwrite? [y/N] ")
        if response.lower() not in ("y", "yes"):
            print("Aborted")
            return 0

    # Generate configuration
    config = generate_config(args)

    # Validate configuration if requested
    if args.validate:
        if not validate_config(config):
            print("Configuration is invalid")
            return 1

    # Save configuration
    try:
        save_config(config, args.output, format)
    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
