#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix the PYTHONPATH for CI environments.

This script ensures that the immuta_toolkit package can be imported by:
1. Adding the src directory to sys.path
2. Setting the PYTHONPATH environment variable
3. Creating a .pythonpath file for shell scripts
4. Installing the package in development mode if needed
"""

import os
import site
import sys
import subprocess
import importlib
from pathlib import Path
from typing import Optional, Tuple, List, Dict, Any, Union, Set


def find_project_root() -> Path:
    """Find the project root directory.

    Returns:
        Path to the project root directory.
    """
    # Start with the directory of this script
    script_dir = Path(__file__).parent.absolute()

    # Go up one level to find the project root
    project_root = script_dir.parent

    # Verify that this is the project root by checking for key files/directories
    if (project_root / "src").exists() and (project_root / "pyproject.toml").exists():
        return project_root

    # If not found, try the current directory
    current_dir = Path.cwd()
    if (current_dir / "src").exists() and (current_dir / "pyproject.toml").exists():
        return current_dir

    # If still not found, use the script parent directory as a fallback
    return project_root


def create_pythonpath_file(src_dir: Path) -> None:
    """Create a .pythonpath file for shell scripts.

    Args:
        src_dir: Path to the src directory.
    """
    pythonpath_file = Path(".pythonpath")

    # Create the file with export commands for different shells
    with open(pythonpath_file, "w") as f:
        f.write(f"# Generated by fix-pythonpath.py\n")
        f.write(f"# Source this file in your shell scripts\n\n")

        # Bash/Zsh export
        f.write(f"# For Bash/Zsh\n")
        f.write(f'export PYTHONPATH="{src_dir}:$PYTHONPATH"\n\n')

        # Fish shell export
        f.write(f"# For Fish shell\n")
        f.write(f"# set -x PYTHONPATH {src_dir}:$PYTHONPATH\n\n")

        # Windows CMD export
        f.write(f"# For Windows CMD\n")
        f.write(f"# set PYTHONPATH={src_dir};%PYTHONPATH%\n\n")

        # PowerShell export
        f.write(f"# For PowerShell\n")
        f.write(f'# $env:PYTHONPATH = "{src_dir};$env:PYTHONPATH"\n')

    print(f"Created {pythonpath_file} file")
    print(f"You can source this file in your shell scripts:")
    print(f"  source .pythonpath")


def fix_pythonpath() -> bool:
    """Add the src directory to PYTHONPATH.

    Returns:
        True if successful, False otherwise.
    """
    print("=== PYTHONPATH Fixer ===")

    # Get the project root directory
    project_root = find_project_root()
    src_dir = project_root / "src"

    # Print current environment
    print(f"Python version: {sys.version}")
    print(f"Project root: {project_root}")
    print(f"Source directory: {src_dir}")
    print(f"Current PYTHONPATH: {os.environ.get('PYTHONPATH', '')}")

    # Add src directory to sys.path
    if str(src_dir) not in sys.path:
        sys.path.insert(0, str(src_dir))
        print(f"Added {src_dir} to sys.path")

    # Add src directory to PYTHONPATH environment variable
    pythonpath = os.environ.get("PYTHONPATH", "")
    if str(src_dir) not in pythonpath:
        # Use platform-specific path separator
        separator = ":" if os.name != "nt" else ";"
        os.environ["PYTHONPATH"] = (
            f"{src_dir}{separator}{pythonpath}" if pythonpath else str(src_dir)
        )
        print(f"Added {src_dir} to PYTHONPATH environment variable")

    # Create .pythonpath file
    create_pythonpath_file(src_dir)

    # Try to import the package
    try:
        import immuta_toolkit

        print(
            f"Successfully imported immuta_toolkit (version: {immuta_toolkit.__version__})"
        )
        return True
    except ImportError as e:
        print(f"Failed to import immuta_toolkit: {e}")

        # Try to install the package in development mode
        print("Attempting to install the package in development mode...")
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-e", "."],
                check=True,
                cwd=project_root,
                capture_output=True,
                text=True,
            )
            print("Installation successful")

            # Try importing again
            try:
                importlib.invalidate_caches()
                import immuta_toolkit

                print(
                    f"Successfully imported immuta_toolkit after installation (version: {immuta_toolkit.__version__})"
                )
                return True
            except ImportError as e:
                print(f"Still failed to import immuta_toolkit after installation: {e}")

                # Try with PDM as a last resort
                print("Trying to install with PDM...")
                try:
                    pdm_result = subprocess.run(
                        ["pdm", "install", "-v"],
                        check=True,
                        cwd=project_root,
                        capture_output=True,
                        text=True,
                    )
                    print("PDM installation successful")

                    # Try importing one more time
                    importlib.invalidate_caches()
                    import immuta_toolkit

                    print(
                        f"Successfully imported immuta_toolkit after PDM installation (version: {immuta_toolkit.__version__})"
                    )
                    return True
                except (subprocess.CalledProcessError, FileNotFoundError) as e:
                    print(f"Failed to install with PDM: {e}")
                    return False
        except subprocess.CalledProcessError as e:
            print(f"Failed to install the package: {e}")
            return False


if __name__ == "__main__":
    try:
        success = fix_pythonpath()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(130)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
