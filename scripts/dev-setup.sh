#!/bin/bash
# Development setup script for Immuta SRE Toolkit
# This script helps set up the development environment

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}  Immuta SRE Toolkit Development Setup  ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""

# Check if PDM is installed
if ! command -v pdm &> /dev/null; then
    echo -e "${YELLOW}PDM is not installed. Installing PDM...${NC}"
    pip install pdm
    echo -e "${GREEN}PDM installed successfully!${NC}"
else
    echo -e "${GREEN}PDM is already installed.${NC}"
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker is not installed. Please install Docker first.${NC}"
    echo "Visit https://docs.docker.com/get-docker/ for installation instructions."
    exit 1
else
    echo -e "${GREEN}Docker is already installed.${NC}"
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose is not installed. Please install Docker Compose first.${NC}"
    echo "Visit https://docs.docker.com/compose/install/ for installation instructions."
    exit 1
else
    echo -e "${GREEN}Docker Compose is already installed.${NC}"
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}Creating .env file from .env.example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}.env file created. Please update it with your credentials.${NC}"
else
    echo -e "${GREEN}.env file already exists.${NC}"
fi

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
pdm install
echo -e "${GREEN}Dependencies installed successfully!${NC}"

# Install pre-commit hooks
echo -e "${YELLOW}Installing pre-commit hooks...${NC}"
pdm run pre-commit install
echo -e "${GREEN}Pre-commit hooks installed successfully!${NC}"

# Build Docker images
echo -e "${YELLOW}Building Docker images...${NC}"
echo -e "${YELLOW}This may take a few minutes...${NC}"
make build-dev
echo -e "${GREEN}Docker images built successfully!${NC}"

# Final instructions
echo ""
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}  Development environment is ready!  ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""
echo -e "You can now run the development container with: ${YELLOW}make run-dev${NC}"
echo -e "Then access the container with: ${YELLOW}make shell${NC}"
echo ""
echo -e "To run tests: ${YELLOW}make test${NC}"
echo -e "To run linting: ${YELLOW}make lint${NC}"
echo -e "To format code: ${YELLOW}make format${NC}"
echo ""
echo -e "For more commands, run: ${YELLOW}make help${NC}"
echo ""
