#!/bin/bash
# Release script for Immuta SRE Toolkit
# This script helps create a new release

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}    Immuta SRE Toolkit Release Script    ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""

# Check if a version is provided
if [ -z "$1" ]; then
    echo -e "${RED}Error: Version number is required.${NC}"
    echo "Usage: $0 <version>"
    echo "Example: $0 1.0.0"
    exit 1
fi

VERSION=$1

# Validate version format (semver)
if ! [[ $VERSION =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
    echo -e "${RED}Error: Version must be in the format x.y.z (e.g., 1.0.0)${NC}"
    exit 1
fi

# Check if we're on the main branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$CURRENT_BRANCH" != "main" ] && [ "$CURRENT_BRANCH" != "master" ]; then
    echo -e "${YELLOW}Warning: You are not on the main/master branch.${NC}"
    read -p "Do you want to continue? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${RED}Release aborted.${NC}"
        exit 1
    fi
fi

# Check if there are uncommitted changes
if ! git diff-index --quiet HEAD --; then
    echo -e "${RED}Error: There are uncommitted changes in the repository.${NC}"
    echo "Please commit or stash your changes before creating a release."
    exit 1
fi

# Update version in pyproject.toml
echo -e "${YELLOW}Updating version in pyproject.toml...${NC}"
sed -i "s/version = \"[0-9]*\.[0-9]*\.[0-9]*\"/version = \"$VERSION\"/" pyproject.toml
echo -e "${GREEN}Version updated in pyproject.toml${NC}"

# Run tests
echo -e "${YELLOW}Running tests...${NC}"
pdm run pytest
echo -e "${GREEN}Tests passed!${NC}"

# Build Docker images
echo -e "${YELLOW}Building Docker images...${NC}"
make build
make build-dev
echo -e "${GREEN}Docker images built successfully!${NC}"

# Commit version change
echo -e "${YELLOW}Committing version change...${NC}"
git add pyproject.toml
git commit -m "Bump version to $VERSION"
echo -e "${GREEN}Version change committed!${NC}"

# Create tag
echo -e "${YELLOW}Creating tag v$VERSION...${NC}"
git tag -a "v$VERSION" -m "Release v$VERSION"
echo -e "${GREEN}Tag created!${NC}"

# Push changes
echo -e "${YELLOW}Do you want to push the changes and tag to the remote repository?${NC}"
read -p "Push changes? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}Pushing changes and tag...${NC}"
    git push origin "$CURRENT_BRANCH"
    git push origin "v$VERSION"
    echo -e "${GREEN}Changes and tag pushed!${NC}"
else
    echo -e "${YELLOW}Changes and tag not pushed. You can push them later with:${NC}"
    echo "  git push origin $CURRENT_BRANCH"
    echo "  git push origin v$VERSION"
fi

# Final instructions
echo ""
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}  Release v$VERSION created successfully!  ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""
echo -e "Next steps:"
echo -e "1. Create a release on GitHub: ${YELLOW}https://github.com/your-org/immuta-sre-toolkit/releases/new?tag=v$VERSION${NC}"
echo -e "2. Build and publish the package: ${YELLOW}pdm build && pdm publish${NC}"
echo ""
