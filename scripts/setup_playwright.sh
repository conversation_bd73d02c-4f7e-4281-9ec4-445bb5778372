#!/bin/bash
# Script to install Playwright and browsers for the Immuta SRE Toolkit

set -e

echo "Setting up Playwright for Immuta SRE Toolkit..."

# Check if PDM is installed
if ! command -v pdm &> /dev/null; then
    echo "PDM is not installed. Installing PDM..."
    pip install --user pdm
fi

# Install Playwright
echo "Installing Playwright..."
pdm add playwright

# Install Playwright browsers
echo "Installing Playwright browsers..."
pdm run playwright install

# Install Playwright dependencies
echo "Installing Playwright system dependencies..."
pdm run playwright install-deps

echo "Playwright setup completed successfully!"
