#!/usr/bin/env python
"""
Build script for creating standalone binaries of the Immuta SRE Toolkit.

This script uses PyInstaller to create standalone executables for Windows, macOS, and Linux.
The resulting binaries can be run without Python or any dependencies installed.
"""

import argparse
import os
import platform
import shutil
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Optional, Dict, Any, Tuple


def parse_args() -> argparse.Namespace:
    """Parse command line arguments.

    Returns:
        Parsed command line arguments.
    """
    parser = argparse.ArgumentParser(
        description="Build standalone binaries for the Immuta SRE Toolkit"
    )
    parser.add_argument(
        "--clean",
        action="store_true",
        help="Clean build directories before building",
    )
    parser.add_argument(
        "--one-file",
        action="store_true",
        help="Create a single executable file instead of a directory",
    )
    parser.add_argument(
        "--output-dir",
        type=str,
        default="dist",
        help="Output directory for the binaries (default: dist)",
    )
    parser.add_argument(
        "--platform",
        type=str,
        choices=["auto", "windows", "macos", "linux"],
        default="auto",
        help="Target platform (default: auto-detect)",
    )
    parser.add_argument(
        "--upx",
        action="store_true",
        help="Compress executable with UPX (if available)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Build with debug information",
    )
    parser.add_argument(
        "--version",
        type=str,
        help="Version to embed in the binary",
    )
    return parser.parse_args()


def get_platform(platform_arg: str) -> str:
    """Get the target platform.

    Args:
        platform_arg: Platform argument from command line.

    Returns:
        Detected platform name.

    Raises:
        SystemExit: If the platform is not supported.
    """
    if platform_arg != "auto":
        return platform_arg

    system = platform.system().lower()
    if system == "windows":
        return "windows"
    elif system == "darwin":
        return "macos"
    elif system == "linux":
        return "linux"
    else:
        print(f"Error: Unsupported platform: {system}")
        print("Supported platforms: windows, macos, linux")
        sys.exit(1)


def clean_build_dirs(output_dir: str) -> None:
    """Clean build directories.

    Args:
        output_dir: Output directory for binaries.
    """
    print("Cleaning build directories...")

    try:
        # Clean PyInstaller build directories
        build_dir = Path("build")
        if build_dir.exists():
            print(f"Removing build directory: {build_dir}")
            shutil.rmtree(build_dir)

        # Clean output directory
        output_path = Path(output_dir)
        if output_path.exists():
            print(f"Removing output directory: {output_path}")
            shutil.rmtree(output_path)

        # Clean PyInstaller cache
        cache_dir = Path("__pycache__")
        if cache_dir.exists():
            print(f"Removing cache directory: {cache_dir}")
            shutil.rmtree(cache_dir)

        # Clean spec files
        for spec_file in Path(".").glob("*.spec"):
            print(f"Removing spec file: {spec_file}")
            spec_file.unlink()

        print("Clean completed successfully")
    except Exception as e:
        print(f"Error during cleanup: {e}")
        print("Continuing with build process...")


def build_binary(
    target_platform: str,
    one_file: bool,
    output_dir: str,
    upx: bool = False,
    debug: bool = False,
    version: Optional[str] = None,
) -> str:
    """Build a standalone binary for the specified platform.

    Args:
        target_platform: Target platform (windows, macos, linux).
        one_file: Whether to build a single executable file.
        output_dir: Output directory for binaries.
        upx: Whether to compress executable with UPX.
        debug: Whether to build with debug information.
        version: Version to embed in the binary.

    Returns:
        Path to the built binary.

    Raises:
        subprocess.CalledProcessError: If PyInstaller fails.
    """
    print(f"Building binary for {target_platform}...")

    # Determine the entry point
    entry_point = "src/immuta_toolkit/cli/main.py"

    # Determine the output name based on platform
    output_name = "immuta.exe" if target_platform == "windows" else "immuta"

    # Build the PyInstaller command
    cmd = [
        "pyinstaller",
        "--name",
        output_name,
        "--log-level",
        "INFO" if not debug else "DEBUG",
        "--clean",
    ]

    # Add platform-specific options
    if target_platform == "windows":
        icon_path = Path("resources/icon.ico")
        if icon_path.exists():
            cmd.extend(["--icon", str(icon_path)])
        else:
            print(f"Warning: Icon file {icon_path} not found, continuing without icon")
    elif target_platform == "macos":
        icon_path = Path("resources/icon.icns")
        if icon_path.exists():
            cmd.extend(["--icon", str(icon_path)])
        else:
            print(f"Warning: Icon file {icon_path} not found, continuing without icon")

        # Add macOS-specific options
        cmd.extend(
            [
                "--osx-bundle-identifier",
                "com.immuta.sre-toolkit",
                "--target-architecture",
                "universal2",
            ]
        )

    # Add one-file option if requested
    if one_file:
        cmd.append("--onefile")
    else:
        cmd.append("--onedir")

    # Add UPX compression if requested
    if upx:
        # Check if UPX is available
        try:
            upx_result = subprocess.run(
                ["upx", "--version"], capture_output=True, text=True, check=False
            )
            if upx_result.returncode == 0:
                cmd.append("--upx-dir=.")
            else:
                print("Warning: UPX not found, continuing without compression")
        except FileNotFoundError:
            print("Warning: UPX not found, continuing without compression")

    # Add version information if provided
    if version:
        cmd.extend(
            [
                "--version-file",
                f"version={version}",
            ]
        )

    # Add output directory
    cmd.extend(["--distpath", output_dir])

    # Add hidden imports for common issues
    cmd.extend(
        [
            "--hidden-import",
            "pkg_resources.py2_warn",
            "--hidden-import",
            "azure.identity",
            "--hidden-import",
            "azure.storage.blob",
            "--hidden-import",
            "azure.keyvault.secrets",
            "--hidden-import",
            "immuta_toolkit",
        ]
    )

    # Add the entry point
    cmd.append(entry_point)

    # Run PyInstaller
    print(f"Running command: {' '.join(cmd)}")
    start_time = time.time()
    try:
        subprocess.run(cmd, check=True)
        end_time = time.time()
        build_time = end_time - start_time

        # Determine the binary path
        if one_file:
            binary_path = os.path.join(output_dir, output_name)
        else:
            binary_path = os.path.join(output_dir, output_name, output_name)

        # Check if the binary exists
        if not os.path.exists(binary_path):
            print(f"Error: Binary not found at {binary_path}")
            sys.exit(1)

        # Get binary size
        binary_size = os.path.getsize(binary_path)
        binary_size_mb = binary_size / (1024 * 1024)

        # Print success message
        print(f"\nBuild successful! Binary created at: {binary_path}")
        print(f"Build time: {build_time:.2f} seconds")
        print(f"Binary size: {binary_size_mb:.2f} MB")
        print(f"You can run it with: {binary_path}")

        return binary_path
    except subprocess.CalledProcessError as e:
        print(f"Error: PyInstaller failed with exit code {e.returncode}")
        print("Check the output above for more details.")
        sys.exit(1)


def main() -> int:
    """Main entry point.

    Returns:
        Exit code (0 for success, non-zero for failure).
    """
    try:
        args = parse_args()

        # Get the target platform
        target_platform = get_platform(args.platform)

        # Clean build directories if requested
        if args.clean:
            clean_build_dirs(args.output_dir)

        # Create output directory if it doesn't exist
        os.makedirs(args.output_dir, exist_ok=True)

        # Build the binary
        binary_path = build_binary(
            target_platform=target_platform,
            one_file=args.one_file,
            output_dir=args.output_dir,
            upx=args.upx if hasattr(args, "upx") else False,
            debug=args.debug if hasattr(args, "debug") else False,
            version=args.version if hasattr(args, "version") else None,
        )

        # Verify the binary is executable
        if os.path.exists(binary_path):
            if target_platform != "windows":
                # Make the binary executable on Unix-like systems
                os.chmod(binary_path, 0o755)

            print(f"Binary is ready for use: {binary_path}")
            return 0
        else:
            print(f"Error: Binary not found at {binary_path}")
            return 1
    except KeyboardInterrupt:
        print("\nBuild cancelled by user.")
        return 130  # Standard exit code for SIGINT
    except Exception as e:
        print(f"Error: {e}")
        if hasattr(args, "debug") and args.debug:
            import traceback

            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
