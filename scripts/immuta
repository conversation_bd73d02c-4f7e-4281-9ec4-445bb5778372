#!/usr/bin/env python3
"""
Immuta SRE Toolkit - Main command-line interface for managing Immuta instances.

This script provides a comprehensive toolkit for managing Immuta instances,
including user management, policy management, data source management, and
tagging. It supports daily BAU tasks for Immuta in a banking/data analytics
environment, emphasizing operational efficiency, automation, safety,
performance, and logging.
"""

import os
import sys

# Add the src directory to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "src"))
)

from immuta_toolkit.cli import main

if __name__ == "__main__":
    main()
