#!/bin/bash
# Script to install dependencies for the Immuta SRE Toolkit

set -e

echo "Installing dependencies for Immuta SRE Toolkit..."

# Check if PDM is installed
if ! command -v pdm &> /dev/null; then
    echo "PDM is not installed. Installing PDM..."
    pip install --user pdm
fi

# Install dependencies using PDM
echo "Installing project dependencies..."
pdm install

# Install development dependencies
echo "Installing development dependencies..."
pdm install -G dev

echo "Dependencies installed successfully!"
