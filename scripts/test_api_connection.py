#!/usr/bin/env python3
"""<PERSON><PERSON>t to test the connection to the Immuta API."""

import os
import sys
import argparse
import requests
from typing import Optional

def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Test connection to the Immuta API")
    parser.add_argument(
        "--api-key",
        help="Immuta API key",
        default=os.getenv("IMMUTA_API_KEY"),
    )
    parser.add_argument(
        "--base-url",
        help="Immuta base URL",
        default=os.getenv("IMMUTA_BASE_URL"),
    )
    return parser.parse_args()

def test_api_connection(base_url: str, api_key: Optional[str] = None) -> bool:
    """Test connection to the Immuta API.
    
    Args:
        base_url: Immuta base URL.
        api_key: Immuta API key.
        
    Returns:
        True if connection is successful, False otherwise.
    """
    # Ensure base URL doesn't end with a slash
    base_url = base_url.rstrip("/")
    
    # Set up headers
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    
    if api_key:
        headers["Authorization"] = f"Bearer {api_key}"
    
    # Test connection to health endpoint
    try:
        response = requests.get(
            f"{base_url}/health",
            headers=headers,
            timeout=10,
        )
        
        if response.status_code == 200:
            print(f"Successfully connected to Immuta API at {base_url}")
            print(f"Response: {response.json()}")
            return True
        else:
            print(f"Failed to connect to Immuta API at {base_url}")
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"Error connecting to Immuta API at {base_url}: {e}")
        return False

def main():
    """Main entry point."""
    args = parse_args()
    
    if not args.base_url:
        print("Error: Base URL is required")
        return 1
    
    success = test_api_connection(
        base_url=args.base_url,
        api_key=args.api_key,
    )
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
