#!/bin/bash
# Script to fix the PDM lock file for CI environments
# This script handles various Python versions and platform-specific issues

set -e

# Print script banner
echo "====================================="
echo "PDM Lock File Fixer (Shell Script)"
echo "====================================="

# Get Python version
PYTHON_VERSION=$(python -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Detected Python version: $PYTHON_VERSION"

# Create backup of existing lock file
if [ -f pdm.lock ]; then
    BACKUP_FILE="pdm.lock.$(date +%Y%m%d%H%M%S).bak"
    echo "Creating backup of existing lock file: $BACKUP_FILE"
    cp pdm.lock "$BACKUP_FILE"

    echo "Removing existing pdm.lock file"
    rm -f pdm.lock
fi

# Create __pypackages__ directory structure
# This is needed for PDM's PEP 582 mode
echo "Creating __pypackages__ directory structure"
mkdir -p "__pypackages__/$PYTHON_VERSION/lib"

# Configure PDM
echo "Configuring PDM"
pdm config python.use_venv false

# Update pyproject.toml to fix dependency issues
echo "Updating pyproject.toml for Python $PYTHON_VERSION..."
if [ -f pyproject.toml ]; then
    # Create backup of pyproject.toml
    PYPROJECT_BACKUP="pyproject.toml.$(date +%Y%m%d%H%M%S).bak"
    cp pyproject.toml "$PYPROJECT_BACKUP"
    echo "Created backup of pyproject.toml: $PYPROJECT_BACKUP"

    # Use Python to update the requires-python line
    python -c "
import re
with open('pyproject.toml', 'r') as f:
    content = f.read()
new_content = re.sub(r'requires-python\s*=\s*\"([^\"]+)\"',
                     f'requires-python = \">={PYTHON_VERSION},<3.14\"',
                     content)
with open('pyproject.toml', 'w') as f:
    f.write(new_content)
print(f'Updated requires-python to >={PYTHON_VERSION},<3.14')
"
fi

# Try different lock strategies
echo "Creating new lock file"

# Strategy 1: Standard lock
echo "Strategy 1: Standard lock"
if pdm lock --no-cross-platform; then
    echo "Lock file created successfully with strategy 1!"
    exit 0
fi

# Strategy 2: No isolation
echo "Strategy 2: No isolation"
if pdm lock --no-cross-platform --no-isolation; then
    echo "Lock file created successfully with strategy 2!"
    exit 0
fi

# Strategy 3: No self
echo "Strategy 3: No self"
if pdm lock --no-cross-platform --no-isolation --no-self; then
    echo "Lock file created successfully with strategy 3!"
    exit 0
fi

# Strategy 4: Fallback to Python script
echo "All shell strategies failed. Falling back to Python script."
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PYTHON_SCRIPT="${SCRIPT_DIR}/fix-lock.py"

if [ -f "$PYTHON_SCRIPT" ]; then
    echo "Running Python script: $PYTHON_SCRIPT"
    python "$PYTHON_SCRIPT"
    exit $?
else
    echo "Error: Python script not found at $PYTHON_SCRIPT"

    # Last resort: Create an empty lock file
    echo "Creating an empty lock file as last resort"
    echo "{}" > pdm.lock
    echo "Created empty lock file. You may need to install dependencies manually."
    exit 1
fi
