#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to fix the PDM lock file for CI environments.

This is a Python version of fix-lock.sh for better cross-platform compatibility.
It handles various Python versions and platform-specific issues.
"""

import os
import shutil
import subprocess
import sys
import platform
import json
import re
import time
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, Union


def get_python_version() -> str:
    """Get the current Python version.

    Returns:
        Python version string in the format "3.x".
    """
    major = sys.version_info.major
    minor = sys.version_info.minor
    return f"{major}.{minor}"


def backup_file(file_path: Path) -> Optional[Path]:
    """Create a backup of a file.

    Args:
        file_path: Path to the file to backup.

    Returns:
        Path to the backup file, or None if backup failed.
    """
    if not file_path.exists():
        return None

    timestamp = time.strftime("%Y%m%d%H%M%S")
    backup_path = file_path.with_suffix(f".{timestamp}.bak")

    try:
        shutil.copy2(file_path, backup_path)
        print(f"Created backup at {backup_path}")
        return backup_path
    except Exception as e:
        print(f"Warning: Failed to create backup: {e}")
        return None


def update_pyproject_toml(python_version: str) -> bool:
    """Update pyproject.toml to fix dependency issues.

    Args:
        python_version: Current Python version.

    Returns:
        True if the file was updated, False otherwise.
    """
    print(f"\nUpdating pyproject.toml for Python {python_version}...")
    pyproject_path = Path("pyproject.toml")

    if not pyproject_path.exists():
        print("Warning: pyproject.toml not found")
        return False

    # Create a backup
    backup_file(pyproject_path)

    try:
        content = pyproject_path.read_text(encoding="utf-8")

        # Find the current requires-python line
        requires_python_match = re.search(r'requires-python\s*=\s*"([^"]+)"', content)
        if not requires_python_match:
            print("Warning: requires-python not found in pyproject.toml")
            return False

        current_requires = requires_python_match.group(1)
        print(f"Current requires-python: {current_requires}")

        # Update to the current Python version
        new_requires = f">={python_version},<3.14"
        new_content = re.sub(
            r'requires-python\s*=\s*"([^"]+)"',
            f'requires-python = "{new_requires}"',
            content,
        )

        # Write the updated content
        pyproject_path.write_text(new_content, encoding="utf-8")
        print(f"Updated requires-python to {new_requires}")
        return True
    except Exception as e:
        print(f"Error updating pyproject.toml: {e}")
        return False


def fix_lock_file() -> bool:
    """Fix the PDM lock file.

    Returns:
        True if the lock file was fixed, False otherwise.
    """
    lock_path = Path("pdm.lock")

    # Backup the existing lock file if it exists
    if lock_path.exists():
        print("\nBacking up existing lock file...")
        backup_file(lock_path)

        print("\nRemoving existing lock file...")
        try:
            os.remove(lock_path)
        except Exception as e:
            print(f"Error removing lock file: {e}")
            return False

    # Create a new lock file
    print("\nCreating new lock file...")

    # Try different lock strategies
    strategies = [
        ["pdm", "lock", "--no-cross-platform"],
        ["pdm", "lock", "--no-cross-platform", "--no-isolation"],
        ["pdm", "lock", "--no-cross-platform", "--no-isolation", "--no-self"],
    ]

    for i, cmd in enumerate(strategies, 1):
        print(f"\nAttempt {i}/{len(strategies)}: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, check=False, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"Lock file created successfully with strategy {i}!")
                return True
            else:
                print(f"Strategy {i} failed with exit code {result.returncode}")
                if result.stderr:
                    print(f"Error output: {result.stderr.strip()}")
        except Exception as e:
            print(f"Error executing strategy {i}: {e}")

    # All strategies failed, create an empty lock file as fallback
    print("\nAll lock attempts failed.")
    print("Creating an empty lock file as fallback...")
    try:
        with open(lock_path, "w") as f:
            f.write("{}")
        print("Created empty lock file.")
        return False
    except Exception as e:
        print(f"Error creating empty lock file: {e}")
        return False


def main() -> int:
    """Main entry point.

    Returns:
        Exit code (0 for success, non-zero for failure).
    """
    try:
        print("=== PDM Lock File Fixer ===")

        # Get the current Python version
        python_version = get_python_version()
        print(f"Current Python version: {python_version}")

        # Create __pypackages__ directory structure
        print("\nCreating __pypackages__ directory if needed...")
        pypackages_dir = Path(f"__pypackages__/{python_version}/lib")
        pypackages_dir.mkdir(parents=True, exist_ok=True)

        # Configure PDM
        print("\nConfiguring PDM...")
        subprocess.run(["pdm", "config", "python.use_venv", "false"], check=True)

        # Update pyproject.toml
        update_pyproject_toml(python_version)

        # Fix the lock file
        if fix_lock_file():
            print("\nLock file fixed successfully!")
            print("You can now run 'pdm sync' to install dependencies.")
            return 0
        else:
            print("\nFailed to fix lock file completely.")
            print("You may need to install dependencies manually.")
            return 1
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return 130
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
