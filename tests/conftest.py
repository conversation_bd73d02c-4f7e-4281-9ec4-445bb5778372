"""Test fixtures for the Immuta SRE Toolkit."""

import os
import sys
from pathlib import Path
from unittest.mock import Magic<PERSON>ock

import pytest

# Add the src directory to the Python path if needed
src_dir = Path(__file__).parent.parent / "src"
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))
    print(f"Added {src_dir} to sys.path")

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))
    print(f"Added {project_root} to sys.path")

# Create mock classes for testing
class MockImmutaClient:
    def __init__(self, api_key="mock_key", base_url="https://example.com", is_local=True, notifier=None, storage=None):
        self.api_key = api_key
        self.base_url = base_url
        self.is_local = is_local
        self.notifier = notifier or MockNotificationManager()
        self.storage = storage or MockStorageManager()

    def make_request(self, method, endpoint, data=None, params=None, headers=None):
        """Make a request to the Immuta API."""
        class MockResponse:
            def json(self):
                return data or {}

        return MockResponse()

class MockNotificationManager:
    def send_notification(self, *args, **kwargs):
        pass

class MockStorageManager:
    def save(self, *args, **kwargs):
        pass

    def load(self, *args, **kwargs):
        pass

    def upload_file(self, *args, **kwargs):
        return "mock_blob"

    def download_file(self, *args, **kwargs):
        return {"mock": "data"}

# Use the mock classes instead of trying to import
ImmutaClient = MockImmutaClient
NotificationManager = MockNotificationManager
StorageManager = MockStorageManager

@pytest.fixture
def mock_client():
    """Return a mock Immuta client."""
    return MockImmutaClient()

@pytest.fixture
def mock_notifier():
    """Return a mock notification manager."""
    return MockNotificationManager()

@pytest.fixture
def mock_storage():
    """Return a mock storage manager."""
    return MockStorageManager()

@pytest.fixture
def mock_client(mock_notifier, mock_storage):
    """Mock Immuta client."""
    client = ImmutaClient(
        api_key="mock_key",
        base_url="https://example.com",
        is_local=True,
        notifier=mock_notifier,
        storage=mock_storage,
    )
    return client
