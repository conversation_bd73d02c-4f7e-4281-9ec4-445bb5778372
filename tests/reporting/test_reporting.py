"""Tests for the reporting system."""

import os
import json
import tempfile
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.reporting.report import Report, ReportEntry, ReportType, PerformanceMetrics
from immuta_toolkit.reporting.manager import ReportManager
from immuta_toolkit.operations.base import OperationResult


@pytest.fixture
def sample_report():
    """Create a sample report for testing."""
    report_id = str(uuid.uuid4())
    report = Report(
        id=report_id,
        type=ReportType.OPERATION,
        title="Test Report",
        description="A test report for unit tests",
    )
    
    # Add some entries
    for i in range(5):
        # Create performance metrics
        perf_metrics = PerformanceMetrics(
            execution_time=0.5 + i * 0.1,
            cpu_usage=10.0 + i * 2.0,
            memory_usage=100.0 + i * 10.0,
            api_calls=i + 1,
        )
        
        # Create entry
        entry = ReportEntry(
            operation_name=f"test_operation_{i}",
            operation_type="test",
            status="success" if i % 3 != 0 else "failure",
            timestamp=datetime.now().timestamp(),
            execution_time=0.5 + i * 0.1,
            api_used=i % 2 == 0,
            web_used=i % 2 != 0,
            attempts=1,
            error="Test error" if i % 3 == 0 else None,
            error_type="TestError" if i % 3 == 0 else None,
            performance_metrics=perf_metrics,
            tags=["test", f"tag_{i}"],
        )
        
        report.add_entry(entry)
    
    return report


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


class TestReport:
    """Tests for the Report class."""
    
    def test_create_report(self):
        """Test creating a report."""
        report_id = str(uuid.uuid4())
        report = Report(
            id=report_id,
            type=ReportType.OPERATION,
            title="Test Report",
        )
        
        assert report.id == report_id
        assert report.type == ReportType.OPERATION
        assert report.title == "Test Report"
        assert len(report.entries) == 0
    
    def test_add_entry(self):
        """Test adding an entry to a report."""
        report = Report(
            id=str(uuid.uuid4()),
            type=ReportType.OPERATION,
            title="Test Report",
        )
        
        entry = ReportEntry(
            operation_name="test_operation",
            status="success",
            timestamp=datetime.now().timestamp(),
            execution_time=0.5,
            api_used=True,
            web_used=False,
            attempts=1,
        )
        
        report.add_entry(entry)
        
        assert len(report.entries) == 1
        assert report.entries[0].operation_name == "test_operation"
    
    def test_add_operation_result(self):
        """Test adding an operation result to a report."""
        report = Report(
            id=str(uuid.uuid4()),
            type=ReportType.OPERATION,
            title="Test Report",
        )
        
        result = OperationResult(
            status="success",
            timestamp=datetime.now().timestamp(),
            execution_time=0.5,
            api_used=True,
            web_used=False,
            attempts=1,
            data={"test": "data"},
        )
        
        report.add_operation_result("test_operation", result)
        
        assert len(report.entries) == 1
        assert report.entries[0].operation_name == "test_operation"
        assert report.entries[0].status == "success"
    
    def test_get_success_count(self, sample_report):
        """Test getting the success count."""
        success_count = sample_report.get_success_count()
        
        # Count success entries manually
        expected_count = sum(1 for entry in sample_report.entries if entry.status == "success")
        
        assert success_count == expected_count
    
    def test_get_failure_count(self, sample_report):
        """Test getting the failure count."""
        failure_count = sample_report.get_failure_count()
        
        # Count failure entries manually
        expected_count = sum(1 for entry in sample_report.entries if entry.status == "failure")
        
        assert failure_count == expected_count
    
    def test_get_total_execution_time(self, sample_report):
        """Test getting the total execution time."""
        total_time = sample_report.get_total_execution_time()
        
        # Calculate total time manually
        expected_time = sum(entry.execution_time for entry in sample_report.entries)
        
        assert total_time == pytest.approx(expected_time)
    
    def test_get_average_execution_time(self, sample_report):
        """Test getting the average execution time."""
        avg_time = sample_report.get_average_execution_time()
        
        # Calculate average time manually
        total_time = sum(entry.execution_time for entry in sample_report.entries)
        expected_avg = total_time / len(sample_report.entries)
        
        assert avg_time == pytest.approx(expected_avg)
    
    def test_get_api_usage_percentage(self, sample_report):
        """Test getting the API usage percentage."""
        api_percentage = sample_report.get_api_usage_percentage()
        
        # Calculate API usage percentage manually
        api_count = sum(1 for entry in sample_report.entries if entry.api_used)
        expected_percentage = (api_count / len(sample_report.entries)) * 100.0
        
        assert api_percentage == pytest.approx(expected_percentage)
    
    def test_get_web_usage_percentage(self, sample_report):
        """Test getting the web usage percentage."""
        web_percentage = sample_report.get_web_usage_percentage()
        
        # Calculate web usage percentage manually
        web_count = sum(1 for entry in sample_report.entries if entry.web_used)
        expected_percentage = (web_count / len(sample_report.entries)) * 100.0
        
        assert web_percentage == pytest.approx(expected_percentage)
    
    def test_get_success_percentage(self, sample_report):
        """Test getting the success percentage."""
        success_percentage = sample_report.get_success_percentage()
        
        # Calculate success percentage manually
        success_count = sum(1 for entry in sample_report.entries if entry.status == "success")
        expected_percentage = (success_count / len(sample_report.entries)) * 100.0
        
        assert success_percentage == pytest.approx(expected_percentage)
    
    def test_get_operation_types(self, sample_report):
        """Test getting the operation types."""
        operation_types = sample_report.get_operation_types()
        
        # Get unique operation types manually
        expected_types = set(entry.operation_type for entry in sample_report.entries if entry.operation_type)
        
        assert set(operation_types) == expected_types
    
    def test_get_entries_by_operation_type(self, sample_report):
        """Test getting entries by operation type."""
        entries = sample_report.get_entries_by_operation_type("test")
        
        # Get entries with operation type "test" manually
        expected_entries = [entry for entry in sample_report.entries if entry.operation_type == "test"]
        
        assert len(entries) == len(expected_entries)
    
    def test_get_entries_by_tag(self, sample_report):
        """Test getting entries by tag."""
        entries = sample_report.get_entries_by_tag("test")
        
        # Get entries with tag "test" manually
        expected_entries = [entry for entry in sample_report.entries if "test" in entry.tags]
        
        assert len(entries) == len(expected_entries)
    
    def test_get_entries_by_status(self, sample_report):
        """Test getting entries by status."""
        entries = sample_report.get_entries_by_status("success")
        
        # Get entries with status "success" manually
        expected_entries = [entry for entry in sample_report.entries if entry.status == "success"]
        
        assert len(entries) == len(expected_entries)
    
    def test_get_error_types(self, sample_report):
        """Test getting the error types."""
        error_types = sample_report.get_error_types()
        
        # Count error types manually
        expected_types = {}
        for entry in sample_report.entries:
            if entry.error_type:
                expected_types[entry.error_type] = expected_types.get(entry.error_type, 0) + 1
        
        assert error_types == expected_types
    
    def test_get_performance_summary(self, sample_report):
        """Test getting the performance summary."""
        perf_summary = sample_report.get_performance_summary()
        
        # Check that the summary contains the expected keys
        assert "execution_time" in perf_summary
        assert "cpu_usage" in perf_summary
        assert "memory_usage" in perf_summary
        assert "api_calls" in perf_summary
        
        # Check execution time metrics
        assert "total" in perf_summary["execution_time"]
        assert "average" in perf_summary["execution_time"]
        assert "min" in perf_summary["execution_time"]
        assert "max" in perf_summary["execution_time"]
    
    def test_get_summary(self, sample_report):
        """Test getting the report summary."""
        summary = sample_report.get_summary()
        
        # Check that the summary contains the expected keys
        assert "id" in summary
        assert "type" in summary
        assert "title" in summary
        assert "created_at" in summary
        assert "updated_at" in summary
        assert "total_operations" in summary
        assert "successful_operations" in summary
        assert "failed_operations" in summary
        assert "success_percentage" in summary
        assert "total_execution_time" in summary
        assert "average_execution_time" in summary
        assert "api_usage_percentage" in summary
        assert "web_usage_percentage" in summary
        assert "operation_types" in summary
        assert "error_types" in summary
        assert "performance" in summary
