"""Tests for the report scheduler."""

import os
import json
import tempfile
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.reporting.report import Report, ReportType
from immuta_toolkit.reporting.manager import ReportManager
from immuta_toolkit.reporting.scheduler import ReportScheduler, ReportSchedule, ScheduleInterval
from immuta_toolkit.reporting.delivery import EmailDelivery


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def mock_report_manager():
    """Create a mock report manager."""
    manager = MagicMock(spec=ReportManager)
    
    # Mock create_report method
    report = MagicMock(spec=Report)
    report.id = str(uuid.uuid4())
    report.type = ReportType.OPERATION
    report.title = "Test Report"
    manager.create_report.return_value = report
    
    # Mock save_report method
    manager.save_report.return_value = "/path/to/report.json"
    
    return manager


@pytest.fixture
def mock_email_delivery():
    """Create a mock email delivery."""
    delivery = MagicMock(spec=EmailDelivery)
    
    # Mock send_report method
    delivery.send_report.return_value = True
    
    return delivery


class TestReportSchedule:
    """Tests for the ReportSchedule class."""
    
    def test_create_schedule(self):
        """Test creating a schedule."""
        schedule_id = str(uuid.uuid4())
        schedule = ReportSchedule(
            id=schedule_id,
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        assert schedule.id == schedule_id
        assert schedule.name == "Test Schedule"
        assert schedule.report_type == ReportType.OPERATION
        assert schedule.interval == ScheduleInterval.DAILY
        assert schedule.recipients == ["<EMAIL>"]
        assert schedule.enabled is True
    
    def test_to_dict(self):
        """Test converting a schedule to a dictionary."""
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        schedule_dict = schedule.to_dict()
        
        assert schedule_dict["id"] == schedule.id
        assert schedule_dict["name"] == schedule.name
        assert schedule_dict["report_type"] == schedule.report_type
        assert schedule_dict["interval"] == schedule.interval
        assert schedule_dict["recipients"] == schedule.recipients
    
    def test_from_dict(self):
        """Test creating a schedule from a dictionary."""
        schedule_id = str(uuid.uuid4())
        schedule_dict = {
            "id": schedule_id,
            "name": "Test Schedule",
            "report_type": ReportType.OPERATION,
            "interval": ScheduleInterval.DAILY,
            "enabled": True,
            "recipients": ["<EMAIL>"],
        }
        
        schedule = ReportSchedule.from_dict(schedule_dict)
        
        assert schedule.id == schedule_id
        assert schedule.name == "Test Schedule"
        assert schedule.report_type == ReportType.OPERATION
        assert schedule.interval == ScheduleInterval.DAILY
        assert schedule.recipients == ["<EMAIL>"]
        assert schedule.enabled is True


class TestReportScheduler:
    """Tests for the ReportScheduler class."""
    
    def test_init(self, mock_report_manager, temp_dir):
        """Test initializing the scheduler."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        assert scheduler.report_manager == mock_report_manager
        assert scheduler.storage_dir == temp_dir
        assert scheduler.schedules == {}
    
    def test_add_schedule(self, mock_report_manager, temp_dir):
        """Test adding a schedule."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        result = scheduler.add_schedule(schedule)
        
        assert result is True
        assert schedule.id in scheduler.schedules
        assert os.path.exists(os.path.join(temp_dir, f"{schedule.id}.json"))
    
    def test_get_schedule(self, mock_report_manager, temp_dir):
        """Test getting a schedule."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        scheduler.add_schedule(schedule)
        
        result = scheduler.get_schedule(schedule.id)
        
        assert result is not None
        assert result.id == schedule.id
        assert result.name == schedule.name
    
    def test_update_schedule(self, mock_report_manager, temp_dir):
        """Test updating a schedule."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        scheduler.add_schedule(schedule)
        
        # Update the schedule
        schedule.name = "Updated Schedule"
        schedule.interval = ScheduleInterval.WEEKLY
        
        result = scheduler.update_schedule(schedule)
        
        assert result is True
        assert scheduler.schedules[schedule.id].name == "Updated Schedule"
        assert scheduler.schedules[schedule.id].interval == ScheduleInterval.WEEKLY
    
    def test_delete_schedule(self, mock_report_manager, temp_dir):
        """Test deleting a schedule."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        scheduler.add_schedule(schedule)
        
        result = scheduler.delete_schedule(schedule.id)
        
        assert result is True
        assert schedule.id not in scheduler.schedules
        assert not os.path.exists(os.path.join(temp_dir, f"{schedule.id}.json"))
    
    def test_list_schedules(self, mock_report_manager, temp_dir):
        """Test listing schedules."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        schedule1 = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule 1",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        schedule2 = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule 2",
            report_type=ReportType.SUMMARY,
            interval=ScheduleInterval.WEEKLY,
            recipients=["<EMAIL>"],
        )
        
        scheduler.add_schedule(schedule1)
        scheduler.add_schedule(schedule2)
        
        schedules = scheduler.list_schedules()
        
        assert len(schedules) == 2
        assert any(s.id == schedule1.id for s in schedules)
        assert any(s.id == schedule2.id for s in schedules)
    
    @patch("immuta_toolkit.reporting.scheduler.threading.Thread")
    def test_start_stop(self, mock_thread, mock_report_manager, temp_dir):
        """Test starting and stopping the scheduler."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            storage_dir=temp_dir,
        )
        
        # Start the scheduler
        scheduler.start()
        
        assert scheduler.running is True
        mock_thread.assert_called_once()
        
        # Stop the scheduler
        scheduler.stop()
        
        assert scheduler.running is False
    
    def test_run_now(self, mock_report_manager, mock_email_delivery, temp_dir):
        """Test running a schedule immediately."""
        scheduler = ReportScheduler(
            report_manager=mock_report_manager,
            email_delivery=mock_email_delivery,
            storage_dir=temp_dir,
        )
        
        schedule = ReportSchedule(
            id=str(uuid.uuid4()),
            name="Test Schedule",
            report_type=ReportType.OPERATION,
            interval=ScheduleInterval.DAILY,
            recipients=["<EMAIL>"],
        )
        
        scheduler.add_schedule(schedule)
        
        # Mock the _run_report method
        scheduler._run_report = MagicMock()
        
        result = scheduler.run_now(schedule.id)
        
        assert result is True
        scheduler._run_report.assert_called_once_with(schedule.id)
