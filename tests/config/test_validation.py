"""Tests for the configuration validation."""

import pytest

from immuta_toolkit.config.validation import (
    ConfigValidator,
    ValidationResult,
    ValidationLevel,
)


class TestConfigValidator:
    """Tests for the ConfigValidator class."""

    def test_init(self):
        """Test initializing the configuration validator."""
        validator = ConfigValidator()

        assert validator.rules is not None
        assert len(validator.rules) > 0  # Default rules should be added

    def test_add_rule(self):
        """Test adding a validation rule."""
        validator = ConfigValidator()

        # Add a rule
        def test_rule(value):
            if value == "invalid":
                return ValidationResult(
                    key="test.key",
                    level=ValidationLevel.ERROR,
                    message="Invalid value",
                    value=value,
                )
            return None

        validator.add_rule("test.key", test_rule)

        assert "test.key" in validator.rules
        assert len(validator.rules["test.key"]) == 1

    def test_validate(self):
        """Test validating a configuration."""
        # Create a custom validator with no default rules
        validator = ConfigValidator(init_default_rules=False)

        # Add a rule
        def test_rule(value):
            if value == "invalid":
                return ValidationResult(
                    key="test.key",
                    level=ValidationLevel.ERROR,
                    message="Invalid value",
                    value=value,
                )
            return None

        validator.add_rule("test.key", test_rule)

        # Validate valid configuration
        config = {"test": {"key": "valid"}}
        results = validator.validate(config)

        assert len(results) == 0

        # Validate invalid configuration
        config = {"test": {"key": "invalid"}}
        results = validator.validate(config)

        assert len(results) == 1
        assert results[0].key == "test.key"
        assert results[0].level == ValidationLevel.ERROR
        assert results[0].message == "Invalid value"
        assert results[0].value == "invalid"

    def test_validate_key(self):
        """Test validating a specific configuration key."""
        validator = ConfigValidator(init_default_rules=False)

        # Add a rule
        def test_rule(value):
            if value == "invalid":
                return ValidationResult(
                    key="test.key",
                    level=ValidationLevel.ERROR,
                    message="Invalid value",
                    value=value,
                )
            return None

        validator.add_rule("test.key", test_rule)

        # Validate valid configuration
        config = {"test": {"key": "valid"}}
        results = validator.validate_key(config, "test.key")

        assert len(results) == 0

        # Validate invalid configuration
        config = {"test": {"key": "invalid"}}
        results = validator.validate_key(config, "test.key")

        assert len(results) == 1
        assert results[0].key == "test.key"
        assert results[0].level == ValidationLevel.ERROR
        assert results[0].message == "Invalid value"
        assert results[0].value == "invalid"

    def test_validate_url(self):
        """Test validating a URL."""
        validator = ConfigValidator()

        # Validate valid URL
        config = {"api": {"base_url": "https://example.com"}}
        results = validator.validate(config)

        # No errors for valid URL
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

        # Validate invalid URL
        config = {"api": {"base_url": "not a url"}}
        results = validator.validate(config)

        # Should have an error for invalid URL
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        assert any(r.key == "api.base_url" for r in errors)

    def test_validate_positive_integer(self):
        """Test validating a positive integer."""
        validator = ConfigValidator()

        # Validate valid positive integer
        config = {"api": {"timeout": 30}}
        results = validator.validate(config)

        # No errors for valid positive integer
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

        # Validate invalid positive integer
        config = {"api": {"timeout": -10}}
        results = validator.validate(config)

        # Should have an error for invalid positive integer
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        assert any(r.key == "api.timeout" for r in errors)

    def test_validate_log_level(self):
        """Test validating a logging level."""
        validator = ConfigValidator()

        # Validate valid logging level
        config = {"logging": {"level": "INFO"}}
        results = validator.validate(config)

        # No errors for valid logging level
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

        # Validate invalid logging level
        config = {"logging": {"level": "INVALID"}}
        results = validator.validate(config)

        # Should have an error for invalid logging level
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        assert any(r.key == "logging.level" for r in errors)

    def test_validate_port(self):
        """Test validating a port number."""
        validator = ConfigValidator()

        # Validate valid port
        config = {"reporting": {"email": {"smtp_port": 587}}}
        results = validator.validate(config)

        # No errors for valid port
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

        # Validate invalid port (too low)
        config = {"reporting": {"email": {"smtp_port": 0}}}
        results = validator.validate(config)

        # Should have an error for invalid port
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        assert any(r.key == "reporting.email.smtp_port" for r in errors)

        # Validate invalid port (too high)
        config = {"reporting": {"email": {"smtp_port": 70000}}}
        results = validator.validate(config)

        # Should have an error for invalid port
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) > 0
        assert any(r.key == "reporting.email.smtp_port" for r in errors)
