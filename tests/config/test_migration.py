"""Tests for the configuration migration."""

import os
import json
import tempfile
from pathlib import Path

import pytest

from immuta_toolkit.config.migration import (
    ConfigMigration,
    MigrationResult,
)
from immuta_toolkit.config.manager import ConfigFormat


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def config_file(temp_dir):
    """Create a temporary configuration file for testing."""
    config_path = os.path.join(temp_dir, "config.json")
    config = {
        "environment": "dev",
        "api": {
            "base_url": "https://example.com",
            "api_key": "test_key",
            "timeout": 30,
        },
        "web": {
            "base_url": "https://example.com",
            "username": "test_user",
            "password": "test_password",
        },
    }
    
    with open(config_path, "w") as f:
        json.dump(config, f)
        
    return config_path


class TestMigrationResult:
    """Tests for the MigrationResult class."""
    
    def test_init(self):
        """Test initializing a migration result."""
        result = MigrationResult()
        
        assert result.success is True
        assert result.changes == []
        assert result.errors == []
        assert result.warnings == []
    
    def test_add_change(self):
        """Test adding a change message."""
        result = MigrationResult()
        
        result.add_change("Test change")
        
        assert len(result.changes) == 1
        assert result.changes[0] == "Test change"
        assert result.success is True
    
    def test_add_error(self):
        """Test adding an error message."""
        result = MigrationResult()
        
        result.add_error("Test error")
        
        assert len(result.errors) == 1
        assert result.errors[0] == "Test error"
        assert result.success is False
    
    def test_add_warning(self):
        """Test adding a warning message."""
        result = MigrationResult()
        
        result.add_warning("Test warning")
        
        assert len(result.warnings) == 1
        assert result.warnings[0] == "Test warning"
        assert result.success is True
    
    def test_str(self):
        """Test string representation."""
        result = MigrationResult()
        
        result.add_change("Test change")
        result.add_error("Test error")
        result.add_warning("Test warning")
        
        str_result = str(result)
        
        assert "MigrationResult" in str_result
        assert "success=False" in str_result
        assert "changes=1" in str_result
        assert "errors=1" in str_result
        assert "warnings=1" in str_result


class TestConfigMigration:
    """Tests for the ConfigMigration class."""
    
    def test_init(self):
        """Test initializing a configuration migration."""
        migration = ConfigMigration()
        
        assert migration.migrations is not None
        assert len(migration.migrations) > 0  # Default migrations should be added
    
    def test_add_migration(self):
        """Test adding a migration function."""
        migration = ConfigMigration()
        
        # Add a migration function
        def test_migration(config):
            config["test"] = "migrated"
            return config
            
        migration.add_migration("test", test_migration)
        
        assert "test" in migration.migrations
        assert migration.migrations["test"] == test_migration
    
    def test_migrate(self):
        """Test migrating a configuration."""
        migration = ConfigMigration()
        
        # Add a migration function
        def test_migration(config):
            config["test"] = "migrated"
            return config
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration
        config = {"version": "0.0.0"}
        result = migration.migrate(config, "test")
        
        assert result.success is True
        assert len(result.changes) == 1
        assert config["test"] == "migrated"
        assert config["version"] == "test"
    
    def test_migrate_already_at_version(self):
        """Test migrating a configuration that is already at the target version."""
        migration = ConfigMigration()
        
        # Add a migration function
        def test_migration(config):
            config["test"] = "migrated"
            return config
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration
        config = {"version": "test"}
        result = migration.migrate(config, "test")
        
        assert result.success is True
        assert len(result.warnings) == 1
        assert "test" not in config  # Should not be modified
    
    def test_migrate_unsupported_version(self):
        """Test migrating a configuration to an unsupported version."""
        migration = ConfigMigration()
        
        # Migrate configuration
        config = {"version": "0.0.0"}
        result = migration.migrate(config, "unsupported")
        
        assert result.success is False
        assert len(result.errors) == 1
    
    def test_migrate_error(self):
        """Test migrating a configuration with an error."""
        migration = ConfigMigration()
        
        # Add a migration function that raises an exception
        def test_migration(config):
            raise ValueError("Test error")
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration
        config = {"version": "0.0.0"}
        result = migration.migrate(config, "test")
        
        assert result.success is False
        assert len(result.errors) == 1
    
    def test_migrate_file(self, config_file):
        """Test migrating a configuration file."""
        migration = ConfigMigration()
        
        # Add a migration function
        def test_migration(config):
            config["test"] = "migrated"
            return config
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration file
        result = migration.migrate_file(config_file, "test", ConfigFormat.JSON)
        
        assert result.success is True
        assert len(result.changes) >= 1
        
        # Check if backup was created
        backup_path = f"{config_file}.bak"
        assert os.path.exists(backup_path)
        
        # Check if file was updated
        with open(config_file, "r") as f:
            config = json.load(f)
            
        assert config["test"] == "migrated"
        assert config["version"] == "test"
    
    def test_migrate_file_no_backup(self, config_file):
        """Test migrating a configuration file without creating a backup."""
        migration = ConfigMigration()
        
        # Add a migration function
        def test_migration(config):
            config["test"] = "migrated"
            return config
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration file
        result = migration.migrate_file(config_file, "test", ConfigFormat.JSON, backup=False)
        
        assert result.success is True
        assert len(result.changes) >= 1
        
        # Check if backup was not created
        backup_path = f"{config_file}.bak"
        assert not os.path.exists(backup_path)
        
        # Check if file was updated
        with open(config_file, "r") as f:
            config = json.load(f)
            
        assert config["test"] == "migrated"
        assert config["version"] == "test"
    
    def test_migrate_file_error(self, config_file):
        """Test migrating a configuration file with an error."""
        migration = ConfigMigration()
        
        # Add a migration function that raises an exception
        def test_migration(config):
            raise ValueError("Test error")
            
        migration.add_migration("test", test_migration)
        
        # Migrate configuration file
        result = migration.migrate_file(config_file, "test", ConfigFormat.JSON)
        
        assert result.success is False
        assert len(result.errors) >= 1
        
        # Check if backup was created
        backup_path = f"{config_file}.bak"
        assert os.path.exists(backup_path)
    
    def test_migrate_to_1_0_0(self):
        """Test migrating a configuration to version 1.0.0."""
        migration = ConfigMigration()
        
        # Create a configuration
        config = {
            "environment": "dev",
            "api": {
                "base_url": "https://example.com",
                "api_key": "test_key",
                "timeout": 30,
            },
            "web": {
                "base_url": "https://example.com",
                "username": "test_user",
                "password": "test_password",
            },
        }
        
        # Migrate configuration
        result = migration.migrate(config, "1.0.0")
        
        assert result.success is True
        assert len(result.changes) == 1
        assert config["version"] == "1.0.0"
        assert "api" in config
        assert "web" in config
        assert "secrets" in config
        assert "storage" in config
        assert "logging" in config
        assert "operation" in config
        assert "features" in config
        assert "profiles" in config
        assert "custom" in config
