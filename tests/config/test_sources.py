"""Tests for the configuration sources."""

import os
import json
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
import yaml
import toml

from immuta_toolkit.config.sources import (
    FileConfigSource,
    EnvConfigSource,
    SecretsConfigSource,
)
from immuta_toolkit.config.manager import ConfigFormat


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def json_config_file(temp_dir):
    """Create a temporary JSON configuration file for testing."""
    config_path = os.path.join(temp_dir, "config.json")
    config = {
        "name": "test_config",
        "value": 123,
        "nested": {
            "key1": "value1",
            "key2": "value2",
        },
    }

    with open(config_path, "w") as f:
        json.dump(config, f)

    return config_path


@pytest.fixture
def yaml_config_file(temp_dir):
    """Create a temporary YAML configuration file for testing."""
    config_path = os.path.join(temp_dir, "config.yaml")
    config = {
        "name": "test_config",
        "value": 123,
        "nested": {
            "key1": "value1",
            "key2": "value2",
        },
    }

    with open(config_path, "w") as f:
        yaml.dump(config, f)

    return config_path


@pytest.fixture
def toml_config_file(temp_dir):
    """Create a temporary TOML configuration file for testing."""
    config_path = os.path.join(temp_dir, "config.toml")
    config = {
        "name": "test_config",
        "value": 123,
        "nested": {
            "key1": "value1",
            "key2": "value2",
        },
    }

    with open(config_path, "w") as f:
        toml.dump(config, f)

    return config_path


class TestFileConfigSource:
    """Tests for the FileConfigSource class."""

    def test_init(self):
        """Test initializing a file configuration source."""
        source = FileConfigSource("config.json", ConfigFormat.JSON)

        assert source.path == Path("config.json")
        assert source.format == ConfigFormat.JSON
        assert source.required is False

    def test_load_json(self, json_config_file):
        """Test loading configuration from a JSON file."""
        source = FileConfigSource(json_config_file, ConfigFormat.JSON)

        config = source.load()

        assert config["name"] == "test_config"
        assert config["value"] == 123
        assert config["nested"]["key1"] == "value1"
        assert config["nested"]["key2"] == "value2"

    def test_load_yaml(self, yaml_config_file):
        """Test loading configuration from a YAML file."""
        source = FileConfigSource(yaml_config_file, ConfigFormat.YAML)

        config = source.load()

        assert config["name"] == "test_config"
        assert config["value"] == 123
        assert config["nested"]["key1"] == "value1"
        assert config["nested"]["key2"] == "value2"

    def test_load_toml(self, toml_config_file):
        """Test loading configuration from a TOML file."""
        source = FileConfigSource(toml_config_file, ConfigFormat.TOML)

        config = source.load()

        assert config["name"] == "test_config"
        assert config["value"] == 123
        assert config["nested"]["key1"] == "value1"
        assert config["nested"]["key2"] == "value2"

    def test_load_nonexistent_file(self):
        """Test loading configuration from a nonexistent file."""
        source = FileConfigSource("nonexistent.json", ConfigFormat.JSON)

        config = source.load()

        assert config == {}

    def test_load_nonexistent_file_required(self):
        """Test loading configuration from a nonexistent file when required."""
        source = FileConfigSource("nonexistent.json", ConfigFormat.JSON, required=True)

        with pytest.raises(FileNotFoundError):
            source.load()

    def test_load_unsupported_format(self, json_config_file):
        """Test loading configuration with an unsupported format."""
        source = FileConfigSource(json_config_file, "unsupported")

        with pytest.raises(ValueError):
            source.load()

    def test_str(self):
        """Test string representation."""
        source = FileConfigSource("config.json", ConfigFormat.JSON)

        str_source = str(source)

        assert "FileConfigSource" in str_source
        assert "config.json" in str_source
        assert "JSON" in str_source


class TestEnvConfigSource:
    """Tests for the EnvConfigSource class."""

    def test_init(self):
        """Test initializing an environment variable configuration source."""
        source = EnvConfigSource()

        assert source.prefix == "IMMUTA_"
        assert source.lowercase is True

    def test_load(self, monkeypatch):
        """Test loading configuration from environment variables."""
        # Set environment variables
        monkeypatch.setenv("TEST_NAME", "env_test")
        monkeypatch.setenv("TEST_VALUE", "456")
        monkeypatch.setenv("TEST_NESTED_KEY3", "value3")
        monkeypatch.setenv("TEST_BOOL_TRUE", "true")
        monkeypatch.setenv("TEST_BOOL_FALSE", "false")
        monkeypatch.setenv("TEST_INT", "789")
        monkeypatch.setenv("TEST_FLOAT", "3.14")

        # Create source
        source = EnvConfigSource(prefix="TEST_")

        # Load configuration
        config = source.load()

        assert config["name"] == "env_test"
        assert config["value"] == 456
        assert config["nested"]["key3"] == "value3"
        assert config["bool"]["true"] is True
        assert config["bool"]["false"] is False
        assert config["int"] == 789
        assert config["float"] == 3.14

    def test_load_no_prefix(self, monkeypatch):
        """Test loading configuration from environment variables with no prefix."""
        # Set environment variables
        monkeypatch.setenv("TEST_NAME", "env_test")

        # Create source with a different prefix
        source = EnvConfigSource(prefix="DIFFERENT_")

        # Load configuration
        config = source.load()

        assert config == {}

    def test_load_no_lowercase(self, monkeypatch):
        """Test loading configuration from environment variables without lowercase conversion."""
        # Set environment variables
        monkeypatch.setenv("TEST_NAME", "env_test")

        # Create source with lowercase=False
        source = EnvConfigSource(prefix="TEST_", lowercase=False)

        # Load configuration
        config = source.load()

        assert config["NAME"] == "env_test"

    def test_str(self):
        """Test string representation."""
        source = EnvConfigSource(prefix="TEST_")

        str_source = str(source)

        assert "EnvConfigSource" in str_source
        assert "TEST_" in str_source


class TestSecretsConfigSource:
    """Tests for the SecretsConfigSource class."""

    def test_init_unsupported_provider(self):
        """Test initializing a secrets manager configuration source with an unsupported provider."""
        with pytest.raises(ValueError):
            SecretsConfigSource("unsupported")

    @patch("immuta_toolkit.config.secrets.AzureKeyVaultSecretsProvider")
    def test_init_azure(self, mock_azure_provider):
        """Test initializing an Azure Key Vault configuration source."""
        # Set up mock
        mock_instance = MagicMock()
        mock_azure_provider.return_value = mock_instance

        # Create source
        source = SecretsConfigSource("azure", vault_url="https://example.vault.azure.net/")

        # Verify the provider was created with the correct arguments
        mock_azure_provider.assert_called_once_with(vault_url="https://example.vault.azure.net/")

    @patch("immuta_toolkit.config.secrets.AWSSecretsManagerProvider")
    def test_init_aws(self, mock_aws_provider):
        """Test initializing an AWS Secrets Manager configuration source."""
        # Set up mock
        mock_instance = MagicMock()
        mock_aws_provider.return_value = mock_instance

        # Create source
        source = SecretsConfigSource("aws", region="us-east-1")

        # Verify the provider was created with the correct arguments
        mock_aws_provider.assert_called_once_with(region="us-east-1")

    @patch("immuta_toolkit.config.secrets.HashiCorpVaultProvider")
    def test_init_hashicorp(self, mock_hashicorp_provider):
        """Test initializing a HashiCorp Vault configuration source."""
        # Set up mock
        mock_instance = MagicMock()
        mock_hashicorp_provider.return_value = mock_instance

        # Create source
        source = SecretsConfigSource("hashicorp", vault_url="https://vault.example.com/")

        # Verify the provider was created with the correct arguments
        mock_hashicorp_provider.assert_called_once_with(vault_url="https://vault.example.com/")

    def test_load_azure(self):
        """Test loading configuration from Azure Key Vault."""
        # Mock the secrets provider
        mock_provider = MagicMock()
        mock_provider.list_secrets.return_value = ["key1", "key2"]
        mock_provider.get_secret.side_effect = lambda key: "value" + key[-1]

        # Mock create_secrets_provider
        with patch("immuta_toolkit.config.secrets.create_secrets_provider", return_value=mock_provider):
            source = SecretsConfigSource("azure", vault_url="https://example.vault.azure.net/")

            # Load configuration
            config = source.load()

            # Verify the provider methods were called
            mock_provider.list_secrets.assert_called_once_with("")
            assert mock_provider.get_secret.call_count == 2
            assert config == {"key1": "value1", "key2": "value2"}

    def test_load_aws(self):
        """Test loading configuration from AWS Secrets Manager."""
        # Mock the secrets provider
        mock_provider = MagicMock()
        mock_provider.list_secrets.return_value = ["key1", "key2"]
        mock_provider.get_secret.side_effect = lambda key: "value" + key[-1]

        # Mock create_secrets_provider
        with patch("immuta_toolkit.config.secrets.create_secrets_provider", return_value=mock_provider):
            source = SecretsConfigSource("aws", region="us-east-1")

            # Load configuration
            config = source.load()

            # Verify the provider methods were called
            mock_provider.list_secrets.assert_called_once_with("")
            assert mock_provider.get_secret.call_count == 2
            assert config == {"key1": "value1", "key2": "value2"}

    def test_load_hashicorp(self):
        """Test loading configuration from HashiCorp Vault."""
        # Mock the secrets provider
        mock_provider = MagicMock()
        mock_provider.list_secrets.return_value = ["key1", "key2"]
        mock_provider.get_secret.side_effect = lambda key: "value" + key[-1]

        # Mock create_secrets_provider
        with patch("immuta_toolkit.config.secrets.create_secrets_provider", return_value=mock_provider):
            source = SecretsConfigSource("hashicorp", vault_url="https://vault.example.com/")

            # Load configuration
            config = source.load()

            # Verify the provider methods were called
            mock_provider.list_secrets.assert_called_once_with("")
            assert mock_provider.get_secret.call_count == 2
            assert config == {"key1": "value1", "key2": "value2"}

    def test_convert_value(self):
        """Test converting values to appropriate types."""
        # Mock the secrets provider
        mock_provider = MagicMock()

        # Mock create_secrets_provider
        with patch("immuta_toolkit.config.secrets.create_secrets_provider", return_value=mock_provider):
            source = SecretsConfigSource("azure", vault_url="https://example.vault.azure.net/")

            # Test boolean conversion
            assert source._convert_value("true") is True
            assert source._convert_value("yes") is True
            assert source._convert_value("1") == 1  # This is converted to an integer, not a boolean
            assert source._convert_value("false") is False
            assert source._convert_value("no") is False
            assert source._convert_value("0") == 0  # This is converted to an integer, not a boolean

            # Test integer conversion
            assert source._convert_value("123") == 123

            # Test float conversion
            assert source._convert_value("3.14") == 3.14

            # Test string (no conversion)
            assert source._convert_value("string") == "string"

    def test_str(self):
        """Test string representation."""
        # Mock the secrets provider
        mock_provider = MagicMock()

        # Mock create_secrets_provider
        with patch("immuta_toolkit.config.secrets.create_secrets_provider", return_value=mock_provider):
            source = SecretsConfigSource("azure", vault_url="https://example.vault.azure.net/")

            str_source = str(source)

            assert "SecretsConfigSource" in str_source
            assert "azure" in str_source
