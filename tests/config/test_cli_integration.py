"""Tests for CLI integration with configuration management."""

import os
import pytest
from unittest.mock import patch, MagicMock

from immuta_toolkit.config import (
    ConfigManager,
    ConfigFormat,
    ConfigProfile,
    ProfileType,
    ConfigValidator,
    ValidationLevel,
    ValidationResult,
    get_config,
    get_config_value,
    set_config_value,
    save_config,
    validate_config,
    load_config,
)


class TestCLIIntegration:
    """Tests for CLI integration with configuration management."""

    def test_load_config(self):
        """Test loading configuration."""
        # Create a mock configuration manager
        with patch("immuta_toolkit.config.get_config_manager") as mock_get_manager:
            manager = MagicMock()
            mock_get_manager.return_value = manager

            # Call load_config
            load_config(
                config_file="config.yaml",
                env_prefix="IMMUTA_",
                secrets_provider="azure",
                vault_url="https://example.vault.azure.net",
            )

            # Check that the manager was called correctly
            manager.from_file.assert_called_with("config.yaml")
            manager.from_env.assert_called_with("IMMUTA_")
            manager.from_secrets.assert_called_with(
                "azure", vault_url="https://example.vault.azure.net"
            )
            manager.load.assert_called_once()

    def test_get_config_value(self):
        """Test getting configuration value."""
        # Create a mock configuration manager
        with patch("immuta_toolkit.config.get_config_manager") as mock_get_manager:
            manager = MagicMock()
            mock_get_manager.return_value = manager

            # Mock get method
            manager.get.return_value = "test-value"

            # Call get_config_value
            value = get_config_value("test.key")

            # Check that the manager was called correctly
            manager.get.assert_called_with("test.key", None)
            assert value == "test-value"

    def test_set_config_value(self):
        """Test setting configuration value."""
        # Create a mock configuration manager
        with patch("immuta_toolkit.config.get_config_manager") as mock_get_manager:
            manager = MagicMock()
            mock_get_manager.return_value = manager

            # Call set_config_value
            set_config_value("test.key", "test-value")

            # Check that the manager was called correctly
            manager.set.assert_called_with("test.key", "test-value")

    def test_save_config(self):
        """Test saving configuration."""
        # Create a mock configuration manager
        with patch("immuta_toolkit.config.get_config_manager") as mock_get_manager:
            manager = MagicMock()
            mock_get_manager.return_value = manager

            # Call save_config
            save_config("config.yaml")

            # Check that the manager was called
            assert manager.save.called
            # Check that the first argument was the file path
            args, kwargs = manager.save.call_args
            assert args[0] == "config.yaml"

    def test_validate_config(self):
        """Test validating configuration."""
        # Create a mock validator
        with patch("immuta_toolkit.config.ConfigValidator") as mock_validator_class:
            # Create mock validator instance
            validator = MagicMock()
            mock_validator_class.return_value = validator

            # Mock config manager
            with patch("immuta_toolkit.config.get_config_manager") as mock_get_manager:
                manager = MagicMock()
                mock_get_manager.return_value = manager

                # Mock config
                config = MagicMock()
                manager.config = config
                config.model_dump.return_value = {"test": "value"}

                # Mock validate method
                results = [
                    ValidationResult(
                        key="test.key",
                        level=ValidationLevel.ERROR,
                        message="Test error",
                        value=None,
                    )
                ]
                validator.validate.return_value = results

                # Call validate_config
                validation_results = validate_config()

                # Check that the validator was called correctly
                validator.validate.assert_called_once_with({"test": "value"})

                # Check that the results are correct
                assert validation_results == results
