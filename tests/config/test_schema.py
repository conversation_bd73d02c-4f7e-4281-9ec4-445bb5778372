"""Tests for the configuration schema."""

import pytest

from immuta_toolkit.config.schema import ConfigSchema


class TestConfigSchema:
    """Tests for the ConfigSchema class."""

    def test_init(self):
        """Test initializing the configuration schema."""
        schema = ConfigSchema()

        assert schema.title == "ImmutaConfig"
        assert schema.description == "Configuration for the Immuta SRE Toolkit"
        assert schema.version == "1.0.0"
        assert schema.properties is not None
        assert schema.required is not None

    def test_environment_property(self):
        """Test the environment property."""
        schema = ConfigSchema()

        assert "environment" in schema.properties
        assert schema.properties["environment"]["type"] == str
        assert "enum" in schema.properties["environment"]
        assert "local" in schema.properties["environment"]["enum"]
        assert "dev" in schema.properties["environment"]["enum"]
        assert "test" in schema.properties["environment"]["enum"]
        assert "prod" in schema.properties["environment"]["enum"]
        assert schema.properties["environment"]["default"] == "dev"

    def test_api_property(self):
        """Test the API property."""
        schema = ConfigSchema()

        assert "api" in schema.properties
        assert (
            schema.properties["api"]["type"] is dict
            or schema.properties["api"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["api"]
        assert "base_url" in schema.properties["api"]["properties"]
        assert "api_key" in schema.properties["api"]["properties"]
        assert "timeout" in schema.properties["api"]["properties"]
        assert "max_retries" in schema.properties["api"]["properties"]
        assert "retry_delay" in schema.properties["api"]["properties"]
        assert "rate_limit" in schema.properties["api"]["properties"]
        assert "rate_limit_window" in schema.properties["api"]["properties"]
        assert "required" in schema.properties["api"]
        assert "base_url" in schema.properties["api"]["required"]

    def test_web_property(self):
        """Test the web property."""
        schema = ConfigSchema()

        assert "web" in schema.properties
        assert (
            schema.properties["web"]["type"] is dict
            or schema.properties["web"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["web"]
        assert "base_url" in schema.properties["web"]["properties"]
        assert "username" in schema.properties["web"]["properties"]
        assert "password" in schema.properties["web"]["properties"]
        assert "headless" in schema.properties["web"]["properties"]
        assert "browser_type" in schema.properties["web"]["properties"]
        assert "timeout" in schema.properties["web"]["properties"]
        assert "screenshot_dir" in schema.properties["web"]["properties"]
        assert "required" in schema.properties["web"]
        assert "base_url" in schema.properties["web"]["required"]

    def test_secrets_property(self):
        """Test the secrets property."""
        schema = ConfigSchema()

        assert "secrets" in schema.properties
        assert (
            schema.properties["secrets"]["type"] is dict
            or schema.properties["secrets"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["secrets"]
        assert "provider" in schema.properties["secrets"]["properties"]
        assert "vault_url" in schema.properties["secrets"]["properties"]
        assert "key_prefix" in schema.properties["secrets"]["properties"]
        assert "client_id" in schema.properties["secrets"]["properties"]
        assert "client_secret" in schema.properties["secrets"]["properties"]
        assert "tenant_id" in schema.properties["secrets"]["properties"]
        assert "region" in schema.properties["secrets"]["properties"]

    def test_storage_property(self):
        """Test the storage property."""
        schema = ConfigSchema()

        assert "storage" in schema.properties
        assert (
            schema.properties["storage"]["type"] is dict
            or schema.properties["storage"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["storage"]
        assert "provider" in schema.properties["storage"]["properties"]
        assert "connection_string" in schema.properties["storage"]["properties"]
        assert "container_name" in schema.properties["storage"]["properties"]
        assert "local_path" in schema.properties["storage"]["properties"]
        assert "database_url" in schema.properties["storage"]["properties"]

    def test_logging_property(self):
        """Test the logging property."""
        schema = ConfigSchema()

        assert "logging" in schema.properties
        assert (
            schema.properties["logging"]["type"] is dict
            or schema.properties["logging"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["logging"]
        assert "level" in schema.properties["logging"]["properties"]
        assert "format" in schema.properties["logging"]["properties"]
        assert "file" in schema.properties["logging"]["properties"]
        assert "use_structlog" in schema.properties["logging"]["properties"]
        assert "max_size" in schema.properties["logging"]["properties"]
        assert "backup_count" in schema.properties["logging"]["properties"]

    def test_reporting_property(self):
        """Test the reporting property."""
        schema = ConfigSchema()

        assert "reporting" in schema.properties
        assert (
            schema.properties["reporting"]["type"] is dict
            or schema.properties["reporting"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["reporting"]
        assert "output_dir" in schema.properties["reporting"]["properties"]
        assert "default_format" in schema.properties["reporting"]["properties"]
        assert "include_timestamp" in schema.properties["reporting"]["properties"]
        assert "email" in schema.properties["reporting"]["properties"]

    def test_operation_property(self):
        """Test the operation property."""
        schema = ConfigSchema()

        assert "operation" in schema.properties
        assert (
            schema.properties["operation"]["type"] is dict
            or schema.properties["operation"]["type"].__name__ == "Dict"
        )
        assert "properties" in schema.properties["operation"]
        assert "use_web_fallback" in schema.properties["operation"]["properties"]
        assert "max_retries" in schema.properties["operation"]["properties"]
        assert "retry_delay" in schema.properties["operation"]["properties"]
        assert "batch_size" in schema.properties["operation"]["properties"]
        assert "parallel" in schema.properties["operation"]["properties"]
        assert "max_workers" in schema.properties["operation"]["properties"]

    def test_features_property(self):
        """Test the features property."""
        schema = ConfigSchema()

        assert "features" in schema.properties
        assert (
            schema.properties["features"]["type"] is dict
            or schema.properties["features"]["type"].__name__ == "Dict"
        )
        assert "default" in schema.properties["features"]
        assert schema.properties["features"]["default"] == {}

    def test_profiles_property(self):
        """Test the profiles property."""
        schema = ConfigSchema()

        assert "profiles" in schema.properties
        assert (
            schema.properties["profiles"]["type"] is dict
            or schema.properties["profiles"]["type"].__name__ == "Dict"
        )
        assert "default" in schema.properties["profiles"]
        assert schema.properties["profiles"]["default"] == {}

    def test_custom_property(self):
        """Test the custom property."""
        schema = ConfigSchema()

        assert "custom" in schema.properties
        assert (
            schema.properties["custom"]["type"] is dict
            or schema.properties["custom"]["type"].__name__ == "Dict"
        )
        assert "default" in schema.properties["custom"]
        assert schema.properties["custom"]["default"] == {}

    def test_required_properties(self):
        """Test the required properties."""
        schema = ConfigSchema()

        assert "environment" in schema.required
        assert "api" in schema.required
        assert "web" in schema.required
