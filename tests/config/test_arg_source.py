"""Tests for the command-line argument configuration source."""

import argparse
from typing import Dict, Any

import pytest

from immuta_toolkit.config.sources import ArgParseConfigSource


class TestArgParseConfigSource:
    """Tests for the ArgParseConfigSource class."""
    
    def test_init(self):
        """Test initializing the command-line argument configuration source."""
        # Create source with default values
        source = ArgParseConfigSource()
        
        assert source.parser is not None
        assert source.args is None
        assert source.prefix == ""
        
        # Create source with custom values
        parser = argparse.ArgumentParser()
        args = parser.parse_args([])
        prefix = "test"
        
        source = ArgParseConfigSource(parser, args, prefix)
        
        assert source.parser == parser
        assert source.args == args
        assert source.prefix == prefix
    
    def test_load_empty(self):
        """Test loading configuration from empty arguments."""
        # Create parser
        parser = argparse.ArgumentParser()
        
        # Create source
        source = ArgParseConfigSource(parser, parser.parse_args([]))
        
        # Load configuration
        config = source.load()
        
        assert config == {}
    
    def test_load_simple(self):
        """Test loading configuration from simple arguments."""
        # Create parser
        parser = argparse.ArgumentParser()
        parser.add_argument("--name", type=str)
        parser.add_argument("--value", type=int)
        parser.add_argument("--enabled", action="store_true")
        
        # Create source
        source = ArgParseConfigSource(parser, parser.parse_args(["--name", "test", "--value", "42", "--enabled"]))
        
        # Load configuration
        config = source.load()
        
        assert config == {"name": "test", "value": 42, "enabled": True}
    
    def test_load_nested(self):
        """Test loading configuration from nested arguments."""
        # Create parser
        parser = argparse.ArgumentParser()
        parser.add_argument("--api.url", type=str)
        parser.add_argument("--api.timeout", type=int)
        parser.add_argument("--logging.level", type=str)
        
        # Create source
        source = ArgParseConfigSource(
            parser, 
            parser.parse_args(["--api.url", "https://example.com", "--api.timeout", "30", "--logging.level", "DEBUG"])
        )
        
        # Load configuration
        config = source.load()
        
        assert config == {
            "api": {
                "url": "https://example.com",
                "timeout": 30,
            },
            "logging": {
                "level": "DEBUG",
            },
        }
    
    def test_load_with_prefix(self):
        """Test loading configuration with a prefix."""
        # Create parser
        parser = argparse.ArgumentParser()
        parser.add_argument("--name", type=str)
        parser.add_argument("--value", type=int)
        
        # Create source
        source = ArgParseConfigSource(
            parser, 
            parser.parse_args(["--name", "test", "--value", "42"]),
            prefix="config",
        )
        
        # Load configuration
        config = source.load()
        
        assert config == {
            "config": {
                "name": "test",
                "value": 42,
            },
        }
    
    def test_str(self):
        """Test string representation."""
        # Create source
        source = ArgParseConfigSource(prefix="test")
        
        assert str(source) == "ArgParseConfigSource(test)"
