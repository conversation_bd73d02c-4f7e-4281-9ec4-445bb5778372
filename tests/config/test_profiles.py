"""Tests for the configuration profiles."""

import pytest

from immuta_toolkit.config.profiles import (
    ConfigProfile,
    ProfileManager,
    ProfileType,
)


class TestConfigProfile:
    """Tests for the ConfigProfile class."""
    
    def test_init(self):
        """Test initializing a configuration profile."""
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        assert profile.name == "test"
        assert profile.type == ProfileType.ENVIRONMENT
        assert profile.description == "Test profile"
        assert profile.values == {"key": "value"}
        assert profile.enabled is True
    
    def test_init_with_string_type(self):
        """Test initializing a configuration profile with a string type."""
        profile = ConfigProfile(
            name="test",
            type="environment",
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        assert profile.name == "test"
        assert profile.type == ProfileType.ENVIRONMENT
        assert profile.description == "Test profile"
        assert profile.values == {"key": "value"}
        assert profile.enabled is True
    
    def test_to_dict(self):
        """Test converting a profile to a dictionary."""
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        profile_dict = profile.to_dict()
        
        assert profile_dict["name"] == "test"
        assert profile_dict["type"] == ProfileType.ENVIRONMENT
        assert profile_dict["description"] == "Test profile"
        assert profile_dict["values"] == {"key": "value"}
        assert profile_dict["enabled"] is True
    
    def test_from_dict(self):
        """Test creating a profile from a dictionary."""
        profile_dict = {
            "name": "test",
            "type": ProfileType.ENVIRONMENT,
            "description": "Test profile",
            "values": {"key": "value"},
            "enabled": True,
        }
        
        profile = ConfigProfile.from_dict(profile_dict)
        
        assert profile.name == "test"
        assert profile.type == ProfileType.ENVIRONMENT
        assert profile.description == "Test profile"
        assert profile.values == {"key": "value"}
        assert profile.enabled is True


class TestProfileManager:
    """Tests for the ProfileManager class."""
    
    def test_init(self):
        """Test initializing the profile manager."""
        manager = ProfileManager()
        
        assert manager.profiles is not None
        assert len(manager.profiles) > 0  # Default profiles should be added
    
    def test_add_profile(self):
        """Test adding a profile."""
        manager = ProfileManager()
        
        # Add a profile
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        manager.add_profile(profile)
        
        assert "test" in manager.profiles
        assert manager.profiles["test"] == profile
    
    def test_get_profile(self):
        """Test getting a profile by name."""
        manager = ProfileManager()
        
        # Add a profile
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        manager.add_profile(profile)
        
        # Get the profile
        result = manager.get_profile("test")
        
        assert result == profile
        
        # Get a nonexistent profile
        result = manager.get_profile("nonexistent")
        
        assert result is None
    
    def test_remove_profile(self):
        """Test removing a profile."""
        manager = ProfileManager()
        
        # Add a profile
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=True,
        )
        
        manager.add_profile(profile)
        
        # Remove the profile
        result = manager.remove_profile("test")
        
        assert result is True
        assert "test" not in manager.profiles
        
        # Remove a nonexistent profile
        result = manager.remove_profile("nonexistent")
        
        assert result is False
    
    def test_enable_disable_profile(self):
        """Test enabling and disabling a profile."""
        manager = ProfileManager()
        
        # Add a profile
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={"key": "value"},
            enabled=False,
        )
        
        manager.add_profile(profile)
        
        # Enable the profile
        result = manager.enable_profile("test")
        
        assert result is True
        assert manager.profiles["test"].enabled is True
        
        # Disable the profile
        result = manager.disable_profile("test")
        
        assert result is True
        assert manager.profiles["test"].enabled is False
        
        # Enable a nonexistent profile
        result = manager.enable_profile("nonexistent")
        
        assert result is False
    
    def test_get_profiles_by_type(self):
        """Test getting profiles by type."""
        manager = ProfileManager()
        
        # Add profiles
        profile1 = ConfigProfile(
            name="test1",
            type=ProfileType.ENVIRONMENT,
            description="Test profile 1",
            values={"key1": "value1"},
            enabled=True,
        )
        
        profile2 = ConfigProfile(
            name="test2",
            type=ProfileType.ROLE,
            description="Test profile 2",
            values={"key2": "value2"},
            enabled=True,
        )
        
        manager.add_profile(profile1)
        manager.add_profile(profile2)
        
        # Get profiles by type
        results = manager.get_profiles_by_type(ProfileType.ENVIRONMENT)
        
        assert len(results) > 0  # Should include default profiles
        assert profile1 in results
        assert profile2 not in results
    
    def test_get_enabled_profiles(self):
        """Test getting enabled profiles."""
        manager = ProfileManager()
        
        # Add profiles
        profile1 = ConfigProfile(
            name="test1",
            type=ProfileType.ENVIRONMENT,
            description="Test profile 1",
            values={"key1": "value1"},
            enabled=True,
        )
        
        profile2 = ConfigProfile(
            name="test2",
            type=ProfileType.ROLE,
            description="Test profile 2",
            values={"key2": "value2"},
            enabled=False,
        )
        
        manager.add_profile(profile1)
        manager.add_profile(profile2)
        
        # Get enabled profiles
        results = manager.get_enabled_profiles()
        
        assert profile1 in results
        assert profile2 not in results
    
    def test_apply_profile(self):
        """Test applying a profile to a configuration."""
        manager = ProfileManager()
        
        # Add a profile
        profile = ConfigProfile(
            name="test",
            type=ProfileType.ENVIRONMENT,
            description="Test profile",
            values={
                "key1": "value1",
                "nested": {
                    "key2": "value2",
                },
            },
            enabled=True,
        )
        
        manager.add_profile(profile)
        
        # Apply the profile
        config = {
            "key1": "original",
            "key3": "value3",
            "nested": {
                "key2": "original",
                "key4": "value4",
            },
        }
        
        result = manager.apply_profile(config, "test")
        
        assert result["key1"] == "value1"  # Overwritten
        assert result["key3"] == "value3"  # Unchanged
        assert result["nested"]["key2"] == "value2"  # Overwritten
        assert result["nested"]["key4"] == "value4"  # Unchanged
        
        # Apply a nonexistent profile
        with pytest.raises(ValueError):
            manager.apply_profile(config, "nonexistent")
    
    def test_apply_profiles(self):
        """Test applying multiple profiles to a configuration."""
        manager = ProfileManager()
        
        # Add profiles
        profile1 = ConfigProfile(
            name="test1",
            type=ProfileType.ENVIRONMENT,
            description="Test profile 1",
            values={
                "key1": "value1",
                "nested": {
                    "key2": "value2",
                },
            },
            enabled=True,
        )
        
        profile2 = ConfigProfile(
            name="test2",
            type=ProfileType.ROLE,
            description="Test profile 2",
            values={
                "key3": "value3",
                "nested": {
                    "key4": "value4",
                },
            },
            enabled=True,
        )
        
        manager.add_profile(profile1)
        manager.add_profile(profile2)
        
        # Apply profiles
        config = {
            "key1": "original",
            "key5": "value5",
            "nested": {
                "key2": "original",
                "key6": "value6",
            },
        }
        
        result = manager.apply_profiles(config)
        
        assert result["key1"] == "value1"  # Overwritten by profile1
        assert result["key3"] == "value3"  # Added by profile2
        assert result["key5"] == "value5"  # Unchanged
        assert result["nested"]["key2"] == "value2"  # Overwritten by profile1
        assert result["nested"]["key4"] == "value4"  # Added by profile2
        assert result["nested"]["key6"] == "value6"  # Unchanged
        
        # Apply profiles with type filter
        config = {
            "key1": "original",
            "key5": "value5",
            "nested": {
                "key2": "original",
                "key6": "value6",
            },
        }
        
        result = manager.apply_profiles(config, [ProfileType.ENVIRONMENT])
        
        assert result["key1"] == "value1"  # Overwritten by profile1
        assert "key3" not in result  # Not added by profile2 (filtered out)
        assert result["key5"] == "value5"  # Unchanged
        assert result["nested"]["key2"] == "value2"  # Overwritten by profile1
        assert "key4" not in result["nested"]  # Not added by profile2 (filtered out)
        assert result["nested"]["key6"] == "value6"  # Unchanged
