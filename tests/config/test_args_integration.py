"""Tests for command-line argument integration with the configuration manager."""

import argparse
import os
import tempfile
from pathlib import Path
from typing import Optional

import pytest
import yaml

from immuta_toolkit.config import (
    ConfigManager,
    ConfigFormat,
    load_config,
    load_args,
    get_config_value,
)
from pydantic import BaseModel, Field


class TestConfig(BaseModel):
    """Test configuration model."""

    api_url: Optional[str] = Field(None, description="API URL")
    api_timeout: Optional[int] = Field(None, description="API timeout")
    logging_level: Optional[str] = Field(None, description="Logging level")
    api: dict = Field(default_factory=dict, description="API configuration")


class TestArgsIntegration:
    """Tests for command-line argument integration with the configuration manager."""

    def test_from_args(self):
        """Test loading configuration from command-line arguments."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfig)

        # Create parser
        parser = argparse.ArgumentParser()
        parser.add_argument("--api-url", type=str)
        parser.add_argument("--api-timeout", type=int)
        parser.add_argument("--logging-level", type=str)

        # Parse arguments
        args = parser.parse_args(
            [
                "--api-url",
                "https://example.com",
                "--api-timeout",
                "30",
                "--logging-level",
                "DEBUG",
            ]
        )

        # Load from arguments
        manager.from_args(parser, args)

        # Load configuration
        config = manager.load()

        # Check values
        assert manager.get("api_url") == "https://example.com"
        assert manager.get("api_timeout") == 30
        assert manager.get("logging_level") == "DEBUG"

    def test_load_args_function(self):
        """Test the load_args function."""
        # Create a test config manager with custom model
        manager = ConfigManager(config_model=TestConfig)

        # Override the global config manager
        import immuta_toolkit.config

        immuta_toolkit.config._config_manager = manager

        # Create parser
        parser = argparse.ArgumentParser()
        parser.add_argument("--api-url", type=str)
        parser.add_argument("--api-timeout", type=int)

        # Parse arguments
        args = parser.parse_args(
            ["--api-url", "https://example.com", "--api-timeout", "30"]
        )

        # Load from arguments
        load_args(parser, args)

        # Check values
        assert get_config_value("api_url") == "https://example.com"
        assert get_config_value("api_timeout") == 30

    def test_load_config_with_args(self):
        """Test the load_config function with command-line arguments."""
        # Create a test config manager with custom model
        manager = ConfigManager(config_model=TestConfig)

        # Override the global config manager
        import immuta_toolkit.config

        immuta_toolkit.config._config_manager = manager

        # Create temporary configuration file
        with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w", delete=False) as f:
            yaml.dump({"api": {"url": "https://file.example.com"}}, f)
            config_file = f.name

        try:
            # Set environment variable
            os.environ["IMMUTA_API_TIMEOUT"] = "60"

            # Create parser
            parser = argparse.ArgumentParser()
            parser.add_argument("--api-url", dest="api.url", type=str)
            parser.add_argument("--logging-level", type=str)

            # Parse arguments
            args = parser.parse_args(
                ["--api-url", "https://args.example.com", "--logging-level", "DEBUG"]
            )

            # Load configuration
            load_config(
                config_file=config_file,
                env_prefix="IMMUTA_",
                arg_parser=parser,
                arg_namespace=args,
            )

            # Check values (command-line arguments should override file and environment variables)
            assert get_config_value("api.url") == "https://args.example.com"
            assert get_config_value("api.timeout") == 60
            assert get_config_value("logging_level") == "DEBUG"
        finally:
            # Clean up
            os.unlink(config_file)
            os.environ.pop("IMMUTA_API_TIMEOUT", None)

    def test_precedence(self):
        """Test configuration source precedence."""
        # Create temporary configuration file
        with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w", delete=False) as f:
            yaml.dump({"api": {"url": "https://file.example.com", "timeout": 10}}, f)
            config_file = f.name

        try:
            # Set environment variables
            os.environ["IMMUTA_API_URL"] = "https://env.example.com"
            os.environ["IMMUTA_API_TIMEOUT"] = "20"

            # Create parser
            parser = argparse.ArgumentParser()
            parser.add_argument("--api-url", dest="api.url", type=str)

            # Parse arguments
            args = parser.parse_args(["--api-url", "https://args.example.com"])

            # Create manager with custom model
            manager = ConfigManager(config_model=TestConfig)

            # Add sources in order of precedence (lowest to highest)
            manager.from_file(config_file)
            manager.from_env("IMMUTA_")
            manager.from_args(parser, args)

            # Load configuration
            config = manager.load()

            # Check values
            assert manager.get("api.url") == "https://args.example.com"  # From args
            assert manager.get("api.timeout") == 20  # From environment
        finally:
            # Clean up
            os.unlink(config_file)
            os.environ.pop("IMMUTA_API_URL", None)
            os.environ.pop("IMMUTA_API_TIMEOUT", None)
