"""Tests for the configuration manager."""

import os
import json
import tempfile
from pathlib import Path
from typing import Dict, Any

import pytest
from pydantic import BaseModel, Field

from immuta_toolkit.config.manager import ConfigManager, ConfigFormat
from immuta_toolkit.config.schema import ConfigSchema
from immuta_toolkit.config.sources import FileConfigSource, EnvConfigSource


class TestConfigModel(BaseModel):
    """Test configuration model."""
    
    name: str = Field("test", description="Name")
    value: int = Field(42, description="Value")
    enabled: bool = Field(True, description="Enabled")
    nested: Dict[str, Any] = Field(default_factory=dict, description="Nested configuration")


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def config_file(temp_dir):
    """Create a temporary configuration file for testing."""
    config_path = os.path.join(temp_dir, "config.json")
    config = {
        "name": "test_config",
        "value": 123,
        "nested": {
            "key1": "value1",
            "key2": "value2",
        },
    }
    
    with open(config_path, "w") as f:
        json.dump(config, f)
        
    return config_path


class TestConfigManager:
    """Tests for the ConfigManager class."""
    
    def test_init(self):
        """Test initializing the configuration manager."""
        # Create manager with default schema
        manager = ConfigManager()
        
        assert manager.schema is not None
        assert manager.config_model is not None
        assert manager.config is not None
        assert len(manager.sources) == 0
    
    def test_init_with_model(self):
        """Test initializing the configuration manager with a custom model."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        assert manager.schema is not None
        assert manager.config_model == TestConfigModel
        assert isinstance(manager.config, TestConfigModel)
        assert len(manager.sources) == 0
    
    def test_add_source(self):
        """Test adding a configuration source."""
        # Create manager
        manager = ConfigManager()
        
        # Add source
        source = FileConfigSource("config.json", ConfigFormat.JSON)
        manager.add_source(source)
        
        assert len(manager.sources) == 1
        assert manager.sources[0] == source
    
    def test_load_defaults(self):
        """Test loading default configuration values."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Load defaults
        manager.load_defaults()
        
        assert manager.config is not None
        assert manager.config.name == "test"
        assert manager.config.value == 42
        assert manager.config.enabled is True
        assert manager.config.nested == {}
    
    def test_from_file(self, config_file):
        """Test loading configuration from a file."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Load from file
        manager.from_file(config_file)
        
        # Load configuration
        config = manager.load()
        
        assert config.name == "test_config"
        assert config.value == 123
        assert config.nested == {"key1": "value1", "key2": "value2"}
    
    def test_from_env(self, monkeypatch):
        """Test loading configuration from environment variables."""
        # Set environment variables
        monkeypatch.setenv("TEST_NAME", "env_test")
        monkeypatch.setenv("TEST_VALUE", "456")
        monkeypatch.setenv("TEST_ENABLED", "false")
        monkeypatch.setenv("TEST_NESTED_KEY3", "value3")
        
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Load from environment variables
        manager.from_env(prefix="TEST_")
        
        # Load configuration
        config = manager.load()
        
        assert config.name == "env_test"
        assert config.value == 456
        assert config.enabled is False
        assert config.nested == {"key3": "value3"}
    
    def test_get(self):
        """Test getting a configuration value."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Get values
        assert manager.get("name") == "test"
        assert manager.get("value") == 42
        assert manager.get("enabled") is True
        assert manager.get("nested") == {}
        assert manager.get("nonexistent") is None
        assert manager.get("nonexistent", "default") == "default"
    
    def test_set(self):
        """Test setting a configuration value."""
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Set values
        manager.set("name", "new_name")
        manager.set("value", 789)
        manager.set("nested.key4", "value4")
        
        # Check values
        assert manager.get("name") == "new_name"
        assert manager.get("value") == 789
        assert manager.get("nested.key4") == "value4"
    
    def test_save(self, temp_dir):
        """Test saving configuration to a file."""
        # Create output path
        output_path = os.path.join(temp_dir, "output.json")
        
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Set values
        manager.set("name", "save_test")
        manager.set("value", 999)
        
        # Save configuration
        manager.save(output_path)
        
        # Check if file exists
        assert os.path.exists(output_path)
        
        # Load saved configuration
        with open(output_path, "r") as f:
            saved_config = json.load(f)
            
        assert saved_config["name"] == "save_test"
        assert saved_config["value"] == 999
    
    def test_save_yaml(self, temp_dir):
        """Test saving configuration to a YAML file."""
        # Create output path
        output_path = os.path.join(temp_dir, "output.yaml")
        
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Set values
        manager.set("name", "yaml_test")
        manager.set("value", 888)
        
        # Save configuration
        manager.save(output_path, ConfigFormat.YAML)
        
        # Check if file exists
        assert os.path.exists(output_path)
        
        # Load saved configuration
        import yaml
        with open(output_path, "r") as f:
            saved_config = yaml.safe_load(f)
            
        assert saved_config["name"] == "yaml_test"
        assert saved_config["value"] == 888
    
    def test_multiple_sources(self, config_file, monkeypatch):
        """Test loading configuration from multiple sources."""
        # Set environment variables
        monkeypatch.setenv("TEST_VALUE", "789")
        monkeypatch.setenv("TEST_NESTED_KEY3", "value3")
        
        # Create manager with custom model
        manager = ConfigManager(config_model=TestConfigModel)
        
        # Add sources
        manager.from_file(config_file)
        manager.from_env(prefix="TEST_")
        
        # Load configuration
        config = manager.load()
        
        # Check values (environment variables should override file values)
        assert config.name == "test_config"  # From file
        assert config.value == 789  # From environment
        assert config.nested == {"key1": "value1", "key2": "value2", "key3": "value3"}  # Merged
