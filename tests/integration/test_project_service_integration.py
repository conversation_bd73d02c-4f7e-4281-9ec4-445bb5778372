"""Integration tests for the project service."""

import os
import shutil
import tempfile
import time
from typing import Dict, Any, List

import pytest

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.services.project_service import ProjectService
from immuta_toolkit.utils.notifications import NotificationManager
from immuta_toolkit.utils.storage import LocalStorageManager


@pytest.fixture
def client():
    """Create an Immuta client for testing."""
    # Use environment variables or defaults for testing
    api_key = os.getenv("IMMUTA_API_KEY", "test_api_key")
    base_url = os.getenv("IMMUTA_BASE_URL", "https://localhost")

    # Use local mode for testing if no API key is provided
    is_local = api_key == "test_api_key"

    # Create notification manager
    notifier = NotificationManager()

    # Create temporary directory for storage
    temp_dir = tempfile.mkdtemp()
    storage = LocalStorageManager(temp_dir)

    # Create client
    client = ImmutaClient(
        api_key=api_key,
        base_url=base_url,
        is_local=is_local,
        notifier=notifier,
        storage=storage,
    )

    yield client

    # Clean up
    shutil.rmtree(temp_dir)


@pytest.fixture
def project_service(client):
    """Create a project service for testing."""
    return ProjectService(client)


@pytest.fixture
def test_project():
    """Create a test project."""
    return {
        "name": f"Test Project {int(time.time())}",
        "description": "Test project for integration testing",
        "tags": ["test", "integration"],
    }


def test_project_lifecycle(project_service, test_project):
    """Test the full lifecycle of a project."""
    # Create project
    created_project = project_service.create_project(
        project=test_project, backup=True, dry_run=False, validate=True
    )

    # Verify project was created
    assert created_project["id"] is not None
    assert created_project["name"] == test_project["name"]
    assert created_project["description"] == test_project["description"]

    project_id = created_project["id"]

    # Get project
    retrieved_project = project_service.get_project(project_id)
    assert retrieved_project["id"] == project_id
    assert retrieved_project["name"] == test_project["name"]

    # Update project
    updated_project = project_service.update_project(
        project_id=project_id,
        project={
            "name": f"{test_project['name']} - Updated",
            "description": f"{test_project['description']} - Updated",
            "tags": test_project["tags"] + ["updated"],
        },
        backup=True,
        dry_run=False,
        validate=True,
    )

    # Verify project was updated
    assert updated_project["id"] == project_id
    assert updated_project["name"] == f"{test_project['name']} - Updated"
    assert updated_project["description"] == f"{test_project['description']} - Updated"
    assert "updated" in updated_project["tags"]

    # Add a member to the project (using a mock user ID)
    mock_user_id = 1
    add_member_result = project_service.add_project_member(
        project_id=project_id, user_id=mock_user_id, role="MEMBER", dry_run=False
    )

    # Verify member was added
    assert add_member_result["success"] is True
    assert add_member_result["project_id"] == project_id
    assert add_member_result["user_id"] == mock_user_id

    # Get project members
    members = project_service.get_project_members(project_id)

    # Verify member is in the list
    if not project_service.client.is_local:
        # In real mode, we should find the member
        assert any(member["id"] == mock_user_id for member in members)

    # Add a data source to the project (using a mock data source ID)
    mock_data_source_id = 1
    add_data_source_result = project_service.add_data_source_to_project(
        project_id=project_id, data_source_id=mock_data_source_id, dry_run=False
    )

    # Verify data source was added
    assert add_data_source_result["success"] is True
    assert add_data_source_result["project_id"] == project_id
    assert add_data_source_result["data_source_id"] == mock_data_source_id

    # Get project data sources
    data_sources = project_service.get_project_data_sources(project_id)

    # Verify data source is in the list
    if not project_service.client.is_local:
        # In real mode, we should find the data source
        assert any(ds["id"] == mock_data_source_id for ds in data_sources)

    # Add a purpose to the project (using a mock purpose ID)
    mock_purpose_id = 1
    add_purpose_result = project_service.add_purpose_to_project(
        project_id=project_id, purpose_id=mock_purpose_id, dry_run=False
    )

    # Verify purpose was added
    assert add_purpose_result["success"] is True
    assert add_purpose_result["project_id"] == project_id
    assert add_purpose_result["purpose_id"] == mock_purpose_id

    # Get project purposes
    purposes = project_service.get_project_purposes(project_id)

    # Verify purpose is in the list
    if not project_service.client.is_local:
        # In real mode, we should find the purpose
        assert any(purpose["id"] == mock_purpose_id for purpose in purposes)

    # Test batch operations
    members = [{"userId": 2, "role": "MEMBER"}, {"userId": 3, "role": "MEMBER"}]
    batch_result = project_service.batch_add_members_to_project(
        project_id=project_id, members=members, dry_run=False
    )

    # Verify batch operation was successful
    assert batch_result["status"] == "success"

    # Delete project
    delete_result = project_service.delete_project(
        project_id=project_id, backup=True, dry_run=False
    )

    # Verify project was deleted
    assert delete_result["status"] == "success"
    assert delete_result["project_id"] == project_id

    # Verify project no longer exists
    with pytest.raises(ValueError):
        project_service.get_project(project_id)


def test_backup_restore_project(client, project_service, test_project):
    """Test backup and restore of a project."""
    # Create project
    created_project = project_service.create_project(
        project=test_project, backup=True, dry_run=False, validate=True
    )

    project_id = created_project["id"]

    # Create a temporary directory for backup
    backup_dir = tempfile.mkdtemp()

    try:
        # Backup the project
        backup_result = client.backup_service.backup_projects(
            output_dir=backup_dir,
            project_ids=[project_id],
            include_members=True,
            include_data_sources=True,
            include_purposes=True,
            retention_days=1,
            dry_run=False,
        )

        # Verify backup was successful
        assert backup_result["status"] == "success"
        assert backup_result["project_count"] == 1

        # Delete the project
        project_service.delete_project(
            project_id=project_id, backup=True, dry_run=False
        )

        # Verify project no longer exists
        with pytest.raises(ValueError):
            project_service.get_project(project_id)

        # Restore the project
        restore_result = client.backup_service.restore_projects(
            backup_dir=backup_dir,
            project_ids=None,  # Restore all projects in the backup
            restore_members=True,
            restore_data_sources=True,
            restore_purposes=True,
            dry_run=False,
        )

        # Verify restore was successful
        assert restore_result["status"] == "success"
        assert restore_result["restored"] == 1

        # Get the restored project
        # For our mock implementation, the project ID should be the same
        try:
            restored_project = project_service.get_project(project_id)

            # Verify project was restored
            assert restored_project is not None
            assert restored_project["id"] == project_id

            # In our mock implementation, the name might be different
            # but we can verify it's a valid project
            assert "name" in restored_project
            assert "description" in restored_project
        except ValueError as e:
            pytest.fail(f"Failed to get restored project: {e}")

        # Clean up the restored project
        project_service.delete_project(
            project_id=restored_project["id"], backup=True, dry_run=False
        )
    finally:
        # Clean up
        shutil.rmtree(backup_dir)
