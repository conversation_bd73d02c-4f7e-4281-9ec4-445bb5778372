"""Tests for API commands."""

import os
import pytest
from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from immuta_toolkit.cli.api_commands import api_group, generate_api_key, add_api_user, list_api_users, disable_api_user


@pytest.fixture
def runner():
    """Fixture for CLI runner."""
    return CliR<PERSON>ner()


@patch("immuta_toolkit.cli.api_commands.jwt.encode")
@patch("immuta_toolkit.cli.api_commands.datetime")
def test_generate_api_key(mock_datetime, mock_jwt_encode, runner):
    """Test generate API key command."""
    # Mock datetime
    from datetime import datetime as real_datetime
    mock_now = real_datetime(2023, 1, 1)
    mock_datetime.utcnow.return_value = mock_now

    # Mock JWT encode
    mock_jwt_encode.return_value = "test-token"

    # Run command
    result = runner.invoke(
        api_group,
        ["generate-key", "--username", "test-user", "--expires-days", "30"],
    )

    # Check result
    assert result.exit_code == 0
    assert "API Key Generated" in result.output
    assert "test-user" in result.output
    assert "test-token" in result.output


@patch("immuta_toolkit.api.auth.USERS", {})
@patch("immuta_toolkit.api.auth.get_password_hash")
def test_add_api_user(mock_get_password_hash, runner):
    """Test add API user command."""
    # Mock password hash
    mock_get_password_hash.return_value = "hashed-password"

    # Run command
    result = runner.invoke(
        api_group,
        ["user-add", "--username", "test-user", "--password", "test-password"],
    )

    # Check result
    assert result.exit_code == 0
    assert "User test-user added successfully" in result.output

    # Check user was added
    from immuta_toolkit.api.auth import USERS
    assert "test-user" in USERS
    assert USERS["test-user"]["username"] == "test-user"
    assert USERS["test-user"]["hashed_password"] == "hashed-password"
    assert USERS["test-user"]["disabled"] is False


@patch("immuta_toolkit.api.auth.USERS", {
    "test-user": {
        "username": "test-user",
        "hashed_password": "hashed-password",
        "disabled": False,
    }
})
def test_list_api_users(runner):
    """Test list API users command."""
    # Run command
    result = runner.invoke(api_group, ["user-list"])

    # Check result
    assert result.exit_code == 0
    assert "API Users" in result.output
    assert "test-user" in result.output
    assert "Active" in result.output


@patch("immuta_toolkit.api.auth.USERS", {
    "test-user": {
        "username": "test-user",
        "hashed_password": "hashed-password",
        "disabled": False,
    }
})
def test_disable_api_user(runner):
    """Test disable API user command."""
    # Run command
    result = runner.invoke(api_group, ["user-disable", "--username", "test-user"])

    # Check result
    assert result.exit_code == 0
    assert "User test-user disabled successfully" in result.output

    # Check user was disabled
    from immuta_toolkit.api.auth import USERS
    assert USERS["test-user"]["disabled"] is True


@patch("immuta_toolkit.cli.api_commands.uvicorn")
def test_start_api(mock_uvicorn, runner):
    """Test start API command."""
    # Run command
    result = runner.invoke(
        api_group,
        ["start", "--host", "0.0.0.0", "--port", "8000", "--reload", "--workers", "2"],
    )

    # Check result
    assert result.exit_code == 0
    # Check for the URL in the output, ignoring rich formatting
    assert "http://0.0.0.0:8000" in result.output

    # Check uvicorn was called
    mock_uvicorn.run.assert_called_once_with(
        "immuta_toolkit.api.app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        workers=2,
    )
