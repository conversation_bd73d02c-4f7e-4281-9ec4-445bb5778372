"""Tests for configuration commands."""

import os
import json
import yaml
import pytest
from unittest.mock import patch, MagicMock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from immuta_toolkit.cli.config_commands import (
    config_group,
    view_config,
    set_config_value_command,
    validate_config_command,
    init_config,
    load_config_command,
    profile_group,
    list_profiles,
    view_profile,
    create_profile,
    update_profile,
    delete_profile,
    enable_profile,
    disable_profile,
    apply_profile,
)
from immuta_toolkit.config import (
    ConfigProfile,
    ProfileType,
    ValidationResult,
    ValidationLevel,
)


@pytest.fixture
def runner():
    """Fixture for CLI runner."""
    return CliRunner()


@pytest.fixture
def mock_config_manager():
    """Fixture for mock configuration manager."""
    with patch("immuta_toolkit.cli.config_commands.get_config_manager") as mock:
        manager = MagicMock()
        mock.return_value = manager
        yield manager


@pytest.fixture
def mock_config_value():
    """Fixture for mock configuration value."""
    with patch("immuta_toolkit.cli.config_commands.get_config_value") as mock:
        yield mock


@pytest.fixture
def mock_set_config_value():
    """Fixture for mock set_config_value."""
    with patch("immuta_toolkit.cli.config_commands.set_config_value") as mock:
        yield mock


@pytest.fixture
def mock_save_config():
    """Fixture for mock save_config."""
    with patch("immuta_toolkit.cli.config_commands.save_config") as mock:
        yield mock


@pytest.fixture
def mock_validate_config():
    """Fixture for mock validate_config."""
    with patch("immuta_toolkit.cli.config_commands.validate_config") as mock:
        yield mock


@pytest.fixture
def mock_load_config():
    """Fixture for mock load_config."""
    with patch("immuta_toolkit.cli.config_commands.load_config") as mock:
        yield mock


@pytest.fixture
def mock_get_config():
    """Fixture for mock get_config."""
    with patch("immuta_toolkit.cli.config_commands.get_config") as mock:
        yield mock


class TestConfigCommands:
    """Tests for configuration commands."""

    def test_view_config(self, runner, mock_get_config, mock_config_value):
        """Test viewing configuration."""
        # Mock configuration
        mock_get_config.return_value = {
            "api": {
                "base_url": "https://example.com",
                "timeout": 30,
            },
            "logging": {
                "level": "INFO",
            },
        }

        # Test viewing full configuration
        result = runner.invoke(view_config, ["--format", "json"])
        assert result.exit_code == 0
        assert "https://example.com" in result.output
        assert "INFO" in result.output

        # Mock specific value
        mock_config_value.return_value = "https://example.com"

        # Test viewing specific key
        result = runner.invoke(view_config, ["--key", "api.base_url"])
        assert result.exit_code == 0
        assert "https://example.com" in result.output

        # Mock missing key
        mock_config_value.return_value = None

        # Test viewing missing key
        result = runner.invoke(view_config, ["--key", "missing.key"])
        assert result.exit_code == 0
        assert "not found" in result.output

    def test_set_config_value(self, runner, mock_set_config_value, mock_save_config, mock_config_manager):
        """Test setting configuration value."""
        # Mock config_file attribute
        mock_config_manager.config_file = "config.yaml"
        # Test setting string value
        result = runner.invoke(
            set_config_value_command, ["api.base_url", "https://example.com"]
        )
        assert result.exit_code == 0
        mock_set_config_value.assert_called_with("api.base_url", "https://example.com")
        # Check for the URL in the output, ignoring rich formatting
        assert "api.base_url" in result.output
        assert "https://example.com" in result.output

        # Test setting integer value
        result = runner.invoke(set_config_value_command, ["api.timeout", "30"])
        assert result.exit_code == 0
        mock_set_config_value.assert_called_with("api.timeout", 30)

        # Test setting boolean value
        result = runner.invoke(set_config_value_command, ["api.enabled", "true"])
        assert result.exit_code == 0
        mock_set_config_value.assert_called_with("api.enabled", True)

        # Test setting float value
        result = runner.invoke(set_config_value_command, ["api.version", "1.5"])
        assert result.exit_code == 0
        mock_set_config_value.assert_called_with("api.version", 1.5)

        # Test saving configuration
        result = runner.invoke(
            set_config_value_command, ["api.base_url", "https://example.com", "--save"]
        )
        assert result.exit_code == 0
        mock_save_config.assert_called_once()

    def test_validate_config(self, runner, mock_validate_config):
        """Test validating configuration."""
        # Mock validation results
        mock_validate_config.return_value = [
            ValidationResult(
                key="api.base_url",
                level=ValidationLevel.ERROR,
                message="URL is required",
                value=None,
            ),
            ValidationResult(
                key="api.timeout",
                level=ValidationLevel.WARNING,
                message="Timeout is too low",
                value=5,
            ),
            ValidationResult(
                key="logging.level",
                level=ValidationLevel.INFO,
                message="Default level is INFO",
                value="INFO",
            ),
        ]

        # Test validating with default level (warning)
        result = runner.invoke(validate_config_command)
        assert result.exit_code == 1  # Exit with error due to ERROR level
        assert "ERROR" in result.output
        assert "WARNING" in result.output
        assert "INFO" not in result.output

        # Test validating with info level
        result = runner.invoke(validate_config_command, ["--level", "info"])
        assert result.exit_code == 1  # Exit with error due to ERROR level
        assert "ERROR" in result.output
        assert "WARNING" in result.output
        assert "INFO" in result.output

        # Test validating with error level
        result = runner.invoke(validate_config_command, ["--level", "error"])
        assert result.exit_code == 1  # Exit with error due to ERROR level
        assert "ERROR" in result.output
        assert "WARNING" not in result.output
        assert "INFO" not in result.output

        # Mock valid configuration
        mock_validate_config.return_value = []

        # Test validating valid configuration
        result = runner.invoke(validate_config_command)
        assert result.exit_code == 0
        assert "valid" in result.output.lower()

    def test_init_config(self, runner, tmp_path):
        """Test initializing configuration."""
        # Create temporary file path
        config_file = tmp_path / "config.yaml"

        # Mock ConfigManager
        with patch("immuta_toolkit.cli.config_commands.ConfigManager") as mock_config_manager_class:
            # Create mock instance
            mock_manager = MagicMock()
            mock_config_manager_class.return_value = mock_manager

            # Test initializing configuration
            with runner.isolated_filesystem():
                result = runner.invoke(
                    init_config, ["--file", str(config_file), "--format", "yaml"]
                )
                assert result.exit_code == 0
                assert "initialized" in result.output.lower()
                mock_manager.load_defaults.assert_called_once()
                mock_manager.save.assert_called_once()

    def test_load_config(self, runner, mock_load_config):
        """Test loading configuration."""
        # Test loading from file
        result = runner.invoke(load_config_command, ["--file", "config.yaml"])
        assert result.exit_code == 0
        mock_load_config.assert_called_with(
            config_file="config.yaml",
            env_prefix="IMMUTA_",
            secrets_provider=None,
        )
        assert "loaded successfully" in result.output.lower()

        # Test loading from Azure Key Vault
        result = runner.invoke(
            load_config_command,
            [
                "--secrets-provider",
                "azure",
                "--vault-url",
                "https://example.vault.azure.net",
            ],
        )
        assert result.exit_code == 0
        mock_load_config.assert_called_with(
            config_file=None,
            env_prefix="IMMUTA_",
            secrets_provider="azure",
            vault_url="https://example.vault.azure.net",
        )

        # Test loading from AWS Secrets Manager
        result = runner.invoke(
            load_config_command,
            [
                "--secrets-provider",
                "aws",
                "--region",
                "us-east-1",
                "--secret-name",
                "immuta-config",
            ],
        )
        assert result.exit_code == 0
        mock_load_config.assert_called_with(
            config_file=None,
            env_prefix="IMMUTA_",
            secrets_provider="aws",
            region="us-east-1",
            secret_name="immuta-config",
        )

        # Test loading from HashiCorp Vault
        result = runner.invoke(
            load_config_command,
            [
                "--secrets-provider",
                "hashicorp",
                "--vault-url",
                "https://vault.example.com:8200",
            ],
        )
        assert result.exit_code == 0
        mock_load_config.assert_called_with(
            config_file=None,
            env_prefix="IMMUTA_",
            secrets_provider="hashicorp",
            vault_url="https://vault.example.com:8200",
        )

        # Test error with missing vault URL
        result = runner.invoke(load_config_command, ["--secrets-provider", "azure"])
        assert result.exit_code == 1
        assert "vault url is required" in result.output.lower()

        # Test error with missing vault URL for HashiCorp
        result = runner.invoke(load_config_command, ["--secrets-provider", "hashicorp"])
        assert result.exit_code == 1
        assert "vault url is required" in result.output.lower()


class TestProfileCommands:
    """Tests for profile commands."""

    def test_list_profiles(self, runner, mock_config_manager):
        """Test listing profiles."""
        # Mock profiles
        profiles = [
            ConfigProfile(
                name="development",
                type=ProfileType.ENVIRONMENT,
                description="Development environment",
                enabled=True,
                values={"api": {"base_url": "https://dev.example.com"}},
            ),
            ConfigProfile(
                name="production",
                type=ProfileType.ENVIRONMENT,
                description="Production environment",
                enabled=False,
                values={"api": {"base_url": "https://prod.example.com"}},
            ),
            ConfigProfile(
                name="admin",
                type=ProfileType.ROLE,
                description="Administrator role",
                enabled=True,
                values={"features": {"admin": True}},
            ),
        ]
        mock_config_manager.profile_manager.get_profiles.return_value = profiles
        mock_config_manager.profile_manager.get_profiles_by_type.side_effect = (
            lambda t: [p for p in profiles if p.type == t]
        )

        # Test listing all profiles
        result = runner.invoke(list_profiles)
        assert result.exit_code == 0
        assert "development" in result.output
        assert "production" in result.output
        assert "admin" in result.output
        assert "environment" in result.output
        assert "role" in result.output

        # Test listing environment profiles
        result = runner.invoke(list_profiles, ["--type", "environment"])
        assert result.exit_code == 0
        assert "development" in result.output
        assert "production" in result.output
        assert "admin" not in result.output

        # Test listing role profiles
        result = runner.invoke(list_profiles, ["--type", "role"])
        assert result.exit_code == 0
        assert "development" not in result.output
        assert "production" not in result.output
        assert "admin" in result.output

        # Test listing enabled profiles
        result = runner.invoke(list_profiles, ["--enabled-only"])
        assert result.exit_code == 0
        assert "development" in result.output
        assert "production" not in result.output
        assert "admin" in result.output

    def test_view_profile(self, runner, mock_config_manager):
        """Test viewing profile."""
        # Mock profile
        profile = ConfigProfile(
            name="development",
            type=ProfileType.ENVIRONMENT,
            description="Development environment",
            enabled=True,
            values={"api": {"base_url": "https://dev.example.com"}},
        )
        mock_config_manager.profile_manager.get_profile.return_value = profile

        # Test viewing profile
        result = runner.invoke(view_profile, ["development", "--format", "json"])
        assert result.exit_code == 0
        assert "development" in result.output
        assert "environment" in result.output
        assert "https://dev.example.com" in result.output

        # Test viewing non-existent profile
        mock_config_manager.profile_manager.get_profile.return_value = None
        result = runner.invoke(view_profile, ["non-existent"])
        assert result.exit_code == 1
        assert "not found" in result.output

    @patch("immuta_toolkit.cli.config_commands.save_config")
    def test_create_profile(self, mock_save_config, runner, mock_config_manager, tmp_path):
        """Test creating profile."""
        # Mock profile manager
        mock_config_manager.profile_manager.get_profile.return_value = None
        mock_config_manager.config_file = "config.yaml"

        # Test creating profile
        result = runner.invoke(
            create_profile,
            [
                "development",
                "--type",
                "environment",
                "--description",
                "Development environment",
                "--enabled",
            ],
        )
        assert result.exit_code == 0
        assert "created successfully" in result.output
        mock_config_manager.profile_manager.add_profile.assert_called_once()

        # Test creating profile with file
        config_file = tmp_path / "profile.json"
        config_file.write_text('{"api": {"base_url": "https://dev.example.com"}}')

        # Reset mock
        mock_config_manager.profile_manager.get_profile.return_value = None
        mock_config_manager.profile_manager.add_profile.reset_mock()

        result = runner.invoke(
            create_profile,
            [
                "production",
                "--type",
                "environment",
                "--file",
                str(config_file),
                "--save",
            ],
        )
        assert result.exit_code == 0
        assert "created successfully" in result.output
        mock_config_manager.profile_manager.add_profile.assert_called_once()
        mock_save_config.assert_called_once_with("config.yaml")

        # Test creating existing profile
        mock_config_manager.profile_manager.get_profile.return_value = MagicMock()
        result = runner.invoke(
            create_profile,
            [
                "development",
                "--type",
                "environment",
            ],
        )
        assert result.exit_code == 1
        assert "already exists" in result.output

    def test_update_profile(
        self, runner, mock_config_manager, mock_save_config, tmp_path
    ):
        """Test updating profile."""
        # Mock profile
        profile = MagicMock()
        mock_config_manager.profile_manager.get_profile.return_value = profile

        # Test updating profile description
        result = runner.invoke(
            update_profile,
            [
                "development",
                "--description",
                "Updated description",
            ],
        )
        assert result.exit_code == 0
        assert "updated successfully" in result.output
        assert profile.description == "Updated description"

        # Test updating profile enabled status
        result = runner.invoke(
            update_profile,
            [
                "development",
                "--enabled",
            ],
        )
        assert result.exit_code == 0
        assert "updated successfully" in result.output
        mock_config_manager.profile_manager.enable_profile.assert_called_with(
            "development"
        )

        # Test updating profile with file
        config_file = tmp_path / "profile.json"
        config_file.write_text('{"api": {"base_url": "https://dev.example.com"}}')

        result = runner.invoke(
            update_profile,
            [
                "development",
                "--file",
                str(config_file),
                "--save",
            ],
        )
        assert result.exit_code == 0
        assert "updated successfully" in result.output
        mock_save_config.assert_called_once()

        # Test updating non-existent profile
        mock_config_manager.profile_manager.get_profile.return_value = None
        result = runner.invoke(
            update_profile,
            [
                "non-existent",
                "--description",
                "Updated description",
            ],
        )
        assert result.exit_code == 1
        assert "not found" in result.output

    def test_delete_profile(self, runner, mock_config_manager, mock_save_config):
        """Test deleting profile."""
        # Mock profile
        profile = MagicMock()
        mock_config_manager.profile_manager.get_profile.return_value = profile

        # Test deleting profile with confirmation
        result = runner.invoke(
            delete_profile,
            [
                "development",
                "--force",
            ],
        )
        assert result.exit_code == 0
        assert "deleted successfully" in result.output
        mock_config_manager.profile_manager.remove_profile.assert_called_with(
            "development"
        )

        # Test deleting profile with save
        result = runner.invoke(
            delete_profile,
            [
                "development",
                "--force",
                "--save",
            ],
        )
        assert result.exit_code == 0
        assert "deleted successfully" in result.output
        mock_save_config.assert_called_once()

        # Test deleting non-existent profile
        mock_config_manager.profile_manager.get_profile.return_value = None
        result = runner.invoke(
            delete_profile,
            [
                "non-existent",
                "--force",
            ],
        )
        assert result.exit_code == 1
        assert "not found" in result.output

    def test_enable_profile(self, runner, mock_config_manager, mock_save_config):
        """Test enabling profile."""
        # Mock profile manager
        mock_config_manager.profile_manager.enable_profile.return_value = True

        # Test enabling profile
        result = runner.invoke(
            enable_profile,
            [
                "development",
            ],
        )
        assert result.exit_code == 0
        assert "enabled successfully" in result.output
        mock_config_manager.profile_manager.enable_profile.assert_called_with(
            "development"
        )

        # Test enabling profile with save
        result = runner.invoke(
            enable_profile,
            [
                "development",
                "--save",
            ],
        )
        assert result.exit_code == 0
        assert "enabled successfully" in result.output
        mock_save_config.assert_called_once()

        # Test enabling non-existent profile
        mock_config_manager.profile_manager.enable_profile.return_value = False
        result = runner.invoke(
            enable_profile,
            [
                "non-existent",
            ],
        )
        assert result.exit_code == 1
        assert "not found" in result.output

    def test_disable_profile(self, runner, mock_config_manager, mock_save_config):
        """Test disabling profile."""
        # Mock profile manager
        mock_config_manager.profile_manager.disable_profile.return_value = True

        # Test disabling profile
        result = runner.invoke(
            disable_profile,
            [
                "development",
            ],
        )
        assert result.exit_code == 0
        assert "disabled successfully" in result.output
        mock_config_manager.profile_manager.disable_profile.assert_called_with(
            "development"
        )

        # Test disabling profile with save
        result = runner.invoke(
            disable_profile,
            [
                "development",
                "--save",
            ],
        )
        assert result.exit_code == 0
        assert "disabled successfully" in result.output
        mock_save_config.assert_called_once()

        # Test disabling non-existent profile
        mock_config_manager.profile_manager.disable_profile.return_value = False
        result = runner.invoke(
            disable_profile,
            [
                "non-existent",
            ],
        )
        assert result.exit_code == 1
        assert "not found" in result.output

    def test_apply_profile(self, runner, mock_config_manager):
        """Test applying profile."""
        # Mock profile manager
        mock_config_manager.profile_manager.apply_profile.return_value = True

        # Test applying profile
        result = runner.invoke(
            apply_profile,
            [
                "development",
            ],
        )
        assert result.exit_code == 0
        assert "applied successfully" in result.output
        mock_config_manager.profile_manager.apply_profile.assert_called_with(
            "development"
        )

        # Test applying non-existent profile
        mock_config_manager.profile_manager.apply_profile.return_value = False
        result = runner.invoke(
            apply_profile,
            [
                "non-existent",
            ],
        )
        assert result.exit_code == 1
        assert "not found" in result.output
