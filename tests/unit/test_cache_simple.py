"""Simple unit tests for the cache utility."""

import time
from unittest.mock import patch

import pytest

from immuta_toolkit.utils.cache import (
    Cache,
    CacheEntry,
)


def test_cache_entry():
    """Test CacheEntry class."""
    # Create a cache entry with a TTL of 10 seconds
    entry = CacheEntry("test_value", 10)

    # Verify the value is stored correctly
    assert entry.value == "test_value"

    # Verify the entry is not expired immediately
    assert not entry.is_expired()

    # Manually set the created_at time to simulate expiration
    entry.created_at = time.time() - 11

    # Verify the entry is now expired
    assert entry.is_expired()


def test_cache_get_set():
    """Test Cache get and set methods."""
    # Create a cache with a name
    cache = Cache(name="test_cache", default_ttl_seconds=10)

    # Set a value in the cache
    cache.set("test_key", "test_value")

    # Get the value from the cache
    value = cache.get("test_key")

    # Verify the value is retrieved correctly
    assert value == "test_value"

    # Manually set the created_at time to simulate expiration
    cache.entries["test_key"].created_at = time.time() - 11

    # Get the value from the cache again
    value = cache.get("test_key")

    # Verify the value is no longer in the cache
    assert value is None
