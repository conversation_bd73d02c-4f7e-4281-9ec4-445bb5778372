"""Unit tests for data source service error handling."""

from unittest.mock import MagicMock

import pytest
import requests

from immuta_toolkit.services.data_source_service import DataSourceService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    return client


class TestDataSourceServiceErrorHandling:
    """Test error handling in the data source service."""

    def test_get_data_source_not_found(self, mock_client):
        """Test getting a data source that doesn't exist."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            data_source_service.get_data_source(
                999
            )  # Use an ID that doesn't exist in the mock data

        # Check the error message
        assert "Data source not found" in str(excinfo.value)

    def test_get_data_source_server_error(self, mock_client):
        """Test getting a data source with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            data_source_service.get_data_source(1)

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_get_data_source_connection_error(self, mock_client):
        """Test getting a data source with a connection error."""
        # Set up mock to raise a connection error
        mock_client.make_request.side_effect = requests.exceptions.ConnectionError(
            "Connection refused"
        )

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            data_source_service.get_data_source(1)

        # Check the error message
        assert "Connection refused" in str(excinfo.value)

    def test_list_data_sources_server_error(self, mock_client):
        """Test listing data sources with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            data_source_service.list_data_sources()

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_create_data_source_invalid_input(self, mock_client):
        """Test creating a data source with invalid input."""
        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method with invalid input
        with pytest.raises(ValueError) as excinfo:
            data_source_service.create_data_source({})  # Empty data source

        # Check the error message
        assert "Data source name is required" in str(excinfo.value)

    def test_create_data_source_server_error(self, mock_client):
        """Test creating a data source with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            data_source_service.create_data_source(
                {
                    "name": "Test Data Source",
                    "description": "Test data source",
                    "handler": {
                        "type": "PostgreSQL",
                        "connectionString": "postgresql://user:pass@localhost:5432/db",
                    },
                }
            )

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_update_data_source_not_found(self, mock_client):
        """Test updating a data source that doesn't exist."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise a 404 error
        mock_client.make_request.side_effect = Exception("Data source not found")

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            data_source_service.update_data_source(999, {"name": "Updated Data Source"})

        # Check the error message
        assert "Data source not found" in str(excinfo.value)

    def test_delete_data_source_not_found(self, mock_client):
        """Test deleting a data source that doesn't exist."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Mock the get_data_source method to raise a ValueError
        data_source_service.get_data_source = MagicMock(
            side_effect=ValueError("Data source not found: 999")
        )

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            data_source_service.delete_data_source(999)

        # Check the error message
        assert "Data source not found" in str(excinfo.value)
