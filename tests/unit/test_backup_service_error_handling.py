"""Unit tests for backup service error handling."""

import json
import os
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.backup_service import BackupService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.data_source_service = MagicMock()
    client.policy_service = MagicMock()
    client.user_service = MagicMock()
    client.purpose_service = MagicMock()
    client.project_service = MagicMock()
    client.tag_service = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestBackupServiceErrorHandling:
    """Test error handling in the backup service."""

    @patch("os.makedirs")
    def test_backup_data_sources_invalid_output_dir(self, mock_makedirs, mock_client):
        """Test backing up data sources with an invalid output directory."""
        # Set up mock to raise a ValueError
        mock_makedirs.side_effect = ValueError("Invalid output directory")

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with an invalid output directory
        with pytest.raises(ValueError) as excinfo:
            backup_service.backup_data_sources(
                output_dir="",  # Empty output directory
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message
        assert "Invalid output directory" in str(excinfo.value)

    def test_backup_data_sources_api_error(self, mock_client):
        """Test backing up data sources with an API error."""
        # Set up mock to raise an exception
        mock_client.data_source_service.list_data_sources.side_effect = Exception(
            "API error"
        )

        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            backup_service.backup_data_sources(
                output_dir="/tmp/backup",
                data_source_ids=None,  # Backup all data sources
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message
        assert "API error" in str(excinfo.value)

    def test_backup_data_sources_invalid_data_source_ids(self, mock_client):
        """Test backing up data sources with invalid data source IDs."""
        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid data source IDs in dry run mode
        result = backup_service.backup_data_sources(
            output_dir="/tmp/backup",
            data_source_ids="invalid",  # Not a list
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    @patch("os.makedirs")
    def test_backup_data_sources_permission_error(self, mock_makedirs, mock_client):
        """Test backing up data sources with a permission error."""
        # Set up mock to raise a permission error
        mock_makedirs.side_effect = PermissionError("Permission denied")

        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(PermissionError) as excinfo:
            backup_service.backup_data_sources(
                output_dir="/tmp/backup",
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message
        assert "Permission denied" in str(excinfo.value)

    def test_backup_policies_invalid_policy_ids(self, mock_client):
        """Test backing up policies with invalid policy IDs."""
        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid policy IDs in dry run mode
        result = backup_service.backup_policies(
            output_dir="/tmp/backup",
            policy_ids="invalid",  # Not a list
            policy_types=["RowRedaction"],
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    def test_backup_policies_invalid_policy_types(self, mock_client):
        """Test backing up policies with invalid policy types."""
        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid policy types in dry run mode
        result = backup_service.backup_policies(
            output_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types="invalid",  # Not a list
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    def test_backup_policies_api_error(self, mock_client):
        """Test backing up policies with an API error."""
        # Set up mock to raise an exception
        mock_client.policy_service.list_policies.side_effect = Exception("API error")

        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            backup_service.backup_policies(
                output_dir="/tmp/backup",
                policy_ids=None,  # Backup all policies
                policy_types=None,  # Backup all policy types
                dry_run=False,
            )

        # Check the error message
        assert "API error" in str(excinfo.value)

    def test_backup_users_invalid_user_ids(self, mock_client):
        """Test backing up users with invalid user IDs."""
        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid user IDs in dry run mode
        result = backup_service.backup_users(
            output_dir="/tmp/backup",
            user_ids="invalid",  # Not a list
            include_groups=True,
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    def test_backup_users_api_error(self, mock_client):
        """Test backing up users with an API error."""
        # Set up mock to raise an exception
        mock_client.user_service.list_users.side_effect = Exception("API error")

        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            backup_service.backup_users(
                output_dir="/tmp/backup",
                user_ids=None,  # Backup all users
                include_groups=True,
                dry_run=False,
            )

        # Check the error message
        assert "API error" in str(excinfo.value)

    def test_backup_purposes_invalid_purpose_ids(self, mock_client):
        """Test backing up purposes with invalid purpose IDs."""
        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid purpose IDs in dry run mode
        result = backup_service.backup_purposes(
            output_dir="/tmp/backup",
            purpose_ids="invalid",  # Not a list
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    def test_backup_purposes_api_error(self, mock_client):
        """Test backing up purposes with an API error."""
        # Set up mock to raise an exception
        mock_client.purpose_service.list_purposes.side_effect = Exception("API error")

        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            backup_service.backup_purposes(
                output_dir="/tmp/backup",
                purpose_ids=None,  # Backup all purposes
                dry_run=False,
            )

        # Check the error message
        assert "API error" in str(excinfo.value)

    def test_restore_data_sources_invalid_backup_dir(self, mock_client):
        """Test restoring data sources with an invalid backup directory."""
        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with an invalid backup directory
        with pytest.raises(ValueError) as excinfo:
            backup_service.restore_data_sources(
                backup_dir="",  # Empty backup directory
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message
        assert "Invalid backup directory" in str(excinfo.value)

    @patch("os.path.isdir")
    def test_restore_data_sources_missing_backup_dir(self, mock_isdir, mock_client):
        """Test restoring data sources with a missing backup directory."""
        # Set up mock to return False for directory existence
        mock_isdir.return_value = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with a non-existent backup directory
        with pytest.raises(ValueError) as excinfo:
            backup_service.restore_data_sources(
                backup_dir="/tmp/nonexistent",
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message
        assert "Invalid backup directory" in str(excinfo.value)

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_data_sources_invalid_data_source_ids(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring data sources with invalid data source IDs."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        }

        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid data source IDs in dry run mode
        result = backup_service.restore_data_sources(
            backup_dir="/tmp/backup",
            data_source_ids="invalid",  # Not a list
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_policies_invalid_policy_ids(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring policies with invalid policy IDs."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "policy_count": 3,
            "policy_types": ["RowRedaction", "ColumnMasking"],
        }

        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid policy IDs in dry run mode
        result = backup_service.restore_policies(
            backup_dir="/tmp/backup",
            policy_ids="invalid",  # Not a list
            policy_types=["RowRedaction"],
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_policies_invalid_policy_types(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring policies with invalid policy types."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "policy_count": 3,
            "policy_types": ["RowRedaction", "ColumnMasking"],
        }

        # Set up mock client to be in dry run mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method with invalid policy types in dry run mode
        result = backup_service.restore_policies(
            backup_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types="invalid",  # Not a list
            dry_run=True,  # Use dry run to avoid actual API calls
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]
