"""Unit tests for the backup CLI commands."""

import os
import tempfile
from unittest.mock import MagicMock, patch

import click.testing
import pytest

from immuta_toolkit.cli.backup_commands import backup_group, restore_group


@pytest.fixture
def cli_runner():
    """Create a Click CLI runner for testing."""
    return click.testing.CliRunner()


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    with patch("immuta_toolkit.cli.backup_commands.ImmutaClient") as mock:
        client_instance = MagicMock()
        mock.from_env.return_value = client_instance
        client_instance.backup_service = MagicMock()
        yield client_instance


class TestBackupCommands:
    """Test the backup CLI commands."""

    def test_backup_projects(self, cli_runner, mock_client):
        """Test the backup projects command."""
        # Set up mock
        mock_client.backup_service.backup_projects.return_value = {
            "status": "success",
            "message": "Backed up 2 projects to /tmp/backup",
            "project_count": 2,
            "backup_id": "test-backup-id",
        }

        # Run the command
        with patch("immuta_toolkit.cli.backup_commands.ImmutaClient.from_env", return_value=mock_client):
            result = cli_runner.invoke(
                backup_group,
                [
                    "projects",
                    "--output-dir",
                    "/tmp/backup",
                    "--project-ids",
                    "1",
                    "--project-ids",
                    "2",
                    "--include-members",
                    "--include-data-sources",
                    "--include-purposes",
                    "--retention-days",
                    "30",
                ],
            )

        # Verify the result
        assert result.exit_code == 0
        mock_client.backup_service.backup_projects.assert_called_once_with(
            output_dir="/tmp/backup",
            project_ids=[1, 2],
            include_members=True,
            include_data_sources=True,
            include_purposes=True,
            retention_days=30,
            dry_run=False,
        )

    def test_backup_projects_dry_run(self, cli_runner, mock_client):
        """Test the backup projects command with dry run."""
        # Set up mock
        mock_client.backup_service.backup_projects.return_value = {
            "status": "success",
            "message": "Dry run: Would backup projects to /tmp/backup",
            "project_count": 2,
        }

        # Run the command
        with patch("immuta_toolkit.cli.backup_commands.ImmutaClient.from_env", return_value=mock_client):
            result = cli_runner.invoke(
                backup_group,
                [
                    "projects",
                    "--output-dir",
                    "/tmp/backup",
                    "--dry-run",
                ],
            )

        # Verify the result
        assert result.exit_code == 0
        mock_client.backup_service.backup_projects.assert_called_once_with(
            output_dir="/tmp/backup",
            project_ids=None,
            include_members=True,
            include_data_sources=True,
            include_purposes=True,
            retention_days=30,
            dry_run=True,
        )

    def test_verify_backup(self, cli_runner, mock_client):
        """Test the verify backup command."""
        # Set up mock
        mock_client.backup_service.verify_backup.return_value = {
            "status": "success",
            "backup_info": True,
            "projects": True,
            "data_sources": True,
            "policies": True,
            "users": True,
            "purposes": True,
            "errors": [],
        }

        # Run the command
        with patch("immuta_toolkit.cli.backup_commands.ImmutaClient.from_env", return_value=mock_client):
            result = cli_runner.invoke(
                backup_group,
                [
                    "verify",
                    "--backup-dir",
                    "/tmp/backup",
                ],
            )

        # Verify the result
        assert result.exit_code == 0
        mock_client.backup_service.verify_backup.assert_called_once_with(
            backup_dir="/tmp/backup",
        )

    def test_restore_projects(self, cli_runner, mock_client):
        """Test the restore projects command."""
        # Set up mock
        mock_client.backup_service.restore_projects.return_value = {
            "status": "success",
            "message": "Restored 2 projects from /tmp/backup",
            "restored": 2,
            "failed": 0,
            "skipped": 0,
        }

        # Run the command
        with patch("immuta_toolkit.cli.backup_commands.ImmutaClient.from_env", return_value=mock_client):
            result = cli_runner.invoke(
                restore_group,
                [
                    "projects",
                    "--backup-dir",
                    "/tmp/backup",
                    "--project-ids",
                    "1",
                    "--project-ids",
                    "2",
                    "--restore-members",
                    "--restore-data-sources",
                    "--restore-purposes",
                ],
            )

        # Verify the result
        assert result.exit_code == 0
        mock_client.backup_service.restore_projects.assert_called_once_with(
            backup_dir="/tmp/backup",
            project_ids=[1, 2],
            restore_members=True,
            restore_data_sources=True,
            restore_purposes=True,
            dry_run=False,
        )

    def test_restore_projects_dry_run(self, cli_runner, mock_client):
        """Test the restore projects command with dry run."""
        # Set up mock
        mock_client.backup_service.restore_projects.return_value = {
            "status": "success",
            "message": "Dry run: Would restore projects from /tmp/backup",
            "project_count": 2,
        }

        # Run the command
        with patch("immuta_toolkit.cli.backup_commands.ImmutaClient.from_env", return_value=mock_client):
            result = cli_runner.invoke(
                restore_group,
                [
                    "projects",
                    "--backup-dir",
                    "/tmp/backup",
                    "--dry-run",
                ],
            )

        # Verify the result
        assert result.exit_code == 0
        mock_client.backup_service.restore_projects.assert_called_once_with(
            backup_dir="/tmp/backup",
            project_ids=None,
            restore_members=True,
            restore_data_sources=True,
            restore_purposes=True,
            dry_run=True,
        )
