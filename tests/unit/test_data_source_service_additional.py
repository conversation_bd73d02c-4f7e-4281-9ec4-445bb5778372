"""Additional unit tests for the data source service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.data_source_service import DataSourceService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.tag_service = MagicMock()
    client.storage = MagicMock()
    return client


class TestDataSourceServiceAdditional:
    """Additional tests for the data source service."""

    def test_get_data_source_by_name(self, mock_client):
        """Test getting a data source by name."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": 1,
            "name": "customer_data",
            "description": "Customer data",
            "columns": {"customer_id": {}, "email": {}, "address": {}},
        }
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_by_name("customer_data")

        # Check the result
        assert result["id"] == 1
        assert result["name"] == "customer_data"
        assert "customer_id" in result["columns"]

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "GET", "dataSource/name/customer_data"
        )

    def test_get_data_source_by_name_local_mode(self, mock_client):
        """Test getting a data source by name in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_by_name("customer_data")

        # Check the result
        assert result["id"] == 1
        assert result["name"] == "customer_data"
        assert "customer_id" in result["columns"]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_get_data_source_by_name_not_found_local_mode(self, mock_client):
        """Test getting a non-existent data source by name in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            data_source_service.get_data_source_by_name("nonexistent_data_source")

        # Check the error message
        assert "Data source not found" in str(excinfo.value)

    def test_subscribe_to_data_source(self, mock_client):
        """Test subscribing to a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.subscribe_to_data_source(1)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "dataSource/subscribe", data={"dataSourceId": 1}
        )

    def test_unsubscribe_from_data_source(self, mock_client):
        """Test unsubscribing from a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.unsubscribe_from_data_source(1)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "dataSource/1/unsubscribe"
        )

    def test_get_data_source_users(self, mock_client):
        """Test getting users with access to a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": 1,
                "email": "<EMAIL>",
                "name": "Admin User",
                "accessLevel": "owner",
            },
            {
                "id": 2,
                "email": "<EMAIL>",
                "name": "Regular User",
                "accessLevel": "subscriber",
            },
        ]
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_users(1)

        # Check the result
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[0]["accessLevel"] == "owner"
        assert result[1]["id"] == 2
        assert result[1]["accessLevel"] == "subscriber"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("GET", "dataSource/1/access")

    def test_add_user_to_data_source(self, mock_client):
        """Test adding a user to a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.add_user_to_data_source(1, 2, "owner")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "dataSource/1/access", data={"id": 2, "accessLevel": "owner"}
        )

    def test_change_user_data_source_access(self, mock_client):
        """Test changing a user's access level to a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.change_user_data_source_access(1, 2, "expert")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "PUT", "dataSource/1/access/2", data={"accessLevel": "expert"}
        )

    def test_remove_user_from_data_source(self, mock_client):
        """Test removing a user from a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.remove_user_from_data_source(1, 2)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "dataSource/1/access/2"
        )

    def test_get_pending_tasks(self, mock_client):
        """Test getting pending tasks."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": 1,
                "state": "pending",
                "type": "columnAdded",
                "reason": "New column detected",
                "dataSource": {"id": 1, "name": "customer_data"},
            }
        ]
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_pending_tasks()

        # Check the result
        assert len(result) == 1
        assert result[0]["id"] == 1
        assert result[0]["state"] == "pending"
        assert result[0]["type"] == "columnAdded"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "GET", "dataSource/tasks/pending"
        )

    def test_get_data_source_tasks(self, mock_client):
        """Test getting tasks for a specific data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": 1,
                "state": "pending",
                "type": "columnAdded",
                "reason": "New column detected",
                "dataSource": {"id": 1, "name": "customer_data"},
            }
        ]
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_tasks(1)

        # Check the result
        assert len(result) == 1
        assert result[0]["id"] == 1
        assert result[0]["state"] == "pending"
        assert result[0]["type"] == "columnAdded"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("GET", "dataSource/1/tasks")

    def test_complete_task(self, mock_client):
        """Test marking a task as complete."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "id": 1}
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.complete_task(1)

        # Check the result
        assert result["success"] is True
        assert result["id"] == 1

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("PUT", "dataSource/tasks/1")
