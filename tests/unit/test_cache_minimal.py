"""Minimal unit tests for the cache utility."""

import time
from unittest.mock import patch

import pytest

from immuta_toolkit.utils.cache import CacheEntry


def test_cache_entry():
    """Test CacheEntry class."""
    # Create a cache entry with a TTL of 10 seconds
    entry = CacheEntry("test_value", 10)

    # Verify the value is stored correctly
    assert entry.value == "test_value"

    # Verify the entry is not expired immediately
    assert not entry.is_expired()

    # Manually set the created_at time to simulate expiration
    entry.created_at = time.time() - 11

    # Verify the entry is now expired
    assert entry.is_expired()


# Create a simple mock cache for testing
class SimpleCache:
    def __init__(self):
        self.data = {}

    def set(self, key, value):
        self.data[key] = value

    def get(self, key):
        return self.data.get(key)

    def delete(self, key):
        if key in self.data:
            del self.data[key]
            return True
        return False

    def clear(self):
        self.data.clear()


def test_simple_cache():
    """Test simple cache implementation."""
    cache = SimpleCache()

    # Set a value
    cache.set("key1", "value1")

    # Get the value
    assert cache.get("key1") == "value1"

    # Delete the value
    assert cache.delete("key1") is True

    # Verify it's gone
    assert cache.get("key1") is None

    # Try to delete a non-existent key
    assert cache.delete("key2") is False

    # Set multiple values
    cache.set("key1", "value1")
    cache.set("key2", "value2")

    # Clear the cache
    cache.clear()

    # Verify all values are gone
    assert cache.get("key1") is None
    assert cache.get("key2") is None
