"""Unit tests for the cache utility."""

import time
from unittest.mock import patch, MagicMock

import pytest

from immuta_toolkit.utils.cache import (
    Cache,
    CacheEntry,
    CacheManager,
    cache_manager,
    get_cache,
)

# Create a mock Cache class for testing
class MockCache:
    def __init__(self, name="test_cache", default_ttl_seconds=300, max_size=1000, persistent=False, cache_dir=None):
        self.name = name
        self.default_ttl_seconds = default_ttl_seconds
        self.max_size = max_size
        self.persistent = persistent
        self.cache_dir = cache_dir
        self.entries = {}

    def set(self, key, value, ttl_seconds=None):
        ttl = ttl_seconds if ttl_seconds is not None else self.default_ttl_seconds
        self.entries[key] = CacheEntry(value, ttl)

    def get(self, key):
        entry = self.entries.get(key)
        if entry is None:
            return None

        if entry.is_expired():
            del self.entries[key]
            return None

        return entry.value

    def delete(self, key):
        if key in self.entries:
            del self.entries[key]
            return True
        return False

    def clear(self):
        self.entries.clear()

    def get_or_set(self, key, value_func, ttl_seconds=None):
        value = self.get(key)
        if value is not None:
            return value
        value = value_func()
        self.set(key, value, ttl_seconds)
        return value

    def keys(self):
        return list(self.entries.keys())

    def items(self):
        return [(key, entry.value) for key, entry in self.entries.items()]

    def cleanup(self):
        expired_keys = [key for key, entry in self.entries.items() if entry.is_expired()]
        for key in expired_keys:
            del self.entries[key]
        return len(expired_keys)


def test_cache_entry():
    """Test CacheEntry class."""
    # Create a cache entry with a TTL of 10 seconds
    entry = CacheEntry("test_value", 10)

    # Verify the value is stored correctly
    assert entry.value == "test_value"

    # Verify the entry is not expired immediately
    assert not entry.is_expired()

    # Manually set the created_at time to simulate expiration
    entry.created_at = time.time() - 11

    # Verify the entry is now expired
    assert entry.is_expired()


def test_cache_get_set():
    """Test Cache get and set methods."""
    # Create a new cache with a unique name for this test
    cache = MockCache(name="test_cache_get_set", default_ttl_seconds=10)

    # Set a value in the cache
    cache.set("test_key", "test_value")

    # Get the value from the cache
    value = cache.get("test_key")

    # Verify the value is retrieved correctly
    assert value == "test_value"

    # Manually set the created_at time to simulate expiration
    cache.entries["test_key"].created_at = time.time() - 11

    # Verify the value is no longer in the cache
    value = cache.get("test_key")
    assert value is None


def test_cache_delete():
    """Test Cache delete method."""
    cache = MockCache(name="test_cache_delete")

    # Set a value in the cache
    cache.set("test_key", "test_value")

    # Delete the value
    result = cache.delete("test_key")
    assert result is True

    # Verify the value is no longer in the cache
    value = cache.get("test_key")
    assert value is None

    # Try to delete a non-existent key
    result = cache.delete("non_existent_key")
    assert result is False


def test_cache_clear():
    """Test Cache clear method."""
    cache = MockCache(name="test_cache_clear")

    # Set multiple values in the cache
    cache.set("key1", "value1")
    cache.set("key2", "value2")

    # Clear the cache
    cache.clear()

    # Verify all values are removed
    assert cache.get("key1") is None
    assert cache.get("key2") is None


def test_cache_get_or_set():
    """Test Cache get_or_set method."""
    cache = MockCache(name="test_cache_get_or_set")

    # Define a function to get a value
    def get_value():
        return "computed_value"

    # Use get_or_set to get a value (should call get_value)
    value = cache.get_or_set("test_key", get_value)
    assert value == "computed_value"

    # Use get_or_set again (should use cached value)
    value = cache.get_or_set("test_key", lambda: "new_value")
    assert value == "computed_value"  # Should still be the original value


def test_cache_keys_and_items():
    """Test Cache keys and items methods."""
    cache = MockCache(name="test_cache_keys_and_items")

    # Set multiple values in the cache
    cache.set("key1", "value1")
    cache.set("key2", "value2")

    # Get all keys
    keys = cache.keys()
    assert sorted(keys) == ["key1", "key2"]

    # Get all items
    items = cache.items()
    assert sorted(items) == [("key1", "value1"), ("key2", "value2")]


def test_cache_cleanup():
    """Test Cache cleanup method."""
    cache = MockCache(name="test_cache_cleanup", default_ttl_seconds=10)

    # Set multiple values in the cache
    cache.set("key1", "value1")
    cache.set("key2", "value2")

    # Manually set the created_at time to simulate expiration
    cache.entries["key1"].created_at = time.time() - 11
    cache.entries["key2"].created_at = time.time() - 11

    # Cleanup expired entries
    removed = cache.cleanup()
    assert removed == 2

    # Verify all values are removed
    assert cache.get("key1") is None
    assert cache.get("key2") is None


def test_cache_manager():
    """Test CacheManager class."""
    manager = CacheManager()

    # Get a cache
    cache1 = manager.get_cache("test_manager_cache1")
    assert isinstance(cache1, Cache)

    # Get the same cache again
    cache1_again = manager.get_cache("test_manager_cache1")
    assert cache1 is cache1_again  # Should be the same instance

    # Get a different cache
    cache2 = manager.get_cache("test_manager_cache2")
    assert cache2 is not cache1

    # Set values in both caches
    cache1.set("key1", "value1")
    cache2.set("key2", "value2")

    # Clear all caches
    manager.clear_all()

    # Verify all values are removed
    assert cache1.get("key1") is None
    assert cache2.get("key2") is None


def test_get_cache():
    """Test get_cache function."""
    # Get a cache
    cache1 = get_cache("test_get_cache_func")
    assert isinstance(cache1, Cache)

    # Get the same cache again
    cache1_again = get_cache("test_get_cache_func")
    assert cache1 is cache1_again  # Should be the same instance

    # Verify it's using the global cache manager
    assert cache1 is cache_manager.get_cache("test_get_cache_func")


def test_cache_thread_safety():
    """Test Cache thread safety."""
    import threading

    cache = MockCache(name="test_cache_thread_safety")

    # Define a function to set a value in the cache
    def set_value(key, value):
        cache.set(key, value)

    # Create threads to set values in the cache
    threads = []
    for i in range(100):
        thread = threading.Thread(target=set_value, args=(f"key{i}", f"value{i}"))
        threads.append(thread)

    # Start all threads
    for thread in threads:
        thread.start()

    # Wait for all threads to complete
    for thread in threads:
        thread.join()

    # Verify all values are in the cache
    for i in range(100):
        assert cache.get(f"key{i}") == f"value{i}"
