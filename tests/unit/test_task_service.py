"""Unit tests for the task service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.task_service import TaskService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_get_pending_tasks(mock_client):
    """Test getting pending tasks."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "state": "pending",
            "type": "columnAdded",
            "reason": "New column detected",
            "dataSource": {"id": 1, "name": "customer_data"},
        },
        {
            "id": 2,
            "state": "pending",
            "type": "unmask",
            "reason": "Unmask request for sensitive data",
            "dataSource": {"id": 2, "name": "transaction_data"},
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test getting pending tasks
    tasks = task_service.get_pending_tasks()

    # Check result
    assert len(tasks) == 2
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"
    assert tasks[1]["id"] == 2
    assert tasks[1]["type"] == "unmask"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/tasks/pending", params={}
    )


def test_get_pending_tasks_with_filter(mock_client):
    """Test getting pending tasks with filter."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 2,
            "state": "pending",
            "type": "unmask",
            "reason": "Unmask request for sensitive data",
            "dataSource": {"id": 2, "name": "transaction_data"},
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test getting pending tasks with filter
    tasks = task_service.get_pending_tasks(task_types=["unmask"])

    # Check result
    assert len(tasks) == 1
    assert tasks[0]["id"] == 2
    assert tasks[0]["type"] == "unmask"

    # Check that request was made with filter
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/tasks/pending", params={"type": ["unmask"]}
    )


def test_get_pending_tasks_local(mock_client):
    """Test getting pending tasks in local mode."""
    # Create task service
    task_service = TaskService(mock_client)

    # Test getting pending tasks
    tasks = task_service.get_pending_tasks()

    # Check result
    assert len(tasks) == 2
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"
    assert tasks[1]["id"] == 2
    assert tasks[1]["type"] == "unmask"

    # Test getting pending tasks with filter
    tasks = task_service.get_pending_tasks(task_types=["unmask"])

    # Check result
    assert len(tasks) == 1
    assert tasks[0]["id"] == 2
    assert tasks[0]["type"] == "unmask"


def test_get_data_source_tasks(mock_client):
    """Test getting data source tasks."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "state": "pending",
            "type": "columnAdded",
            "reason": "New column detected",
            "dataSource": {"id": 10, "name": "customer_data"},
        },
        {
            "id": 2,
            "state": "pending",
            "type": "unmask",
            "reason": "Unmask request for sensitive data",
            "dataSource": {"id": 10, "name": "customer_data"},
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test getting data source tasks
    tasks = task_service.get_data_source_tasks(data_source_id=10)

    # Check result
    assert len(tasks) == 2
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"
    assert tasks[1]["id"] == 2
    assert tasks[1]["type"] == "unmask"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/tasks", params={}
    )


def test_get_data_source_tasks_with_filter(mock_client):
    """Test getting data source tasks with filter."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "state": "pending",
            "type": "columnAdded",
            "reason": "New column detected",
            "dataSource": {"id": 10, "name": "customer_data"},
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test getting data source tasks with filter
    tasks = task_service.get_data_source_tasks(
        data_source_id=10, task_types=["columnAdded"]
    )

    # Check result
    assert len(tasks) == 1
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"

    # Check that request was made with filter
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/tasks", params={"type": ["columnAdded"]}
    )


def test_get_data_source_tasks_local(mock_client):
    """Test getting data source tasks in local mode."""
    # Create task service
    task_service = TaskService(mock_client)

    # Test getting data source tasks
    tasks = task_service.get_data_source_tasks(data_source_id=10)

    # Check result
    assert len(tasks) == 2
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"
    assert tasks[1]["id"] == 2
    assert tasks[1]["type"] == "unmask"

    # Test getting data source tasks with filter
    tasks = task_service.get_data_source_tasks(
        data_source_id=10, task_types=["columnAdded"]
    )

    # Check result
    assert len(tasks) == 1
    assert tasks[0]["id"] == 1
    assert tasks[0]["type"] == "columnAdded"


def test_complete_task(mock_client):
    """Test completing a task."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    
    # Mock get task response
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": 1,
            "state": "pending",
            "type": "columnAdded",
            "reason": "New column detected",
            "dataSource": {"id": 10, "name": "customer_data"},
        },
        {
            "success": True,
            "id": 1,
            "message": "Task completed",
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test completing a task
    result = task_service.complete_task(task_id=1)

    # Check result
    assert result["success"] is True
    assert result["id"] == 1
    assert "message" in result

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "dataSource/tasks/1")
    mock_client.make_request.assert_any_call("PUT", "dataSource/tasks/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_complete_task_dry_run(mock_client):
    """Test completing a task in dry run mode."""
    # Create task service
    task_service = TaskService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test completing a task in dry run mode
    result = task_service.complete_task(task_id=1, dry_run=True)

    # Check result
    assert result["success"] is True
    assert result["id"] == 1
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "complete_task_dry_run",
        {"task_id": 1},
        "Success",
    )


def test_delete_task(mock_client):
    """Test deleting a task."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    
    # Mock get task response
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": 1,
            "state": "pending",
            "type": "columnAdded",
            "reason": "New column detected",
            "dataSource": {"id": 10, "name": "customer_data"},
        },
        {
            "success": True,
            "id": 1,
            "message": "Task deleted",
        },
    ]

    # Create task service
    task_service = TaskService(mock_client)

    # Test deleting a task
    result = task_service.delete_task(task_id=1)

    # Check result
    assert result["success"] is True
    assert result["id"] == 1
    assert "message" in result

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "dataSource/tasks/1")
    mock_client.make_request.assert_any_call("DELETE", "dataSource/tasks/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_task_dry_run(mock_client):
    """Test deleting a task in dry run mode."""
    # Create task service
    task_service = TaskService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test deleting a task in dry run mode
    result = task_service.delete_task(task_id=1, dry_run=True)

    # Check result
    assert result["success"] is True
    assert result["id"] == 1
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "delete_task_dry_run",
        {"task_id": 1},
        "Success",
    )
