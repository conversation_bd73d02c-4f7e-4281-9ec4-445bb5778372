"""Unit tests for the dictionary service."""

import json
import os
import tempfile
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest

from immuta_toolkit.services.dictionary_service import DictionaryService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_get_data_dictionary_local(mock_client):
    """Test getting data dictionary in local mode."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Test getting data dictionary
    dictionary = dictionary_service.get_data_dictionary(1)
    
    # Check dictionary
    assert "columns" in dictionary
    assert len(dictionary["columns"]) == 2
    assert dictionary["columns"][0]["name"] == "customer_id"
    assert dictionary["columns"][1]["name"] == "email"
    assert "pii" in dictionary["columns"][1]["tags"]


@patch("immuta_toolkit.services.dictionary_service.DictionaryService._backup_data_source")
def test_update_data_dictionary_local(mock_backup, mock_client):
    """Test updating data dictionary in local mode."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Test updating data dictionary
    dictionary = {
        "columns": [
            {
                "name": "customer_id",
                "description": "Updated description",
                "business_definition": "Updated business definition",
            }
        ]
    }
    
    result = dictionary_service.update_data_dictionary(
        data_source_id=1,
        dictionary=dictionary,
        backup=True,
        dry_run=False,
        validate=True,
    )
    
    # Check result
    assert result["status"] == "success"
    
    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "dictionary_update",
        {"data_source_id": 1},
        "Success",
    )
    
    # Check that backup was called
    mock_backup.assert_called_once_with(1)


def test_update_data_dictionary_dry_run(mock_client):
    """Test updating data dictionary in dry run mode."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Test updating data dictionary in dry run mode
    dictionary = {
        "columns": [
            {
                "name": "customer_id",
                "description": "Updated description",
                "business_definition": "Updated business definition",
            }
        ]
    }
    
    result = dictionary_service.update_data_dictionary(
        data_source_id=1,
        dictionary=dictionary,
        backup=True,
        dry_run=True,
        validate=True,
    )
    
    # Check result
    assert result["status"] == "dry_run_success"
    
    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "dictionary_update",
        {"data_source_id": 1, "dry_run": True},
        "Success",
    )


def test_validate_dictionary(mock_client):
    """Test validating dictionary."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Test with valid dictionary
    valid_dictionary = {
        "columns": [
            {
                "name": "customer_id",
                "description": "Customer ID",
                "business_definition": "Primary key for customer records",
            }
        ]
    }
    
    # This should not raise an exception
    dictionary_service._validate_dictionary(valid_dictionary)
    
    # Test with invalid dictionary (missing columns)
    invalid_dictionary = {}
    
    with pytest.raises(ValueError):
        dictionary_service._validate_dictionary(invalid_dictionary)
        
    # Test with invalid dictionary (missing name)
    invalid_dictionary = {
        "columns": [
            {
                "description": "Customer ID",
                "business_definition": "Primary key for customer records",
            }
        ]
    }
    
    with pytest.raises(ValueError):
        dictionary_service._validate_dictionary(invalid_dictionary)


def test_dataframe_to_dictionary(mock_client):
    """Test converting DataFrame to dictionary."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Create DataFrame
    df = pd.DataFrame({
        "name": ["customer_id", "email"],
        "description": ["Customer ID", "Customer email address"],
        "business_definition": ["Primary key for customer records", "Primary contact email"],
        "data_type": ["INTEGER", "VARCHAR"],
        "tags": ["identifier", "pii,contact"],
    })
    
    # Convert to dictionary
    dictionary = dictionary_service._dataframe_to_dictionary(df)
    
    # Check dictionary
    assert "columns" in dictionary
    assert len(dictionary["columns"]) == 2
    assert dictionary["columns"][0]["name"] == "customer_id"
    assert dictionary["columns"][0]["description"] == "Customer ID"
    assert dictionary["columns"][0]["business_definition"] == "Primary key for customer records"
    assert dictionary["columns"][0]["data_type"] == "INTEGER"
    assert dictionary["columns"][0]["tags"] == ["identifier"]
    assert dictionary["columns"][1]["name"] == "email"
    assert dictionary["columns"][1]["tags"] == ["pii", "contact"]


def test_dictionary_to_dataframe(mock_client):
    """Test converting dictionary to DataFrame."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Create dictionary
    dictionary = {
        "columns": [
            {
                "name": "customer_id",
                "description": "Customer ID",
                "business_definition": "Primary key for customer records",
                "data_type": "INTEGER",
                "tags": ["identifier"],
            },
            {
                "name": "email",
                "description": "Customer email address",
                "business_definition": "Primary contact email",
                "data_type": "VARCHAR",
                "tags": ["pii", "contact"],
            },
        ]
    }
    
    # Convert to DataFrame
    df = dictionary_service._dictionary_to_dataframe(dictionary)
    
    # Check DataFrame
    assert len(df) == 2
    assert list(df.columns) == ["name", "description", "business_definition", "data_type", "tags"]
    assert df.iloc[0]["name"] == "customer_id"
    assert df.iloc[1]["name"] == "email"
    assert df.iloc[0]["tags"] == "identifier"
    assert df.iloc[1]["tags"] == "pii,contact"


def test_import_export_data_dictionary(mock_client):
    """Test importing and exporting data dictionary."""
    # Create dictionary service
    dictionary_service = DictionaryService(mock_client)
    
    # Mock client methods
    mock_client.data_source_service.get_data_source.return_value = {
        "columns": [
            {
                "name": "customer_id",
                "description": "Customer ID",
                "businessDefinition": "Primary key for customer records",
                "dataType": "INTEGER",
                "tags": ["identifier"],
            },
            {
                "name": "email",
                "description": "Customer email address",
                "businessDefinition": "Primary contact email",
                "dataType": "VARCHAR",
                "tags": ["pii", "contact"],
            },
        ]
    }
    
    # Create temporary files
    with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as json_file, \
         tempfile.NamedTemporaryFile(suffix=".csv", delete=False) as csv_file, \
         tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as excel_file:
        json_path = json_file.name
        csv_path = csv_file.name
        excel_path = excel_file.name
    
    try:
        # Test exporting to JSON
        dictionary_service.export_data_dictionary(1, json_path, format="json")
        
        # Check that file was created
        assert os.path.exists(json_path)
        
        # Check file content
        with open(json_path, "r") as f:
            exported_json = json.load(f)
            assert "columns" in exported_json
            assert len(exported_json["columns"]) == 2
            
        # Test exporting to CSV
        dictionary_service.export_data_dictionary(1, csv_path, format="csv")
        
        # Check that file was created
        assert os.path.exists(csv_path)
        
        # Test exporting to Excel
        dictionary_service.export_data_dictionary(1, excel_path, format="excel")
        
        # Check that file was created
        assert os.path.exists(excel_path)
        
        # Test importing from JSON
        mock_client.reset_mock()
        dictionary_service.import_data_dictionary(1, json_path)
        
        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
        
    finally:
        # Clean up
        for path in [json_path, csv_path, excel_path]:
            if os.path.exists(path):
                os.unlink(path)
