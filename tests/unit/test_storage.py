"""Unit tests for the storage module."""

import json
import os
import tempfile
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.utils.storage import StorageManager


# This fixture is no longer needed as we're mocking directly in each test


@patch("azure.storage.blob.BlobServiceClient.from_connection_string")
def test_storage_manager_init(mock_from_connection_string):
    """Test storage manager initialization."""
    # Mock BlobServiceClient
    mock_client = MagicMock()
    mock_from_connection_string.return_value = mock_client

    # Test with default values (local mode)
    storage_manager = StorageManager(is_local=True)
    assert storage_manager.is_local is True
    assert storage_manager.connection_string is None
    assert storage_manager.container_name == "immuta-backups"

    # Test with custom values (Azure mode)
    storage_manager = StorageManager(
        is_local=False,
        connection_string="DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net",
        container_name="custom-container",
    )
    assert storage_manager.is_local is False
    assert (
        storage_manager.connection_string
        == "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net"
    )
    assert storage_manager.container_name == "custom-container"
    assert storage_manager.blob_service_client is not None


@patch("azure.storage.blob.BlobServiceClient.from_connection_string")
def test_storage_manager_init_with_connection_string(mock_from_connection_string):
    """Test storage manager initialization with connection string."""
    # Mock BlobServiceClient
    mock_client = MagicMock()
    mock_from_connection_string.return_value = mock_client

    # Create storage manager
    storage_manager = StorageManager(
        is_local=False,
        connection_string="DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net",
    )

    # Check that BlobServiceClient was created
    mock_from_connection_string.assert_called_once_with(
        "DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net"
    )
    assert storage_manager.blob_service_client == mock_client


def test_upload_file_local():
    """Test uploading a file in local mode."""
    # Create storage manager with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        storage_manager = StorageManager(is_local=True, local_dir=temp_dir)

        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"test content")
            temp_file_path = temp_file.name

        try:
            # Test uploading file
            storage_manager.upload_file(
                file_path=temp_file_path,
                blob_name="test-blob",
                metadata={"key": "value"},
            )

            # Check that file was copied to local directory
            local_path = os.path.join(temp_dir, "test-blob")
            assert os.path.exists(local_path)

            # Check file content
            with open(local_path, "rb") as f:
                content = f.read()
                assert content == b"test content"

            # Check metadata file
            metadata_path = os.path.join(temp_dir, "metadata.jsonl")
            assert os.path.exists(metadata_path)

            # Check metadata content
            with open(metadata_path, "r") as f:
                line = f.readline()
                metadata = json.loads(line)
                assert metadata["blob_name"] == "test-blob"
                assert metadata["key"] == "value"
        finally:
            # Clean up
            os.unlink(temp_file_path)


@patch("azure.storage.blob.BlobServiceClient.from_connection_string")
def test_upload_file_azure(mock_from_connection_string):
    """Test uploading a file to Azure Blob Storage."""
    # Mock BlobServiceClient
    mock_client = MagicMock()
    mock_from_connection_string.return_value = mock_client

    # Mock blob client
    mock_blob_client = MagicMock()
    mock_client.get_blob_client.return_value = mock_blob_client

    # Create storage manager
    storage_manager = StorageManager(
        is_local=False,
        connection_string="DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net",
    )

    # Create temporary file
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        temp_file.write(b"test content")
        temp_file_path = temp_file.name

    try:
        # Test uploading file
        storage_manager.upload_file(
            file_path=temp_file_path,
            blob_name="test-blob",
            metadata={"key": "value"},
        )

        # Check that blob client was retrieved
        mock_client.get_blob_client.assert_called_once_with(
            container="immuta-backups", blob="test-blob"
        )

        # Check that blob was uploaded
        mock_blob_client.upload_blob.assert_called_once()
    finally:
        # Clean up
        os.unlink(temp_file_path)


def test_download_file_local():
    """Test downloading a file in local mode."""
    # Create storage manager with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        storage_manager = StorageManager(is_local=True, local_dir=temp_dir)

        # Create a test blob in the local directory
        blob_content = b"test blob content"
        blob_name = "test-blob"
        blob_path = os.path.join(temp_dir, blob_name)
        with open(blob_path, "wb") as f:
            f.write(blob_content)

        # Create temporary file for output
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            output_path = temp_file.name

        try:
            # Test downloading file
            storage_manager.download_file(
                blob_name=blob_name,
                file_path=output_path,
            )

            # Check that file was downloaded with correct content
            with open(output_path, "rb") as f:
                content = f.read()
                assert content == blob_content
        finally:
            # Clean up
            os.unlink(output_path)


@patch("azure.storage.blob.BlobServiceClient.from_connection_string")
def test_download_file_azure(mock_from_connection_string):
    """Test downloading a file from Azure Blob Storage."""
    # Mock BlobServiceClient
    mock_client = MagicMock()
    mock_from_connection_string.return_value = mock_client

    # Mock blob client
    mock_blob_client = MagicMock()
    mock_client.get_blob_client.return_value = mock_blob_client

    # Mock download_blob
    class MockDownloadedBlob:
        def readall(self):
            return b"downloaded content"

    mock_blob_client.download_blob.return_value = MockDownloadedBlob()

    # Create storage manager
    storage_manager = StorageManager(
        is_local=False,
        connection_string="DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net",
    )

    # Create temporary file for output
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        output_path = temp_file.name

    try:
        # Test downloading file
        storage_manager.download_file(
            blob_name="test-blob",
            file_path=output_path,
        )

        # Check that blob client was retrieved
        mock_client.get_blob_client.assert_called_once_with(
            container="immuta-backups", blob="test-blob"
        )

        # Check that download_blob was called
        mock_blob_client.download_blob.assert_called_once()

        # Check file content
        with open(output_path, "rb") as f:
            content = f.read()
            assert content == b"downloaded content"
    finally:
        # Clean up
        os.unlink(output_path)


def test_list_blobs_local():
    """Test listing blobs in local mode."""
    # Create storage manager with a temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        storage_manager = StorageManager(is_local=True, local_dir=temp_dir)

        # Create test blobs
        blob_names = ["test-blob1", "test-blob2", "other-blob"]
        for name in blob_names:
            with open(os.path.join(temp_dir, name), "wb") as f:
                f.write(b"test content")

        # Create metadata file
        with open(os.path.join(temp_dir, "metadata.jsonl"), "w") as f:
            f.write(json.dumps({"blob_name": "test-blob1", "key1": "value1"}) + "\n")
            f.write(json.dumps({"blob_name": "test-blob2", "key2": "value2"}) + "\n")

        # Test listing blobs with prefix
        blobs = storage_manager.list_blobs(prefix="test-")

        # Check result
        assert len(blobs) == 2
        assert blobs[0]["name"] == "test-blob1"
        assert blobs[1]["name"] == "test-blob2"

        # Test listing all blobs
        all_blobs = storage_manager.list_blobs()
        assert len(all_blobs) == 3


@patch("azure.storage.blob.BlobServiceClient.from_connection_string")
def test_list_blobs_azure(mock_from_connection_string):
    """Test listing blobs from Azure Blob Storage."""
    # Mock BlobServiceClient
    mock_client = MagicMock()
    mock_from_connection_string.return_value = mock_client

    # Mock container client
    mock_container_client = MagicMock()
    mock_client.get_container_client.return_value = mock_container_client

    # Mock blob client
    mock_blob_client = MagicMock()
    mock_client.get_blob_client.return_value = mock_blob_client

    # Mock list_blobs
    class MockBlob:
        def __init__(self, name):
            self.name = name

    mock_container_client.list_blobs.return_value = [
        MockBlob("test-blob1"),
        MockBlob("test-blob2"),
    ]

    # Mock blob properties
    class MockProperties:
        def __init__(self, size, creation_time, metadata):
            self.size = size
            self.creation_time = creation_time
            self.metadata = metadata

    mock_blob_client.get_blob_properties.side_effect = [
        MockProperties(100, datetime.now(), {"key1": "value1"}),
        MockProperties(200, datetime.now(), {"key2": "value2"}),
    ]

    # Create storage manager
    storage_manager = StorageManager(
        is_local=False,
        connection_string="DefaultEndpointsProtocol=https;AccountName=test;AccountKey=key;EndpointSuffix=core.windows.net",
    )

    # Test listing blobs
    blobs = storage_manager.list_blobs(prefix="test-")

    # Check result
    assert len(blobs) == 2
    assert "test-blob1" in [blob["name"] for blob in blobs]
    assert "test-blob2" in [blob["name"] for blob in blobs]

    # Check that container client was retrieved
    mock_client.get_container_client.assert_called_once_with("immuta-backups")

    # Check that list_blobs was called
    mock_container_client.list_blobs.assert_called_once_with(name_starts_with="test-")
