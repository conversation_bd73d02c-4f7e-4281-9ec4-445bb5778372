"""Unit tests for the Snowflake audit logger."""

from unittest.mock import <PERSON><PERSON>ock, patch

import pytest

from immuta_toolkit.utils.snowflake import SnowflakeAuditLogger


@pytest.fixture
def mock_snowflake_pool():
    """Mock Snowflake connection pool."""
    pool = MagicMock()
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    mock_connection.cursor.return_value = mock_cursor
    pool.get_connection.return_value = mock_connection
    return pool


def test_snowflake_audit_logger_initialization(mock_snowflake_pool):
    """Test Snowflake audit logger initialization."""
    # Create audit logger
    logger = SnowflakeAuditLogger(
        connection_pool=mock_snowflake_pool,
        table_name="TEST_AUDIT_LOGS",
    )

    # Check attributes
    assert logger.connection_pool == mock_snowflake_pool
    assert logger.table_name == "TEST_AUDIT_LOGS"

    # Check that table was created
    mock_connection = mock_snowflake_pool.get_connection.return_value
    mock_cursor = mock_connection.cursor.return_value
    mock_cursor.execute.assert_called_once()
    # Updated to check for quoted table name
    assert (
        'CREATE TABLE IF NOT EXISTS "TEST_AUDIT_LOGS"'
        in mock_cursor.execute.call_args[0][0]
    )

    # Check that connection was released
    mock_snowflake_pool.release_connection.assert_called_once_with(mock_connection)


def test_snowflake_audit_logger_log_audit_event(mock_snowflake_pool):
    """Test logging an audit event."""
    # Create audit logger
    logger = SnowflakeAuditLogger(
        connection_pool=mock_snowflake_pool,
        table_name="TEST_AUDIT_LOGS",
    )

    # Reset mock calls from initialization
    mock_snowflake_pool.reset_mock()
    mock_connection = mock_snowflake_pool.get_connection.return_value
    mock_cursor = mock_connection.cursor.return_value
    mock_cursor.reset_mock()

    # Log audit event
    logger.log_audit_event(
        operation="TEST_OPERATION",
        status="SUCCESS",
        details={"key": "value", "email": "<EMAIL>"},
        error=None,
        user_id="test_user",
        client_ip="127.0.0.1",
    )

    # Check that connection was acquired
    mock_snowflake_pool.get_connection.assert_called_once()

    # Check that SQL was executed
    mock_cursor.execute.assert_called_once()
    sql = mock_cursor.execute.call_args[0][0]
    # Updated to check for quoted table name
    assert 'INSERT INTO "TEST_AUDIT_LOGS"' in sql

    # Check parameters
    params = mock_cursor.execute.call_args[0][1]
    assert params[0] == "TEST_OPERATION"
    assert params[1] == "SUCCESS"
    assert "key" in params[2]
    assert "value" in params[2]
    assert "u***@example.com" in params[2]  # Email should be sanitized
    assert params[3] is None
    assert params[4] == "test_user"
    assert params[5] == "127.0.0.1"

    # Check that cursor was closed
    mock_cursor.close.assert_called_once()

    # Check that connection was released
    mock_snowflake_pool.release_connection.assert_called_once_with(mock_connection)


def test_snowflake_audit_logger_log_audit_event_with_error(mock_snowflake_pool):
    """Test logging an audit event with an error."""
    # Create audit logger
    logger = SnowflakeAuditLogger(
        connection_pool=mock_snowflake_pool,
        table_name="TEST_AUDIT_LOGS",
    )

    # Reset mock calls from initialization
    mock_snowflake_pool.reset_mock()
    mock_connection = mock_snowflake_pool.get_connection.return_value
    mock_cursor = mock_connection.cursor.return_value
    mock_cursor.reset_mock()

    # Log audit event with error
    logger.log_audit_event(
        operation="TEST_OPERATION",
        status="FAILURE",
        details={"key": "value"},
        error="Test error message",
        user_id="test_user",
        client_ip="127.0.0.1",
    )

    # Check parameters
    params = mock_cursor.execute.call_args[0][1]
    assert params[0] == "TEST_OPERATION"
    assert params[1] == "FAILURE"
    assert params[3] == "Test error message"


def test_snowflake_audit_logger_sanitize_details():
    """Test sanitizing details."""
    # Create audit logger
    logger = SnowflakeAuditLogger(
        connection_pool=MagicMock(),
        table_name="TEST_AUDIT_LOGS",
    )

    # Test sanitizing email
    email = "<EMAIL>"
    sanitized_email = logger._sanitize_email(email)
    assert sanitized_email == "u***@example.com"

    # Test sanitizing details with email
    details = {"email": "<EMAIL>"}
    sanitized_details = logger._sanitize_details(details)
    assert sanitized_details["email"] == "u***@example.com"

    # Test sanitizing details with user_id
    details = {"user_id": 12345}
    sanitized_details = logger._sanitize_details(details)
    assert sanitized_details["user_id"].startswith("user_")

    # Test sanitizing nested details
    details = {
        "user": {
            "email": "<EMAIL>",
            "user_id": 12345,
        }
    }
    sanitized_details = logger._sanitize_details(details)
    assert sanitized_details["user"]["email"] == "u***@example.com"
    assert sanitized_details["user"]["user_id"].startswith("user_")


def test_snowflake_audit_logger_error_handling(mock_snowflake_pool):
    """Test error handling when logging an audit event."""
    # Create audit logger
    logger = SnowflakeAuditLogger(
        connection_pool=mock_snowflake_pool,
        table_name="TEST_AUDIT_LOGS",
    )

    # Reset mock calls from initialization
    mock_snowflake_pool.reset_mock()
    mock_connection = mock_snowflake_pool.get_connection.return_value
    mock_cursor = mock_connection.cursor.return_value

    # Make cursor.execute raise an exception
    mock_cursor.execute.side_effect = Exception("Test exception")

    # Log audit event (should not raise an exception)
    logger.log_audit_event(
        operation="TEST_OPERATION",
        status="SUCCESS",
        details={"key": "value"},
    )

    # Check that connection was released
    mock_snowflake_pool.release_connection.assert_called_once_with(mock_connection)
