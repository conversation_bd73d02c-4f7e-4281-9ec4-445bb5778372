"""Unit tests for project service purpose management methods."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestProjectServicePurposes:
    """Test project purpose management methods."""

    def test_get_project_purposes(self, mock_client):
        """Test getting project purposes."""
        # Set up mock response for get_project
        project_response = MagicMock()
        project_response.status_code = 200
        project_response.json.return_value = {
            "id": 1,
            "name": "Test Project",
            "description": "Test project description",
        }

        # Set up mock response for get_project_purposes
        purposes_response = MagicMock()
        purposes_response.status_code = 200
        purposes_response.json.return_value = [
            {"id": 1, "name": "Marketing", "description": "Marketing purpose"},
            {"id": 2, "name": "Analytics", "description": "Analytics purpose"},
        ]

        # Configure mock to return different responses for different calls
        mock_client.make_request.side_effect = [project_response, purposes_response]

        # Mock the cache to return None
        mock_cache = MagicMock()
        mock_cache.get.return_value = None

        # Create project service
        project_service = ProjectService(mock_client)
        project_service.cache = mock_cache

        # Call the method
        result = project_service.get_project_purposes(1)

        # Check the result - the implementation returns all purposes
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[0]["name"] == "Marketing"
        assert result[1]["id"] == 2
        assert result[1]["name"] == "Analytics"

        # Check that both API endpoints were called
        mock_client.make_request.assert_any_call("GET", "projects/1")
        mock_client.make_request.assert_any_call("GET", "projects/1/purposes")

    def test_get_project_purposes_local_mode(self, mock_client):
        """Test getting project purposes in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Mock the get_project method to return a project with purposes
        mock_project = {
            "id": 1,
            "name": "Test Project",
            "description": "Test project description",
            "purposes": [1, 2],
        }

        # Mock the cache to return None
        mock_cache = MagicMock()
        mock_cache.get.return_value = None

        # Create project service with a patched get_project method
        project_service = ProjectService(mock_client)
        project_service.cache = mock_cache
        project_service.get_project = MagicMock(return_value=mock_project)

        # Call the method
        result = project_service.get_project_purposes(1)

        # Check the result - in local mode, we get a purpose for each ID
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[1]["id"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_add_purpose_to_project(self, mock_client):
        """Test adding a purpose to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_purpose_to_project(1, 3)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/purposes", data={"purposeId": 3}
        )

    def test_add_purpose_to_project_dry_run(self, mock_client):
        """Test adding a purpose to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_purpose_to_project(1, 3, dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "purpose_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_add_purpose_to_project_local_mode(self, mock_client):
        """Test adding a purpose to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_purpose_to_project(1, 3)

        # Check the result - local mode returns additional info
        assert result["success"] is True
        assert "project_id" in result
        assert "purpose_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_purpose_from_project(self, mock_client):
        """Test removing a purpose from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_purpose_from_project(1, 2)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/purposes/2"
        )

    def test_remove_purpose_from_project_dry_run(self, mock_client):
        """Test removing a purpose from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_purpose_from_project(1, 2, dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "purpose_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_purpose_from_project_local_mode(self, mock_client):
        """Test removing a purpose from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_purpose_from_project(1, 2)

        # Check the result - local mode returns additional info
        assert result["success"] is True
        assert "project_id" in result
        assert "purpose_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
