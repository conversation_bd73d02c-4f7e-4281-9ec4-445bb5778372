"""Unit tests for the metrics module."""

import time
from unittest.mock import MagicMock, patch

import pytest
from prometheus_client import Counter, Gauge, Histogram, Summary

from immuta_toolkit.utils.metrics import (
    API_REQUESTS,
    API_REQUEST_DURATION,
    BACKUP_DURATION,
    BACKUP_SIZE,
    CACHE_HITS,
    CACHE_MISSES,
    CACHE_SIZE,
    OPERATION_DURATION,
    OPERATION_ERRORS,
    RESTORE_DURATION,
    start_metrics_server,
    track_api_request,
    track_backup,
    track_cache_hit,
    track_cache_miss,
    track_operation_time,
    track_restore,
    update_cache_size,
)


@patch("immuta_toolkit.utils.metrics.start_http_server")
def test_start_metrics_server(mock_start_http_server):
    """Test starting the metrics server."""
    # Call the function
    start_metrics_server(port=8000)

    # Check that the server was started
    mock_start_http_server.assert_called_once_with(8000)

    # Test with exception
    mock_start_http_server.side_effect = Exception("Test error")
    start_metrics_server(port=8000)  # Should not raise exception


@patch("immuta_toolkit.utils.metrics.API_REQUESTS.labels")
@patch("immuta_toolkit.utils.metrics.API_REQUEST_DURATION.labels")
def test_track_api_request(mock_duration_labels, mock_requests_labels):
    """Test tracking API requests."""
    # Set up mocks
    mock_inc = MagicMock()
    mock_requests_labels.return_value.inc = mock_inc

    mock_observe = MagicMock()
    mock_duration_labels.return_value.observe = mock_observe

    # Call the function
    track_api_request("GET", "users", "200", 0.5)

    # Check that the metrics were updated
    mock_requests_labels.assert_called_once_with(
        method="GET", endpoint="users", status="200"
    )
    mock_inc.assert_called_once()

    mock_duration_labels.assert_called_once_with(method="GET", endpoint="users")
    mock_observe.assert_called_once_with(0.5)


@patch("immuta_toolkit.utils.metrics.CACHE_HITS.labels")
def test_track_cache_hit(mock_labels):
    """Test tracking cache hits."""
    # Set up mock
    mock_inc = MagicMock()
    mock_labels.return_value.inc = mock_inc

    # Call the function
    track_cache_hit("test_cache")

    # Check that the metric was updated
    mock_labels.assert_called_once_with(cache_name="test_cache")
    mock_inc.assert_called_once()


@patch("immuta_toolkit.utils.metrics.CACHE_MISSES.labels")
def test_track_cache_miss(mock_labels):
    """Test tracking cache misses."""
    # Set up mock
    mock_inc = MagicMock()
    mock_labels.return_value.inc = mock_inc

    # Call the function
    track_cache_miss("test_cache")

    # Check that the metric was updated
    mock_labels.assert_called_once_with(cache_name="test_cache")
    mock_inc.assert_called_once()


@patch("immuta_toolkit.utils.metrics.CACHE_SIZE.labels")
def test_update_cache_size(mock_labels):
    """Test updating cache size."""
    # Set up mock
    mock_set = MagicMock()
    mock_labels.return_value.set = mock_set

    # Call the function
    update_cache_size("test_cache", 10)

    # Check that the metric was updated
    mock_labels.assert_called_once_with(cache_name="test_cache")
    mock_set.assert_called_once_with(10)


@patch("immuta_toolkit.utils.metrics.BACKUP_SIZE.labels")
@patch("immuta_toolkit.utils.metrics.BACKUP_DURATION.labels")
def test_track_backup(mock_duration_labels, mock_size_labels):
    """Test tracking backup operations."""
    # Set up mocks
    mock_set = MagicMock()
    mock_size_labels.return_value.set = mock_set

    mock_observe = MagicMock()
    mock_duration_labels.return_value.observe = mock_observe

    # Call the function
    track_backup("users", 1024, 5.0)

    # Check that the metrics were updated
    mock_size_labels.assert_called_once_with(backup_type="users")
    mock_set.assert_called_once_with(1024)

    mock_duration_labels.assert_called_once_with(backup_type="users")
    mock_observe.assert_called_once_with(5.0)


@patch("immuta_toolkit.utils.metrics.RESTORE_DURATION.labels")
def test_track_restore(mock_labels):
    """Test tracking restore operations."""
    # Set up mock
    mock_observe = MagicMock()
    mock_labels.return_value.observe = mock_observe

    # Call the function
    track_restore("users", 3.0)

    # Check that the metric was updated
    mock_labels.assert_called_once_with(restore_type="users")
    mock_observe.assert_called_once_with(3.0)


@patch.object(OPERATION_DURATION, "labels")
@patch.object(OPERATION_ERRORS, "labels")
def test_track_operation_time_decorator(mock_error_labels, mock_duration_labels):
    """Test the track_operation_time decorator."""
    # Set up mocks
    mock_observe = MagicMock()
    mock_duration_labels.return_value.observe = mock_observe

    mock_inc = MagicMock()
    mock_error_labels.return_value.inc = mock_inc

    # Define a test function with the decorator
    @track_operation_time("test_operation")
    def test_function(arg1, arg2=None):
        """Test function."""
        if arg1 == "error":
            raise ValueError("Test error")
        return arg1 + (arg2 or "")

    # Call the function successfully
    result = test_function("hello", arg2=" world")

    # Check that the result is correct
    assert result == "hello world"

    # Check that the duration metric was updated
    mock_duration_labels.assert_called_once_with(operation="test_operation")
    mock_observe.assert_called_once()

    # Call the function with an error
    with pytest.raises(ValueError):
        test_function("error")

    # Check that the error metric was updated
    mock_error_labels.assert_called_once_with(
        operation="test_operation", error_type="ValueError"
    )
    mock_inc.assert_called_once()


def test_metrics_initialization():
    """Test that metrics are initialized correctly."""
    # Check that the metrics are initialized
    assert isinstance(API_REQUESTS, Counter)
    assert isinstance(API_REQUEST_DURATION, Histogram)
    assert isinstance(OPERATION_DURATION, Histogram)
    assert isinstance(OPERATION_ERRORS, Counter)
    assert isinstance(CACHE_HITS, Counter)
    assert isinstance(CACHE_MISSES, Counter)
    assert isinstance(CACHE_SIZE, Gauge)
    assert isinstance(BACKUP_SIZE, Gauge)
    assert isinstance(BACKUP_DURATION, Histogram)
    assert isinstance(RESTORE_DURATION, Histogram)
