"""Unit tests for project service tag management methods."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestProjectServiceTags:
    """Test project tag management methods."""

    def test_list_project_tags(self, mock_client):
        """Test listing project tags."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {"name": "analytics", "value": "true"},
            {"name": "customer", "value": "true"},
        ]
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.list_project_tags(1)

        # Check the result
        assert len(result) == 2
        assert result[0]["name"] == "analytics"
        assert result[1]["name"] == "customer"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("GET", "projects/1/tags")

    def test_list_project_tags_local_mode(self, mock_client):
        """Test listing project tags in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Mock the cache to return a specific value
        mock_cache = MagicMock()
        mock_cache.get.return_value = None
        project_service.cache = mock_cache

        # Call the method
        result = project_service.list_project_tags(1)

        # Check the result
        assert len(result) == 2
        assert result[0] == "analytics"
        assert result[1] == "customer"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_add_tag_to_project(self, mock_client):
        """Test adding a tag to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_tag_to_project(1, "security")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/tags", data={"name": "security"}
        )

    def test_add_tag_to_project_dry_run(self, mock_client):
        """Test adding a tag to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_tag_to_project(1, "security", dry_run=True)

        # Check the result
        assert result["status"] == "dry_run_success"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_add_tag_to_project_local_mode(self, mock_client):
        """Test adding a tag to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_tag_to_project(1, "security")

        # Check the result
        assert result["status"] == "success"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_tag_from_project(self, mock_client):
        """Test removing a tag from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_tag_from_project(1, "customer")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/tags/customer"
        )

    def test_remove_tag_from_project_dry_run(self, mock_client):
        """Test removing a tag from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_tag_from_project(1, "customer", dry_run=True)

        # Check the result
        assert result["status"] == "dry_run_success"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_tag_from_project_local_mode(self, mock_client):
        """Test removing a tag from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_tag_from_project(1, "customer")

        # Check the result
        assert result["status"] == "success"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
