"""Unit tests for the backup service."""

import json
import os
import tempfile
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.backup_service import BackupService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.data_source_service = MagicMock()
    client.policy_service = MagicMock()
    client.user_service = MagicMock()
    client.purpose_service = MagicMock()
    client.tag_service = MagicMock()
    return client


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


def test_backup_data_sources_dry_run(mock_client, temp_dir):
    """Test backing up data sources in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up data sources in dry run mode
    result = backup_service.backup_data_sources(
        output_dir=temp_dir,
        data_source_ids=[1, 2],
        include_policies=True,
        include_tags=True,
        include_metadata=True,
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["data_source_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_data_sources_dry_run",
        {
            "output_dir": temp_dir,
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        },
        "Success",
    )


def test_backup_data_sources_local(mock_client, temp_dir):
    """Test backing up data sources in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up data sources in local mode
    result = backup_service.backup_data_sources(
        output_dir=temp_dir,
        data_source_ids=[1, 2],
        include_policies=True,
        include_tags=True,
        include_metadata=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["data_source_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_data_sources",
        {
            "output_dir": temp_dir,
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        },
        "Success",
    )


def test_backup_policies_dry_run(mock_client, temp_dir):
    """Test backing up policies in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up policies in dry run mode
    result = backup_service.backup_policies(
        output_dir=temp_dir,
        policy_ids=[1, 2],
        policy_types=["subscription", "masking"],
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["policy_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_policies_dry_run",
        {
            "output_dir": temp_dir,
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
        },
        "Success",
    )


def test_backup_policies_local(mock_client, temp_dir):
    """Test backing up policies in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up policies in local mode
    result = backup_service.backup_policies(
        output_dir=temp_dir,
        policy_ids=[1, 2],
        policy_types=["subscription", "masking"],
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["policy_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_policies",
        {
            "output_dir": temp_dir,
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
        },
        "Success",
    )


def test_backup_users_dry_run(mock_client, temp_dir):
    """Test backing up users in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up users in dry run mode
    result = backup_service.backup_users(
        output_dir=temp_dir,
        user_ids=[1, 2],
        include_groups=True,
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["user_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_users_dry_run",
        {
            "output_dir": temp_dir,
            "user_count": 2,
            "include_groups": True,
        },
        "Success",
    )


def test_backup_users_local(mock_client, temp_dir):
    """Test backing up users in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up users in local mode
    result = backup_service.backup_users(
        output_dir=temp_dir,
        user_ids=[1, 2],
        include_groups=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["user_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_users",
        {
            "output_dir": temp_dir,
            "user_count": 2,
            "include_groups": True,
        },
        "Success",
    )


def test_backup_purposes_dry_run(mock_client, temp_dir):
    """Test backing up purposes in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up purposes in dry run mode
    result = backup_service.backup_purposes(
        output_dir=temp_dir,
        purpose_ids=[1, 2],
        include_subscribers=True,
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["purpose_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_purposes_dry_run",
        {
            "output_dir": temp_dir,
            "purpose_count": 2,
            "include_subscribers": True,
        },
        "Success",
    )


def test_backup_purposes_local(mock_client, temp_dir):
    """Test backing up purposes in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up purposes in local mode
    result = backup_service.backup_purposes(
        output_dir=temp_dir,
        purpose_ids=[1, 2],
        include_subscribers=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["purpose_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_purposes",
        {
            "output_dir": temp_dir,
            "purpose_count": 2,
            "include_subscribers": True,
        },
        "Success",
    )


def test_backup_all_dry_run(mock_client, temp_dir):
    """Test backing up all configurations in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up all configurations in dry run mode
    result = backup_service.backup_all(
        output_dir=temp_dir,
        include_data_sources=True,
        include_policies=True,
        include_users=True,
        include_purposes=True,
        include_tags=True,
        include_metadata=True,
        include_groups=True,
        include_subscribers=True,
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_all_dry_run",
        {
            "output_dir": temp_dir,
            "include_data_sources": True,
            "include_policies": True,
            "include_users": True,
            "include_purposes": True,
        },
        "Success",
    )


def test_backup_all_local(mock_client, temp_dir):
    """Test backing up all configurations in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Test backing up all configurations in local mode
    result = backup_service.backup_all(
        output_dir=temp_dir,
        include_data_sources=True,
        include_policies=True,
        include_users=True,
        include_purposes=True,
        include_tags=True,
        include_metadata=True,
        include_groups=True,
        include_subscribers=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "backup_all",
        {
            "output_dir": temp_dir,
            "include_data_sources": True,
            "include_policies": True,
            "include_users": True,
            "include_purposes": True,
        },
        "Success",
    )


def test_restore_data_sources_dry_run(mock_client, temp_dir):
    """Test restoring data sources in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Create backup directory structure
    os.makedirs(os.path.join(temp_dir, "data_sources"), exist_ok=True)
    with open(os.path.join(temp_dir, "backup_info.json"), "w") as f:
        json.dump({"timestamp": 123456789}, f)

    # Test restoring data sources in dry run mode
    result = backup_service.restore_data_sources(
        backup_dir=temp_dir,
        data_source_ids=[1, 2],
        include_policies=True,
        include_tags=True,
        include_metadata=True,
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["data_source_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "restore_data_sources_dry_run",
        {
            "backup_dir": temp_dir,
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        },
        "Success",
    )


def test_restore_data_sources_local(mock_client, temp_dir):
    """Test restoring data sources in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Create backup directory structure
    os.makedirs(os.path.join(temp_dir, "data_sources"), exist_ok=True)
    with open(os.path.join(temp_dir, "backup_info.json"), "w") as f:
        json.dump({"timestamp": 123456789}, f)

    # Test restoring data sources in local mode
    result = backup_service.restore_data_sources(
        backup_dir=temp_dir,
        data_source_ids=[1, 2],
        include_policies=True,
        include_tags=True,
        include_metadata=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["data_source_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "restore_data_sources",
        {
            "backup_dir": temp_dir,
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        },
        "Success",
    )


def test_restore_policies_dry_run(mock_client, temp_dir):
    """Test restoring policies in dry run mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Create backup directory structure
    os.makedirs(os.path.join(temp_dir, "policies"), exist_ok=True)
    with open(os.path.join(temp_dir, "backup_info.json"), "w") as f:
        json.dump({"timestamp": 123456789}, f)
    with open(os.path.join(temp_dir, "policies", "all_policies.json"), "w") as f:
        json.dump([], f)

    # Test restoring policies in dry run mode
    result = backup_service.restore_policies(
        backup_dir=temp_dir,
        policy_ids=[1, 2],
        policy_types=["subscription", "masking"],
        dry_run=True,
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["policy_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "restore_policies_dry_run",
        {
            "backup_dir": temp_dir,
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
        },
        "Success",
    )


def test_restore_policies_local(mock_client, temp_dir):
    """Test restoring policies in local mode."""
    # Create backup service
    backup_service = BackupService(mock_client)

    # Create backup directory structure
    os.makedirs(os.path.join(temp_dir, "policies"), exist_ok=True)
    with open(os.path.join(temp_dir, "backup_info.json"), "w") as f:
        json.dump({"timestamp": 123456789}, f)
    with open(os.path.join(temp_dir, "policies", "all_policies.json"), "w") as f:
        json.dump([], f)

    # Test restoring policies in local mode
    result = backup_service.restore_policies(
        backup_dir=temp_dir,
        policy_ids=[1, 2],
        policy_types=["subscription", "masking"],
    )

    # Check result
    assert result["status"] == "success"
    assert "message" in result
    assert result["policy_count"] == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "restore_policies",
        {
            "backup_dir": temp_dir,
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
        },
        "Success",
    )
