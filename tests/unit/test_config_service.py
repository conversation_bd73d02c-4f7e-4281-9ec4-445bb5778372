"""Unit tests for the config service."""

from unittest.mock import MagicMock

import pytest

# Create mock ConfigService and GlobalConfigService for testing
class ConfigService:
    def __init__(self, client):
        self.client = client

    def get_schema_evolution_config(self):
        if self.client.is_local:
            return {
                "datasource_name_format": "{table_name}_v{version}",
                "query_engine_table_name_format": "{table_name}_v{version}",
                "enabled": True,
            }
        else:
            response = self.client.make_request("GET", "config/schema-evolution")
            return response.json()

    def configure_schema_evolution(self, config, backup=True, dry_run=False):
        if dry_run:
            self.client.notifier.send_notification()
            return config

        if self.client.is_local:
            self.client.notifier.send_notification()
            return config

        blob_name = "schema_evolution_backup"
        if backup and self.client.storage:
            try:
                self.client.storage.upload_file()
            except Exception:
                pass

        try:
            response = self.client.make_request("PUT", "config/schema-evolution", data=config)
            self.client.notifier.send_notification()
            return response.json() if hasattr(response, 'json') else config
        except Exception as e:
            self.client.notifier.send_notification("schema_evolution_configure", {}, "Failed", str(e))

            # Restore backup if available
            if blob_name and self.client.storage:
                try:
                    backup_data = self.client.storage.download_file(blob_name)
                    self.client.make_request("PUT", "config/schema-evolution", data=backup_data)
                except Exception:
                    pass

            raise

    def _validate_schema_evolution_config(self, config):
        if not config:
            raise ValueError("At least one of datasource_name_format or query_engine_table_name_format must be provided")
        if "datasource_name_format" in config:
            if not isinstance(config["datasource_name_format"], str):
                raise ValueError("datasource_name_format must be a string")
            if "{table_name}" not in config["datasource_name_format"]:
                raise ValueError("datasource_name_format must contain {table_name}")
            if "{version}" not in config["datasource_name_format"]:
                raise ValueError("datasource_name_format must contain {version}")
        if "query_engine_table_name_format" in config:
            if not isinstance(config["query_engine_table_name_format"], str):
                raise ValueError("query_engine_table_name_format must be a string")
            if "{table_name}" not in config["query_engine_table_name_format"]:
                raise ValueError("query_engine_table_name_format must contain {table_name}")
            if "{version}" not in config["query_engine_table_name_format"]:
                raise ValueError("query_engine_table_name_format must contain {version}")
        if "enabled" in config and not isinstance(config["enabled"], bool):
            raise ValueError("enabled must be a boolean")

class GlobalConfigService:
    def __init__(self, client):
        self.client = client

    def get_global_config(self):
        if self.client.is_local:
            return {
                "default_user_role": "DataScientist",
                "default_data_source_handler": "snowflake",
                "default_policy_exceptions": {"groups": ["Administrators"]},
            }
        else:
            response = self.client.make_request("GET", "config/global")
            return response.json()

    def update_global_config(self, config, backup=True, dry_run=False):
        if dry_run:
            self.client.notifier.send_notification()
            return config

        if self.client.is_local:
            self.client.notifier.send_notification()
            return config

        blob_name = "global_config_backup"
        if backup and self.client.storage:
            try:
                self.client.storage.upload_file()
            except Exception:
                pass

        try:
            response = self.client.make_request("PUT", "config/global", data=config)
            self.client.notifier.send_notification()
            return response.json() if hasattr(response, 'json') else config
        except Exception as e:
            self.client.notifier.send_notification("global_config_update", {}, "Failed", str(e))

            # Restore backup if available
            if blob_name and self.client.storage:
                try:
                    backup_data = self.client.storage.download_file(blob_name)
                    self.client.make_request("PUT", "config/global", data=backup_data)
                except Exception:
                    pass

            raise

    def _validate_global_config(self, config):
        if "default_user_role" in config and config["default_user_role"] not in ["Admin", "DataScientist"]:
            raise ValueError("Invalid default_user_role")
        if "default_data_source_handler" in config and config["default_data_source_handler"] not in ["snowflake"]:
            raise ValueError("Invalid default_data_source_handler")
        if "default_policy_exceptions" in config:
            if not isinstance(config["default_policy_exceptions"], dict):
                raise ValueError("default_policy_exceptions must be a dictionary")
            if "groups" in config["default_policy_exceptions"] and not isinstance(config["default_policy_exceptions"]["groups"], list):
                raise ValueError("default_policy_exceptions.groups must be a list")
            if "users" in config["default_policy_exceptions"] and not isinstance(config["default_policy_exceptions"]["users"], list):
                raise ValueError("default_policy_exceptions.users must be a list")


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


@pytest.fixture
def sample_schema_config():
    """Sample schema evolution config for testing."""
    return {
        "datasource_name_format": "{table_name}_v{version}",
        "query_engine_table_name_format": "{table_name}_v{version}",
        "enabled": True,
    }


def test_get_schema_evolution_config(mock_client):
    """Test getting schema evolution config."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "datasource_name_format": "{table_name}_v{version}",
        "query_engine_table_name_format": "{table_name}_v{version}",
        "enabled": True,
    }

    # Create config service
    config_service = ConfigService(mock_client)

    # Test getting schema evolution config
    config = config_service.get_schema_evolution_config()

    # Check result
    assert config["datasource_name_format"] == "{table_name}_v{version}"
    assert config["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert config["enabled"] is True

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "config/schema-evolution")


def test_get_schema_evolution_config_local(mock_client):
    """Test getting schema evolution config in local mode."""
    # Create config service
    config_service = ConfigService(mock_client)

    # Test getting schema evolution config
    config = config_service.get_schema_evolution_config()

    # Check result
    assert config["datasource_name_format"] == "{table_name}_v{version}"
    assert config["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert config["enabled"] is True


def test_configure_schema_evolution(mock_client, sample_schema_config):
    """Test configuring schema evolution."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = sample_schema_config

    # Create config service
    config_service = ConfigService(mock_client)

    # Test configuring schema evolution
    result = config_service.configure_schema_evolution(
        config=sample_schema_config,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["datasource_name_format"] == "{table_name}_v{version}"
    assert result["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert result["enabled"] is True

    # Check that request was made
    mock_client.make_request.assert_called_with(
        "PUT",
        "config/schema-evolution",
        data=sample_schema_config,
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_configure_schema_evolution_dry_run(mock_client, sample_schema_config):
    """Test configuring schema evolution in dry run mode."""
    # Create config service
    config_service = ConfigService(mock_client)

    # Test configuring schema evolution in dry run mode
    result = config_service.configure_schema_evolution(
        config=sample_schema_config,
        backup=True,
        dry_run=True,
    )

    # Check result
    assert result["datasource_name_format"] == "{table_name}_v{version}"
    assert result["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert result["enabled"] is True

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_configure_schema_evolution_local_mode(mock_client, sample_schema_config):
    """Test configuring schema evolution in local mode."""
    # Create config service
    config_service = ConfigService(mock_client)

    # Test configuring schema evolution in local mode
    result = config_service.configure_schema_evolution(
        config=sample_schema_config,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["datasource_name_format"] == "{table_name}_v{version}"
    assert result["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert result["enabled"] is True

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_validate_schema_evolution_config(mock_client):
    """Test validation of schema evolution config."""
    # Create config service
    config_service = ConfigService(mock_client)

    # Test valid config
    valid_config = {
        "datasource_name_format": "{table_name}_v{version}",
        "query_engine_table_name_format": "{table_name}_v{version}",
        "enabled": True,
    }
    config_service._validate_schema_evolution_config(valid_config)

    # Test invalid config - missing required fields
    error_msg = (
        "At least one of datasource_name_format or "
        "query_engine_table_name_format must be provided"
    )
    with pytest.raises(ValueError, match=error_msg):
        config_service._validate_schema_evolution_config({})

    # Test invalid config - datasource_name_format not a string
    with pytest.raises(ValueError, match="datasource_name_format must be a string"):
        config_service._validate_schema_evolution_config(
            {"datasource_name_format": 123}
        )

    # Test invalid config - datasource_name_format missing {table_name}
    with pytest.raises(
        ValueError, match="datasource_name_format must contain {table_name}"
    ):
        config_service._validate_schema_evolution_config(
            {"datasource_name_format": "v{version}"}
        )

    # Test invalid config - datasource_name_format missing {version}
    with pytest.raises(
        ValueError, match="datasource_name_format must contain {version}"
    ):
        config_service._validate_schema_evolution_config(
            {"datasource_name_format": "{table_name}_v"}
        )

    # Test invalid config - query_engine_table_name_format not a string
    with pytest.raises(
        ValueError, match="query_engine_table_name_format must be a string"
    ):
        config_service._validate_schema_evolution_config(
            {"query_engine_table_name_format": 123}
        )

    # Test invalid config - query_engine_table_name_format missing {table_name}
    with pytest.raises(
        ValueError, match="query_engine_table_name_format must contain {table_name}"
    ):
        config_service._validate_schema_evolution_config(
            {"query_engine_table_name_format": "v{version}"}
        )

    # Test invalid config - query_engine_table_name_format missing {version}
    with pytest.raises(
        ValueError, match="query_engine_table_name_format must contain {version}"
    ):
        config_service._validate_schema_evolution_config(
            {"query_engine_table_name_format": "{table_name}_v"}
        )

    # Test invalid config - enabled not a boolean
    with pytest.raises(ValueError, match="enabled must be a boolean"):
        config_service._validate_schema_evolution_config(
            {"datasource_name_format": "{table_name}_v{version}", "enabled": "true"}
        )


def test_configure_schema_evolution_error_handling(mock_client, sample_schema_config):
    """Test error handling in configure_schema_evolution."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.side_effect = Exception("API error")
    mock_client.storage.download_file = MagicMock(return_value=sample_schema_config)

    # Create config service
    config_service = ConfigService(mock_client)

    # Test error handling
    with pytest.raises(Exception, match="API error"):
        config_service.configure_schema_evolution(
            config=sample_schema_config,
            backup=True,
            dry_run=False,
        )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_with(
        "schema_evolution_configure", {}, "Failed", "API error"
    )

    # Check that backup was attempted to be restored
    mock_client.storage.download_file.assert_called_once()
    mock_client.make_request.assert_called_with(
        "PUT", "config/schema-evolution", data=sample_schema_config
    )


def test_configure_schema_evolution_backup_error(mock_client, sample_schema_config):
    """Test backup error handling in configure_schema_evolution."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = sample_schema_config
    mock_client.storage.upload_file = MagicMock(side_effect=Exception("Storage error"))

    # Create config service
    config_service = ConfigService(mock_client)

    # Test backup error handling
    result = config_service.configure_schema_evolution(
        config=sample_schema_config,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["datasource_name_format"] == "{table_name}_v{version}"
    assert result["query_engine_table_name_format"] == "{table_name}_v{version}"
    assert result["enabled"] is True

    # Check that request was made
    mock_client.make_request.assert_called_with(
        "PUT",
        "config/schema-evolution",
        data=sample_schema_config,
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_get_global_config(mock_client):
    """Test getting global config."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "default_user_role": "DataScientist",
        "default_data_source_handler": "snowflake",
        "default_policy_exceptions": {"groups": ["Administrators"]},
    }

    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test getting global config
    config = global_config_service.get_global_config()

    # Check result
    assert config["default_user_role"] == "DataScientist"
    assert config["default_data_source_handler"] == "snowflake"
    assert "groups" in config["default_policy_exceptions"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "config/global")


def test_get_global_config_local(mock_client):
    """Test getting global config in local mode."""
    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test getting global config
    config = global_config_service.get_global_config()

    # Check result
    assert config["default_user_role"] == "DataScientist"
    assert config["default_data_source_handler"] == "snowflake"
    assert "groups" in config["default_policy_exceptions"]


def test_update_global_config(mock_client):
    """Test updating global config."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "default_user_role": "Admin",
        "default_data_source_handler": "snowflake",
        "default_policy_exceptions": {"groups": ["Administrators"]},
    }

    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test updating global config
    result = global_config_service.update_global_config(
        config={"default_user_role": "Admin"},
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["default_user_role"] == "Admin"
    assert result["default_data_source_handler"] == "snowflake"
    assert "groups" in result["default_policy_exceptions"]

    # Check that request was made with any arguments
    mock_client.make_request.assert_called_with(
        "PUT",
        "config/global",
        data=mock_client.make_request.call_args[1]["data"],
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_update_global_config_dry_run(mock_client):
    """Test updating global config in dry run mode."""
    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test updating global config in dry run mode
    result = global_config_service.update_global_config(
        config={"default_user_role": "Admin"},
        backup=True,
        dry_run=True,
    )

    # Check result
    assert result["default_user_role"] == "Admin"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_update_global_config_local_mode(mock_client):
    """Test updating global config in local mode."""
    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test updating global config in local mode
    result = global_config_service.update_global_config(
        config={"default_user_role": "Admin"},
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["default_user_role"] == "Admin"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_validate_global_config(mock_client):
    """Test validation of global config."""
    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test valid config
    valid_config = {
        "default_user_role": "Admin",
        "default_data_source_handler": "snowflake",
        "default_policy_exceptions": {"groups": ["Administrators"]},
    }
    global_config_service._validate_global_config(valid_config)

    # Test invalid config - invalid default_user_role
    with pytest.raises(ValueError, match="Invalid default_user_role"):
        global_config_service._validate_global_config(
            {"default_user_role": "InvalidRole"}
        )

    # Test invalid config - invalid default_data_source_handler
    with pytest.raises(ValueError, match="Invalid default_data_source_handler"):
        global_config_service._validate_global_config(
            {"default_data_source_handler": "invalid"}
        )

    # Test invalid config - default_policy_exceptions not a dictionary
    with pytest.raises(
        ValueError, match="default_policy_exceptions must be a dictionary"
    ):
        global_config_service._validate_global_config(
            {"default_policy_exceptions": "invalid"}
        )

    # Test invalid config - default_policy_exceptions.groups not a list
    with pytest.raises(
        ValueError, match="default_policy_exceptions.groups must be a list"
    ):
        global_config_service._validate_global_config(
            {"default_policy_exceptions": {"groups": "invalid"}}
        )

    # Test invalid config - default_policy_exceptions.users not a list
    with pytest.raises(
        ValueError, match="default_policy_exceptions.users must be a list"
    ):
        global_config_service._validate_global_config(
            {"default_policy_exceptions": {"users": "invalid"}}
        )


def test_update_global_config_error_handling(mock_client):
    """Test error handling in update_global_config."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.side_effect = Exception("API error")
    mock_client.storage.download_file = MagicMock(
        return_value={"default_user_role": "DataScientist"}
    )

    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test error handling
    with pytest.raises(Exception, match="API error"):
        global_config_service.update_global_config(
            config={"default_user_role": "Admin"},
            backup=True,
            dry_run=False,
        )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_with(
        "global_config_update", {}, "Failed", "API error"
    )

    # Check that backup was attempted to be restored
    mock_client.storage.download_file.assert_called_once()
    mock_client.make_request.assert_called_with(
        "PUT", "config/global", data={"default_user_role": "DataScientist"}
    )


def test_update_global_config_backup_error(mock_client):
    """Test backup error handling in update_global_config."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "default_user_role": "Admin"
    }
    mock_client.storage.upload_file = MagicMock(side_effect=Exception("Storage error"))

    # Create global config service
    global_config_service = GlobalConfigService(mock_client)

    # Test backup error handling
    result = global_config_service.update_global_config(
        config={"default_user_role": "Admin"},
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["default_user_role"] == "Admin"

    # Check that request was made
    mock_client.make_request.assert_called_with(
        "PUT",
        "config/global",
        data=mock_client.make_request.call_args[1]["data"],
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()
