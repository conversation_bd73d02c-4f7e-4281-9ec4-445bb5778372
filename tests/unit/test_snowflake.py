"""Unit tests for the Snowflake utilities."""

import time
from unittest.mock import Magic<PERSON><PERSON>, patch

import pytest

from immuta_toolkit.utils.snowflake import SnowflakeConnectionPool


@pytest.fixture
def mock_snowflake_credentials():
    """Mock Snowflake credentials."""
    return {
        "account": "test_account",
        "user": "test_user",
        "password": "test_password",
        "database": "test_database",
        "schema": "test_schema",
        "warehouse": "test_warehouse",
    }


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_initialization(
    mock_connect, mock_snowflake_credentials
):
    """Test Snowflake connection pool initialization."""
    # Create connection pool
    pool = SnowflakeConnectionPool(
        credentials=mock_snowflake_credentials,
        max_connections=5,
        connection_timeout=5,
        idle_timeout=60,
    )

    # Check attributes
    assert pool.credentials == mock_snowflake_credentials
    assert pool.max_connections == 5
    assert pool.connection_timeout == 5
    assert pool.idle_timeout == 60
    assert pool.connections == {}
    assert pool.connection_locks == {}
    assert hasattr(pool, "pool_lock")

    # Check that no connections were created yet
    mock_connect.assert_not_called()


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_get_connection(
    mock_connect, mock_snowflake_credentials
):
    """Test getting a connection from the pool."""
    # Mock Snowflake connection
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    mock_connection.cursor.return_value = mock_cursor
    mock_connect.return_value = mock_connection

    # Create connection pool
    pool = SnowflakeConnectionPool(credentials=mock_snowflake_credentials)

    # Get connection
    connection = pool.get_connection()

    # Check that connection was created
    mock_connect.assert_called_once_with(
        account="test_account",
        user="test_user",
        password="test_password",
        database="test_database",
        schema="test_schema",
        warehouse="test_warehouse",
        role=None,
        autocommit=True,
    )

    # Check that connection was returned
    assert connection == mock_connection

    # Check that connection was added to the pool
    assert len(pool.connections) == 1
    assert 0 in pool.connections
    assert pool.connections[0]["connection"] == mock_connection
    assert "last_used" in pool.connections[0]

    # Check that connection lock was created and acquired
    assert 0 in pool.connection_locks
    assert pool.connection_locks[0].locked()


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_release_connection(
    mock_connect, mock_snowflake_credentials
):
    """Test releasing a connection back to the pool."""
    # Mock Snowflake connection
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    mock_connection.cursor.return_value = mock_cursor
    mock_connect.return_value = mock_connection

    # Create connection pool
    pool = SnowflakeConnectionPool(credentials=mock_snowflake_credentials)

    # Get connection
    connection = pool.get_connection()

    # Check that connection lock is acquired
    assert pool.connection_locks[0].locked()

    # Release connection
    pool.release_connection(connection)

    # Check that connection lock is released
    assert not pool.connection_locks[0].locked()


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_reuse_connection(
    mock_connect, mock_snowflake_credentials
):
    """Test reusing a connection from the pool."""
    # Mock Snowflake connection
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    mock_connection.cursor.return_value = mock_cursor
    mock_connect.return_value = mock_connection

    # Create connection pool
    pool = SnowflakeConnectionPool(credentials=mock_snowflake_credentials)

    # Get connection
    connection1 = pool.get_connection()

    # Release connection
    pool.release_connection(connection1)

    # Get connection again
    connection2 = pool.get_connection()

    # Check that the same connection was returned
    assert connection2 == connection1

    # Check that only one connection was created
    mock_connect.assert_called_once()


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_multiple_connections(
    mock_connect, mock_snowflake_credentials
):
    """Test getting multiple connections from the pool."""
    # Mock Snowflake connections
    mock_connection1 = MagicMock()
    mock_connection2 = MagicMock()
    mock_cursor = MagicMock()
    mock_connection1.cursor.return_value = mock_cursor
    mock_connection2.cursor.return_value = mock_cursor
    mock_connect.side_effect = [mock_connection1, mock_connection2]

    # Create connection pool
    pool = SnowflakeConnectionPool(credentials=mock_snowflake_credentials)

    # Get first connection
    connection1 = pool.get_connection()

    # Get second connection
    connection2 = pool.get_connection()

    # Check that two different connections were returned
    assert connection1 == mock_connection1
    assert connection2 == mock_connection2

    # Check that two connections were created
    assert mock_connect.call_count == 2

    # Check that two connections were added to the pool
    assert len(pool.connections) == 2
    assert 0 in pool.connections
    assert 1 in pool.connections
    assert pool.connections[0]["connection"] == mock_connection1
    assert pool.connections[1]["connection"] == mock_connection2


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_max_connections(
    mock_connect, mock_snowflake_credentials
):
    """Test reaching the maximum number of connections in the pool."""
    # Mock Snowflake connections
    mock_connections = [MagicMock() for _ in range(3)]
    mock_cursor = MagicMock()
    for conn in mock_connections:
        conn.cursor.return_value = mock_cursor
    mock_connect.side_effect = mock_connections

    # Create connection pool with max 2 connections
    pool = SnowflakeConnectionPool(
        credentials=mock_snowflake_credentials,
        max_connections=2,
        connection_timeout=1,
    )

    # Get first connection
    connection1 = pool.get_connection()

    # Get second connection
    _ = pool.get_connection()

    # Try to get third connection (should wait for timeout)
    with pytest.raises(Exception, match="Timeout waiting for a Snowflake connection"):
        pool.get_connection()

    # Release first connection
    pool.release_connection(connection1)

    # Get third connection (should reuse first connection)
    connection3 = pool.get_connection()

    # Check that only two connections were created
    assert mock_connect.call_count == 2

    # Check that connection3 is the same as connection1
    assert connection3 == connection1


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_cleanup_idle_connections(
    mock_connect, mock_snowflake_credentials
):
    """Test cleaning up idle connections."""
    # Mock Snowflake connection
    mock_connection = MagicMock()
    mock_cursor = MagicMock()
    mock_connection.cursor.return_value = mock_cursor
    mock_connect.return_value = mock_connection

    # Create connection pool with short idle timeout
    pool = SnowflakeConnectionPool(
        credentials=mock_snowflake_credentials,
        idle_timeout=0.1,  # 100ms
    )

    # Get connection
    connection = pool.get_connection()

    # Release connection
    pool.release_connection(connection)

    # Wait for idle timeout
    time.sleep(0.2)

    # Get connection again (should create a new one)
    with patch.object(
        pool, "_cleanup_idle_connections", wraps=pool._cleanup_idle_connections
    ) as mock_cleanup:
        _ = pool.get_connection()

        # Check that cleanup was called
        mock_cleanup.assert_called_once()

    # Check that a new connection was created
    assert mock_connect.call_count == 2


@patch("snowflake.connector.connect")
def test_snowflake_connection_pool_close_all_connections(
    mock_connect, mock_snowflake_credentials
):
    """Test closing all connections in the pool."""
    # Mock Snowflake connections
    mock_connection1 = MagicMock()
    mock_connection2 = MagicMock()
    mock_cursor = MagicMock()
    mock_connection1.cursor.return_value = mock_cursor
    mock_connection2.cursor.return_value = mock_cursor
    mock_connect.side_effect = [mock_connection1, mock_connection2]

    # Create connection pool
    pool = SnowflakeConnectionPool(credentials=mock_snowflake_credentials)

    # Get connections
    connection1 = pool.get_connection()
    pool.release_connection(connection1)
    connection2 = pool.get_connection()
    pool.release_connection(connection2)

    # Manually add connections to the pool to ensure they're there
    pool.connections = {
        0: {"connection": mock_connection1, "last_used": time.time()},
        1: {"connection": mock_connection2, "last_used": time.time()},
    }

    # Close all connections
    pool.close_all_connections()

    # Check that connections were closed
    assert mock_connection1.close.call_count >= 1
    assert mock_connection2.close.call_count >= 1

    # Check that connections were removed from the pool
    assert pool.connections == {}
