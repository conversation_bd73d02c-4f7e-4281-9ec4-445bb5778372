"""Unit tests for the project service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_list_projects(mock_client):
    """Test listing projects."""
    # Create project service
    project_service = ProjectService(mock_client)

    # Test listing projects
    projects = project_service.list_projects()

    # Check result
    assert len(projects) == 2
    assert projects[0]["id"] == 1
    assert projects[0]["name"] == "Customer Analytics"
    assert projects[1]["id"] == 2
    assert projects[1]["name"] == "Fraud Detection"


def test_list_projects_api(mock_client):
    """Test listing projects via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "name": "Customer Analytics",
            "description": "Project for customer analytics",
        },
        {
            "id": 2,
            "name": "Fraud Detection",
            "description": "Project for fraud detection",
        },
    ]

    # Create project service
    project_service = ProjectService(mock_client)

    # Test listing projects
    projects = project_service.list_projects(limit=10, offset=0)

    # Check result
    assert len(projects) == 2
    assert projects[0]["id"] == 1
    assert projects[0]["name"] == "Customer Analytics"
    assert projects[1]["id"] == 2
    assert projects[1]["name"] == "Fraud Detection"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "projects", params={"limit": 10, "offset": 0}
    )


def test_get_project(mock_client):
    """Test getting project by ID."""
    # Create project service
    project_service = ProjectService(mock_client)

    # Make sure the _deleted_projects set exists and contains 999
    project_service._deleted_projects = {999}

    # Test getting project by ID
    project = project_service.get_project(1)

    # Check result
    assert project["id"] == 1
    assert project["name"] == "Customer Analytics"
    assert project["description"] == "Project for customer analytics"
    assert project["owner"]["id"] == 1
    assert project["owner"]["email"] == "<EMAIL>"
    assert len(project["members"]) == 2
    assert project["members"][0]["id"] == 1
    assert project["members"][0]["email"] == "<EMAIL>"
    assert project["members"][0]["role"] == "OWNER"
    assert project["members"][1]["id"] == 2
    assert project["members"][1]["email"] == "<EMAIL>"
    assert project["members"][1]["role"] == "MEMBER"
    assert project["dataSources"] == [1, 2]
    assert project["purposes"] == [1]
    assert project["tags"] == ["analytics", "customer"]

    # Test getting project by ID that doesn't exist
    with pytest.raises(ValueError):
        project_service.get_project(999)


def test_get_project_api(mock_client):
    """Test getting project by ID via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Customer Analytics",
        "description": "Project for customer analytics",
        "owner": {"id": 1, "email": "<EMAIL>"},
        "members": [
            {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
            {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
        ],
        "dataSources": [1, 2],
        "purposes": [1],
        "tags": ["analytics", "customer"],
    }

    # Create project service
    project_service = ProjectService(mock_client)

    # Make sure cache is empty
    project_service.cache.delete("project_1")

    # Test getting project by ID
    project = project_service.get_project(1, use_cache=False)

    # Check result
    assert project["id"] == 1
    assert project["name"] == "Customer Analytics"
    assert project["description"] == "Project for customer analytics"
    assert project["owner"]["id"] == 1
    assert project["owner"]["email"] == "<EMAIL>"
    assert len(project["members"]) == 2
    assert project["members"][0]["id"] == 1
    assert project["members"][0]["email"] == "<EMAIL>"
    assert project["members"][0]["role"] == "OWNER"
    assert project["members"][1]["id"] == 2
    assert project["members"][1]["email"] == "<EMAIL>"
    assert project["members"][1]["role"] == "MEMBER"
    assert project["dataSources"] == [1, 2]
    assert project["purposes"] == [1]
    assert project["tags"] == ["analytics", "customer"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "projects/1")


def test_create_project(mock_client):
    """Test creating a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 3,
        "name": "New Project",
        "description": "New project description",
        "owner": {"id": 1, "email": "<EMAIL>"},
        "members": [{"id": 1, "email": "<EMAIL>", "role": "OWNER"}],
        "dataSources": [],
        "purposes": [],
        "tags": ["new", "project"],
    }

    # Disable storage to avoid backup
    mock_client.storage = None

    # Create project service
    project_service = ProjectService(mock_client)

    # Test creating a project
    project_data = {
        "name": "New Project",
        "description": "New project description",
        "tags": ["new", "project"],
    }
    result = project_service.create_project(
        project=project_data, backup=False, dry_run=False, validate=True
    )

    # Check result
    assert result["id"] == 3
    assert result["name"] == "New Project"
    assert result["description"] == "New project description"
    assert result["owner"]["id"] == 1
    assert result["owner"]["email"] == "<EMAIL>"
    assert len(result["members"]) == 1
    assert result["members"][0]["id"] == 1
    assert result["members"][0]["email"] == "<EMAIL>"
    assert result["members"][0]["role"] == "OWNER"
    assert result["dataSources"] == []
    assert result["purposes"] == []
    assert result["tags"] == ["new", "project"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "projects", data=project_data
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_create_project_dry_run(mock_client):
    """Test creating a project in dry run mode."""
    # Create project service
    project_service = ProjectService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test creating a project in dry run mode
    project_data = {
        "name": "New Project",
        "description": "New project description",
        "tags": ["new", "project"],
    }
    result = project_service.create_project(
        project=project_data, backup=True, dry_run=True, validate=True
    )

    # Check result
    assert result["id"] == 0
    assert result["name"] == "New Project"
    assert result["description"] == "New project description"
    assert result["tags"] == ["new", "project"]

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "project_create_dry_run",
        {
            "name": "New Project",
            "description": "New project description",
        },
        "Success",
    )


def test_create_project_invalid(mock_client):
    """Test creating an invalid project."""
    # Create project service
    project_service = ProjectService(mock_client)

    # Test creating a project with missing name
    invalid_project = {
        "description": "New project description",
    }
    with pytest.raises(ValueError):
        project_service.create_project(
            project=invalid_project, backup=True, dry_run=False, validate=True
        )

    # Test creating a project with missing description
    invalid_project = {
        "name": "New Project",
    }
    with pytest.raises(ValueError):
        project_service.create_project(
            project=invalid_project, backup=True, dry_run=False, validate=True
        )


def test_update_project(mock_client):
    """Test updating a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Set up mock responses for different calls
    def mock_response(*args, **_):
        if args[0] == "GET" and args[1] == "projects/1":
            # First call - get project
            mock_resp = MagicMock()
            mock_resp.json.return_value = {
                "id": 1,
                "name": "Customer Analytics",
                "description": "Project for customer analytics",
                "owner": {"id": 1, "email": "<EMAIL>"},
                "members": [
                    {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                    {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
                ],
                "dataSources": [1, 2],
                "purposes": [1],
                "tags": ["analytics", "customer"],
            }
            return mock_resp
        elif args[0] == "PUT" and args[1] == "projects/1":
            # Second call - update project
            mock_resp = MagicMock()
            mock_resp.json.return_value = {
                "id": 1,
                "name": "Customer Analytics Updated",
                "description": "Project for customer analytics - updated",
                "owner": {"id": 1, "email": "<EMAIL>"},
                "members": [
                    {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                    {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
                ],
                "dataSources": [1, 2],
                "purposes": [1],
                "tags": ["analytics", "customer", "updated"],
            }
            return mock_resp

    mock_client.make_request.side_effect = mock_response

    # Create project service
    project_service = ProjectService(mock_client)

    # Make sure cache is empty
    project_service.cache.delete("project_1")

    # Test updating a project
    project_data = {
        "name": "Customer Analytics Updated",
        "description": "Project for customer analytics - updated",
        "tags": ["analytics", "customer", "updated"],
    }
    result = project_service.update_project(
        project_id=1, project=project_data, backup=True, dry_run=False, validate=True
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Customer Analytics Updated"
    assert result["description"] == "Project for customer analytics - updated"
    assert result["tags"] == ["analytics", "customer", "updated"]

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "projects/1")
    mock_client.make_request.assert_any_call("PUT", "projects/1", data=project_data)

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_project(mock_client):
    """Test deleting a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Customer Analytics",
        "description": "Project for customer analytics",
        "owner": {"id": 1, "email": "<EMAIL>"},
        "members": [
            {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
            {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
        ],
        "dataSources": [1, 2],
        "purposes": [1],
        "tags": ["analytics", "customer"],
    }

    # Create project service
    project_service = ProjectService(mock_client)

    # Test deleting a project
    result = project_service.delete_project(project_id=1, backup=True, dry_run=False)

    # Check result
    assert result["status"] == "success"
    assert result["project_id"] == 1

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "projects/1")
    mock_client.make_request.assert_any_call("DELETE", "projects/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_project_member(mock_client):
    """Test adding a member to a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "project_id": 1,
        "user_id": 3,
        "role": "MEMBER",
    }

    # Create project service
    project_service = ProjectService(mock_client)

    # Test adding a member to a project
    result = project_service.add_project_member(
        project_id=1, user_id=3, role="MEMBER", dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["user_id"] == 3
    assert result["role"] == "MEMBER"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "projects/1/members", data={"userId": 3, "role": "MEMBER"}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_project_member_invalid_role(mock_client):
    """Test adding a member to a project with an invalid role."""
    # Create project service
    project_service = ProjectService(mock_client)

    # Test adding a member to a project with an invalid role
    with pytest.raises(ValueError):
        project_service.add_project_member(
            project_id=1, user_id=3, role="INVALID", dry_run=False
        )


def test_remove_project_member(mock_client):
    """Test removing a member from a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Create project service
    project_service = ProjectService(mock_client)

    # Test removing a member from a project
    result = project_service.remove_project_member(
        project_id=1, user_id=2, dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["user_id"] == 2

    # Check that request was made
    mock_client.make_request.assert_called_once_with("DELETE", "projects/1/members/2")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_data_source_to_project(mock_client):
    """Test adding a data source to a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "project_id": 1,
        "data_source_id": 3,
    }

    # Create project service
    project_service = ProjectService(mock_client)

    # Test adding a data source to a project
    result = project_service.add_data_source_to_project(
        project_id=1, data_source_id=3, dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["data_source_id"] == 3

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "projects/1/dataSources", data={"dataSourceId": 3}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_remove_data_source_from_project(mock_client):
    """Test removing a data source from a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Create project service
    project_service = ProjectService(mock_client)

    # Test removing a data source from a project
    result = project_service.remove_data_source_from_project(
        project_id=1, data_source_id=2, dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["data_source_id"] == 2

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "DELETE", "projects/1/dataSources/2"
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_purpose_to_project(mock_client):
    """Test adding a purpose to a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "project_id": 1,
        "purpose_id": 2,
    }

    # Create project service
    project_service = ProjectService(mock_client)

    # Test adding a purpose to a project
    result = project_service.add_purpose_to_project(
        project_id=1, purpose_id=2, dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["purpose_id"] == 2

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "projects/1/purposes", data={"purposeId": 2}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_remove_purpose_from_project(mock_client):
    """Test removing a purpose from a project."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Create project service
    project_service = ProjectService(mock_client)

    # Test removing a purpose from a project
    result = project_service.remove_purpose_from_project(
        project_id=1, purpose_id=1, dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert result["project_id"] == 1
    assert result["purpose_id"] == 1

    # Check that request was made
    mock_client.make_request.assert_called_once_with("DELETE", "projects/1/purposes/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_get_project_members(mock_client):
    """Test getting project members."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
        {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
    ]

    # Create project service
    project_service = ProjectService(mock_client)

    # Test getting project members
    members = project_service.get_project_members(project_id=1)

    # Check result
    assert len(members) == 2
    assert members[0]["id"] == 1
    assert members[0]["email"] == "<EMAIL>"
    assert members[0]["role"] == "OWNER"
    assert members[1]["id"] == 2
    assert members[1]["email"] == "<EMAIL>"
    assert members[1]["role"] == "MEMBER"

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "projects/1")
    mock_client.make_request.assert_any_call("GET", "projects/1/members")


def test_get_project_data_sources(mock_client):
    """Test getting project data sources."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Set up mock responses for different calls
    def mock_response(*args, **_):
        if args[0] == "GET" and args[1] == "projects/1":
            # First call - get project
            mock_resp = MagicMock()
            mock_resp.json.return_value = {
                "id": 1,
                "name": "Customer Analytics",
                "description": "Project for customer analytics",
                "dataSources": [1, 2],
            }
            return mock_resp
        elif args[0] == "GET" and args[1] == "projects/1/dataSources":
            # Second call - get data sources
            mock_resp = MagicMock()
            mock_resp.json.return_value = [
                {"id": 1, "name": "Customer Data"},
                {"id": 2, "name": "Transaction Data"},
            ]
            return mock_resp

    mock_client.make_request.side_effect = mock_response

    # Create project service
    project_service = ProjectService(mock_client)

    # Make sure cache is empty
    project_service.cache.delete("project_1")
    project_service.cache.delete("project_1_data_sources")

    # Test getting project data sources
    data_sources = project_service.get_project_data_sources(project_id=1)

    # Check result
    assert len(data_sources) == 2
    assert data_sources[0]["id"] == 1
    assert data_sources[0]["name"] == "Customer Data"
    assert data_sources[1]["id"] == 2
    assert data_sources[1]["name"] == "Transaction Data"

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "projects/1")
    mock_client.make_request.assert_any_call("GET", "projects/1/dataSources")


def test_get_project_purposes(mock_client):
    """Test getting project purposes."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Set up mock responses for different calls
    def mock_response(*args, **_):
        if args[0] == "GET" and args[1] == "projects/1":
            # First call - get project
            mock_resp = MagicMock()
            mock_resp.json.return_value = {
                "id": 1,
                "name": "Customer Analytics",
                "description": "Project for customer analytics",
                "purposes": [1],
            }
            return mock_resp
        elif args[0] == "GET" and args[1] == "projects/1/purposes":
            # Second call - get purposes
            mock_resp = MagicMock()
            mock_resp.json.return_value = [
                {"id": 1, "name": "Marketing Analytics"},
            ]
            return mock_resp

    mock_client.make_request.side_effect = mock_response

    # Create project service
    project_service = ProjectService(mock_client)

    # Make sure cache is empty
    project_service.cache.delete("project_1")
    project_service.cache.delete("project_1_purposes")

    # Test getting project purposes
    purposes = project_service.get_project_purposes(project_id=1)

    # Check result
    assert len(purposes) == 1
    assert purposes[0]["id"] == 1
    assert purposes[0]["name"] == "Marketing Analytics"

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "projects/1")
    mock_client.make_request.assert_any_call("GET", "projects/1/purposes")
