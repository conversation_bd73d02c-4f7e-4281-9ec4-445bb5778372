"""Unit tests for the healthcheck module."""

import json
import threading
import time
from http.server import HTTPServer
from unittest.mock import MagicMock, patch, PropertyMock

import pytest
import requests

from immuta_toolkit.utils.healthcheck import (
    HealthcheckHandler,
    _health_status,
    check_api_health,
    check_cache_health,
    check_storage_health,
    set_version,
    start_healthcheck_server,
    update_component_status,
)


class TestHealthcheckHandler:
    """Test the HealthcheckHandler class."""

    def test_handle_health(self):
        """Test handling health check requests."""
        # Create a handler instance directly without going through the server initialization
        handler = HealthcheckHandler.__new__(HealthcheckHandler)

        # Set up the handler attributes manually
        handler.path = "/health"
        handler.send_response = MagicMock()
        handler.send_header = MagicMock()
        handler.end_headers = MagicMock()
        handler.wfile = MagicMock()

        # Create a mock health status
        current_time = int(time.time())
        health_status = {
            "status": "up",
            "version": "1.0.0",
            "components": {
                "api": {"status": "up", "last_check": current_time},
                "cache": {"status": "up", "last_check": current_time},
                "storage": {"status": "up", "last_check": current_time},
            },
            "uptime": 60,
            "start_time": current_time - 60,
        }

        # Mock the json.dumps function to return our health status
        with patch("json.dumps", return_value=json.dumps(health_status)) as mock_dumps:
            # Call the handler method directly
            handler._handle_health()

            # Check that json.dumps was called with the right arguments
            mock_dumps.assert_called_once()

            # Check that the response was sent correctly
            handler.send_response.assert_called_once_with(200)
            handler.send_header.assert_called_once_with(
                "Content-Type", "application/json"
            )
            handler.end_headers.assert_called_once()
            handler.wfile.write.assert_called_once()

            # Check that the response contains the expected data
            response_bytes = handler.wfile.write.call_args[0][0]
            response_data = json.loads(
                response_bytes.decode("utf-8")
                if isinstance(response_bytes, bytes)
                else response_bytes
            )
            assert response_data["status"] == "up"
            assert response_data["version"] == "1.0.0"
            assert "components" in response_data
            assert "uptime" in response_data

    def test_handle_health_degraded(self):
        """Test handling health check requests with degraded status."""
        # Create a handler instance directly without going through the server initialization
        handler = HealthcheckHandler.__new__(HealthcheckHandler)

        # Set up the handler attributes manually
        handler.path = "/health"
        handler.send_response = MagicMock()
        handler.send_header = MagicMock()
        handler.end_headers = MagicMock()
        handler.wfile = MagicMock()

        # Create a mock health status with degraded component
        current_time = int(time.time())
        health_status = {
            "status": "degraded",
            "version": "1.0.0",
            "components": {
                "api": {"status": "degraded", "last_check": current_time},
                "cache": {"status": "up", "last_check": current_time},
                "storage": {"status": "up", "last_check": current_time},
            },
            "uptime": 60,
            "start_time": current_time - 60,
        }

        # Mock the json.dumps function to return our health status
        with patch("json.dumps", return_value=json.dumps(health_status)) as mock_dumps:
            # Call the handler method directly
            handler._handle_health()

            # Check that json.dumps was called with the right arguments
            mock_dumps.assert_called_once()

            # Check that the response was sent correctly
            handler.send_response.assert_called_once_with(200)  # Still 200 for degraded

            # Check that the response contains the expected data
            response_bytes = handler.wfile.write.call_args[0][0]
            response_data = json.loads(
                response_bytes.decode("utf-8")
                if isinstance(response_bytes, bytes)
                else response_bytes
            )
            assert response_data["status"] == "degraded"
            assert response_data["components"]["api"]["status"] == "degraded"


def test_update_component_status():
    """Test updating component status."""
    # Save original health status
    original_status = _health_status.copy()

    try:
        # Update a component status
        update_component_status("test_component", "up", {"response_time_ms": 100})

        # Check that the component was updated
        assert "test_component" in _health_status["components"]
        assert _health_status["components"]["test_component"]["status"] == "up"
        assert _health_status["components"]["test_component"]["response_time_ms"] == 100
        assert "last_check" in _health_status["components"]["test_component"]
    finally:
        # Restore original health status
        _health_status.clear()
        _health_status.update(original_status)


def test_set_version():
    """Test setting the version."""
    # Save original health status
    original_status = _health_status.copy()

    try:
        # Set the version
        set_version("2.0.0")

        # Check that the version was updated
        assert _health_status["version"] == "2.0.0"
    finally:
        # Restore original health status
        _health_status.clear()
        _health_status.update(original_status)


@patch("immuta_toolkit.utils.healthcheck.HTTPServer")
@patch("immuta_toolkit.utils.healthcheck.threading.Thread")
def test_start_healthcheck_server(mock_thread, mock_server):
    """Test starting the healthcheck server."""
    # Set up mocks
    mock_server_instance = MagicMock()
    mock_server.return_value = mock_server_instance

    mock_thread_instance = MagicMock()
    mock_thread.return_value = mock_thread_instance

    # Call the function
    start_healthcheck_server(port=8000)

    # Check that the server was created and started
    mock_server.assert_called_once()
    mock_thread.assert_called_once_with(target=mock_server_instance.serve_forever)
    assert mock_thread_instance.daemon is True
    mock_thread_instance.start.assert_called_once()


def test_check_api_health():
    """Test checking API health."""
    # Create a mock client
    mock_client = MagicMock()
    mock_response = MagicMock()
    mock_response.status_code = 200
    mock_response.elapsed.total_seconds.return_value = 0.1
    mock_response.json.return_value = {"version": "1.0.0"}
    mock_client.make_request.return_value = mock_response

    # Call the function
    status, details = check_api_health(mock_client)

    # Check the result
    assert status == "up"
    assert details["response_time_ms"] == 100
    assert details["version"] == "1.0.0"

    # Test with error response
    mock_response.status_code = 500
    status, details = check_api_health(mock_client)
    assert status == "degraded"

    # Test with exception
    mock_client.make_request.side_effect = Exception("Test error")
    status, details = check_api_health(mock_client)
    assert status == "down"
    assert details["error"] == "Test error"


def test_check_storage_health():
    """Test checking storage health."""
    # Create a mock storage
    mock_storage = MagicMock()

    # Test with check_connection method
    mock_storage.check_connection = MagicMock()
    status, details = check_storage_health(mock_storage)
    assert status == "up"
    mock_storage.check_connection.assert_called_once()

    # Test with list_containers method
    delattr(mock_storage, "check_connection")
    mock_storage.list_containers = MagicMock()
    status, details = check_storage_health(mock_storage)
    assert status == "up"
    mock_storage.list_containers.assert_called_once()

    # Test with list_blobs method
    delattr(mock_storage, "list_containers")
    mock_storage.list_blobs = MagicMock()
    status, details = check_storage_health(mock_storage)
    assert status == "up"
    mock_storage.list_blobs.assert_called_once()

    # Test with no health check method
    delattr(mock_storage, "list_blobs")
    status, details = check_storage_health(mock_storage)
    assert status == "unknown"
    assert details["reason"] == "No health check method available"

    # Test with exception
    mock_storage.check_connection = MagicMock(side_effect=Exception("Test error"))
    status, details = check_storage_health(mock_storage)
    assert status == "down"
    assert details["error"] == "Test error"


def test_check_cache_health():
    """Test checking cache health."""
    # Create a mock cache manager
    mock_cache_manager = MagicMock()
    mock_cache_manager.caches = {
        "cache1": MagicMock(entries={"key1": "value1", "key2": "value2"}),
        "cache2": MagicMock(entries={"key3": "value3"}),
    }

    # Call the function
    status, details = check_cache_health(mock_cache_manager)

    # Check the result
    assert status == "up"
    assert details["cache_count"] == 2
    assert details["total_entries"] == 3

    # Test with exception
    mock_cache_manager_error = MagicMock()
    # Make accessing .caches raise an exception
    type(mock_cache_manager_error).caches = PropertyMock(
        side_effect=Exception("Test error")
    )

    status, details = check_cache_health(mock_cache_manager_error)
    assert status == "down"
    assert details["error"] == "Test error"
