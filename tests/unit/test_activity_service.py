"""Unit tests for the activity service."""

from datetime import datetime, timedelta
from unittest.mock import MagicMock

import pytest

from immuta_toolkit.services.activity_service import ActivityService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    return client


def test_get_policy_activity(mock_client):
    """Test getting policy activity."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "timestamp": "2023-01-01T00:00:00Z",
            "policyId": 1,
            "policyName": "Test Policy",
            "dataSourceId": 1,
            "dataSourceName": "Test Data Source",
            "userId": 1,
            "userName": "Test User",
            "action": "QUERY",
            "effect": "ALLOW",
            "reason": "Policy allowed access",
        },
        {
            "id": 2,
            "timestamp": "2023-01-02T00:00:00Z",
            "policyId": 2,
            "policyName": "Restricted Policy",
            "dataSourceId": 2,
            "dataSourceName": "Sensitive Data Source",
            "userId": 2,
            "userName": "Another User",
            "action": "QUERY",
            "effect": "DENY",
            "reason": "Policy denied access",
        },
    ]

    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting policy activity
    start_time = datetime(2023, 1, 1)
    end_time = datetime(2023, 1, 3)
    activities = activity_service.get_policy_activity(
        start_time=start_time,
        end_time=end_time,
        policy_id=1,
        data_source_id=1,
        user_id=1,
        limit=10,
        offset=0,
    )

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["policyId"] == 1
    assert activities[0]["effect"] == "ALLOW"
    assert activities[1]["id"] == 2
    assert activities[1]["policyId"] == 2
    assert activities[1]["effect"] == "DENY"

    # Check that request was made with correct parameters
    mock_client.make_request.assert_called_once_with(
        "GET",
        "activity/policy",
        params={
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "policyId": 1,
            "dataSourceId": 1,
            "userId": 1,
            "limit": 10,
            "offset": 0,
        },
    )


def test_get_policy_activity_local(mock_client):
    """Test getting policy activity in local mode."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting policy activity
    activities = activity_service.get_policy_activity()

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["policyId"] == 1
    assert activities[0]["effect"] == "ALLOW"
    assert activities[1]["id"] == 2
    assert activities[1]["policyId"] == 2
    assert activities[1]["effect"] == "DENY"


def test_get_user_activity(mock_client):
    """Test getting user activity."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "timestamp": "2023-01-01T00:00:00Z",
            "userId": 1,
            "userName": "Test User",
            "actionType": "LOGIN",
            "details": "User logged in",
        },
        {
            "id": 2,
            "timestamp": "2023-01-02T00:00:00Z",
            "userId": 2,
            "userName": "Another User",
            "actionType": "CREATE",
            "details": "User created a data source",
            "resourceId": 1,
            "resourceType": "DATA_SOURCE",
        },
    ]

    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting user activity
    start_time = datetime(2023, 1, 1)
    end_time = datetime(2023, 1, 3)
    activities = activity_service.get_user_activity(
        start_time=start_time,
        end_time=end_time,
        user_id=1,
        action_type="LOGIN",
        limit=10,
        offset=0,
    )

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["userId"] == 1
    assert activities[0]["actionType"] == "LOGIN"
    assert activities[1]["id"] == 2
    assert activities[1]["userId"] == 2
    assert activities[1]["actionType"] == "CREATE"

    # Check that request was made with correct parameters
    mock_client.make_request.assert_called_once_with(
        "GET",
        "activity/user",
        params={
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "userId": 1,
            "actionType": "LOGIN",
            "limit": 10,
            "offset": 0,
        },
    )


def test_get_user_activity_local(mock_client):
    """Test getting user activity in local mode."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting user activity
    activities = activity_service.get_user_activity()

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["userId"] == 1
    assert activities[0]["actionType"] == "LOGIN"
    assert activities[1]["id"] == 2
    assert activities[1]["userId"] == 2
    assert activities[1]["actionType"] == "CREATE"


def test_get_data_source_activity(mock_client):
    """Test getting data source activity."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "timestamp": "2023-01-01T00:00:00Z",
            "dataSourceId": 1,
            "dataSourceName": "Test Data Source",
            "userId": 1,
            "userName": "Test User",
            "actionType": "QUERY",
            "details": "User queried the data source",
        },
        {
            "id": 2,
            "timestamp": "2023-01-02T00:00:00Z",
            "dataSourceId": 2,
            "dataSourceName": "Sensitive Data Source",
            "userId": 2,
            "userName": "Another User",
            "actionType": "EXPORT",
            "details": "User exported data from the data source",
        },
    ]

    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting data source activity
    start_time = datetime(2023, 1, 1)
    end_time = datetime(2023, 1, 3)
    activities = activity_service.get_data_source_activity(
        start_time=start_time,
        end_time=end_time,
        data_source_id=1,
        user_id=1,
        action_type="QUERY",
        limit=10,
        offset=0,
    )

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["dataSourceId"] == 1
    assert activities[0]["actionType"] == "QUERY"
    assert activities[1]["id"] == 2
    assert activities[1]["dataSourceId"] == 2
    assert activities[1]["actionType"] == "EXPORT"

    # Check that request was made with correct parameters
    mock_client.make_request.assert_called_once_with(
        "GET",
        "activity/dataSource",
        params={
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "dataSourceId": 1,
            "userId": 1,
            "actionType": "QUERY",
            "limit": 10,
            "offset": 0,
        },
    )


def test_get_data_source_activity_local(mock_client):
    """Test getting data source activity in local mode."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test getting data source activity
    activities = activity_service.get_data_source_activity()

    # Check result
    assert len(activities) == 2
    assert activities[0]["id"] == 1
    assert activities[0]["dataSourceId"] == 1
    assert activities[0]["actionType"] == "QUERY"
    assert activities[1]["id"] == 2
    assert activities[1]["dataSourceId"] == 2
    assert activities[1]["actionType"] == "EXPORT"


def test_export_activity_report(mock_client):
    """Test exporting activity report."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "report_type": "policy",
        "format": "csv",
        "url": "https://example.com/reports/policy_activity_report.csv",
    }

    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test exporting activity report
    start_time = datetime(2023, 1, 1)
    end_time = datetime(2023, 1, 3)
    result = activity_service.export_activity_report(
        report_type="policy",
        start_time=start_time,
        end_time=end_time,
        format="csv",
    )

    # Check result
    assert result["success"] is True
    assert result["report_type"] == "policy"
    assert result["format"] == "csv"
    assert result["url"] == "https://example.com/reports/policy_activity_report.csv"

    # Check that request was made with correct parameters
    mock_client.make_request.assert_called_once_with(
        "POST",
        "activity/policy/export",
        data={
            "startTime": start_time.isoformat(),
            "endTime": end_time.isoformat(),
            "format": "csv",
        },
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_export_activity_report_dry_run(mock_client):
    """Test exporting activity report in dry run mode."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test exporting activity report in dry run mode
    result = activity_service.export_activity_report(
        report_type="policy",
        format="csv",
        dry_run=True,
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["report_type"] == "policy"
    assert result["format"] == "csv"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "export_activity_report_dry_run",
        {
            "report_type": "policy",
            "format": "csv",
        },
        "Success",
    )


def test_export_activity_report_invalid_type(mock_client):
    """Test exporting activity report with invalid report type."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test exporting activity report with invalid report type
    with pytest.raises(ValueError):
        activity_service.export_activity_report(
            report_type="invalid",
            format="csv",
        )


def test_export_activity_report_invalid_format(mock_client):
    """Test exporting activity report with invalid format."""
    # Create activity service
    activity_service = ActivityService(mock_client)

    # Test exporting activity report with invalid format
    with pytest.raises(ValueError):
        activity_service.export_activity_report(
            report_type="policy",
            format="invalid",
        )
