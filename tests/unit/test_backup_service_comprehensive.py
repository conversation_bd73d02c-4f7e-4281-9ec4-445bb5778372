"""Comprehensive unit tests for the backup service."""

import json
import os
import tempfile
from unittest.mock import MagicMock, patch, mock_open

import pytest

from immuta_toolkit.services.backup_service import BackupService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.data_source_service = MagicMock()
    client.policy_service = MagicMock()
    client.user_service = MagicMock()
    client.purpose_service = MagicMock()
    client.project_service = MagicMock()
    client.tag_service = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestBackupServiceComprehensive:
    """Comprehensive tests for the backup service."""

    def test_backup_info_creation(self, mock_client):
        """Test that backup info is created correctly."""
        # Set up mock data sources
        mock_client.data_source_service.list_data_sources.return_value = [
            {"id": 1, "name": "Data Source 1"},
            {"id": 2, "name": "Data Source 2"},
        ]

        # Create backup service
        backup_service = BackupService(mock_client)

        # Mock the makedirs function
        with (
            patch("os.makedirs") as mock_makedirs,
            patch("builtins.open", new_callable=mock_open()) as mock_file,
            patch("json.dump") as mock_json_dump,
        ):
            # Call the method
            backup_service.backup_data_sources(
                output_dir="/tmp/backup",
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

            # Check that backup info was created
            mock_json_dump.assert_called()
            # Get the first call arguments
            args, _ = mock_json_dump.call_args_list[0]
            backup_info = args[0]

            # Check backup info contents
            assert "timestamp" in backup_info
            assert "datetime" in backup_info
            assert backup_info["data_source_count"] == 2
            assert backup_info["include_policies"] is True
            assert backup_info["include_tags"] is True
            assert backup_info["include_metadata"] is True

    @patch("os.makedirs")
    @patch("builtins.open", new_callable=mock_open)
    @patch("json.dump")
    def test_backup_data_sources_with_error_handling(
        self, mock_json_dump, mock_file, mock_makedirs, mock_client
    ):
        """Test backing up data sources with error handling."""
        # Set up mock data sources
        mock_client.data_source_service.list_data_sources.return_value = [
            {"id": 1, "name": "Data Source 1"},
            {"id": 2, "name": "Data Source 2"},
        ]

        # Set up mock policies with an error for the second data source
        mock_client.policy_service.list_policies.side_effect = [
            [{"id": 1, "name": "Policy 1"}],
            Exception("Failed to get policies"),
        ]

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_data_sources(
            output_dir="/tmp/backup",
            data_source_ids=None,  # Backup all data sources
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Backed up" in result["message"]
        assert result["data_source_count"] == 2

        # Check that warning was logged for the failed policy backup
        mock_client.notifier.send_notification.assert_called()

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=mock_open)
    @patch("json.load")
    def test_restore_data_sources_with_missing_files(
        self, mock_json_load, mock_file, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring data sources with missing files."""
        # Set up mock client to be in local mode to avoid API calls
        mock_client.is_local = True

        # Mock directory existence - backup directory exists but data sources directory doesn't
        mock_isdir.side_effect = lambda path: "data_sources" not in path

        # Mock file existence - backup_info.json exists
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        }

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            backup_service.restore_data_sources(
                backup_dir="/tmp/backup",
                data_source_ids=[1, 2],
                include_policies=True,
                include_tags=True,
                include_metadata=True,
                dry_run=False,
            )

        # Check the error message contains the expected text
        assert "Data sources directory not found" in str(excinfo.value)

    def test_restore_projects_real_mode(self, mock_client):
        """Test restoring projects in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Set up mock project service
        mock_client.project_service._deleted_projects = set()
        mock_client.project_service.cache = MagicMock()

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode to avoid complex mocking
        result = backup_service.restore_projects(
            backup_dir="/tmp/backup",
            project_ids=[1, 2],
            restore_members=True,
            restore_data_sources=True,
            restore_purposes=True,
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Dry run" in result["message"]
        assert mock_client.notifier.send_notification.called
