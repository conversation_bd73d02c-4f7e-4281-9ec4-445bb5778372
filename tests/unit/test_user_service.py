"""Unit tests for the user service."""

from unittest.mock import Magic<PERSON>ock

import pytest

from immuta_toolkit.models import UserModel, UserAttributes, UserRole
from immuta_toolkit.services.user_service import UserService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_list_users(mock_client):
    """Test listing users."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "email": "<EMAIL>", "name": "User 1"},
        {"id": 2, "email": "<EMAIL>", "name": "User 2"},
    ]

    # Create user service
    user_service = UserService(mock_client)

    # Test listing users
    users = user_service.list_users(limit=10, offset=0)

    # Check result
    assert len(users) == 2
    assert users[0]["id"] == 1
    assert users[1]["email"] == "<EMAIL>"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "bim/users", params={"limit": 10, "offset": 0}
    )


def test_list_users_local(mock_client):
    """Test listing users in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test listing users
    users = user_service.list_users(limit=10, offset=0)

    # Check result
    assert len(users) == 2
    assert users[0]["email"] == "<EMAIL>"
    assert users[1]["email"] == "<EMAIL>"


def test_get_user_by_id(mock_client):
    """Test getting user by ID."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "email": "<EMAIL>",
        "name": "User 1",
        "attributes": {"role": "Admin"},
        "groups": ["Group1"],
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test getting user by ID
    user = user_service.get_user(1)

    # Check result
    assert user["id"] == 1
    assert user["email"] == "<EMAIL>"
    assert user["attributes"]["role"] == "Admin"

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "bim/user/1")


def test_get_user_by_email(mock_client):
    """Test getting user by email."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock list_users response
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "email": "<EMAIL>", "name": "User 1"},
        {"id": 2, "email": "<EMAIL>", "name": "User 2"},
    ]

    # Create user service
    user_service = UserService(mock_client)

    # Test getting user by email
    user = user_service.get_user("<EMAIL>")

    # Check result
    assert user["id"] == 2
    assert user["email"] == "<EMAIL>"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "bim/users", params={"limit": 1000, "offset": 0}
    )


def test_get_user_local(mock_client):
    """Test getting user in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting user by ID
    user = user_service.get_user(1)

    # Check result
    assert user["id"] == 1
    assert user["email"] == "<EMAIL>"
    assert user["attributes"]["role"] == "Admin"

    # Test getting user by email
    user = user_service.get_user("<EMAIL>")

    # Check result
    assert user["id"] == 2
    assert user["email"] == "<EMAIL>"
    assert user["attributes"]["role"] == "DataScientist"


def test_create_user(mock_client):
    """Test creating a user."""
    # Set up mock client
    mock_client.is_local = True  # Use local mode to avoid API calls

    # Create user service
    user_service = UserService(mock_client)

    # Create user model
    user = UserModel(
        email="<EMAIL>",
        name="New User",
        attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
        groups=["Group1"],
    )

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test creating user
    result = user_service.create_user(
        user=user,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["id"] == 3
    assert "email" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_create_user_dry_run(mock_client):
    """Test creating a user in dry run mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Create user model
    user = UserModel(
        email="<EMAIL>",
        name="New User",
        attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
        groups=["Group1"],
    )

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test creating user in dry run mode
    result = user_service.create_user(
        user=user,
        backup=True,
        dry_run=True,
    )

    # Check result
    assert result["id"] == 0

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "user_create_dry_run",
        {
            "email": "<EMAIL>",
            "name": "New User",
            "role": "data_scientist",
            "groups": "Group1",
        },
        "Success",
    )


def test_update_user(mock_client):
    """Test updating a user."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_user and update responses
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": 1,
            "email": "<EMAIL>",
            "name": "User 1",
            "attributes": {"role": "DataScientist"},
            "groups": ["Group1"],
        },
        {
            "id": 1,
            "email": "<EMAIL>",
            "name": "Updated User",
            "attributes": {"role": "Admin"},
            "groups": ["Group1", "Group2"],
        },
    ]

    # Create user service
    user_service = UserService(mock_client)

    # Create user model for update
    user = UserModel(
        email="<EMAIL>",
        name="Updated User",
        attributes=UserAttributes(role=UserRole.ADMIN),
        groups=["Group1", "Group2"],
    )

    # Test updating user
    result = user_service.update_user(
        user_id=1,
        user=user,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Updated User"
    assert result["attributes"]["role"] == "Admin"
    assert "Group2" in result["groups"]

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "bim/user/1")
    mock_client.make_request.assert_any_call(
        "PUT",
        "bim/user/1",
        data=user.model_dump(),
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_update_user_dry_run(mock_client):
    """Test updating a user in dry run mode."""
    # Set up mock client
    mock_client.is_local = True  # Use local mode to avoid API calls

    # Create user service
    user_service = UserService(mock_client)

    # Create user model for update
    user = UserModel(
        email="<EMAIL>",
        name="Updated User",
        attributes=UserAttributes(role=UserRole.ADMIN),
        groups=["Group1", "Group2"],
    )

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test updating user in dry run mode
    result = user_service.update_user(
        user_id=1,
        user=user,
        backup=True,
        dry_run=True,
    )

    # Check result
    assert result["id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_user(mock_client):
    """Test deleting a user."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_user response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "email": "<EMAIL>",
        "name": "User 1",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test deleting user
    result = user_service.delete_user(
        user_id=1,
        backup=True,
        dry_run=False,
    )

    # Check result
    assert result["status"] == "success"
    assert result["user_id"] == 1

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "bim/user/1")
    mock_client.make_request.assert_any_call("DELETE", "bim/user/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_get_user_access_info(mock_client):
    """Test getting user access information for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "user": {
            "id": 1,
            "name": "User 1",
            "email": "<EMAIL>",
        },
        "dataSource": {
            "id": 10,
            "name": "Test Data Source",
        },
        "policies": [
            {
                "id": 1,
                "name": "Test Policy",
                "type": "subscription",
                "effect": "allow",
            }
        ],
        "access": "ALLOWED",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test getting user access info
    result = user_service.get_user_access_info(data_source_id=10, user_id=1)

    # Check result
    assert result["user"]["id"] == 1
    assert result["dataSource"]["id"] == 10
    assert result["access"] == "ALLOWED"
    assert len(result["policies"]) == 1
    assert result["policies"][0]["name"] == "Test Policy"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/users/1/policyInfo"
    )


def test_get_user_access_info_local(mock_client):
    """Test getting user access information for a data source in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting user access info
    result = user_service.get_user_access_info(data_source_id=10, user_id=1)

    # Check result
    assert result["user"]["id"] == 1
    assert result["dataSource"]["id"] == 10
    assert result["access"] == "ALLOWED"
    assert len(result["policies"]) == 1
    assert result["policies"][0]["name"] == "Test Policy"


def test_get_user_visibility_info(mock_client):
    """Test getting user visibility information for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "user": {
            "id": 1,
            "name": "User 1",
            "email": "<EMAIL>",
        },
        "dataSource": {
            "id": 10,
            "name": "Test Data Source",
        },
        "columns": {
            "customer_id": {"visible": True, "masked": False},
            "email": {"visible": True, "masked": True},
            "address": {"visible": False, "masked": False},
        },
        "rowFilter": "region = 'US'",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test getting user visibility info
    result = user_service.get_user_visibility_info(data_source_id=10, user_id=1)

    # Check result
    assert result["user"]["id"] == 1
    assert result["dataSource"]["id"] == 10
    assert result["columns"]["customer_id"]["visible"] is True
    assert result["columns"]["email"]["masked"] is True
    assert result["columns"]["address"]["visible"] is False
    assert result["rowFilter"] == "region = 'US'"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/users/1/visibilityReport"
    )


def test_get_user_visibility_info_local(mock_client):
    """Test getting user visibility information for a data source in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting user visibility info
    result = user_service.get_user_visibility_info(data_source_id=10, user_id=1)

    # Check result
    assert result["user"]["id"] == 1
    assert result["dataSource"]["id"] == 10
    assert result["columns"]["customer_id"]["visible"] is True
    assert result["columns"]["email"]["masked"] is True
    assert result["columns"]["address"]["visible"] is False
    assert result["rowFilter"] == "region = 'US'"


def test_get_current_user_visibility_info(mock_client):
    """Test getting current user visibility information for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "dataSource": {
            "id": 10,
            "name": "Test Data Source",
        },
        "columns": {
            "customer_id": {"visible": True, "masked": False},
            "email": {"visible": True, "masked": True},
            "address": {"visible": False, "masked": False},
        },
        "rowFilter": "region = 'US'",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test getting current user visibility info
    result = user_service.get_current_user_visibility_info(data_source_id=10)

    # Check result
    assert result["dataSource"]["id"] == 10
    assert result["columns"]["customer_id"]["visible"] is True
    assert result["columns"]["email"]["masked"] is True
    assert result["columns"]["address"]["visible"] is False
    assert result["rowFilter"] == "region = 'US'"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/visibilityReport"
    )


def test_get_current_user_visibility_info_local(mock_client):
    """Test getting current user visibility information for a data source in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting current user visibility info
    result = user_service.get_current_user_visibility_info(data_source_id=10)

    # Check result
    assert result["dataSource"]["id"] == 10
    assert result["columns"]["customer_id"]["visible"] is True
    assert result["columns"]["email"]["masked"] is True
    assert result["columns"]["address"]["visible"] is False
    assert result["rowFilter"] == "region = 'US'"


def test_get_users_by_access_level(mock_client):
    """Test getting users by access level for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>",
            "accessLevel": "OWNER",
        },
        {
            "id": 2,
            "name": "Expert User",
            "email": "<EMAIL>",
            "accessLevel": "EXPERT",
        },
    ]

    # Create user service
    user_service = UserService(mock_client)

    # Test getting users by access level
    result = user_service.get_users_by_access_level(
        data_source_id=10, access_level="OWNER"
    )

    # Check result
    assert len(result) == 2
    assert result[0]["id"] == 1
    assert result[0]["accessLevel"] == "OWNER"
    assert result[1]["id"] == 2
    assert result[1]["accessLevel"] == "EXPERT"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/access", params={"accessLevel": "OWNER"}
    )


def test_get_users_by_access_level_local(mock_client):
    """Test getting users by access level for a data source in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting users by access level
    result = user_service.get_users_by_access_level(
        data_source_id=10, access_level="OWNER"
    )

    # Check result
    assert len(result) == 1
    assert result[0]["id"] == 1
    assert result[0]["accessLevel"] == "OWNER"

    # Test getting all users
    result = user_service.get_users_by_access_level(data_source_id=10)

    # Check result
    assert len(result) == 4
    assert result[0]["accessLevel"] == "OWNER"
    assert result[1]["accessLevel"] == "EXPERT"
    assert result[2]["accessLevel"] == "ALLOWED"
    assert result[3]["accessLevel"] == "DENIED"


def test_add_user_to_data_source(mock_client):
    """Test adding a user to a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "User added successfully",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test adding a user to a data source
    result = user_service.add_user_to_data_source(
        data_source_id=10, user_id=1, access_level="EXPERT", dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST",
        "dataSource/10/access",
        data={"userId": 1, "accessLevel": "EXPERT"},
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_user_to_data_source_dry_run(mock_client):
    """Test adding a user to a data source in dry run mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test adding a user to a data source in dry run mode
    result = user_service.add_user_to_data_source(
        data_source_id=10, user_id=1, access_level="EXPERT", dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "add_user_to_data_source_dry_run",
        {
            "data_source_id": 10,
            "user_id": 1,
            "access_level": "EXPERT",
        },
        "Success",
    )


def test_add_user_to_data_source_invalid_access_level(mock_client):
    """Test adding a user to a data source with an invalid access level."""
    # Create user service
    user_service = UserService(mock_client)

    # Test adding a user to a data source with an invalid access level
    with pytest.raises(ValueError):
        user_service.add_user_to_data_source(
            data_source_id=10, user_id=1, access_level="INVALID", dry_run=False
        )


def test_change_user_access_level(mock_client):
    """Test changing a user's access level for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "User access level changed successfully",
    }

    # Create user service
    user_service = UserService(mock_client)

    # Test changing a user's access level
    result = user_service.change_user_access_level(
        data_source_id=10, user_id=1, access_level="DENIED", dry_run=False
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "PUT",
        "dataSource/10/access/1",
        data={"accessLevel": "DENIED"},
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_change_user_access_level_dry_run(mock_client):
    """Test changing a user's access level for a data source in dry run mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test changing a user's access level in dry run mode
    result = user_service.change_user_access_level(
        data_source_id=10, user_id=1, access_level="DENIED", dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "change_user_access_level_dry_run",
        {
            "data_source_id": 10,
            "user_id": 1,
            "access_level": "DENIED",
        },
        "Success",
    )


def test_change_user_access_level_invalid_access_level(mock_client):
    """Test changing a user's access level with an invalid access level."""
    # Create user service
    user_service = UserService(mock_client)

    # Test changing a user's access level with an invalid access level
    with pytest.raises(ValueError):
        user_service.change_user_access_level(
            data_source_id=10, user_id=1, access_level="INVALID", dry_run=False
        )


def test_get_users_who_can_unmask(mock_client):
    """Test getting users who can unmask a specific column in a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "name": "Admin User",
            "profileId": 1,
            "iamid": "bim",
        },
        {
            "name": "Expert User",
            "profileId": 2,
            "iamid": "bim",
        },
    ]

    # Create user service
    user_service = UserService(mock_client)

    # Test getting users who can unmask a column
    result = user_service.get_users_who_can_unmask(
        data_source_id=10, column_name="email"
    )

    # Check result
    assert len(result) == 2
    assert result[0]["name"] == "Admin User"
    assert result[0]["profileId"] == 1
    assert result[1]["name"] == "Expert User"
    assert result[1]["profileId"] == 2

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/email/unmaskUsers"
    )


def test_get_users_who_can_unmask_local(mock_client):
    """Test getting users who can unmask a specific column in a data source in local mode."""
    # Create user service
    user_service = UserService(mock_client)

    # Test getting users who can unmask a column
    result = user_service.get_users_who_can_unmask(
        data_source_id=10, column_name="email"
    )

    # Check result
    assert len(result) == 2
    assert result[0]["name"] == "Admin User"
    assert result[0]["profileId"] == 1
    assert result[1]["name"] == "Expert User"
    assert result[1]["profileId"] == 2
