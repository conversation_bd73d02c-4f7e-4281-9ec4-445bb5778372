"""Unit tests for user service error handling."""

from unittest.mock import Magic<PERSON>ock, patch

import pytest
import requests
from pydantic import ValidationError

from immuta_toolkit.models import UserModel, UserAttributes, UserRole
from immuta_toolkit.services.user_service import UserService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    return client


class TestUserServiceErrorHandling:
    """Test error handling in the user service."""

    def test_get_user_not_found(self, mock_client):
        """Test getting a user that doesn't exist."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create user service
        user_service = UserService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            user_service.get_user(999)  # Use an ID that doesn't exist in the mock data

        # Check the error message
        assert "User not found" in str(excinfo.value)

    def test_get_user_server_error(self, mock_client):
        """Test getting a user with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create user service
        user_service = UserService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.get_user(1)

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_get_user_connection_error(self, mock_client):
        """Test getting a user with a connection error."""
        # Set up mock to raise a connection error
        mock_client.make_request.side_effect = requests.exceptions.ConnectionError(
            "Connection refused"
        )

        # Create user service
        user_service = UserService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.get_user(1)

        # Check the error message
        assert "Connection refused" in str(excinfo.value)

    def test_list_users_server_error(self, mock_client):
        """Test listing users with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create user service
        user_service = UserService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.list_users()

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_create_user_invalid_input(self, mock_client):
        """Test creating a user with invalid input."""
        # Create user service
        user_service = UserService(mock_client)

        # Call the method with invalid input
        with pytest.raises(AttributeError) as excinfo:
            user_service.create_user({})  # Empty user

        # Check the error message
        assert "model_dump" in str(excinfo.value)

    def test_create_user_server_error(self, mock_client):
        """Test creating a user with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create user service
        user_service = UserService(mock_client)

        # Create a valid UserModel
        user_model = UserModel(
            email="<EMAIL>",
            name="Test User",
            attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
            groups=["test-group"],
        )

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.create_user(user_model)

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_update_user_not_found(self, mock_client):
        """Test updating a user that doesn't exist."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise a 404 error
        mock_client.make_request.side_effect = Exception("User not found")

        # Create user service
        user_service = UserService(mock_client)

        # Create a valid UserModel
        user_model = UserModel(
            email="<EMAIL>",
            name="Updated User",
            attributes=UserAttributes(role=UserRole.DATA_SCIENTIST),
            groups=["test-group"],
        )

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.update_user(999, user_model)

        # Check the error message
        assert "User not found" in str(excinfo.value)

    def test_delete_user_not_found(self, mock_client):
        """Test deleting a user that doesn't exist."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise a 404 error
        mock_client.make_request.side_effect = Exception("User not found")

        # Create user service
        user_service = UserService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            user_service.delete_user(999)

        # Check the error message
        assert "User not found" in str(excinfo.value)
