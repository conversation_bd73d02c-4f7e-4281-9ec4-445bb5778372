"""Unit tests for policy service error handling."""

from unittest.mock import MagicMock

import pytest
import requests

from immuta_toolkit.services.policy_service import PolicyService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    return client


class TestPolicyServiceErrorHandling:
    """Test error handling in the policy service."""

    def test_get_policy_not_found(self, mock_client):
        """Test getting a policy that doesn't exist."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(ValueError) as excinfo:
            policy_service.get_policy(
                999
            )  # Use an ID that doesn't exist in the mock data

        # Check the error message
        assert "Policy not found" in str(excinfo.value)

    def test_get_policy_server_error(self, mock_client):
        """Test getting a policy with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.get_policy(1)

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_get_policy_connection_error(self, mock_client):
        """Test getting a policy with a connection error."""
        # Set up mock to raise a connection error
        mock_client.make_request.side_effect = requests.exceptions.ConnectionError(
            "Connection refused"
        )

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.get_policy(1)

        # Check the error message
        assert "Connection refused" in str(excinfo.value)

    def test_list_policies_server_error(self, mock_client):
        """Test listing policies with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.list_policies()

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_create_policy_invalid_input(self, mock_client):
        """Test creating a policy with invalid input."""
        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method with invalid input
        with pytest.raises(ValueError) as excinfo:
            policy_service.create_policy({})  # Empty policy

        # Check the error message
        assert "Missing required fields" in str(excinfo.value)

    def test_create_policy_server_error(self, mock_client):
        """Test creating a policy with a server error."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Mock the _validate_policy method to do nothing
        policy_service._validate_policy = MagicMock()

        # Set up mock to raise an exception
        mock_client.make_request.side_effect = Exception("Internal server error")

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.create_policy(
                {
                    "name": "Test Policy",
                    "description": "Test policy",
                    "actions": [{"type": "mask", "fields": ["email"]}],
                    "rules": [{"type": "tag", "value": "pii"}],
                }
            )

        # Check the error message
        assert "Internal server error" in str(excinfo.value)

    def test_update_policy_not_found(self, mock_client):
        """Test updating a policy that doesn't exist."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Mock the _validate_policy method to do nothing
        policy_service._validate_policy = MagicMock()

        # Set up mock to raise a 404 error
        mock_client.make_request.side_effect = Exception("Policy not found")

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.update_policy(
                999,
                {
                    "name": "Updated Policy",
                    "actions": [{"type": "mask", "fields": ["email"]}],
                    "rules": [{"type": "tag", "value": "pii"}],
                },
            )

        # Check the error message
        assert "Policy not found" in str(excinfo.value)

    def test_delete_policy_not_found(self, mock_client):
        """Test deleting a policy that doesn't exist."""
        # Set up mock client to not be in local mode
        mock_client.is_local = False

        # Set up mock to raise a 404 error
        mock_client.make_request.side_effect = Exception("Policy not found")

        # Create policy service
        policy_service = PolicyService(mock_client)

        # Call the method and check that it raises the expected exception
        with pytest.raises(Exception) as excinfo:
            policy_service.delete_policy(999)

        # Check the error message
        assert "Policy not found" in str(excinfo.value)
