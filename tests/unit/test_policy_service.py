"""Unit tests for the policy service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.policy_service import PolicyService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


@pytest.fixture
def sample_policy():
    """Sample policy for testing."""
    return {
        "name": "Test Policy",
        "description": "Test policy description",
        "actions": [{"type": "mask", "fields": ["email", "phone"]}],
        "rules": [{"type": "tag", "value": "pii"}],
    }


def test_list_policies(mock_client):
    """Test listing policies."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "name": "Policy 1", "description": "Description 1"},
        {"id": 2, "name": "Policy 2", "description": "Description 2"},
    ]

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test listing policies
    policies = policy_service.list_policies(limit=10, offset=0)

    # Check result
    assert len(policies) == 2
    assert policies[0]["id"] == 1
    assert policies[1]["name"] == "Policy 2"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "policy", params={"limit": 10, "offset": 0}
    )


def test_list_policies_local(mock_client):
    """Test listing policies in local mode."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test listing policies
    policies = policy_service.list_policies(limit=10, offset=0)

    # Check result
    assert len(policies) == 2
    assert policies[0]["name"] == "PII Masking Policy"
    assert policies[1]["name"] == "Financial Data Policy"


def test_get_policy(mock_client):
    """Test getting a policy."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Policy 1",
        "description": "Description 1",
        "actions": [{"type": "mask", "fields": ["email"]}],
        "rules": [{"type": "tag", "value": "pii"}],
    }

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test getting policy
    policy = policy_service.get_policy(1)

    # Check result
    assert policy["id"] == 1
    assert policy["name"] == "Policy 1"
    assert policy["actions"][0]["type"] == "mask"

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "policy/1")


def test_get_policy_local(mock_client):
    """Test getting a policy in local mode."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test getting policy
    policy = policy_service.get_policy(1)

    # Check result
    assert policy["id"] == 1
    assert policy["name"] == "PII Masking Policy"
    assert policy["actions"][0]["type"] == "mask"


@patch("time.time", return_value=12345)
def test_create_policy(_, mock_client, sample_policy):
    """Test creating a policy."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 3,
        "name": "Test Policy",
        "description": "Test policy description",
        "actions": [{"type": "mask", "fields": ["email", "phone"]}],
        "rules": [{"type": "tag", "value": "pii"}],
    }

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test creating policy
    result = policy_service.create_policy(
        policy=sample_policy,
        backup=True,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["id"] == 3
    assert result["name"] == "Test Policy"
    assert result["actions"][0]["type"] == "mask"

    # Check that POST request was made
    mock_client.make_request.assert_any_call(
        "POST",
        "policy",
        data=sample_policy,
    )

    # Check that storage was used for backup
    mock_client.storage.upload_file.assert_called_once()

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


def test_create_policy_dry_run(mock_client, sample_policy):
    """Test creating a policy in dry run mode."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test creating policy in dry run mode
    result = policy_service.create_policy(
        policy=sample_policy,
        backup=True,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["id"] == 0
    assert result["name"] == "Test Policy"

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


@patch("time.time", return_value=12345)
def test_update_policy(_, mock_client, sample_policy):
    """Test updating a policy."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_policy response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Test Policy",
        "description": "Test policy description",
        "actions": [{"type": "mask", "fields": ["email", "phone"]}],
        "rules": [{"type": "tag", "value": "pii"}],
    }

    # The implementation will get the current policy for backup

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test updating policy
    result = policy_service.update_policy(
        policy_id=1,
        policy=sample_policy,
        backup=True,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Test Policy"
    assert "phone" in result["actions"][0]["fields"]

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "policy/1")
    mock_client.make_request.assert_any_call(
        "PUT",
        "policy/1",
        data=sample_policy,
    )

    # Check that storage was used for backup
    mock_client.storage.upload_file.assert_called_once()

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


def test_update_policy_dry_run(mock_client, sample_policy):
    """Test updating a policy in dry run mode."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_policy response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Policy 1",
        "description": "Description 1",
        "actions": [{"type": "mask", "fields": ["email"]}],
        "rules": [{"type": "tag", "value": "pii"}],
    }

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test updating policy in dry run mode
    result = policy_service.update_policy(
        policy_id=1,
        policy=sample_policy,
        backup=True,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Test Policy"

    # In dry run mode, we don't need to make any API calls
    # The implementation might not make any calls or might make a GET call
    # So we don't assert on make_request

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


@patch("time.time", return_value=12345)
def test_delete_policy(_, mock_client):
    """Test deleting a policy."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_policy response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Policy 1",
        "description": "Description 1",
    }

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test deleting policy
    policy_service.delete_policy(
        policy_id=1,
        backup=True,
        dry_run=False,
    )

    # No result to check as the method returns None

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "policy/1")
    mock_client.make_request.assert_any_call("DELETE", "policy/1")

    # Check that storage was used for backup
    mock_client.storage.upload_file.assert_called_once()

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_policy_dry_run(mock_client):
    """Test deleting a policy in dry run mode."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_policy response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Policy 1",
        "description": "Description 1",
    }

    # Create policy service
    policy_service = PolicyService(mock_client)

    # Test deleting policy in dry run mode
    policy_service.delete_policy(
        policy_id=1,
        backup=True,
        dry_run=True,
    )

    # No result to check as the method returns None

    # In dry run mode, we don't need to make any API calls
    # The implementation might not make any calls or might make a GET call
    # So we don't assert on make_request

    # Check that notification was sent with any arguments
    mock_client.notifier.send_notification.assert_called_once()


def test_validate_policy_success(mock_client):
    """Test successful policy validation."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Valid policy
    valid_policy = {
        "name": "Valid Policy",
        "description": "A valid policy",
        "actions": [{"type": "mask", "fields": ["email", "phone"]}],
        "rules": [{"type": "tag", "value": "pii"}],
        "exceptions": {"groups": ["Admins"], "users": ["<EMAIL>"]},
        "circumstances": [{"type": "purpose", "value": "analytics"}],
    }

    # This should not raise an exception
    policy_service._validate_policy(valid_policy)


def test_validate_policy_missing_fields(mock_client):
    """Test policy validation with missing fields."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Policy missing required fields
    invalid_policy = {
        "name": "Invalid Policy",
        "description": "An invalid policy",
        # Missing actions and rules
    }

    # This should raise a ValueError
    with pytest.raises(ValueError) as excinfo:
        policy_service._validate_policy(invalid_policy)

    # Check error message
    assert "Missing required fields" in str(excinfo.value)


def test_validate_policy_invalid_action(mock_client):
    """Test policy validation with invalid action."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Policy with invalid action type
    invalid_policy = {
        "name": "Invalid Policy",
        "description": "An invalid policy",
        "actions": [{"type": "invalid_action"}],
        "rules": [{"type": "tag", "value": "pii"}],
    }

    # This should raise a ValueError
    with pytest.raises(ValueError) as excinfo:
        policy_service._validate_policy(invalid_policy)

    # Check error message
    assert "Invalid action type" in str(excinfo.value)


def test_validate_policy_invalid_rule(mock_client):
    """Test policy validation with invalid rule."""
    # Create policy service
    policy_service = PolicyService(mock_client)

    # Policy with invalid rule type
    invalid_policy = {
        "name": "Invalid Policy",
        "description": "An invalid policy",
        "actions": [{"type": "mask", "fields": ["email"]}],
        "rules": [{"type": "invalid_rule", "value": "something"}],
    }

    # This should raise a ValueError
    with pytest.raises(ValueError) as excinfo:
        policy_service._validate_policy(invalid_policy)

    # Check error message
    assert "Invalid rule type" in str(excinfo.value)
