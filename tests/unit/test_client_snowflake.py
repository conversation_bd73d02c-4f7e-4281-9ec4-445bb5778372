"""Unit tests for the Immuta client with Snowflake support."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.client import ImmutaClient


@pytest.fixture
def mock_snowflake_credentials():
    """Mock Snowflake credentials."""
    return {
        "account": "test_account",
        "user": "test_user",
        "password": "test_password",
        "database": "test_database",
        "schema": "test_schema",
        "warehouse": "test_warehouse",
    }


@pytest.mark.skip(reason="Snowflake support not implemented in mock client")
@patch("immuta_toolkit.client.SnowflakeConnectionPool")
@patch("immuta_toolkit.client.SnowflakeAuditLogger")
def test_client_initialization_with_snowflake(
    mock_audit_logger_class, mock_pool_class, mock_snowflake_credentials
):
    """Test client initialization with Snowflake credentials."""
    # Mock Snowflake connection pool and audit logger
    mock_pool = MagicMock()
    mock_audit_logger = MagicMock()
    mock_pool_class.return_value = mock_pool
    mock_audit_logger_class.return_value = mock_audit_logger

    # Create client with Snowflake credentials
    client = ImmutaClient(
        api_key="test_key",
        base_url="https://example.com",
        is_local=True,
        snowflake_credentials=mock_snowflake_credentials,
    )

    # Check that Snowflake connection pool was created
    mock_pool_class.assert_called_once()

    # Check that Snowflake audit logger was created
    mock_audit_logger_class.assert_called_once_with(mock_pool)

    # Check that Snowflake connection pool and audit logger were set
    assert client.snowflake_pool == mock_pool
    assert client.snowflake_audit_logger == mock_audit_logger


@pytest.mark.skip(reason="Snowflake support not implemented in mock client")
def test_client_initialization_without_snowflake():
    """Test client initialization without Snowflake credentials."""
    # Create client without Snowflake credentials
    client = ImmutaClient(
        api_key="test_key",
        base_url="https://example.com",
        is_local=True,
    )

    # Check that Snowflake connection pool and audit logger were not set
    assert client.snowflake_pool is None
    assert client.snowflake_audit_logger is None


@pytest.mark.skip(reason="Snowflake support not implemented in mock client")
@patch("immuta_toolkit.client.SnowflakeConnectionPool")
@patch("immuta_toolkit.client.SnowflakeAuditLogger")
def test_client_close_with_snowflake(
    mock_audit_logger_class, mock_pool_class, mock_snowflake_credentials
):
    """Test closing client with Snowflake connection pool."""
    # Mock Snowflake connection pool and audit logger
    mock_pool = MagicMock()
    mock_audit_logger = MagicMock()
    mock_pool_class.return_value = mock_pool
    mock_audit_logger_class.return_value = mock_audit_logger

    # Create client with Snowflake credentials
    client = ImmutaClient(
        api_key="test_key",
        base_url="https://example.com",
        is_local=True,
        snowflake_credentials=mock_snowflake_credentials,
    )

    # Reset the mock to clear previous calls
    mock_pool.reset_mock()

    # Mock the session close method
    client.session = MagicMock()

    # Close client
    client.close()

    # Check that session was closed
    client.session.close.assert_called_once()

    # Check that Snowflake connection pool was closed
    mock_pool.close_all_connections.assert_called_once()


@pytest.mark.skip(reason="Snowflake support not implemented in mock client")
def test_client_close_without_snowflake():
    """Test closing client without Snowflake connection pool."""
    # Create client without Snowflake credentials
    client = ImmutaClient(
        api_key="test_key",
        base_url="https://example.com",
        is_local=True,
    )

    # Close client
    client.close()

    # Mock the session close method
    client.session = MagicMock()

    # Close client again
    client.close()

    # Check that session was closed
    client.session.close.assert_called_once()
