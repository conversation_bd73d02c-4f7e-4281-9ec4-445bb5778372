"""Unit tests for the batch service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.batch_service import BatchService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = False
    client.notifier = MagicMock()
    client.storage = MagicMock()
    client.timeout = 30
    client.user_service = MagicMock()
    client.data_source_service = MagicMock()
    client.tag_service = MagicMock()
    client.policy_service = MagicMock()
    return client


def test_batch_update_users_validation(mock_client):
    """Test batch update users validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    # Should not raise an exception
    batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid users (missing user_id)
    invalid_users = [
        {"role": "Admin"},
        {"user_id": 2, "role": "DataScientist"},
    ]

    with pytest.raises(ValueError, match="Missing user_id in user:"):
        batch_service.batch_update_users(
            users=invalid_users,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid users (missing role)
    invalid_users = [
        {"user_id": 1},
        {"user_id": 2, "role": "DataScientist"},
    ]

    with pytest.raises(ValueError, match="Missing role in user:"):
        batch_service.batch_update_users(
            users=invalid_users,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid users (invalid role)
    invalid_users = [
        {"user_id": 1, "role": "InvalidRole"},
        {"user_id": 2, "role": "DataScientist"},
    ]

    with pytest.raises(ValueError, match="Invalid role for user"):
        batch_service.batch_update_users(
            users=invalid_users,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid users (invalid groups)
    invalid_users = [
        {"user_id": 1, "role": "Admin", "groups": "Group1"},
        {"user_id": 2, "role": "DataScientist"},
    ]

    with pytest.raises(ValueError, match="Groups must be a list for user"):
        batch_service.batch_update_users(
            users=invalid_users,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_update_users_dry_run(mock_client):
    """Test batch update users in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    result = batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_users"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_user_update_dry_run",
        {
            "user_count": 2,
            "roles_updated": ["Admin", "DataScientist"],
            "groups_updated": [[], ["Group1", "Group2"]],
            "details": "Would update 2 users with roles and group memberships",
        },
        "Success",
    )


def test_batch_update_users_local(mock_client):
    """Test batch update users in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    result = batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_users"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_user_update",
        {"user_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_users_api(mock_progress, mock_client):
    """Test batch update users via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "results": [
            {"status": "success", "user_id": 1},
            {"status": "success", "user_id": 2},
        ]
    }

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    result = batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=False,
        validate=True,
        batch_size=10,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_users"] == []

    # Check that API was called
    mock_client.make_request.assert_called_once_with(
        "PUT",
        "user/batch",
        data={
            "users": [
                {"user_id": 1, "attributes": {"role": "Admin"}, "groups": []},
                {
                    "user_id": 2,
                    "attributes": {"role": "DataScientist"},
                    "groups": ["Group1", "Group2"],
                },
            ]
        },
        timeout=mock_client.timeout * 3,
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_user_update",
        {
            "user_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_users_api_partial_success(mock_progress, mock_client):
    """Test batch update users via API with partial success."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "results": [
            {"status": "success", "user_id": 1},
            {"status": "error", "user_id": 2, "error": "User not found"},
        ]
    }

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    result = batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=False,
        validate=True,
        batch_size=10,
    )

    # Check result
    assert result["status"] == "partial_success"
    assert result["updated"] == 1
    assert result["failed"] == 1
    assert len(result["failed_users"]) == 1
    assert result["failed_users"][0]["user_id"] == 2

    # Check that API was called
    mock_client.make_request.assert_called_once_with(
        "PUT",
        "user/batch",
        data={
            "users": [
                {"user_id": 1, "attributes": {"role": "Admin"}, "groups": []},
                {
                    "user_id": 2,
                    "attributes": {"role": "DataScientist"},
                    "groups": ["Group1", "Group2"],
                },
            ]
        },
        timeout=mock_client.timeout * 3,
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_user_update",
        {
            "user_count": 2,
            "successful_count": 1,
            "failed_count": 1,
            "backup": None,
        },
        "Partial Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_users_api_failure(mock_progress, mock_client):
    """Test batch update users via API with failure."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.side_effect = Exception("API error")

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    # The method handles the exception internally and returns a result
    result = batch_service.batch_update_users(
        users=valid_users,
        backup=False,
        dry_run=False,
        validate=True,
        batch_size=10,
    )

    # Check result
    assert result["status"] == "failure"
    assert result["updated"] == 0
    assert result["failed"] == 2
    assert len(result["failed_users"]) == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_user_update",
        {
            "user_count": 2,
            "successful_count": 0,
            "failed_count": 2,
            "backup": None,
        },
        "Partial Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_users_with_backup(mock_progress, mock_client):
    """Test batch update users with backup."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "results": [
            {"status": "success", "user_id": 1},
            {"status": "success", "user_id": 2},
        ]
    }
    mock_client.user_service.list_users.return_value = [
        {"id": 1, "username": "user1", "role": "Admin"},
        {"id": 2, "username": "user2", "role": "DataScientist"},
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid users
    valid_users = [
        {"user_id": 1, "role": "Admin"},
        {"user_id": 2, "role": "DataScientist", "groups": ["Group1", "Group2"]},
    ]

    result = batch_service.batch_update_users(
        users=valid_users,
        backup=True,
        dry_run=False,
        validate=True,
        batch_size=10,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_users"] == []

    # Check that backup was created
    mock_client.user_service.list_users.assert_called_once()
    mock_client.storage.upload_file.assert_called_once()


def test_batch_tag_data_sources_validation(mock_client):
    """Test batch tag data sources validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    # Should not raise an exception
    batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid data sources (missing data_source_id)
    invalid_data_sources = [
        {"tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError, match="Missing data_source_id in data source:"):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (missing tags)
    invalid_data_sources = [
        {"data_source_id": 1},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError, match="Missing tags in data source:"):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (invalid tags)
    invalid_data_sources = [
        {"data_source_id": 1, "tags": "tag1"},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError, match="Tags must be a list for data source"):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_tag_data_sources_dry_run(mock_client):
    """Test batch tag data sources in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources_dry_run",
        {
            "data_source_count": 2,
            "tags": [["tag1", "tag2"], ["tag3", "tag4"]],
            "details": "Would tag 2 data sources",
        },
        "Success",
    )


def test_batch_tag_data_sources_local(mock_client):
    """Test batch tag data sources in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources",
        {"data_source_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_tag_data_sources_api(mock_progress, mock_client):
    """Test batch tag data sources via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.tag_service.add_tags_to_data_source = MagicMock()

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that API was called
    assert mock_client.tag_service.add_tags_to_data_source.call_count == 2
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=1, tags=["tag1", "tag2"]
    )
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=2, tags=["tag3", "tag4"]
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources",
        {
            "data_source_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_tag_data_sources_api_partial_success(mock_progress, mock_client):
    """Test batch tag data sources via API with partial success."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.tag_service.add_tags_to_data_source = MagicMock()
    mock_client.tag_service.add_tags_to_data_source.side_effect = [
        None,  # Success for first data source
        Exception("API error"),  # Failure for second data source
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "partial_success"
    assert result["tagged"] == 1
    assert result["failed"] == 1
    assert len(result["failed_data_sources"]) == 1
    assert result["failed_data_sources"][0]["data_source_id"] == 2

    # Check that API was called
    assert mock_client.tag_service.add_tags_to_data_source.call_count == 2
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=1, tags=["tag1", "tag2"]
    )
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=2, tags=["tag3", "tag4"]
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources",
        {
            "data_source_count": 2,
            "successful_count": 1,
            "failed_count": 1,
            "backup": None,
        },
        "Partial Success",
    )


def test_batch_create_policies_validation(mock_client):
    """Test batch create policies validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    # Should not raise an exception
    batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid policies (missing name)
    invalid_policies = [
        {"policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError, match="Missing name in policy:"):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid policies (missing policy_type)
    invalid_policies = [
        {"name": "Policy 1"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError, match="Missing policy_type in policy:"):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid policies (invalid policy_type)
    invalid_policies = [
        {"name": "Policy 1", "policy_type": "invalid_type"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError, match="Invalid policy_type:"):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_create_policies_dry_run(mock_client):
    """Test batch create policies in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    # In dry run mode, created_policies is an empty list in the actual implementation
    assert "created_policies" in result

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies_dry_run",
        {
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
            "details": "Would create 2 policies",
        },
        "Success",
    )


def test_batch_create_policies_local(mock_client):
    """Test batch create policies in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    assert len(result["created_policies"]) == 2

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies",
        {"policy_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_create_policies_api(mock_progress, mock_client):
    """Test batch create policies via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.policy_service.create_policy = MagicMock()
    mock_client.policy_service.create_policy.side_effect = [
        {"id": 1, "name": "Policy 1", "policy_type": "subscription"},
        {"id": 2, "name": "Policy 2", "policy_type": "masking"},
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    assert len(result["created_policies"]) == 2

    # Check that API was called
    assert mock_client.policy_service.create_policy.call_count == 2
    mock_client.policy_service.create_policy.assert_any_call(
        policy=valid_policies[0], backup=False
    )
    mock_client.policy_service.create_policy.assert_any_call(
        policy=valid_policies[1], backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies",
        {
            "policy_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_create_policies_api_partial_success(mock_progress, mock_client):
    """Test batch create policies via API with partial success."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.policy_service.create_policy = MagicMock()
    mock_client.policy_service.create_policy.side_effect = [
        {"id": 1, "name": "Policy 1", "policy_type": "subscription"},
        Exception("API error"),
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "partial_success"
    assert result["created"] == 1
    assert result["failed"] == 1
    assert len(result["failed_policies"]) == 1
    assert result["failed_policies"][0]["policy"]["name"] == "Policy 2"
    assert len(result["created_policies"]) == 1
    assert result["created_policies"][0]["name"] == "Policy 1"

    # Check that API was called
    assert mock_client.policy_service.create_policy.call_count == 2
    mock_client.policy_service.create_policy.assert_any_call(
        policy=valid_policies[0], backup=False
    )
    mock_client.policy_service.create_policy.assert_any_call(
        policy=valid_policies[1], backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies",
        {
            "policy_count": 2,
            "successful_count": 1,
            "failed_count": 1,
            "backup": None,
        },
        "Partial Success",
    )


def test_batch_update_data_source_metadata_validation(mock_client):
    """Test batch update data source metadata validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    # Should not raise an exception
    batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid data sources (missing data_source_id)
    invalid_data_sources = [
        {"metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    with pytest.raises(ValueError, match="Missing data_source_id in data source:"):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (missing metadata)
    invalid_data_sources = [
        {"data_source_id": 1},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    with pytest.raises(ValueError, match="Missing metadata in data source:"):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (invalid metadata)
    invalid_data_sources = [
        {"data_source_id": 1, "metadata": "invalid"},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    with pytest.raises(
        ValueError, match="Metadata must be a dictionary for data source"
    ):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_update_data_source_metadata_dry_run(mock_client):
    """Test batch update data source metadata in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata_dry_run",
        {
            "data_source_count": 2,
            "details": "Would update metadata for 2 data sources",
        },
        "Success",
    )


def test_batch_update_data_source_metadata_local(mock_client):
    """Test batch update data source metadata in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata",
        {"data_source_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_data_source_metadata_api(mock_progress, mock_client):
    """Test batch update data source metadata via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.data_source_service.update_data_source = MagicMock()

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that API was called
    assert mock_client.data_source_service.update_data_source.call_count == 2
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=1, metadata={"owner": "user1", "department": "IT"}, backup=False
    )
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=2, metadata={"owner": "user2", "department": "HR"}, backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata",
        {
            "data_source_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_data_source_metadata_api_partial_success(
    mock_progress, mock_client
):
    """Test batch update data source metadata via API with partial success."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.data_source_service.update_data_source = MagicMock()
    mock_client.data_source_service.update_data_source.side_effect = [
        None,  # Success for first data source
        Exception("API error"),  # Failure for second data source
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"owner": "user1", "department": "IT"}},
        {"data_source_id": 2, "metadata": {"owner": "user2", "department": "HR"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "partial_success"
    assert result["updated"] == 1
    assert result["failed"] == 1
    assert len(result["failed_data_sources"]) == 1
    assert result["failed_data_sources"][0]["data_source_id"] == 2

    # Check that API was called
    assert mock_client.data_source_service.update_data_source.call_count == 2
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=1, metadata={"owner": "user1", "department": "IT"}, backup=False
    )
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=2, metadata={"owner": "user2", "department": "HR"}, backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata",
        {
            "data_source_count": 2,
            "successful_count": 1,
            "failed_count": 1,
            "backup": None,
        },
        "Partial Success",
    )
