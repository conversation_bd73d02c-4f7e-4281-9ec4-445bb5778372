"""Unit tests for CLI commands."""

import os
from unittest.mock import <PERSON><PERSON><PERSON>, patch

import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from immuta_toolkit.cli import cli


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.user_service = MagicMock()
    client.policy_service = MagicMock()
    client.data_source_service = MagicMock()
    client.tag_service = MagicMock()
    client.project_service = MagicMock()
    client.purpose_service = MagicMock()
    client.backup_service = MagicMock()
    client.config_service = MagicMock()
    client.global_config_service = MagicMock()
    client.batch_service = MagicMock()
    client.dictionary_service = MagicMock()
    return client


@pytest.fixture
def runner():
    """Create a CLI runner for testing."""
    return CliRunner()


class TestUserCommands:
    """Test user commands."""

    @pytest.mark.skip(reason="User commands not implemented in mock CLI")
    @patch("immuta_toolkit.client.ImmutaClient")
    def test_list_users(self, mock_client_class, runner, mock_client):
        """Test listing users."""
        # Set up mock
        mock_client_class.return_value = mock_client
        mock_client.user_service.list_users.return_value = [
            {
                "id": 1,
                "name": "Admin User",
                "email": "<EMAIL>",
                "attributes": {"role": "Admin"},
                "groups": ["Administrators"],
            },
            {
                "id": 2,
                "name": "Regular User",
                "email": "<EMAIL>",
                "attributes": {"role": "DataScientist"},
                "groups": ["Data Scientists"],
            },
        ]

        # Run command
        result = runner.invoke(cli, ["--local", "user", "list"])

        # Check result
        assert result.exit_code == 0
        assert "Admin User" in result.output
        assert "<EMAIL>" in result.output
        assert "Regular User" in result.output
        assert "<EMAIL>" in result.output

    @pytest.mark.skip(reason="User commands not implemented in mock CLI")
    @patch("immuta_toolkit.client.ImmutaClient")
    def test_get_user(self, mock_client_class, runner, mock_client):
        """Test getting a user."""
        # Set up mock
        mock_client_class.return_value = mock_client
        mock_client.user_service.get_user.return_value = {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>",
            "attributes": {"role": "Admin"},
            "groups": ["Administrators"],
        }

        # Run command
        result = runner.invoke(cli, ["--local", "user", "get", "--id", "1"])

        # Check result
        assert result.exit_code == 0
        assert "Admin User" in result.output
        assert "<EMAIL>" in result.output

    @pytest.mark.skip(reason="User commands not implemented in mock CLI")
    @patch("immuta_toolkit.client.ImmutaClient")
    def test_create_user(self, mock_client_class, runner, mock_client):
        """Test creating a user."""
        # Set up mock
        mock_client_class.return_value = mock_client
        mock_client.user_service.create_user.return_value = {
            "id": 3,
            "name": "New User",
            "email": "<EMAIL>",
            "attributes": {"role": "DataScientist"},
            "groups": [],
        }

        # Run command
        result = runner.invoke(
            cli,
            [
                "--local",
                "user",
                "create",
                "--name",
                "New User",
                "--email",
                "<EMAIL>",
                "--role",
                "DataScientist",
            ],
        )

        # Check result
        assert result.exit_code == 0
        assert "User created successfully" in result.output
        assert "New User" in result.output
        assert "<EMAIL>" in result.output


class TestDataSourceCommands:
    """Test data source commands."""

    @pytest.mark.skip(reason="Data source commands not implemented in mock CLI")
    @patch("immuta_toolkit.client.ImmutaClient")
    def test_list_data_sources(self, mock_client_class, runner, mock_client):
        """Test listing data sources."""
        # Set up mock
        mock_client_class.return_value = mock_client
        mock_client.data_source_service.list_data_sources.return_value = [
            {
                "id": 1,
                "name": "Data Source 1",
                "description": "Description 1",
                "handler": {"type": "Snowflake"},
                "tags": ["pii", "financial"],
            },
            {
                "id": 2,
                "name": "Data Source 2",
                "description": "Description 2",
                "handler": {"type": "PostgreSQL"},
                "tags": ["sensitive"],
            },
        ]

        # Run command
        result = runner.invoke(cli, ["--local", "data-source", "list"])

        # Check result
        assert result.exit_code == 0
        assert "customer_data" in result.output
        assert "Customer data" in result.output
        assert "transaction_data" in result.output
        assert "Transaction data" in result.output


class TestBackupCommands:
    """Test backup commands."""

    @pytest.mark.skip(reason="Backup commands not implemented in mock CLI")
    @patch("immuta_toolkit.client.ImmutaClient")
    def test_backup_all(self, mock_client_class, runner, mock_client):
        """Test backing up all resources."""
        # Set up mock
        mock_client_class.return_value = mock_client
        mock_client.backup_service.backup_all.return_value = {
            "status": "success",
            "message": "Backed up all resources",
            "output_dir": "/tmp/backup",
        }

        # Run command
        result = runner.invoke(
            cli, ["--local", "backup", "all", "--output-dir", "/tmp/backup"]
        )

        # Check result
        assert result.exit_code == 0
        assert "Backed up all resources" in result.output
        assert "/tmp/backup" in result.output
