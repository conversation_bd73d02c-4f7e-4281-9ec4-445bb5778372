"""Unit tests for project service data source management methods."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestProjectServiceDataSources:
    """Test project data source management methods."""

    def test_get_project_data_sources(self, mock_client):
        """Test getting project data sources."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {"id": 1, "name": "Customer Data", "description": "Customer data source"},
            {
                "id": 2,
                "name": "Transaction Data",
                "description": "Transaction data source",
            },
        ]
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.get_project_data_sources(1)

        # Check the result
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[0]["name"] == "Customer Data"
        assert result[1]["id"] == 2
        assert result[1]["name"] == "Transaction Data"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "GET", "projects/1/dataSources"
        )

    def test_get_project_data_sources_local_mode(self, mock_client):
        """Test getting project data sources in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.get_project_data_sources(1)

        # Check the result - in local mode, an empty list is returned
        assert isinstance(result, list)
        # Local mode doesn't populate data

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_add_data_source_to_project(self, mock_client):
        """Test adding a data source to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_data_source_to_project(1, 3)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/dataSources", data={"dataSourceId": 3}
        )

    def test_add_data_source_to_project_dry_run(self, mock_client):
        """Test adding a data source to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_data_source_to_project(1, 3, dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "data_source_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_add_data_source_to_project_local_mode(self, mock_client):
        """Test adding a data source to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_data_source_to_project(1, 3)

        # Check the result - local mode returns additional info
        assert result["success"] is True
        assert "project_id" in result
        assert "data_source_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_data_source_from_project(self, mock_client):
        """Test removing a data source from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_data_source_from_project(1, 2)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/dataSources/2"
        )

    def test_remove_data_source_from_project_dry_run(self, mock_client):
        """Test removing a data source from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_data_source_from_project(1, 2, dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "data_source_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_data_source_from_project_local_mode(self, mock_client):
        """Test removing a data source from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_data_source_from_project(1, 2)

        # Check the result - local mode returns additional info
        assert result["success"] is True
        assert "project_id" in result
        assert "data_source_id" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
