"""Unit tests for the data source service."""

import os
import tempfile
from unittest.mock import Magic<PERSON>ock

import pytest
import yaml

from immuta_toolkit.services.data_source_service import DataSourceService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    client.tag_service = MagicMock()
    return client


@pytest.fixture
def sample_data_source():
    """Sample data source for testing."""
    return {
        "name": "Test Data Source",
        "description": "Test data source description",
        "handler": {
            "type": "snowflake",
            "connectionString": "snowflake://user:password@account/database/schema?warehouse=warehouse",
            "table": "test_table",
        },
        "tags": ["pii", "financial"],
        "columns": [
            {
                "name": "id",
                "dataType": "INTEGER",
                "description": "Unique identifier",
            },
            {
                "name": "email",
                "dataType": "VARCHAR",
                "description": "User email",
                "tags": ["pii", "email"],
            },
        ],
    }


def test_list_data_sources(mock_client):
    """Test listing data sources."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "name": "Data Source 1", "description": "Description 1"},
        {"id": 2, "name": "Data Source 2", "description": "Description 2"},
    ]

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test listing data sources
    data_sources = data_source_service.list_data_sources(limit=10, offset=0)

    # Check result
    assert len(data_sources) == 2
    assert data_sources[0]["id"] == 1
    assert data_sources[1]["name"] == "Data Source 2"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource", params={"limit": 10, "offset": 0}
    )


def test_list_data_sources_local(mock_client):
    """Test listing data sources in local mode."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test listing data sources
    data_sources = data_source_service.list_data_sources(limit=10, offset=0)

    # Check result
    assert len(data_sources) == 2
    assert data_sources[0]["name"] == "customer_data"
    assert data_sources[1]["name"] == "transaction_data"


def test_get_data_source(mock_client):
    """Test getting a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Data Source 1",
        "description": "Description 1",
        "handler": {"type": "snowflake", "table": "test_table"},
        "columns": [
            {"name": "id", "dataType": "INTEGER"},
            {"name": "email", "dataType": "VARCHAR", "tags": ["pii"]},
        ],
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test getting data source
    data_source = data_source_service.get_data_source(1)

    # Check result
    assert data_source["id"] == 1
    assert data_source["name"] == "Data Source 1"
    assert data_source["handler"]["type"] == "snowflake"
    assert data_source["columns"][1]["tags"] == ["pii"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "dataSource/1")


def test_get_data_source_local(mock_client):
    """Test getting a data source in local mode."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test getting data source
    data_source = data_source_service.get_data_source(1)

    # Check result
    assert data_source["id"] == 1
    assert data_source["name"] == "customer_data"
    assert "email" in data_source["columns"]


def test_create_data_source(mock_client, sample_data_source):
    """Test creating a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 3,
        "name": "Test Data Source",
        "description": "Test data source description",
        "handler": {"type": "snowflake", "table": "test_table"},
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Disable storage to avoid backup
    mock_client.storage = None

    # Test creating data source
    result = data_source_service.create_data_source(
        data_source=sample_data_source,
        backup=False,  # Set backup to False to avoid the list_data_sources call
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["id"] == 3
    assert result["name"] == "Test Data Source"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "dataSource", data=sample_data_source
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_create_data_source_dry_run(mock_client, sample_data_source):
    """Test creating a data source in dry run mode."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test creating data source in dry run mode
    result = data_source_service.create_data_source(
        data_source=sample_data_source,
        backup=True,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["id"] == 0
    assert result["name"] == "Test Data Source"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "data_source_create_dry_run",
        {
            "name": "Test Data Source",
            "handler_type": "snowflake",
            "tags": "pii, financial",
        },
        "Success",
    )


def test_update_data_source(mock_client, sample_data_source):
    """Test updating a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_data_source and update responses
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": 1,
            "name": "Original Data Source",
            "description": "Original description",
        },
        {
            "id": 1,
            "name": "Test Data Source",
            "description": "Test data source description",
        },
    ]

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test updating data source
    result = data_source_service.update_data_source(
        data_source_id=1,
        data_source=sample_data_source,
        backup=True,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Test Data Source"

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "dataSource/1")
    mock_client.make_request.assert_any_call(
        "PUT", "dataSource/1", data=sample_data_source
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_tag_existing_data_sources(mock_client):
    """Test tagging existing data sources."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock list_data_sources and get_data_source responses
    mock_client.make_request.return_value.json.side_effect = [
        [
            {"id": 1, "name": "customer_data"},
            {"id": 2, "name": "transaction_data"},
        ],
        {
            "id": 1,
            "name": "customer_data",
            "columns": {
                "id": {"dataType": "INTEGER"},
                "email": {"dataType": "VARCHAR", "tags": []},
                "address": {"dataType": "VARCHAR", "tags": []},
            },
        },
        {
            "id": 2,
            "name": "transaction_data",
            "columns": {
                "id": {"dataType": "INTEGER"},
                "amount": {"dataType": "DECIMAL", "tags": []},
                "credit_card": {"dataType": "VARCHAR", "tags": []},
            },
        },
    ]

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Create temporary tag config file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
        yaml.dump(
            {
                "data_source_tags": {
                    "customer": ["pii", "sensitive"],
                    "transaction": ["financial"],
                },
                "column_tags": {
                    "email": ["pii", "email"],
                    "address": ["pii", "address"],
                    "credit_card": ["pci", "financial"],
                },
            },
            f,
        )
        config_path = f.name

    try:
        # Test tagging existing data sources
        result = data_source_service.tag_existing_data_sources(
            config_file=config_path,
            backup=True,
            dry_run=False,
            validate=True,
        )

        # Check result
        assert "tagged" in result
        assert "skipped" in result
        assert "failed" in result

        # Check that a request was made
        assert mock_client.make_request.call_count >= 1

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
    finally:
        # Clean up
        os.unlink(config_path)


def test_tag_existing_data_sources_dry_run(mock_client):
    """Test tagging existing data sources in dry run mode."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock list_data_sources response
    mock_client.make_request.return_value.json.return_value = [
        {"id": 1, "name": "customer_data"},
        {"id": 2, "name": "transaction_data"},
    ]

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Create temporary tag config file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
        yaml.dump(
            {
                "data_source_tags": {
                    "customer": ["pii", "sensitive"],
                    "transaction": ["financial"],
                },
                "column_tags": {
                    "email": ["pii", "email"],
                    "address": ["pii", "address"],
                    "credit_card": ["pci", "financial"],
                },
            },
            f,
        )
        config_path = f.name

    try:
        # Test tagging existing data sources in dry run mode
        result = data_source_service.tag_existing_data_sources(
            config_file=config_path,
            backup=True,
            dry_run=True,
            validate=True,
        )

        # Check result
        assert "total" in result

        # Check that a request was made
        mock_client.make_request.assert_called_once()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
    finally:
        # Clean up
        os.unlink(config_path)


def test_trigger_schema_monitoring_data_source_ids(mock_client):
    """Test triggering schema monitoring with data source IDs."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "Schema monitoring triggered for data sources: [1, 2]",
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test triggering schema monitoring
    result = data_source_service.trigger_schema_monitoring(
        data_source_ids=[1, 2],
        dry_run=False,
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "dataSource/detectRemoteChanges", data={"dataSourceIds": [1, 2]}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_trigger_schema_monitoring_schema_id(mock_client):
    """Test triggering schema monitoring with schema ID."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "Schema monitoring triggered for schema: 123",
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test triggering schema monitoring
    result = data_source_service.trigger_schema_monitoring(
        schema_id=123,
        dry_run=False,
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "dataSource/detectRemoteChanges", data={"schemaId": 123}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_trigger_schema_monitoring_hostname_database(mock_client):
    """Test triggering schema monitoring with hostname and database."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "Schema monitoring triggered for database.example.com:5432/mydb",
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test triggering schema monitoring
    result = data_source_service.trigger_schema_monitoring(
        hostname="database.example.com",
        database="mydb",
        port=5432,
        table="users",
        dry_run=False,
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST",
        "dataSource/detectRemoteChanges",
        data={
            "hostname": "database.example.com",
            "database": "mydb",
            "port": 5432,
            "table": "users",
        },
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_trigger_schema_monitoring_dry_run(mock_client):
    """Test triggering schema monitoring in dry run mode."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test triggering schema monitoring in dry run mode
    result = data_source_service.trigger_schema_monitoring(
        data_source_ids=[1, 2],
        dry_run=True,
    )

    # Check result
    assert result["success"] is True
    assert result["message"] == "Dry run completed"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "schema_monitoring_dry_run",
        {
            "data_source_ids": [1, 2],
            "schema_id": None,
            "hostname": None,
            "database": None,
            "port": None,
            "table": None,
        },
        "Success",
    )


def test_trigger_schema_monitoring_invalid_params(mock_client):
    """Test triggering schema monitoring with invalid parameters."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test with both schema_id and data_source_ids
    with pytest.raises(ValueError):
        data_source_service.trigger_schema_monitoring(
            data_source_ids=[1, 2],
            schema_id=123,
        )

    # Test with no parameters
    with pytest.raises(ValueError):
        data_source_service.trigger_schema_monitoring()


def test_refresh_views(mock_client):
    """Test refreshing views."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "message": "Views refreshed for data sources: [1, 2]",
    }

    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test refreshing views
    result = data_source_service.refresh_views(
        data_source_ids=[1, 2],
        dry_run=False,
    )

    # Check result
    assert result["success"] is True
    assert "message" in result

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST",
        "dataSource/bulkRefreshViews",
        data={"dataSourceIds": [1, 2]},
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_refresh_views_dry_run(mock_client):
    """Test refreshing views in dry run mode."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test refreshing views in dry run mode
    result = data_source_service.refresh_views(
        data_source_ids=[1, 2],
        dry_run=True,
    )

    # Check result
    assert result["success"] is True
    assert result["message"] == "Dry run completed"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "refresh_views_dry_run",
        {"data_source_ids": [1, 2]},
        "Success",
    )


def test_refresh_views_invalid_params(mock_client):
    """Test refreshing views with invalid parameters."""
    # Create data source service
    data_source_service = DataSourceService(mock_client)

    # Test with empty data_source_ids
    with pytest.raises(ValueError):
        data_source_service.refresh_views(
            data_source_ids=[],
        )
