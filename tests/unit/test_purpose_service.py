"""Unit tests for the purpose service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.purpose_service import PurposeService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_list_purposes(mock_client):
    """Test listing purposes."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "name": "Marketing Analytics",
            "description": "For marketing analytics purposes",
            "approvalRequired": True,
            "autoApproval": False,
            "approvalWorkflow": "manual",
            "approvers": ["<EMAIL>"],
            "tags": ["marketing", "analytics"],
        },
        {
            "id": 2,
            "name": "Fraud Detection",
            "description": "For fraud detection purposes",
            "approvalRequired": True,
            "autoApproval": True,
            "approvalWorkflow": "auto",
            "approvers": [],
            "tags": ["fraud", "security"],
        },
    ]

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test listing purposes
    purposes = purpose_service.list_purposes(limit=10, offset=0)

    # Check result
    assert len(purposes) == 2
    assert purposes[0]["id"] == 1
    assert purposes[0]["name"] == "Marketing Analytics"
    assert purposes[1]["id"] == 2
    assert purposes[1]["name"] == "Fraud Detection"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "purposes", params={"limit": 10, "offset": 0}
    )


def test_list_purposes_local(mock_client):
    """Test listing purposes in local mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test listing purposes
    purposes = purpose_service.list_purposes()

    # Check result
    assert len(purposes) == 2
    assert purposes[0]["id"] == 1
    assert purposes[0]["name"] == "Marketing Analytics"
    assert purposes[1]["id"] == 2
    assert purposes[1]["name"] == "Fraud Detection"


def test_get_purpose(mock_client):
    """Test getting purpose by ID."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Marketing Analytics",
        "description": "For marketing analytics purposes",
        "approvalRequired": True,
        "autoApproval": False,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>"],
        "tags": ["marketing", "analytics"],
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose by ID
    purpose = purpose_service.get_purpose(1)

    # Check result
    assert purpose["id"] == 1
    assert purpose["name"] == "Marketing Analytics"
    assert purpose["approvalRequired"] is True
    assert purpose["autoApproval"] is False
    assert purpose["approvers"] == ["<EMAIL>"]
    assert purpose["tags"] == ["marketing", "analytics"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "purposes/1")


def test_get_purpose_local(mock_client):
    """Test getting purpose by ID in local mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose by ID
    purpose = purpose_service.get_purpose(1)

    # Check result
    assert purpose["id"] == 1
    assert purpose["name"] == "Marketing Analytics"
    assert purpose["approvalRequired"] is True
    assert purpose["autoApproval"] is False
    assert purpose["approvers"] == ["<EMAIL>"]
    assert purpose["tags"] == ["marketing", "analytics"]

    # Test getting purpose by ID that doesn't exist
    with pytest.raises(ValueError):
        purpose_service.get_purpose(999)


def test_create_purpose(mock_client):
    """Test creating a purpose."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": 3,
        "name": "Customer Segmentation",
        "description": "For customer segmentation purposes",
        "approvalRequired": True,
        "autoApproval": False,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>"],
        "tags": ["customer", "segmentation"],
    }

    # Disable storage to avoid backup
    mock_client.storage = None

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test creating a purpose
    purpose_data = {
        "name": "Customer Segmentation",
        "description": "For customer segmentation purposes",
        "approvalRequired": True,
        "autoApproval": False,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>"],
        "tags": ["customer", "segmentation"],
    }
    result = purpose_service.create_purpose(
        purpose=purpose_data, backup=False, dry_run=False, validate=True
    )

    # Check result
    assert result["id"] == 3
    assert result["name"] == "Customer Segmentation"
    assert result["approvalRequired"] is True
    assert result["autoApproval"] is False
    assert result["approvers"] == ["<EMAIL>"]
    assert result["tags"] == ["customer", "segmentation"]

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "purposes", data=purpose_data
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_create_purpose_dry_run(mock_client):
    """Test creating a purpose in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test creating a purpose in dry run mode
    purpose_data = {
        "name": "Customer Segmentation",
        "description": "For customer segmentation purposes",
        "approvalRequired": True,
        "autoApproval": False,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>"],
        "tags": ["customer", "segmentation"],
    }
    result = purpose_service.create_purpose(
        purpose=purpose_data, backup=True, dry_run=True, validate=True
    )

    # Check result
    assert result["id"] == 0
    assert result["name"] == "Customer Segmentation"
    assert result["approvalRequired"] is True
    assert result["autoApproval"] is False
    assert result["approvers"] == ["<EMAIL>"]
    assert result["tags"] == ["customer", "segmentation"]

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "purpose_create_dry_run",
        {
            "name": "Customer Segmentation",
            "description": "For customer segmentation purposes",
            "approval_required": True,
        },
        "Success",
    )


def test_create_purpose_invalid(mock_client):
    """Test creating an invalid purpose."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test creating a purpose with missing name
    invalid_purpose = {
        "description": "For customer segmentation purposes",
        "approvalRequired": True,
    }
    with pytest.raises(ValueError):
        purpose_service.create_purpose(
            purpose=invalid_purpose, backup=True, dry_run=False, validate=True
        )

    # Test creating a purpose with missing description
    invalid_purpose = {
        "name": "Customer Segmentation",
        "approvalRequired": True,
    }
    with pytest.raises(ValueError):
        purpose_service.create_purpose(
            purpose=invalid_purpose, backup=True, dry_run=False, validate=True
        )


def test_update_purpose(mock_client):
    """Test updating a purpose."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_purpose and update responses
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": 1,
            "name": "Marketing Analytics",
            "description": "For marketing analytics purposes",
            "approvalRequired": True,
            "autoApproval": False,
            "approvalWorkflow": "manual",
            "approvers": ["<EMAIL>"],
            "tags": ["marketing", "analytics"],
        },
        {
            "id": 1,
            "name": "Marketing Analytics Updated",
            "description": "For marketing analytics purposes - updated",
            "approvalRequired": True,
            "autoApproval": True,
            "approvalWorkflow": "manual",
            "approvers": ["<EMAIL>", "<EMAIL>"],
            "tags": ["marketing", "analytics", "updated"],
        },
    ]

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test updating a purpose
    purpose_data = {
        "name": "Marketing Analytics Updated",
        "description": "For marketing analytics purposes - updated",
        "approvalRequired": True,
        "autoApproval": True,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>", "<EMAIL>"],
        "tags": ["marketing", "analytics", "updated"],
    }
    result = purpose_service.update_purpose(
        purpose_id=1, purpose=purpose_data, backup=True, dry_run=False, validate=True
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Marketing Analytics Updated"
    assert result["description"] == "For marketing analytics purposes - updated"
    assert result["approvalRequired"] is True
    assert result["autoApproval"] is True
    assert result["approvers"] == ["<EMAIL>", "<EMAIL>"]
    assert result["tags"] == ["marketing", "analytics", "updated"]

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "purposes/1")
    mock_client.make_request.assert_any_call("PUT", "purposes/1", data=purpose_data)

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_update_purpose_dry_run(mock_client):
    """Test updating a purpose in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test updating a purpose in dry run mode
    purpose_data = {
        "name": "Marketing Analytics Updated",
        "description": "For marketing analytics purposes - updated",
        "approvalRequired": True,
        "autoApproval": True,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>", "<EMAIL>"],
        "tags": ["marketing", "analytics", "updated"],
    }
    result = purpose_service.update_purpose(
        purpose_id=1, purpose=purpose_data, backup=True, dry_run=True, validate=True
    )

    # Check result
    assert result["id"] == 1
    assert result["name"] == "Marketing Analytics Updated"
    assert result["description"] == "For marketing analytics purposes - updated"
    assert result["approvalRequired"] is True
    assert result["autoApproval"] is True
    assert result["approvers"] == ["<EMAIL>", "<EMAIL>"]
    assert result["tags"] == ["marketing", "analytics", "updated"]

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "purpose_update_dry_run",
        {
            "id": 1,
            "name": "Marketing Analytics Updated",
            "description": "For marketing analytics purposes - updated",
        },
        "Success",
    )


def test_delete_purpose(mock_client):
    """Test deleting a purpose."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()

    # Mock get_purpose response
    mock_client.make_request.return_value.json.return_value = {
        "id": 1,
        "name": "Marketing Analytics",
        "description": "For marketing analytics purposes",
        "approvalRequired": True,
        "autoApproval": False,
        "approvalWorkflow": "manual",
        "approvers": ["<EMAIL>"],
        "tags": ["marketing", "analytics"],
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test deleting a purpose
    result = purpose_service.delete_purpose(purpose_id=1, backup=True, dry_run=False)

    # Check result
    assert result["status"] == "success"
    assert result["purpose_id"] == 1

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "purposes/1")
    mock_client.make_request.assert_any_call("DELETE", "purposes/1")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_purpose_dry_run(mock_client):
    """Test deleting a purpose in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test deleting a purpose in dry run mode
    result = purpose_service.delete_purpose(purpose_id=1, backup=True, dry_run=True)

    # Check result
    assert result["status"] == "dry_run_success"
    assert result["purpose_id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "purpose_delete_dry_run",
        {
            "purpose_id": 1,
            "name": "Marketing Analytics",
        },
        "Success",
    )


def test_get_purpose_approvals(mock_client):
    """Test getting purpose approvals."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "purposeId": 1,
            "userId": 1,
            "userName": "John Doe",
            "userEmail": "<EMAIL>",
            "status": "pending",
            "requestedAt": "2023-01-01T00:00:00Z",
            "reason": "Need access for marketing analysis",
        },
        {
            "id": 2,
            "purposeId": 1,
            "userId": 2,
            "userName": "Jane Smith",
            "userEmail": "<EMAIL>",
            "status": "approved",
            "requestedAt": "2023-01-02T00:00:00Z",
            "approvedAt": "2023-01-03T00:00:00Z",
            "reason": "Need access for customer segmentation",
        },
    ]

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose approvals
    approvals = purpose_service.get_purpose_approvals(purpose_id=1)

    # Check result
    assert len(approvals) == 2
    assert approvals[0]["id"] == 1
    assert approvals[0]["status"] == "pending"
    assert approvals[1]["id"] == 2
    assert approvals[1]["status"] == "approved"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "purposes/1/approvals", params={}
    )


def test_get_purpose_approvals_with_filter(mock_client):
    """Test getting purpose approvals with filter."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "purposeId": 1,
            "userId": 1,
            "userName": "John Doe",
            "userEmail": "<EMAIL>",
            "status": "pending",
            "requestedAt": "2023-01-01T00:00:00Z",
            "reason": "Need access for marketing analysis",
        },
    ]

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose approvals with filter
    approvals = purpose_service.get_purpose_approvals(purpose_id=1, status="pending")

    # Check result
    assert len(approvals) == 1
    assert approvals[0]["id"] == 1
    assert approvals[0]["status"] == "pending"

    # Check that request was made with filter
    mock_client.make_request.assert_called_once_with(
        "GET", "purposes/1/approvals", params={"status": "pending"}
    )


def test_get_purpose_approvals_local(mock_client):
    """Test getting purpose approvals in local mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose approvals
    approvals = purpose_service.get_purpose_approvals(purpose_id=1)

    # Check result
    assert len(approvals) == 3
    assert approvals[0]["id"] == 1
    assert approvals[0]["status"] == "pending"
    assert approvals[1]["id"] == 2
    assert approvals[1]["status"] == "approved"
    assert approvals[2]["id"] == 3
    assert approvals[2]["status"] == "denied"

    # Test getting purpose approvals with filter
    approvals = purpose_service.get_purpose_approvals(purpose_id=1, status="pending")

    # Check result
    assert len(approvals) == 1
    assert approvals[0]["id"] == 1
    assert approvals[0]["status"] == "pending"


def test_approve_purpose_request(mock_client):
    """Test approving a purpose request."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "purpose_id": 1,
        "approval_id": 1,
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test approving a purpose request
    result = purpose_service.approve_purpose_request(purpose_id=1, approval_id=1)

    # Check result
    assert result["success"] is True
    assert result["purpose_id"] == 1
    assert result["approval_id"] == 1

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "purposes/1/approvals/1/approve"
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_approve_purpose_request_dry_run(mock_client):
    """Test approving a purpose request in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test approving a purpose request in dry run mode
    result = purpose_service.approve_purpose_request(
        purpose_id=1, approval_id=1, dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["purpose_id"] == 1
    assert result["approval_id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "approve_purpose_request_dry_run",
        {
            "purpose_id": 1,
            "approval_id": 1,
        },
        "Success",
    )


def test_deny_purpose_request(mock_client):
    """Test denying a purpose request."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "purpose_id": 1,
        "approval_id": 1,
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test denying a purpose request
    result = purpose_service.deny_purpose_request(
        purpose_id=1, approval_id=1, reason="Insufficient justification"
    )

    # Check result
    assert result["success"] is True
    assert result["purpose_id"] == 1
    assert result["approval_id"] == 1

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST",
        "purposes/1/approvals/1/deny",
        data={"reason": "Insufficient justification"},
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_deny_purpose_request_dry_run(mock_client):
    """Test denying a purpose request in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test denying a purpose request in dry run mode
    result = purpose_service.deny_purpose_request(
        purpose_id=1, approval_id=1, reason="Insufficient justification", dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["purpose_id"] == 1
    assert result["approval_id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "deny_purpose_request_dry_run",
        {
            "purpose_id": 1,
            "approval_id": 1,
            "reason": "Insufficient justification",
        },
        "Success",
    )


def test_deny_purpose_request_no_reason(mock_client):
    """Test denying a purpose request without a reason."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test denying a purpose request without a reason
    with pytest.raises(ValueError):
        purpose_service.deny_purpose_request(purpose_id=1, approval_id=1, reason="")


def test_request_purpose_subscription(mock_client):
    """Test requesting a purpose subscription."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "purpose_id": 1,
        "approval_id": 4,
        "status": "pending",
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test requesting a purpose subscription
    result = purpose_service.request_purpose_subscription(
        purpose_id=1, reason="Need access for marketing analysis"
    )

    # Check result
    assert result["success"] is True
    assert result["purpose_id"] == 1
    assert result["approval_id"] == 4
    assert result["status"] == "pending"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST",
        "purposes/1/subscribe",
        data={"reason": "Need access for marketing analysis"},
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_request_purpose_subscription_dry_run(mock_client):
    """Test requesting a purpose subscription in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test requesting a purpose subscription in dry run mode
    result = purpose_service.request_purpose_subscription(
        purpose_id=1, reason="Need access for marketing analysis", dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["purpose_id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "request_purpose_subscription_dry_run",
        {
            "purpose_id": 1,
            "reason": "Need access for marketing analysis",
        },
        "Success",
    )


def test_request_purpose_subscription_no_reason(mock_client):
    """Test requesting a purpose subscription without a reason."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test requesting a purpose subscription without a reason
    with pytest.raises(ValueError):
        purpose_service.request_purpose_subscription(purpose_id=1, reason="")


def test_get_purpose_subscribers(mock_client):
    """Test getting purpose subscribers."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "subscriptionDate": "2023-01-03T00:00:00Z",
        },
        {
            "id": 2,
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "subscriptionDate": "2023-01-05T00:00:00Z",
        },
    ]

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose subscribers
    subscribers = purpose_service.get_purpose_subscribers(purpose_id=1)

    # Check result
    assert len(subscribers) == 2
    assert subscribers[0]["id"] == 1
    assert subscribers[0]["name"] == "John Doe"
    assert subscribers[1]["id"] == 2
    assert subscribers[1]["name"] == "Jane Smith"

    # Check that request was made
    mock_client.make_request.assert_called_once_with("GET", "purposes/1/subscribers")


def test_get_purpose_subscribers_local(mock_client):
    """Test getting purpose subscribers in local mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test getting purpose subscribers
    subscribers = purpose_service.get_purpose_subscribers(purpose_id=1)

    # Check result
    assert len(subscribers) == 2
    assert subscribers[0]["id"] == 1
    assert subscribers[0]["name"] == "John Doe"
    assert subscribers[1]["id"] == 2
    assert subscribers[1]["name"] == "Jane Smith"


def test_remove_purpose_subscriber(mock_client):
    """Test removing a purpose subscriber."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "purpose_id": 1,
        "user_id": 1,
    }

    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Test removing a purpose subscriber
    result = purpose_service.remove_purpose_subscriber(purpose_id=1, user_id=1)

    # Check result
    assert result["success"] is True
    assert result["purpose_id"] == 1
    assert result["user_id"] == 1

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "DELETE", "purposes/1/subscribers/1"
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_remove_purpose_subscriber_dry_run(mock_client):
    """Test removing a purpose subscriber in dry run mode."""
    # Create purpose service
    purpose_service = PurposeService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test removing a purpose subscriber in dry run mode
    result = purpose_service.remove_purpose_subscriber(
        purpose_id=1, user_id=1, dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["purpose_id"] == 1
    assert result["user_id"] == 1

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "remove_purpose_subscriber_dry_run",
        {
            "purpose_id": 1,
            "user_id": 1,
        },
        "Success",
    )
