"""Unit tests for the blob service."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.blob_service import BlobService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


def test_get_blob_handler_types(mock_client):
    """Test getting blob handler types."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {
            "id": "s3",
            "name": "Amazon S3",
            "description": "Amazon S3 blob handler",
        },
        {
            "id": "azure",
            "name": "Azure Blob Storage",
            "description": "Azure Blob Storage blob handler",
        },
    ]

    # Create blob service
    blob_service = BlobService(mock_client)

    # Test getting blob handler types
    handlers = blob_service.get_blob_handler_types()

    # Check result
    assert len(handlers) == 2
    assert handlers[0]["id"] == "s3"
    assert handlers[0]["name"] == "Amazon S3"
    assert handlers[1]["id"] == "azure"
    assert handlers[1]["name"] == "Azure Blob Storage"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/blobHandlerTypes"
    )


def test_get_blob_handler_types_local(mock_client):
    """Test getting blob handler types in local mode."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Test getting blob handler types
    handlers = blob_service.get_blob_handler_types()

    # Check result
    assert len(handlers) == 3
    assert handlers[0]["id"] == "s3"
    assert handlers[0]["name"] == "Amazon S3"
    assert handlers[1]["id"] == "azure"
    assert handlers[1]["name"] == "Azure Blob Storage"
    assert handlers[2]["id"] == "gcs"
    assert handlers[2]["name"] == "Google Cloud Storage"


def test_save_blob_metadata(mock_client):
    """Test saving blob metadata."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "id": 1,
        "data_source_id": 10,
        "name": "test-blob",
    }

    # Create blob service
    blob_service = BlobService(mock_client)

    # Test saving blob metadata
    blob_metadata = {
        "name": "test-blob",
        "size": 1024,
        "contentType": "application/octet-stream",
        "handler": {"type": "s3", "bucket": "test-bucket", "key": "test-key"},
    }
    result = blob_service.save_blob_metadata(
        data_source_id=10, blob_metadata=blob_metadata
    )

    # Check result
    assert result["success"] is True
    assert result["id"] == 1
    assert result["data_source_id"] == 10
    assert result["name"] == "test-blob"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "dataSource/10/blobs", data=blob_metadata
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_save_blob_metadata_dry_run(mock_client):
    """Test saving blob metadata in dry run mode."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test saving blob metadata in dry run mode
    blob_metadata = {
        "name": "test-blob",
        "size": 1024,
        "contentType": "application/octet-stream",
        "handler": {"type": "s3", "bucket": "test-bucket", "key": "test-key"},
    }
    result = blob_service.save_blob_metadata(
        data_source_id=10, blob_metadata=blob_metadata, dry_run=True
    )

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["data_source_id"] == 10
    assert result["blob_name"] == "test-blob"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "save_blob_metadata_dry_run",
        {"data_source_id": 10, "blob_name": "test-blob"},
        "Success",
    )


def test_save_blob_metadata_invalid(mock_client):
    """Test saving invalid blob metadata."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Test saving invalid blob metadata
    invalid_metadata = {
        "name": "test-blob",
        "size": 1024,
        # Missing contentType and handler
    }
    with pytest.raises(ValueError):
        blob_service.save_blob_metadata(data_source_id=10, blob_metadata=invalid_metadata)


def test_persist_blob(mock_client):
    """Test persisting a blob."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "success": True,
        "data_source_id": 10,
        "blob_id": "blob123",
    }

    # Create blob service
    blob_service = BlobService(mock_client)

    # Test persisting a blob
    result = blob_service.persist_blob(data_source_id=10, blob_id="blob123")

    # Check result
    assert result["success"] is True
    assert result["data_source_id"] == 10
    assert result["blob_id"] == "blob123"

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "POST", "dataSource/10/persistBlob", data={"blobId": "blob123"}
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_persist_blob_dry_run(mock_client):
    """Test persisting a blob in dry run mode."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test persisting a blob in dry run mode
    result = blob_service.persist_blob(data_source_id=10, blob_id="blob123", dry_run=True)

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["data_source_id"] == 10
    assert result["blob_id"] == "blob123"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "persist_blob_dry_run",
        {"data_source_id": 10, "blob_id": "blob123"},
        "Success",
    )


def test_get_blob(mock_client):
    """Test getting a blob."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "id": "blob123",
        "name": "test-blob",
        "size": 1024,
        "contentType": "application/octet-stream",
        "handler": {"type": "s3", "bucket": "test-bucket", "key": "test-key"},
        "dataSourceId": 10,
    }

    # Create blob service
    blob_service = BlobService(mock_client)

    # Test getting a blob
    blob = blob_service.get_blob(data_source_id=10, blob_id="blob123")

    # Check result
    assert blob["id"] == "blob123"
    assert blob["name"] == "test-blob"
    assert blob["size"] == 1024
    assert blob["contentType"] == "application/octet-stream"
    assert blob["handler"]["type"] == "s3"
    assert blob["dataSourceId"] == 10

    # Check that request was made
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/10/blob/blob123"
    )


def test_get_blob_local(mock_client):
    """Test getting a blob in local mode."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Test getting a blob
    blob = blob_service.get_blob(data_source_id=10, blob_id="blob123")

    # Check result
    assert blob["id"] == "blob123"
    assert blob["name"] == "blob_blob123"
    assert blob["size"] == 1024
    assert blob["contentType"] == "application/octet-stream"
    assert blob["handler"]["type"] == "s3"
    assert blob["dataSourceId"] == 10


def test_delete_blob(mock_client):
    """Test deleting a blob."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    
    # Mock get blob and delete blob responses
    mock_client.make_request.return_value.json.side_effect = [
        {
            "id": "blob123",
            "name": "test-blob",
            "size": 1024,
            "contentType": "application/octet-stream",
            "handler": {"type": "s3", "bucket": "test-bucket", "key": "test-key"},
            "dataSourceId": 10,
        },
        {
            "success": True,
            "data_source_id": 10,
            "blob_id": "blob123",
        },
    ]

    # Create blob service
    blob_service = BlobService(mock_client)

    # Test deleting a blob
    result = blob_service.delete_blob(data_source_id=10, blob_id="blob123")

    # Check result
    assert result["success"] is True
    assert result["data_source_id"] == 10
    assert result["blob_id"] == "blob123"

    # Check that requests were made
    assert mock_client.make_request.call_count == 2
    mock_client.make_request.assert_any_call("GET", "dataSource/10/blob/blob123")
    mock_client.make_request.assert_any_call("DELETE", "dataSource/10/blob/blob123")

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_delete_blob_dry_run(mock_client):
    """Test deleting a blob in dry run mode."""
    # Create blob service
    blob_service = BlobService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test deleting a blob in dry run mode
    result = blob_service.delete_blob(data_source_id=10, blob_id="blob123", dry_run=True)

    # Check result
    assert result["success"] is True
    assert "message" in result
    assert result["data_source_id"] == 10
    assert result["blob_id"] == "blob123"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "delete_blob_dry_run",
        {"data_source_id": 10, "blob_id": "blob123"},
        "Success",
    )
