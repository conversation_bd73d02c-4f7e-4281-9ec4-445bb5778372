"""Unit tests for the notifications module."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.utils.notifications import NotificationConfig, NotificationManager


@pytest.fixture
def mock_config():
    """Mock notification config."""
    return NotificationConfig(
        enabled=True,
        email_enabled=True,
        email_recipients=["<EMAIL>"],
        slack_enabled=True,
        slack_webhook="https://hooks.slack.com/services/xxx/yyy/zzz",
        teams_enabled=True,
        teams_webhook="https://outlook.office.com/webhook/xxx/yyy/zzz",
    )


@pytest.fixture
def mock_env_config(monkeypatch):
    """Mock notification config from environment variables."""
    monkeypatch.setenv("EMAIL_ENABLED", "true")
    monkeypatch.setenv("EMAIL_RECIPIENTS", "<EMAIL>,<EMAIL>")
    monkeypatch.setenv("SMTP_FROM", "<EMAIL>")
    monkeypatch.setenv("SMTP_SERVER", "smtp.example.com")
    monkeypatch.setenv("SMTP_PORT", "587")
    monkeypatch.setenv("SMTP_USER", "user")
    monkeypatch.setenv("SMTP_PASSWORD", "password")
    monkeypatch.setenv("SLACK_ENABLED", "true")
    monkeypatch.setenv("SLACK_WEBHOOK", "https://hooks.slack.com/services/xxx/yyy/zzz")
    monkeypatch.setenv("TEAMS_ENABLED", "true")
    monkeypatch.setenv(
        "TEAMS_WEBHOOK", "https://outlook.office.com/webhook/xxx/yyy/zzz"
    )


def test_notification_config_init():
    """Test notification config initialization."""
    # Test with default values
    config = NotificationConfig()
    assert config.email_enabled is False
    assert config.email_recipients == []
    assert config.slack_enabled is False
    assert config.teams_enabled is False

    # Test with custom values
    config = NotificationConfig(
        email_enabled=True,
        email_recipients=["<EMAIL>"],
        slack_enabled=True,
        slack_webhook="https://hooks.slack.com/services/xxx/yyy/zzz",
    )
    assert config.email_enabled is True
    assert config.email_recipients == ["<EMAIL>"]
    assert config.slack_enabled is True
    assert config.slack_webhook == "https://hooks.slack.com/services/xxx/yyy/zzz"
    assert config.teams_enabled is False


def test_notification_config_from_env(mock_env_config):
    """Test notification config from environment variables."""
    config = NotificationConfig.from_env()

    assert config.email_enabled is True
    assert "<EMAIL>" in config.email_recipients
    assert "<EMAIL>" in config.email_recipients
    assert config.slack_enabled is True
    assert config.slack_webhook == "https://hooks.slack.com/services/xxx/yyy/zzz"
    assert config.teams_enabled is True
    assert config.teams_webhook == "https://outlook.office.com/webhook/xxx/yyy/zzz"


def test_notification_manager_init(mock_config):
    """Test notification manager initialization."""
    # Test with default config
    manager = NotificationManager()
    assert manager.config.email_enabled is False
    assert manager.config.slack_enabled is False
    assert manager.config.teams_enabled is False

    # Test with custom config
    manager = NotificationManager(mock_config)
    assert manager.config.email_enabled is True
    assert manager.config.slack_enabled is True
    assert manager.config.teams_enabled is True


@patch("immuta_toolkit.utils.notifications.NotificationManager._send_email")
@patch("immuta_toolkit.utils.notifications.NotificationManager._send_slack")
@patch("immuta_toolkit.utils.notifications.NotificationManager._send_teams")
@patch("immuta_toolkit.utils.notifications.NotificationManager.should_notify")
def test_send_notification(
    mock_should_notify, mock_teams, mock_slack, mock_email, mock_config
):
    """Test sending notifications."""
    # Create notification manager
    manager = NotificationManager(mock_config)

    # Mock should_notify to return True
    mock_should_notify.return_value = True

    # Test sending notification
    manager.send_notification(
        operation="test_event",
        details={"key": "value"},
        status="Success",
    )

    # Check that all notification methods were called
    mock_email.assert_called_once()
    mock_slack.assert_called_once()
    mock_teams.assert_called_once()


@patch("smtplib.SMTP")
def test_send_email(mock_smtp, mock_config):
    """Test sending email."""
    # Create notification manager
    manager = NotificationManager(mock_config)

    # Mock SMTP instance
    mock_smtp_instance = MagicMock()
    mock_smtp.return_value = mock_smtp_instance

    # Test sending email
    manager._send_email(
        subject="Test Subject",
        body="Test Body",
    )

    # Check that SMTP was initialized correctly
    mock_smtp.assert_called_once()

    # Check that send_message was called
    mock_smtp_instance.send_message.assert_called_once()

    # Check that quit was called
    mock_smtp_instance.quit.assert_called_once()


@patch("requests.post")
def test_send_slack(mock_post, mock_config):
    """Test sending Slack notification."""
    # Create notification manager
    manager = NotificationManager(mock_config)

    # Test sending Slack notification
    manager._send_slack(
        message="Test Message",
    )

    # Check that post was called
    mock_post.assert_called_once_with(
        mock_config.slack_webhook,
        json={"text": "Test Message"},
        headers={"Content-Type": "application/json"},
        timeout=10,  # Added timeout parameter
    )


@patch("requests.post")
def test_send_teams(mock_post, mock_config):
    """Test sending Teams notification."""
    # Create notification manager
    manager = NotificationManager(mock_config)

    # Test sending Teams notification
    manager._send_teams(
        title="Test Title",
        text="Test Text",
    )

    # Check that post was called
    mock_post.assert_called_once_with(
        mock_config.teams_webhook,
        json={
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "0076D7",
            "summary": "Test Title",
            "sections": [
                {
                    "activityTitle": "Test Title",
                    "activitySubtitle": mock_post.call_args[1]["json"]["sections"][0][
                        "activitySubtitle"
                    ],
                    "text": "Test Text",
                }
            ],
        },
        headers={"Content-Type": "application/json"},
        timeout=10,  # Added timeout parameter
    )


def test_format_message():
    """Test formatting message."""
    # Create notification manager
    manager = NotificationManager()

    # Test with simple data
    operation = "test_operation"
    details = {"key": "value", "number": 123}
    status = "Success"

    formatted = manager.format_message(operation, details, status)
    assert "test_operation" in formatted
    assert "Success" in formatted
    assert "key: value" in formatted
    assert "number: 123" in formatted

    # Test with error
    error = "Test error"
    formatted = manager.format_message(operation, details, status, error)
    assert "test_operation" in formatted
    assert "Success" in formatted
    assert "Test error" in formatted
    assert "key: value" in formatted
    assert "number: 123" in formatted
