"""Unit tests for data source service tag-related methods."""

import json
import os
from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.data_source_service import DataSourceService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.tag_service = MagicMock()
    client.storage = MagicMock()
    return client


@pytest.fixture
def tag_config():
    """Create a sample tag configuration."""
    return {
        "data_source_tags": {
            "customer": ["PII", "Sensitive"],
            "transaction": ["Financial"],
        },
        "column_tags": {
            "email": ["PII", "Email"],
            "address": ["PII", "Address"],
            "phone": ["PII", "Phone"],
            "ssn": ["PII", "SSN"],
            "credit_card": ["PII", "Financial", "CreditCard"],
        },
    }


class TestDataSourceServiceTags:
    """Test tag-related methods in the data source service."""

    def test_get_data_source_tags(self, mock_client):
        """Test getting tags for a data source."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "tags": [
                {
                    "name": "PII",
                    "source": "curated",
                    "modelType": "datasource",
                    "modelId": "1",
                    "addedBy": 1,
                    "deleted": False,
                }
            ]
        }
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_tags(1)

        # Check the result
        assert "tags" in result
        assert len(result["tags"]) == 1
        assert result["tags"][0]["name"] == "PII"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("GET", "dataSource/1/tags")

    def test_get_data_source_tags_local_mode(self, mock_client):
        """Test getting tags for a data source in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.get_data_source_tags(1)

        # Check the result
        assert "tags" in result
        assert len(result["tags"]) == 1
        assert result["tags"][0]["name"] == "PII"

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_add_data_source_tag(self, mock_client):
        """Test adding a tag to a data source."""
        # Set up mock client's tag service
        mock_client.tag_service.add_tag.return_value = {
            "name": "PII",
            "source": "curated",
            "modelType": "datasource",
            "modelId": "1",
            "addedBy": 1,
            "deleted": False,
        }

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.add_data_source_tag(1, "PII")

        # Check the result
        assert result["name"] == "PII"
        assert result["modelType"] == "datasource"
        assert result["modelId"] == "1"

        # Check that the tag service was called correctly
        mock_client.tag_service.add_tag.assert_called_once_with(
            {"name": "PII", "modelType": "datasource", "modelId": "1"}
        )

    def test_add_data_source_tag_local_mode(self, mock_client):
        """Test adding a tag to a data source in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Call the method
        result = data_source_service.add_data_source_tag(1, "PII")

        # Check the result
        assert result["name"] == "PII"
        assert result["modelType"] == "datasource"
        assert result["modelId"] == "1"

        # Check that the tag service was not called
        mock_client.tag_service.add_tag.assert_not_called()

    @patch("rich.progress.Progress")
    def test_tag_existing_data_sources(self, mock_progress, mock_client, tag_config):
        """Test tagging existing data sources."""
        # Set up mock response for list_data_sources
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {
                "id": 1,
                "name": "customer_data",
                "columns": {"email": {}, "address": {}},
            },
            {
                "id": 2,
                "name": "transaction_data",
                "columns": {"transaction_id": {}, "credit_card": {}},
            },
        ]
        mock_client.make_request.return_value = mock_response

        # Create data source service
        data_source_service = DataSourceService(mock_client)

        # Create a temporary config file
        with patch("builtins.open", MagicMock()):
            with patch("json.load", return_value=tag_config):
                # Call the method
                result = data_source_service.tag_existing_data_sources(
                    config_file="tag_config.json", validate=False
                )

        # Check the result
        assert result["total"] == 2
        assert (
            result["skipped"] == 2
        )  # No tags match because patterns don't match exactly
        assert result["failed"] == 0

        # The tag service should not be called since no tags matched
        mock_client.tag_service.add_tags.assert_not_called()
