"""Unit tests for the batch operations."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.batch_service import BatchService


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = None
    client.tag_service = MagicMock()
    client.data_source_service = MagicMock()
    client.policy_service = MagicMock()
    return client


def test_batch_tag_data_sources_validation(mock_client):
    """Test batch tag data sources validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    # Should not raise an exception
    batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid data sources (missing data_source_id)
    invalid_data_sources = [
        {"tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (missing tags)
    invalid_data_sources = [
        {"data_source_id": 1},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (invalid tags)
    invalid_data_sources = [
        {"data_source_id": 1, "tags": "tag1"},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_tag_data_sources(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_tag_data_sources_dry_run(mock_client):
    """Test batch tag data sources in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources_dry_run",
        {
            "data_source_count": 2,
            "tags": [["tag1", "tag2"], ["tag3", "tag4"]],
            "details": "Would tag 2 data sources",
        },
        "Success",
    )


def test_batch_tag_data_sources_local(mock_client):
    """Test batch tag data sources in local mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources",
        {"data_source_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_tag_data_sources_api(mock_progress, mock_client):
    """Test batch tag data sources via API."""
    # Set up mock client
    mock_client.is_local = False

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "tags": ["tag1", "tag2"]},
        {"data_source_id": 2, "tags": ["tag3", "tag4"]},
    ]

    result = batch_service.batch_tag_data_sources(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["tagged"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that tag service was called
    assert mock_client.tag_service.add_tags_to_data_source.call_count == 2
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=1, tags=["tag1", "tag2"]
    )
    mock_client.tag_service.add_tags_to_data_source.assert_any_call(
        data_source_id=2, tags=["tag3", "tag4"]
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_tag_data_sources",
        {
            "data_source_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


def test_batch_create_policies_validation(mock_client):
    """Test batch create policies validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    # Should not raise an exception
    batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid policies (missing name)
    invalid_policies = [
        {"policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid policies (missing policy_type)
    invalid_policies = [
        {"name": "Policy 1"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid policies (invalid policy_type)
    invalid_policies = [
        {"name": "Policy 1", "policy_type": "invalid_type"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_create_policies(
            policies=invalid_policies,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_create_policies_dry_run(mock_client):
    """Test batch create policies in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    assert result["created_policies"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies_dry_run",
        {
            "policy_count": 2,
            "policy_types": ["subscription", "masking"],
            "details": "Would create 2 policies",
        },
        "Success",
    )


def test_batch_create_policies_local(mock_client):
    """Test batch create policies in local mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    assert len(result["created_policies"]) == 2
    assert result["created_policies"][0]["name"] == "Policy 1"
    assert result["created_policies"][1]["name"] == "Policy 2"

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies",
        {"policy_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_create_policies_api(mock_progress, mock_client):
    """Test batch create policies via API."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.policy_service.create_policy.side_effect = [
        {"id": 1, "name": "Policy 1", "policy_type": "subscription"},
        {"id": 2, "name": "Policy 2", "policy_type": "masking"},
    ]

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid policies
    valid_policies = [
        {"name": "Policy 1", "policy_type": "subscription"},
        {"name": "Policy 2", "policy_type": "masking"},
    ]

    result = batch_service.batch_create_policies(
        policies=valid_policies,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["created"] == 2
    assert result["failed"] == 0
    assert result["failed_policies"] == []
    assert len(result["created_policies"]) == 2
    assert result["created_policies"][0]["id"] == 1
    assert result["created_policies"][0]["name"] == "Policy 1"
    assert result["created_policies"][1]["id"] == 2
    assert result["created_policies"][1]["name"] == "Policy 2"

    # Check that policy service was called
    assert mock_client.policy_service.create_policy.call_count == 2
    mock_client.policy_service.create_policy.assert_any_call(
        policy={"name": "Policy 1", "policy_type": "subscription"}, backup=False
    )
    mock_client.policy_service.create_policy.assert_any_call(
        policy={"name": "Policy 2", "policy_type": "masking"}, backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_create_policies",
        {
            "policy_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )


def test_batch_update_data_source_metadata_validation(mock_client):
    """Test batch update data source metadata validation."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"description": "Description 1"}},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    # Should not raise an exception
    batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Test with invalid data sources (missing data_source_id)
    invalid_data_sources = [
        {"metadata": {"description": "Description 1"}},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (missing metadata)
    invalid_data_sources = [
        {"data_source_id": 1},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )

    # Test with invalid data sources (invalid metadata)
    invalid_data_sources = [
        {"data_source_id": 1, "metadata": "Invalid metadata"},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    with pytest.raises(ValueError):
        batch_service.batch_update_data_source_metadata(
            data_sources=invalid_data_sources,
            backup=False,
            dry_run=True,
            validate=True,
        )


def test_batch_update_data_source_metadata_dry_run(mock_client):
    """Test batch update data source metadata in dry run mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"description": "Description 1"}},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=True,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata_dry_run",
        {
            "data_source_count": 2,
            "details": "Would update metadata for 2 data sources",
        },
        "Success",
    )


def test_batch_update_data_source_metadata_local(mock_client):
    """Test batch update data source metadata in local mode."""
    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"description": "Description 1"}},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata",
        {"data_source_count": 2, "backup": None},
        "Success",
    )


@patch("immuta_toolkit.services.batch_service.Progress")
def test_batch_update_data_source_metadata_api(mock_progress, mock_client):
    """Test batch update data source metadata via API."""
    # Set up mock client
    mock_client.is_local = False

    # Create batch service
    batch_service = BatchService(mock_client)

    # Test with valid data sources
    valid_data_sources = [
        {"data_source_id": 1, "metadata": {"description": "Description 1"}},
        {"data_source_id": 2, "metadata": {"description": "Description 2"}},
    ]

    result = batch_service.batch_update_data_source_metadata(
        data_sources=valid_data_sources,
        backup=False,
        dry_run=False,
        validate=True,
    )

    # Check result
    assert result["status"] == "success"
    assert result["updated"] == 2
    assert result["failed"] == 0
    assert result["failed_data_sources"] == []

    # Check that data source service was called
    assert mock_client.data_source_service.update_data_source.call_count == 2
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=1, metadata={"description": "Description 1"}, backup=False
    )
    mock_client.data_source_service.update_data_source.assert_any_call(
        data_source_id=2, metadata={"description": "Description 2"}, backup=False
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once_with(
        "batch_update_data_source_metadata",
        {
            "data_source_count": 2,
            "successful_count": 2,
            "failed_count": 0,
            "backup": None,
        },
        "Success",
    )
