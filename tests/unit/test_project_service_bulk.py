"""Unit tests for project service bulk operations methods."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestProjectServiceBulk:
    """Test project bulk operations methods."""

    def test_batch_add_data_sources_to_project(self, mock_client):
        """Test batch adding data sources to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "added": 3}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_data_sources_to_project(1, [3, 4, 5])

        # Check the result
        assert result["success"] is True
        assert result["added"] == 3

        # Check that the API endpoint was called for each data source
        mock_client.make_request.assert_any_call(
            "POST", "projects/1/dataSources", data={"dataSourceId": 3}
        )
        mock_client.make_request.assert_any_call(
            "POST", "projects/1/dataSources", data={"dataSourceId": 4}
        )
        mock_client.make_request.assert_any_call(
            "POST", "projects/1/dataSources", data={"dataSourceId": 5}
        )

    def test_batch_add_data_sources_to_project_dry_run(self, mock_client):
        """Test batch adding data sources to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_data_sources_to_project(
            1, [3, 4, 5], dry_run=True
        )

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "data_source_ids" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_batch_add_data_sources_to_project_local_mode(self, mock_client):
        """Test batch adding data sources to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_data_sources_to_project(1, [3, 4, 5])

        # Check the result - local mode returns success info
        assert result["success"] is True
        assert "added" in result
        assert result["added"] == 3
        assert "failed" in result
        assert "errors" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notifications were sent (one for each data source and one for the batch)
        assert mock_client.notifier.send_notification.call_count == 4

    def test_bulk_remove_data_sources_from_project(self, mock_client):
        """Test bulk removing data sources from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "removed": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_data_sources_from_project(1, [1, 2])

        # Check the result
        assert result["success"] is True
        assert result["removed"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/dataSources/bulk", data={"dataSourceIds": [1, 2]}
        )

    def test_bulk_remove_data_sources_from_project_dry_run(self, mock_client):
        """Test bulk removing data sources from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_data_sources_from_project(
            1, [1, 2], dry_run=True
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["data_source_ids"] == [1, 2]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_data_sources_from_project_local_mode(self, mock_client):
        """Test bulk removing data sources from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_data_sources_from_project(1, [1, 2])

        # Check the result
        assert result["status"] == "success"
        assert result["removed"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_batch_add_members_to_project(self, mock_client):
        """Test batch adding members to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "added": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_members_to_project(
            1, [{"userId": 3, "role": "MEMBER"}, {"userId": 4, "role": "MEMBER"}]
        )

        # Check the result
        assert result["success"] is True
        assert result["added"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST",
            "projects/1/members/bulk",
            data={
                "members": [
                    {"userId": 3, "role": "MEMBER"},
                    {"userId": 4, "role": "MEMBER"},
                ]
            },
        )

    def test_batch_add_members_to_project_dry_run(self, mock_client):
        """Test batch adding members to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_members_to_project(
            1,
            [{"userId": 3, "role": "MEMBER"}, {"userId": 4, "role": "MEMBER"}],
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert len(result["members"]) == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_batch_add_members_to_project_local_mode(self, mock_client):
        """Test batch adding members to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.batch_add_members_to_project(
            1, [{"userId": 3, "role": "MEMBER"}, {"userId": 4, "role": "MEMBER"}]
        )

        # Check the result
        assert result["status"] == "success"
        assert result["added"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_members_from_project(self, mock_client):
        """Test bulk removing members from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "removed": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_members_from_project(1, [2, 3])

        # Check the result
        assert result["success"] is True
        assert result["removed"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/members/bulk", data={"userIds": [2, 3]}
        )

    def test_bulk_remove_members_from_project_dry_run(self, mock_client):
        """Test bulk removing members from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_members_from_project(
            1, [2, 3], dry_run=True
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["user_ids"] == [2, 3]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_members_from_project_local_mode(self, mock_client):
        """Test bulk removing members from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_members_from_project(1, [2, 3])

        # Check the result
        assert result["status"] == "success"
        assert result["removed"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_add_purposes_to_project(self, mock_client):
        """Test bulk adding purposes to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "added": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_purposes_to_project(1, [3, 4])

        # Check the result
        assert result["success"] is True
        assert result["added"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/purposes/bulk", data={"purposeIds": [3, 4]}
        )

    def test_bulk_add_purposes_to_project_dry_run(self, mock_client):
        """Test bulk adding purposes to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_purposes_to_project(1, [3, 4], dry_run=True)

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["purpose_ids"] == [3, 4]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_add_purposes_to_project_local_mode(self, mock_client):
        """Test bulk adding purposes to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_purposes_to_project(1, [3, 4])

        # Check the result
        assert result["status"] == "success"
        assert result["added"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_purposes_from_project(self, mock_client):
        """Test bulk removing purposes from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "removed": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_purposes_from_project(1, [1, 2])

        # Check the result
        assert result["success"] is True
        assert result["removed"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/purposes/bulk", data={"purposeIds": [1, 2]}
        )

    def test_bulk_remove_purposes_from_project_dry_run(self, mock_client):
        """Test bulk removing purposes from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_purposes_from_project(
            1, [1, 2], dry_run=True
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["purpose_ids"] == [1, 2]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_purposes_from_project_local_mode(self, mock_client):
        """Test bulk removing purposes from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_purposes_from_project(1, [1, 2])

        # Check the result
        assert result["status"] == "success"
        assert result["removed"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_add_tags_to_project(self, mock_client):
        """Test bulk adding tags to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "added": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_tags_to_project(1, ["security", "finance"])

        # Check the result
        assert result["success"] is True
        assert result["added"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/tags/bulk", data={"tags": ["security", "finance"]}
        )

    def test_bulk_add_tags_to_project_dry_run(self, mock_client):
        """Test bulk adding tags to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_tags_to_project(
            1, ["security", "finance"], dry_run=True
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["tags"] == ["security", "finance"]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_add_tags_to_project_local_mode(self, mock_client):
        """Test bulk adding tags to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_add_tags_to_project(1, ["security", "finance"])

        # Check the result
        assert result["status"] == "success"
        assert result["added"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_tags_from_project(self, mock_client):
        """Test bulk removing tags from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True, "removed": 2}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_tags_from_project(
            1, ["analytics", "customer"]
        )

        # Check the result
        assert result["success"] is True
        assert result["removed"] == 2

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/tags/bulk", data={"tags": ["analytics", "customer"]}
        )

    def test_bulk_remove_tags_from_project_dry_run(self, mock_client):
        """Test bulk removing tags from a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_tags_from_project(
            1, ["analytics", "customer"], dry_run=True
        )

        # Check the result
        assert result["status"] == "dry_run_success"
        assert result["tags"] == ["analytics", "customer"]

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_bulk_remove_tags_from_project_local_mode(self, mock_client):
        """Test bulk removing tags from a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.bulk_remove_tags_from_project(
            1, ["analytics", "customer"]
        )

        # Check the result
        assert result["status"] == "success"
        assert result["removed"] == 2

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
