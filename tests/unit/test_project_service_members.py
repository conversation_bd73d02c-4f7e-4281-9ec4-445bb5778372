"""Unit tests for project service member management methods."""

from unittest.mock import MagicMock, patch

import pytest

from immuta_toolkit.services.project_service import ProjectService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.make_request = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestProjectServiceMembers:
    """Test project member management methods."""

    def test_get_project_members(self, mock_client):
        """Test getting project members."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = [
            {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
            {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
        ]
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.get_project_members(1)

        # Check the result
        assert len(result) == 2
        assert result[0]["id"] == 1
        assert result[0]["role"] == "OWNER"
        assert result[1]["id"] == 2
        assert result[1]["role"] == "MEMBER"

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with("GET", "projects/1/members")

    def test_get_project_members_local_mode(self, mock_client):
        """Test getting project members in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.get_project_members(1)

        # Check the result - in local mode, an empty list is returned
        assert isinstance(result, list)
        # Local mode doesn't populate data

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

    def test_add_project_member(self, mock_client):
        """Test adding a member to a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_project_member(1, 3, "MEMBER")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/members", data={"userId": 3, "role": "MEMBER"}
        )

    def test_add_project_member_dry_run(self, mock_client):
        """Test adding a member to a project in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_project_member(1, 3, "MEMBER", dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "user_id" in result
        assert "role" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_add_project_member_local_mode(self, mock_client):
        """Test adding a member to a project in local mode."""
        # Set up mock client to be in local mode
        mock_client.is_local = True

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_project_member(1, 3, "MEMBER")

        # Check the result - local mode returns additional info
        assert result["success"] is True
        assert "project_id" in result
        assert "user_id" in result
        assert "role" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_add_project_member_update_role(self, mock_client):
        """Test updating a project member's role."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_project_member(1, 2, "OWNER")

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "POST", "projects/1/members", data={"userId": 2, "role": "OWNER"}
        )

    def test_add_project_member_update_role_dry_run(self, mock_client):
        """Test updating a project member's role in dry run mode."""
        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.add_project_member(1, 2, "OWNER", dry_run=True)

        # Check the result - dry run returns additional info
        assert result["success"] is True
        assert "message" in result
        assert "project_id" in result
        assert "user_id" in result
        assert "role" in result

        # Check that the API was not called
        mock_client.make_request.assert_not_called()

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_remove_project_member(self, mock_client):
        """Test removing a member from a project."""
        # Set up mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"success": True}
        mock_client.make_request.return_value = mock_response

        # Create project service
        project_service = ProjectService(mock_client)

        # Call the method
        result = project_service.remove_project_member(1, 2)

        # Check the result
        assert result["success"] is True

        # Check that the correct API endpoint was called
        mock_client.make_request.assert_called_once_with(
            "DELETE", "projects/1/members/2"
        )
