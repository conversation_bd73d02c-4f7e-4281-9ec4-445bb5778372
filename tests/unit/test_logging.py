"""Unit tests for the logging module."""

import logging
import os
import tempfile

from immuta_toolkit.utils.logging import setup_logging, get_logger


def test_setup_logging_default():
    """Test setup_logging with default parameters."""
    # Reset logging
    root_logger = logging.getLogger("immuta_toolkit")
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Setup logging with default parameters
    setup_logging()

    # Check that the immuta_toolkit logger has a handler
    assert len(root_logger.handlers) > 0


def test_setup_logging_custom_level():
    """Test setup_logging with custom level."""
    # Reset logging
    logger = logging.getLogger("immuta_toolkit")
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Setup logging with custom level
    setup_logging(level="DEBUG")

    # Check that environment variable was set correctly
    assert os.environ.get("IMMUTA_LOG_LEVEL") == "DEBUG"


def test_setup_logging_with_file():
    """Test setup_logging with log file."""
    # Reset logging
    logger = logging.getLogger("immuta_toolkit")
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create temporary log file
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        log_file = temp_file.name

    try:
        # Setup logging with log file
        setup_logging(log_file=log_file)

        # Check that environment variable was set correctly
        assert os.environ.get("IMMUTA_LOG_FILE") == log_file

        # Get a new logger to ensure it picks up the file handler
        test_logger = get_logger("test_with_file")

        # Log a message
        test_logger.info("Test message for file")

        # Check that the message was written to the file
        with open(log_file, "r") as f:
            content = f.read()
            assert "Test message for file" in content
    finally:
        # Clean up
        os.unlink(log_file)


def test_get_logger():
    """Test get_logger function."""
    # Reset logging
    root_logger = logging.getLogger("immuta_toolkit")
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Setup logging
    setup_logging()

    # Get logger
    logger = get_logger("test_logger")

    # Check logger name
    assert logger.name == "test_logger"

    # Check that logger has handlers
    assert len(logger.handlers) > 0

    # Check that one of the handlers is a RichHandler
    from rich.logging import RichHandler

    rich_handlers = [h for h in logger.handlers if isinstance(h, RichHandler)]
    assert len(rich_handlers) > 0


def test_setup_logging_file_creation():
    """Test that setup_logging sets the log file environment variable."""
    # Reset logging
    logger = logging.getLogger("immuta_toolkit")
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Setup logging with log file
    log_file = "/tmp/test_immuta_log.log"
    setup_logging(log_file=log_file)

    # Check that environment variable was set correctly
    assert os.environ.get("IMMUTA_LOG_FILE") == log_file


def test_logger_output():
    """Test that logger outputs messages correctly."""
    # Reset logging
    logger = logging.getLogger("immuta_toolkit")
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create temporary log file
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        log_file = temp_file.name

    try:
        # Setup logging with log file
        setup_logging(level="DEBUG", log_file=log_file)

        # Get logger
        logger = get_logger("test_logger")

        # Log messages at different levels
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")

        # Check log file content
        with open(log_file, "r") as f:
            content = f.read()
            assert "Info message" in content
            assert "Warning message" in content
            assert "Error message" in content
            assert "Critical message" in content
    finally:
        # Clean up
        os.unlink(log_file)
