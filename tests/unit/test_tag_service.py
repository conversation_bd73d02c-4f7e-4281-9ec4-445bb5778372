"""Unit tests for the tag service."""

import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

import pytest
import yaml

from immuta_toolkit.services.tag_service import TagService
from immuta_toolkit.utils.cache import Cache


@pytest.fixture
def mock_client():
    """Mock Immuta client."""
    client = MagicMock()
    client.is_local = True
    client.notifier = MagicMock()
    client.storage = MagicMock()
    # Create a real Cache object instead of a dict
    client.tag_cache = Cache(name="tag_cache", default_ttl_seconds=300)
    return client


def test_validate_tags(mock_client):
    """Test validating tags."""
    # Set up mock client with existing tags in cache
    mock_client.tag_cache.set("all_tags", ["existing_tag"])

    # Create tag service
    tag_service = TagService(mock_client)

    # Test with valid tags
    valid_tags = ["new_tag", "another_tag"]
    result = tag_service._validate_tags(valid_tags)
    assert result == valid_tags

    # Test with invalid tags
    with pytest.raises(ValueError):
        tag_service._validate_tags(["invalid tag with spaces"])

    # Test with existing tags
    result = tag_service._validate_tags(["existing_tag", "new_tag"])
    assert result == ["new_tag"]

    # Test with empty tags
    result = tag_service._validate_tags([])
    assert result == []


def test_validate_tags_cache_expired(mock_client):
    """Test validating tags with expired cache."""
    # Set up mock client with expired cache (don't set any cache value)
    mock_client.is_local = True

    # Create tag service
    tag_service = TagService(mock_client)

    # Test with valid tags
    valid_tags = ["new_tag", "another_tag"]
    result = tag_service._validate_tags(valid_tags)

    # Get the cached tags
    cached_tags = mock_client.tag_cache.get("all_tags")

    # Check that cache was refreshed with default tags
    assert "sensitive" in cached_tags
    assert "pci" in cached_tags
    assert "pii" in cached_tags

    # Check result
    assert result == valid_tags


@patch("immuta_toolkit.services.tag_service.TagService._validate_tags")
def test_add_tags(mock_validate_tags, mock_client):
    """Test adding tags."""
    # Set up mock client
    mock_client.is_local = True
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {"status": "valid"}

    # Create tag service
    tag_service = TagService(mock_client)

    # Test adding tags
    tag_service.add_tags(
        data_source_id=1,
        tags=["tag1", "tag2"],
        column_tags={"column1": ["tag3"]},
        backup=True,
        dry_run=False,
        validate=True,
    )

    # Check that tags were validated
    mock_validate_tags.assert_any_call(["tag1", "tag2"])
    mock_validate_tags.assert_any_call(["tag3"])

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()


def test_add_tags_dry_run(mock_client):
    """Test adding tags in dry run mode."""
    # Set up mock client
    mock_client.is_local = True
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {"status": "valid"}

    # Create tag service
    tag_service = TagService(mock_client)

    # Reset mock to clear any previous calls
    mock_client.notifier.send_notification.reset_mock()

    # Test adding tags in dry run mode
    tag_service.add_tags(
        data_source_id=1,
        tags=["tag1", "tag2"],
        column_tags={"column1": ["tag3"]},
        backup=True,
        dry_run=True,
        validate=True,
    )

    # Check that notification was sent
    mock_client.notifier.send_notification.assert_called_once()

    # Get the actual call arguments
    call_args = mock_client.notifier.send_notification.call_args[0]
    assert call_args[0] == "data_source_tag_dry_run"
    assert call_args[1]["data_source_id"] == 1
    assert call_args[1]["tags"] == "tag1, tag2"
    assert "column1" in call_args[1]["column_tags"]
    assert "tag3" in call_args[1]["column_tags"]
    assert call_args[2] == "Success"


def test_load_tag_config(mock_client):
    """Test loading tag configuration."""
    # Create tag service
    tag_service = TagService(mock_client)

    # Create temporary config file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
        yaml.dump(
            {
                "data_source_tags": {
                    "customer": ["pii", "sensitive"],
                    "transaction": ["financial"],
                },
                "column_tags": {
                    "email": ["pii", "email"],
                    "address": ["pii", "address"],
                    "credit_card": ["pci", "financial"],
                },
            },
            f,
        )
        config_path = f.name

    try:
        # Test loading config
        config = tag_service.load_tag_config(config_path)

        # Check config
        assert "data_source_tags" in config
        assert "column_tags" in config
        assert "customer" in config["data_source_tags"]
        assert "email" in config["column_tags"]
        assert "pii" in config["data_source_tags"]["customer"]
        assert "pci" in config["column_tags"]["credit_card"]
    finally:
        # Clean up
        os.unlink(config_path)

    # Test with non-existent file
    with pytest.raises(FileNotFoundError):
        tag_service.load_tag_config("non_existent_file.yaml")

    # Test with invalid file
    with tempfile.NamedTemporaryFile(mode="w", suffix=".yaml", delete=False) as f:
        f.write("invalid_yaml:\n  - missing: colon\n  value")
        invalid_path = f.name

    try:
        with pytest.raises(Exception):
            tag_service.load_tag_config(invalid_path)
    finally:
        # Clean up
        os.unlink(invalid_path)


def test_get_tags(mock_client):
    """Test getting all tags."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = [
        {"name": "tag1", "source": "curated"},
        {"name": "tag2", "source": "curated"},
    ]

    # Create tag service
    tag_service = TagService(mock_client)

    # Test getting tags
    tags = tag_service.get_tags()

    # Check that the correct endpoint was called
    mock_client.make_request.assert_called_once_with("GET", "tag")

    # Check the result
    assert len(tags) == 2
    assert tags[0]["name"] == "tag1"
    assert tags[1]["name"] == "tag2"


def test_get_tags_local(mock_client):
    """Test getting all tags in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create tag service
    tag_service = TagService(mock_client)

    # Test getting tags
    tags = tag_service.get_tags()

    # Check the result
    assert len(tags) == 7
    assert any(tag["name"] == "sensitive" for tag in tags)
    assert any(tag["name"] == "pci" for tag in tags)
    assert any(tag["name"] == "pii" for tag in tags)
    assert any(tag["name"] == "restricted" for tag in tags)
    assert any(tag["name"] == "public" for tag in tags)
    assert any(tag["name"] == "financial" for tag in tags)
    assert any(tag["name"] == "health" for tag in tags)


def test_get_data_source_tags(mock_client):
    """Test getting tags for a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "tags": [
            {
                "name": "sensitive",
                "source": "curated",
                "modelType": "datasource",
                "modelId": "1",
                "addedBy": 1,
                "deleted": False,
            }
        ]
    }

    # Create tag service
    tag_service = TagService(mock_client)

    # Test getting data source tags
    tags = tag_service.get_data_source_tags(1)

    # Check that the correct endpoint was called
    mock_client.make_request.assert_called_once_with(
        "GET", "dataSource/1/tags", params={}
    )

    # Check the result
    assert "tags" in tags
    assert len(tags["tags"]) == 1
    assert tags["tags"][0]["name"] == "sensitive"


def test_get_data_source_tags_with_params(mock_client):
    """Test getting tags for a data source with parameters."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "tags": [
            {
                "name": "sensitive",
                "source": "curated",
                "modelType": "datasource",
                "modelId": "1",
                "addedBy": 1,
                "deleted": False,
            }
        ]
    }

    # Create tag service
    tag_service = TagService(mock_client)

    # Test getting data source tags with parameters
    tags = tag_service.get_data_source_tags(1, blob_id="blob123", blob_tags_only=True)

    # Check that the correct endpoint was called with parameters
    mock_client.make_request.assert_called_once_with(
        "GET",
        "dataSource/1/tags",
        params={"blobId": "blob123", "blobTagsOnly": True},
    )

    # Check the result
    assert "tags" in tags
    assert len(tags["tags"]) == 1
    assert tags["tags"][0]["name"] == "sensitive"


def test_get_data_source_tags_local(mock_client):
    """Test getting tags for a data source in local mode."""
    # Set up mock client
    mock_client.is_local = True

    # Create tag service
    tag_service = TagService(mock_client)

    # Test getting data source tags
    tags = tag_service.get_data_source_tags(1)

    # Check the result
    assert "tags" in tags
    assert len(tags["tags"]) == 1
    assert tags["tags"][0]["name"] == "sensitive"
    assert tags["tags"][0]["modelId"] == "1"


def test_add_data_source_tag(mock_client):
    """Test adding a tag to a data source."""
    # Set up mock client
    mock_client.is_local = False
    mock_client.make_request = MagicMock()
    mock_client.make_request.return_value.json.return_value = {
        "name": "sensitive",
        "source": "curated",
        "modelType": "datasource",
        "modelId": "1",
        "addedBy": 1,
        "deleted": False,
    }

    # Create tag service
    tag_service = TagService(mock_client)

    # Mock the add_tag method
    tag_service.add_tag = MagicMock()
    tag_service.add_tag.return_value = {
        "name": "sensitive",
        "source": "curated",
        "modelType": "datasource",
        "modelId": "1",
        "addedBy": 1,
        "deleted": False,
    }

    # Test adding a tag to a data source
    result = tag_service.add_data_source_tag(1, "sensitive")

    # Check that add_tag was called with the correct parameters
    tag_service.add_tag.assert_called_once_with(
        {"name": "sensitive", "modelType": "datasource", "modelId": "1"}
    )

    # Check the result
    assert result["name"] == "sensitive"
    assert result["modelType"] == "datasource"
    assert result["modelId"] == "1"
