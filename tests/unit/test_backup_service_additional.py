"""Additional unit tests for the backup service."""

import json
import os
import tempfile
from unittest.mock import <PERSON>Mock, patch

import pytest

from immuta_toolkit.services.backup_service import BackupService


@pytest.fixture
def mock_client():
    """Create a mock client for testing."""
    client = MagicMock()
    client.is_local = False
    client.data_source_service = MagicMock()
    client.policy_service = MagicMock()
    client.user_service = MagicMock()
    client.purpose_service = MagicMock()
    client.project_service = MagicMock()
    client.tag_service = MagicMock()
    client.notifier = MagicMock()
    client.storage = MagicMock()
    return client


class TestBackupDataSources:
    """Test backup_data_sources method."""

    def test_backup_data_sources_dry_run(self, mock_client):
        """Test backing up data sources in dry run mode."""
        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode
        result = backup_service.backup_data_sources(
            output_dir="/tmp/backup",
            data_source_ids=[1, 2],
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Would backup data sources" in result["message"]
        assert result["data_source_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_backup_data_sources_local_mode(self, mock_client):
        """Test backing up data sources in local mode."""
        # Set client to local mode
        mock_client.is_local = True

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_data_sources(
            output_dir="/tmp/backup",
            data_source_ids=[1, 2],
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Mock: Backed up data sources" in result["message"]
        assert result["data_source_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.makedirs")
    @patch("builtins.open", new_callable=MagicMock)
    def test_backup_data_sources_real_mode(self, mock_open, mock_makedirs, mock_client):
        """Test backing up data sources in real mode."""
        # Set up mock data sources
        mock_client.data_source_service.list_data_sources.return_value = [
            {
                "id": 1,
                "name": "Data Source 1",
                "description": "Test data source 1",
                "metadata": {"key1": "value1"},
            },
            {
                "id": 2,
                "name": "Data Source 2",
                "description": "Test data source 2",
                "metadata": {"key2": "value2"},
            },
        ]

        # Set up mock policies
        mock_client.policy_service.list_policies.return_value = [
            {"id": 1, "name": "Policy 1", "data_source_id": 1},
            {"id": 2, "name": "Policy 2", "data_source_id": 1},
        ]

        # Set up mock tags
        mock_client.tag_service.get_data_source_tags.return_value = [
            {"name": "tag1", "value": "value1"},
            {"name": "tag2", "value": "value2"},
        ]

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_data_sources(
            output_dir="/tmp/backup",
            data_source_ids=None,  # Backup all data sources
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Backed up 2 data sources" in result["message"]
        assert result["data_source_count"] == 2

        # Check that directories were created
        mock_makedirs.assert_called()

        # Check that files were written
        assert (
            mock_open.call_count >= 7
        )  # backup_info.json + 2 data sources * 3 files (data_source.json, policies.json, tags.json)

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()


class TestBackupPolicies:
    """Test backup_policies method."""

    def test_backup_policies_dry_run(self, mock_client):
        """Test backing up policies in dry run mode."""
        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode
        result = backup_service.backup_policies(
            output_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types=["RowRedaction", "ColumnMasking"],
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Would backup policies" in result["message"]
        assert result["policy_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_backup_policies_local_mode(self, mock_client):
        """Test backing up policies in local mode."""
        # Set client to local mode
        mock_client.is_local = True

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_policies(
            output_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types=["RowRedaction", "ColumnMasking"],
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Mock: Backed up policies" in result["message"]
        assert result["policy_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.makedirs")
    @patch("builtins.open", new_callable=MagicMock)
    def test_backup_policies_real_mode(self, mock_open, mock_makedirs, mock_client):
        """Test backing up policies in real mode."""
        # Set up mock policies
        mock_client.policy_service.list_policies.return_value = [
            {"id": 1, "name": "Policy 1", "policy_type": "RowRedaction"},
            {"id": 2, "name": "Policy 2", "policy_type": "ColumnMasking"},
            {"id": 3, "name": "Policy 3", "policy_type": "RowRedaction"},
        ]

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_policies(
            output_dir="/tmp/backup",
            policy_ids=None,  # Backup all policies
            policy_types=["RowRedaction"],  # Only backup RowRedaction policies
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Backed up" in result["message"]
        assert result["policy_count"] == 2  # Only 2 RowRedaction policies
        assert "RowRedaction" in result["policy_types"]

        # Check that directories were created
        mock_makedirs.assert_called()

        # Check that files were written
        assert (
            mock_open.call_count >= 3
        )  # backup_info.json + all_policies.json + RowRedaction_policies.json

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()


class TestBackupUsers:
    """Test backup_users method."""

    def test_backup_users_dry_run(self, mock_client):
        """Test backing up users in dry run mode."""
        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode
        result = backup_service.backup_users(
            output_dir="/tmp/backup",
            user_ids=[1, 2],
            include_groups=True,
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Would backup users" in result["message"]
        assert result["user_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    def test_backup_users_local_mode(self, mock_client):
        """Test backing up users in local mode."""
        # Set client to local mode
        mock_client.is_local = True

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_users(
            output_dir="/tmp/backup",
            user_ids=[1, 2],
            include_groups=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Mock: Backed up users" in result["message"]
        assert result["user_count"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.makedirs")
    @patch("builtins.open", new_callable=MagicMock)
    def test_backup_users_real_mode(self, mock_open, mock_makedirs, mock_client):
        """Test backing up users in real mode."""
        # Set up mock users
        mock_client.user_service.list_users.return_value = [
            {"id": 1, "name": "User 1", "email": "<EMAIL>"},
            {"id": 2, "name": "User 2", "email": "<EMAIL>"},
        ]

        # Set up mock groups
        mock_client.user_service.list_groups.return_value = [
            {"id": 1, "name": "Group 1"},
            {"id": 2, "name": "Group 2"},
        ]

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.backup_users(
            output_dir="/tmp/backup",
            user_ids=None,  # Backup all users
            include_groups=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Backed up 2 users" in result["message"]
        assert result["user_count"] == 2

        # Check that directories were created
        mock_makedirs.assert_called()

        # Check that files were written
        assert (
            mock_open.call_count >= 3
        )  # backup_info.json + all_users.json + all_groups.json

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()


class TestRestoreDataSources:
    """Test restore_data_sources method."""

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_data_sources_dry_run(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring data sources in dry run mode."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        }

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode
        result = backup_service.restore_data_sources(
            backup_dir="/tmp/backup",
            data_source_ids=[1, 2],
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Would restore data sources" in result["message"]

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_data_sources_local_mode(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring data sources in local mode."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "data_source_count": 2,
            "include_policies": True,
            "include_tags": True,
            "include_metadata": True,
        }

        # Set client to local mode
        mock_client.is_local = True

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.restore_data_sources(
            backup_dir="/tmp/backup",
            data_source_ids=[1, 2],
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Mock: Restored data sources" in result["message"]

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("os.listdir")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_data_sources_real_mode(
        self,
        mock_json_load,
        mock_open,
        mock_listdir,
        mock_isfile,
        mock_isdir,
        mock_client,
    ):
        """Test restoring data sources in real mode."""
        # Set up mocks
        mock_isdir.return_value = True
        mock_isfile.return_value = True
        mock_listdir.return_value = ["1"]  # Directory for data source 1

        # Mock backup info
        mock_json_load.side_effect = [
            # First call - backup_info.json
            {
                "timestamp": 1234567890,
                "datetime": "2023-01-01T00:00:00",
                "data_source_count": 2,
                "include_policies": True,
                "include_tags": True,
                "include_metadata": True,
            },
            # Second call - data_source.json for data source 1
            {
                "id": 1,
                "name": "Data Source 1",
                "description": "Test data source 1",
                "metadata": {"key1": "value1"},
            },
            # Third call - policies.json for data source 1
            [
                {"id": 1, "name": "Policy 1", "data_source_id": 1},
                {"id": 2, "name": "Policy 2", "data_source_id": 1},
            ],
            # Fourth call - tags.json for data source 1
            [
                {"name": "tag1", "value": "value1"},
                {"name": "tag2", "value": "value2"},
            ],
        ]

        # Set up mock client responses
        mock_client.data_source_service.create_data_source.return_value = {"id": 1}
        mock_client.policy_service.create_policy.return_value = {"id": 1}

        # Set client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.restore_data_sources(
            backup_dir="/tmp/backup",
            data_source_ids=[1],
            include_policies=True,
            include_tags=True,
            include_metadata=True,
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()


class TestRestorePolicies:
    """Test restore_policies method."""

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_policies_dry_run(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring policies in dry run mode."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "policy_count": 3,
            "policy_types": ["RowRedaction", "ColumnMasking"],
        }

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method in dry run mode
        result = backup_service.restore_policies(
            backup_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types=["RowRedaction", "ColumnMasking"],
            dry_run=True,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Would restore policies" in result["message"]

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_policies_local_mode(
        self, mock_json_load, mock_open, mock_isfile, mock_isdir, mock_client
    ):
        """Test restoring policies in local mode."""
        # Mock directory and file existence
        mock_isdir.return_value = True
        mock_isfile.return_value = True

        # Mock backup info
        mock_json_load.return_value = {
            "timestamp": 1234567890,
            "datetime": "2023-01-01T00:00:00",
            "policy_count": 3,
            "policy_types": ["RowRedaction", "ColumnMasking"],
        }

        # Set client to local mode
        mock_client.is_local = True

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.restore_policies(
            backup_dir="/tmp/backup",
            policy_ids=[1, 2],
            policy_types=["RowRedaction", "ColumnMasking"],
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "Mock: Restored policies" in result["message"]

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()

    @patch("os.path.isdir")
    @patch("os.path.isfile")
    @patch("os.listdir")
    @patch("builtins.open", new_callable=MagicMock)
    @patch("json.load")
    def test_restore_policies_real_mode(
        self,
        mock_json_load,
        mock_open,
        mock_listdir,
        mock_isfile,
        mock_isdir,
        mock_client,
    ):
        """Test restoring policies in real mode."""
        # Set up mocks
        mock_isdir.return_value = True
        mock_isfile.return_value = True
        mock_listdir.return_value = ["all_policies.json"]

        # Mock backup info
        mock_json_load.side_effect = [
            # First call - backup_info.json
            {
                "timestamp": 1234567890,
                "datetime": "2023-01-01T00:00:00",
                "policy_count": 3,
                "policy_types": ["RowRedaction", "ColumnMasking"],
            },
            # Second call - all_policies.json
            [
                {"id": 1, "name": "Policy 1", "policy_type": "RowRedaction"},
                {"id": 2, "name": "Policy 2", "policy_type": "ColumnMasking"},
                {"id": 3, "name": "Policy 3", "policy_type": "RowRedaction"},
            ],
        ]

        # Set up mock client responses
        mock_client.policy_service.create_policy.return_value = {"id": 1}

        # Set client to not be in local mode
        mock_client.is_local = False

        # Create backup service
        backup_service = BackupService(mock_client)

        # Call the method
        result = backup_service.restore_policies(
            backup_dir="/tmp/backup",
            policy_ids=[1, 3],  # Only restore policies 1 and 3
            policy_types=["RowRedaction"],  # Only restore RowRedaction policies
            dry_run=False,
        )

        # Check the result
        assert result["status"] == "success"
        assert "restored" in result
        assert result["restored"] == 2

        # Check that notification was sent
        mock_client.notifier.send_notification.assert_called_once()
