"""Unit tests for the secrets manager."""

import os
from unittest.mock import patch, MagicMock

import pytest

from immuta_toolkit.utils.secrets import (
    SecretsManager,
    EnvSecretsManager,
    AzureKeyVaultSecretsManager,
    get_secrets_manager,
)


class TestSecretsManager:
    """Test the SecretsManager base class."""

    def test_abstract_methods(self):
        """Test that abstract methods raise NotImplementedError."""
        # Cannot instantiate abstract class directly
        with pytest.raises(TypeError):
            manager = SecretsManager()

        # Create a concrete implementation for testing
        class TestSecretsManagerImpl(SecretsManager):
            def get_secret(self, secret_name, default=None):
                return "test_value"

            def set_secret(self, secret_name, secret_value):
                pass

            def list_secrets(self):
                return []

            def delete_secret(self, secret_name):
                pass

        # Create an instance of the implementation
        manager = TestSecretsManagerImpl()

        # Test that methods work
        assert manager.get_secret("test_secret") == "test_value"


class TestEnvSecretsManager:
    """Test the EnvSecretsManager class."""

    def test_get_secret_exists(self):
        """Test getting a secret that exists in the environment."""
        # Set up environment variable
        os.environ["TEST_SECRET"] = "test_value"

        manager = EnvSecretsManager()
        value = manager.get_secret("TEST_SECRET")

        assert value == "test_value"

        # Clean up
        del os.environ["TEST_SECRET"]

    def test_get_secret_not_exists(self):
        """Test getting a secret that doesn't exist in the environment."""
        # Ensure the environment variable doesn't exist
        if "NON_EXISTENT_SECRET" in os.environ:
            del os.environ["NON_EXISTENT_SECRET"]

        manager = EnvSecretsManager()

        with pytest.raises(ValueError):
            manager.get_secret("NON_EXISTENT_SECRET")

    def test_get_secret_with_default(self):
        """Test getting a secret with a default value."""
        # Ensure the environment variable doesn't exist
        if "NON_EXISTENT_SECRET" in os.environ:
            del os.environ["NON_EXISTENT_SECRET"]

        manager = EnvSecretsManager()
        value = manager.get_secret("NON_EXISTENT_SECRET", default="default_value")

        assert value == "default_value"


class TestAzureKeyVaultSecretsManager:
    """Test the AzureKeyVaultSecretsManager class."""

    @patch("immuta_toolkit.utils.secrets.DefaultAzureCredential")
    @patch("immuta_toolkit.utils.secrets.SecretClient")
    def test_init(self, mock_secret_client, mock_credential):
        """Test initialization of AzureKeyVaultSecretsManager."""
        # Set up mocks
        mock_credential_instance = MagicMock()
        mock_credential.return_value = mock_credential_instance

        mock_client_instance = MagicMock()
        mock_secret_client.return_value = mock_client_instance

        # Initialize manager
        manager = AzureKeyVaultSecretsManager("https://test-vault.vault.azure.net/")

        # Verify mocks were called correctly
        mock_credential.assert_called_once()
        mock_secret_client.assert_called_once_with(
            vault_url="https://test-vault.vault.azure.net/",
            credential=mock_credential_instance,
        )

        # Verify client was set
        assert manager.client is mock_client_instance

    @patch("immuta_toolkit.utils.secrets.DefaultAzureCredential")
    @patch("immuta_toolkit.utils.secrets.SecretClient")
    def test_get_secret_exists(self, mock_secret_client, mock_credential):
        """Test getting a secret that exists in Azure Key Vault."""
        # Set up mocks
        mock_client_instance = MagicMock()
        mock_secret_client.return_value = mock_client_instance

        mock_secret = MagicMock()
        mock_secret.value = "test_value"
        mock_client_instance.get_secret.return_value = mock_secret

        # Initialize manager
        manager = AzureKeyVaultSecretsManager("https://test-vault.vault.azure.net/")

        # Get secret
        value = manager.get_secret("test-secret")

        # Verify client was called correctly
        mock_client_instance.get_secret.assert_called_once_with("test-secret")

        # Verify value was returned
        assert value == "test_value"

    @patch("immuta_toolkit.utils.secrets.DefaultAzureCredential")
    @patch("immuta_toolkit.utils.secrets.SecretClient")
    def test_get_secret_not_exists(self, mock_secret_client, mock_credential):
        """Test getting a secret that doesn't exist in Azure Key Vault."""
        # Set up mocks
        mock_client_instance = MagicMock()
        mock_secret_client.return_value = mock_client_instance

        # Make get_secret raise an exception
        mock_client_instance.get_secret.side_effect = Exception("Secret not found")

        # Initialize manager
        manager = AzureKeyVaultSecretsManager("https://test-vault.vault.azure.net/")

        # Get secret
        with pytest.raises(ValueError):
            manager.get_secret("non-existent-secret")

        # Verify client was called correctly
        mock_client_instance.get_secret.assert_called_once_with("non-existent-secret")

    @patch("immuta_toolkit.utils.secrets.DefaultAzureCredential")
    @patch("immuta_toolkit.utils.secrets.SecretClient")
    def test_get_secret_with_default(self, mock_secret_client, mock_credential):
        """Test getting a secret with a default value."""
        # Set up mocks
        mock_client_instance = MagicMock()
        mock_secret_client.return_value = mock_client_instance

        # Make get_secret raise an exception
        mock_client_instance.get_secret.side_effect = Exception("Secret not found")

        # Initialize manager
        manager = AzureKeyVaultSecretsManager("https://test-vault.vault.azure.net/")

        # Get secret with default
        value = manager.get_secret("non-existent-secret", default="default_value")

        # Verify client was called correctly
        mock_client_instance.get_secret.assert_called_once_with("non-existent-secret")

        # Verify default value was returned
        assert value == "default_value"


class TestGetSecretsManager:
    """Test the get_secrets_manager function."""

    def test_get_env_secrets_manager(self):
        """Test getting an EnvSecretsManager."""
        manager = get_secrets_manager(provider="env")
        assert isinstance(manager, EnvSecretsManager)

    @patch("immuta_toolkit.utils.secrets.AzureKeyVaultSecretsManager")
    def test_get_azure_secrets_manager(self, mock_azure_manager):
        """Test getting an AzureKeyVaultSecretsManager."""
        # Set up mock
        mock_instance = MagicMock()
        mock_azure_manager.return_value = mock_instance

        # Get manager
        manager = get_secrets_manager(
            provider="azure", vault_url="https://test-vault.vault.azure.net/"
        )

        # Verify mock was called correctly
        mock_azure_manager.assert_called_once_with(
            vault_url="https://test-vault.vault.azure.net/",
            client_id=None,
            client_secret=None,
            tenant_id=None,
        )

        # Verify manager was returned
        assert manager is mock_instance

    def test_get_azure_secrets_manager_no_vault_url(self):
        """Test getting an AzureKeyVaultSecretsManager without a vault URL."""
        with pytest.raises(ValueError):
            get_secrets_manager(provider="azure")

    def test_get_unknown_secrets_manager(self):
        """Test getting an unknown secrets manager."""
        with pytest.raises(ValueError):
            get_secrets_manager(provider="unknown")
