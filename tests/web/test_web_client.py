"""Tests for the enhanced web client."""

import os
import pytest
from unittest.mock import MagicMock, patch

from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.web.exceptions import (
    WebAutomationError,
    AuthenticationError,
    ElementNotFoundError,
    NavigationError,
)
from immuta_toolkit.models import UserModel


@pytest.fixture
def mock_playwright():
    """Create a mock playwright instance."""
    with patch("immuta_toolkit.web.client.sync_playwright") as mock_sync_playwright:
        playwright = MagicMock()
        mock_sync_playwright.return_value.start.return_value = playwright

        # Set up browser instances
        chromium = MagicMock()
        firefox = MagicMock()
        webkit = MagicMock()

        playwright.chromium = chromium
        playwright.firefox = firefox
        playwright.webkit = webkit

        # Set up browser launch
        browser = MagicMock()
        chromium.launch.return_value = browser
        firefox.launch.return_value = browser
        webkit.launch.return_value = browser

        # Set up context
        context = MagicMock()
        browser.new_context.return_value = context

        # Set up page
        page = MagicMock()
        context.new_page.return_value = page

        yield playwright


@pytest.fixture
def mock_page():
    """Create a mock page."""
    with patch("immuta_toolkit.web.client.sync_playwright") as mock_sync_playwright:
        page = MagicMock()

        # Set up playwright
        playwright = MagicMock()
        mock_sync_playwright.return_value.start.return_value = playwright

        # Set up browser
        browser = MagicMock()
        playwright.chromium.launch.return_value = browser

        # Set up context
        context = MagicMock()
        browser.new_context.return_value = context

        # Set up page
        context.new_page.return_value = page

        yield page


@pytest.fixture
def mock_client(mock_page):
    """Create a mock client with mocked components."""
    with patch("immuta_toolkit.web.client.LoginPage") as mock_login_page, \
         patch("immuta_toolkit.web.client.UsersPage") as mock_users_page, \
         patch("immuta_toolkit.web.client.DataSourcesPage") as mock_data_sources_page, \
         patch("immuta_toolkit.web.client.PoliciesPage") as mock_policies_page, \
         patch("immuta_toolkit.web.client.ProjectsPage") as mock_projects_page, \
         patch("immuta_toolkit.web.client.NavigationComponent") as mock_navigation, \
         patch("immuta_toolkit.web.client.DialogComponent") as mock_dialog, \
         patch("immuta_toolkit.web.client.UserFormComponent") as mock_user_form, \
         patch("immuta_toolkit.web.client.UserCardComponent") as mock_user_card, \
         patch("immuta_toolkit.web.client.os.makedirs"):

        # Set up login page
        login_page = MagicMock()
        mock_login_page.return_value = login_page

        # Set up users page
        users_page = MagicMock()
        mock_users_page.return_value = users_page

        # Set up data sources page
        data_sources_page = MagicMock()
        mock_data_sources_page.return_value = data_sources_page

        # Set up policies page
        policies_page = MagicMock()
        mock_policies_page.return_value = policies_page

        # Set up projects page
        projects_page = MagicMock()
        mock_projects_page.return_value = projects_page

        # Set up navigation component
        navigation = MagicMock()
        mock_navigation.return_value = navigation
        navigation.is_logged_in.return_value = True

        # Set up dialog component
        dialog = MagicMock()
        mock_dialog.return_value = dialog

        # Set up user form component
        user_form = MagicMock()
        mock_user_form.return_value = user_form

        # Set up user card component
        user_card = MagicMock()
        mock_user_card.return_value = user_card

        # Create client
        client = ImmutaWebClient(
            base_url="https://example.com",
            username="test_user",
            password="test_password",
            headless=True,
        )

        # Set up components for testing
        client.login_page = login_page
        client.users_page = users_page
        client.data_sources_page = data_sources_page
        client.policies_page = policies_page
        client.projects_page = projects_page
        client.navigation = navigation
        client.dialog = dialog

        yield client


class TestImmutaWebClient:
    """Tests for the ImmutaWebClient class."""

    def test_init(self, mock_playwright):
        """Test client initialization."""
        client = ImmutaWebClient(
            base_url="https://example.com",
            username="test_user",
            password="test_password",
            headless=True,
        )

        assert client.base_url == "https://example.com"
        assert client.username == "test_user"
        assert client.password == "test_password"
        assert client.headless is True
        assert client.browser_type == "chromium"
        assert client.timeout == 30000
        assert client.retry_count == 3
        assert client.retry_delay == 500
        assert os.path.exists(client.screenshot_dir)

        # Verify playwright was initialized
        mock_playwright.chromium.launch.assert_called_once()

        # Clean up
        client.close()

    def test_login(self, mock_client):
        """Test login."""
        # Login is called during initialization
        mock_client.login_page.login.assert_called_once_with(
            mock_client.username, mock_client.password
        )

        # Verify login verification
        mock_client.navigation.is_logged_in.assert_called_once()

    def test_login_failure(self, mock_client):
        """Test login failure."""
        # Set up login failure
        mock_client.navigation.is_logged_in.return_value = False

        # Mock screenshot
        mock_client.take_screenshot = MagicMock(return_value="test_screenshot.png")

        # Test login failure
        with pytest.raises(AuthenticationError):
            mock_client.login()

    def test_create_user(self, mock_client):
        """Test create user."""
        # Set up user
        user = UserModel(
            email="<EMAIL>",
            name="Test User",
            attributes={
                "role": "data_scientist",
                "department": "Engineering",
                "title": "Software Engineer"
            }
        )

        # Set up user form
        user_form = MagicMock()
        with patch("immuta_toolkit.web.client.UserFormComponent") as mock_user_form:
            mock_user_form.return_value = user_form

            # Call create_user
            result = mock_client.create_user(user)

            # Verify navigation
            mock_client.navigation.navigate_to_users.assert_called_once()

            # Verify click create button
            mock_client.users_page.click.assert_called_once_with("users.create_button")

            # Verify user form creation
            mock_user_form.assert_called_once_with(mock_client.page, mock_client.timeout)

            # Verify user creation
            user_form.create_user.assert_called_once_with(user)

            # Verify result
            assert result["email"] == "<EMAIL>"
            assert result["name"] == "Test User"
            assert result["role"] == "data_scientist"

    def test_get_user(self, mock_client):
        """Test get user."""
        # Set up user card
        user_card = MagicMock()
        user_card.find.return_value = True
        user_card.get_details.return_value = {
            "email": "<EMAIL>",
            "name": "Test User",
            "role": "DataScientist",
        }

        with patch("immuta_toolkit.web.client.UserCardComponent") as mock_user_card:
            mock_user_card.return_value = user_card

            # Call get_user
            result = mock_client.get_user("<EMAIL>")

            # Verify navigation
            mock_client.navigation.navigate_to_users.assert_called_once()

            # Verify search
            mock_client.users_page.fill.assert_called_once_with(
                "users.search_input", "<EMAIL>"
            )

            # Verify user card creation
            mock_user_card.assert_called_once_with(
                mock_client.page, "<EMAIL>", mock_client.timeout
            )

            # Verify user card find
            user_card.find.assert_called_once()

            # Verify user card get_details
            user_card.get_details.assert_called_once()

            # Verify result
            assert result["email"] == "<EMAIL>"
            assert result["name"] == "Test User"
            assert result["role"] == "DataScientist"

    def test_delete_user(self, mock_client):
        """Test delete user."""
        # Set up user card
        user_card = MagicMock()
        user_card.find.return_value = True

        with patch("immuta_toolkit.web.client.UserCardComponent") as mock_user_card:
            mock_user_card.return_value = user_card

            # Call delete_user
            result = mock_client.delete_user("<EMAIL>")

            # Verify navigation
            mock_client.navigation.navigate_to_users.assert_called_once()

            # Verify search
            mock_client.users_page.fill.assert_called_once_with(
                "users.search_input", "<EMAIL>"
            )

            # Verify user card creation
            mock_user_card.assert_called_once_with(
                mock_client.page, "<EMAIL>", mock_client.timeout
            )

            # Verify user card find
            user_card.find.assert_called_once()

            # Verify user card delete
            user_card.delete.assert_called_once()

            # Verify dialog confirm
            mock_client.dialog.confirm.assert_called_once()

            # Verify result
            assert result is True
