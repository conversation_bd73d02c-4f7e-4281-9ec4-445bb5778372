"""Tests for the enhanced base page object."""

import os
import pytest
from unittest.mock import MagicMock, patch

from playwright.sync_api import Page, ElementHandle, TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.web.page_objects.enhanced_base_page import EnhancedBasePage
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
    ElementNotClickableError,
    NavigationError,
    TimeoutError,
    FormSubmissionError,
    WebAutomationError,
)


@pytest.fixture
def mock_page():
    """Create a mock page object."""
    page = MagicMock(spec=Page)
    page.url = "https://example.com"
    return page


@pytest.fixture
def mock_element():
    """Create a mock element handle."""
    element = MagicMock(spec=ElementHandle)
    element.text_content.return_value = "Test text"
    element.bounding_box.return_value = {"x": 0, "y": 0, "width": 100, "height": 100}
    return element


@pytest.fixture
def base_page(mock_page):
    """Create a base page object with a mock page."""
    with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
        mock_get_selector.return_value = "test-selector"
        return EnhancedBasePage(mock_page, "https://example.com")


class TestEnhancedBasePage:
    """Tests for the EnhancedBasePage class."""

    def test_init(self, mock_page):
        """Test initialization."""
        page = EnhancedBasePage(mock_page, "https://example.com")
        assert page.page == mock_page
        assert page.base_url == "https://example.com"
        assert page.timeout == 10000
        assert page.retry_count == 3
        assert page.retry_delay == 500
        assert page.screenshot_on_error is True
        assert page.performance_metrics == {}

    def test_navigate(self, base_page):
        """Test navigation."""
        base_page.navigate("test")
        base_page.page.goto.assert_called_once_with("https://example.com/test")
        base_page.page.wait_for_load_state.assert_called()

    def test_navigate_error(self, base_page):
        """Test navigation error."""
        base_page.page.goto.side_effect = Exception("Navigation error")
        
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.create_error_context") as mock_create_error_context:
            mock_create_error_context.return_value = {
                "screenshot_path": "test.png",
                "page_url": "https://example.com",
            }
            
            with pytest.raises(NavigationError):
                base_page.navigate("test")

    def test_wait_for_selector(self, base_page, mock_element):
        """Test waiting for a selector."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            base_page.page.wait_for_selector.return_value = mock_element
            
            element = base_page.wait_for_selector("test.selector")
            
            assert element == mock_element
            base_page.page.wait_for_selector.assert_called_once_with(
                "test-selector", timeout=10000, state="visible"
            )

    def test_wait_for_selector_not_found(self, base_page):
        """Test waiting for a selector that is not found."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            base_page.page.wait_for_selector.return_value = None
            
            with patch("immuta_toolkit.web.page_objects.enhanced_base_page.create_error_context") as mock_create_error_context:
                mock_create_error_context.return_value = {
                    "screenshot_path": "test.png",
                    "page_url": "https://example.com",
                }
                
                with pytest.raises(ElementNotFoundError):
                    base_page.wait_for_selector("test.selector")

    def test_wait_for_selector_timeout(self, base_page):
        """Test waiting for a selector that times out."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            base_page.page.wait_for_selector.side_effect = PlaywrightTimeoutError("Timeout")
            
            with patch("immuta_toolkit.web.page_objects.enhanced_base_page.create_error_context") as mock_create_error_context:
                mock_create_error_context.return_value = {
                    "screenshot_path": "test.png",
                    "page_url": "https://example.com",
                }
                
                with pytest.raises(ElementNotVisibleError):
                    base_page.wait_for_selector("test.selector")

    def test_click(self, base_page):
        """Test clicking an element."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            
            base_page.click("test.selector")
            
            base_page.page.click.assert_called_once_with(
                "test-selector", timeout=10000, force=False
            )

    def test_click_error(self, base_page):
        """Test clicking an element that fails."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            base_page.page.click.side_effect = Exception("Click error")
            
            with patch("immuta_toolkit.web.page_objects.enhanced_base_page.create_error_context") as mock_create_error_context:
                mock_create_error_context.return_value = {
                    "screenshot_path": "test.png",
                    "page_url": "https://example.com",
                }
                
                with pytest.raises(ElementNotClickableError):
                    base_page.click("test.selector", retry=False)

    def test_fill(self, base_page):
        """Test filling a form field."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            
            base_page.fill("test.selector", "test value")
            
            base_page.page.fill.assert_called_once_with(
                "test-selector", "test value", timeout=10000
            )

    def test_fill_error(self, base_page):
        """Test filling a form field that fails."""
        with patch("immuta_toolkit.web.page_objects.enhanced_base_page.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            base_page.page.fill.side_effect = Exception("Fill error")
            
            with patch("immuta_toolkit.web.page_objects.enhanced_base_page.create_error_context") as mock_create_error_context:
                mock_create_error_context.return_value = {
                    "screenshot_path": "test.png",
                    "page_url": "https://example.com",
                }
                
                with pytest.raises(FormSubmissionError):
                    base_page.fill("test.selector", "test value", retry=False)

    def test_get_performance_metrics(self, base_page):
        """Test getting performance metrics."""
        base_page.performance_metrics = {
            "navigation": [1.0, 2.0, 3.0],
            "click": [0.5, 1.5],
        }
        
        metrics = base_page.get_performance_metrics()
        
        assert metrics["navigation"]["min"] == 1.0
        assert metrics["navigation"]["max"] == 3.0
        assert metrics["navigation"]["avg"] == 2.0
        assert metrics["navigation"]["count"] == 3
        assert metrics["navigation"]["total"] == 6.0
        
        assert metrics["click"]["min"] == 0.5
        assert metrics["click"]["max"] == 1.5
        assert metrics["click"]["avg"] == 1.0
        assert metrics["click"]["count"] == 2
        assert metrics["click"]["total"] == 2.0
