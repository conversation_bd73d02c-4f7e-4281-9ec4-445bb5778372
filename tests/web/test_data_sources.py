"""Tests for data sources page."""

import os
import time
import pytest
from playwright.sync_api import expect

from immuta_toolkit.web.page_objects.data_sources_page import DataSourcesPage


@pytest.fixture(scope="function")
def data_sources_page(page, base_url):
    """Fixture for data sources page."""
    return DataSourcesPage(page, base_url)


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_create_data_source(login_page, data_sources_page):
    """Test creating a data source."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create data source
    data_source = {
        "name": f"Test Data Source {int(time.time())}",
        "description": "Test data source created by automation",
        "handler_type": "snowflake",
        "account": "myaccount",
        "warehouse": "mywarehouse",
        "database": "mydatabase",
        "schema": "myschema",
        "username": "myusername",
        "password": "mypassword",
    }
    
    try:
        result = data_sources_page.create_data_source(data_source)
        
        # Verify data source was created
        assert result["name"] == data_source["name"]
        assert result["description"] == data_source["description"]
        assert result["handler_type"] == data_source["handler_type"]
        
        # Verify data source exists
        found_data_source = data_sources_page.search_data_source(data_source["name"])
        assert found_data_source is not None
        assert found_data_source["name"] == data_source["name"]
    finally:
        # Clean up
        try:
            data_sources_page.delete_data_source(data_source["name"])
        except Exception as e:
            print(f"Failed to clean up data source: {e}")


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_delete_data_source(login_page, data_sources_page):
    """Test deleting a data source."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create data source
    data_source = {
        "name": f"Test Data Source Delete {int(time.time())}",
        "description": "Test data source for deletion",
        "handler_type": "snowflake",
        "account": "myaccount",
        "warehouse": "mywarehouse",
        "database": "mydatabase",
        "schema": "myschema",
        "username": "myusername",
        "password": "mypassword",
    }
    
    # Create data source
    data_sources_page.create_data_source(data_source)
    
    # Delete data source
    result = data_sources_page.delete_data_source(data_source["name"])
    assert result is True
    
    # Verify data source no longer exists
    found_data_source = data_sources_page.search_data_source(data_source["name"])
    assert found_data_source is None
