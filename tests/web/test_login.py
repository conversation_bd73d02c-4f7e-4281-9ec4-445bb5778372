"""Tests for login page."""

import os
import pytest
from playwright.sync_api import expect

from immuta_toolkit.web.page_objects.login_page import LoginPage


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_login(login_page):
    """Test login functionality."""
    # Get credentials from environment variables
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    
    # Login
    login_page.login(username, password)
    
    # Verify login was successful
    assert login_page.is_logged_in(), "Login failed"
