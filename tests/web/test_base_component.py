"""Tests for the base component object."""

import pytest
from unittest.mock import Magic<PERSON><PERSON>, patch

from playwright.sync_api import Page, Element<PERSON>andle

from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)


@pytest.fixture
def mock_page():
    """Create a mock page object."""
    page = MagicMock(spec=Page)
    page.url = "https://example.com"
    return page


@pytest.fixture
def mock_element():
    """Create a mock element handle."""
    element = MagicMock(spec=ElementHandle)
    element.text_content.return_value = "Test text"
    return element


@pytest.fixture
def base_component(mock_page):
    """Create a base component object with a mock page."""
    with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
        mock_get_selector.return_value = "test-selector"
        return BaseComponent(mock_page, "test.root")


class TestBaseComponent:
    """Tests for the BaseComponent class."""

    def test_init(self, mock_page):
        """Test initialization."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "test-selector"
            component = BaseComponent(mock_page, "test.root")
            
            assert component.page == mock_page
            assert component.root_selector_key == "test.root"
            assert component.root_selector == "test-selector"
            assert component.timeout == 10000

    def test_get_root_element(self, base_component, mock_element):
        """Test getting the root element."""
        base_component.page.wait_for_selector.return_value = mock_element
        
        element = base_component.get_root_element()
        
        assert element == mock_element
        base_component.page.wait_for_selector.assert_called_once_with(
            "test-selector", timeout=10000
        )

    def test_get_root_element_not_found(self, base_component):
        """Test getting the root element when it is not found."""
        base_component.page.wait_for_selector.return_value = None
        
        with pytest.raises(ElementNotFoundError):
            base_component.get_root_element()

    def test_get_root_element_error(self, base_component):
        """Test getting the root element when an error occurs."""
        base_component.page.wait_for_selector.side_effect = Exception("Element not found")
        
        with pytest.raises(ElementNotFoundError):
            base_component.get_root_element()

    def test_is_visible(self, base_component):
        """Test checking if the component is visible."""
        base_component.page.wait_for_selector.return_value = MagicMock()
        
        assert base_component.is_visible() is True
        base_component.page.wait_for_selector.assert_called_once_with(
            "test-selector", timeout=5000, state="visible"
        )

    def test_is_visible_not_visible(self, base_component):
        """Test checking if the component is visible when it is not."""
        base_component.page.wait_for_selector.side_effect = Exception("Element not visible")
        
        assert base_component.is_visible() is False

    def test_wait_for_visible(self, base_component):
        """Test waiting for the component to be visible."""
        base_component.wait_for_visible()
        
        base_component.page.wait_for_selector.assert_called_once_with(
            "test-selector", timeout=10000, state="visible"
        )

    def test_wait_for_visible_error(self, base_component):
        """Test waiting for the component to be visible when an error occurs."""
        base_component.page.wait_for_selector.side_effect = Exception("Element not visible")
        
        with pytest.raises(ElementNotVisibleError):
            base_component.wait_for_visible()

    def test_wait_for_hidden(self, base_component):
        """Test waiting for the component to be hidden."""
        base_component.wait_for_hidden()
        
        base_component.page.wait_for_selector.assert_called_once_with(
            "test-selector", timeout=10000, state="hidden"
        )

    def test_wait_for_hidden_error(self, base_component):
        """Test waiting for the component to be hidden when an error occurs."""
        base_component.page.wait_for_selector.side_effect = Exception("Element still visible")
        
        with pytest.raises(ElementNotVisibleError):
            base_component.wait_for_hidden()

    def test_get_child_element(self, base_component, mock_element):
        """Test getting a child element."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "child-selector"
            base_component.get_root_element = MagicMock(return_value=mock_element)
            mock_element.wait_for_selector.return_value = mock_element
            
            child = base_component.get_child_element("test.child")
            
            assert child == mock_element
            mock_element.wait_for_selector.assert_called_once_with(
                "child-selector", timeout=10000
            )

    def test_get_child_element_not_found(self, base_component, mock_element):
        """Test getting a child element when it is not found."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "child-selector"
            base_component.get_root_element = MagicMock(return_value=mock_element)
            mock_element.wait_for_selector.return_value = None
            
            with pytest.raises(ElementNotFoundError):
                base_component.get_child_element("test.child")

    def test_get_child_element_error(self, base_component, mock_element):
        """Test getting a child element when an error occurs."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "child-selector"
            base_component.get_root_element = MagicMock(return_value=mock_element)
            mock_element.wait_for_selector.side_effect = Exception("Child not found")
            
            with pytest.raises(ElementNotFoundError):
                base_component.get_child_element("test.child")

    def test_click_child(self, base_component, mock_element):
        """Test clicking a child element."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "child-selector"
            base_component.get_child_element = MagicMock(return_value=mock_element)
            
            base_component.click_child("test.child")
            
            mock_element.click.assert_called_once_with(timeout=10000, force=False)

    def test_click_child_error(self, base_component, mock_element):
        """Test clicking a child element when an error occurs."""
        with patch("immuta_toolkit.web.components.base_component.get_selector") as mock_get_selector:
            mock_get_selector.return_value = "child-selector"
            base_component.get_child_element = MagicMock(return_value=mock_element)
            mock_element.click.side_effect = Exception("Click error")
            
            with pytest.raises(ElementNotFoundError):
                base_component.click_child("test.child")

    def test_get_child_text(self, base_component, mock_element):
        """Test getting text from a child element."""
        base_component.get_child_element = MagicMock(return_value=mock_element)
        
        text = base_component.get_child_text("test.child")
        
        assert text == "Test text"
