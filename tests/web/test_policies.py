"""Tests for policies page."""

import os
import time
import pytest
from playwright.sync_api import expect

from immuta_toolkit.web.page_objects.policies_page import PoliciesPage


@pytest.fixture(scope="function")
def policies_page(page, base_url):
    """Fixture for policies page."""
    return PoliciesPage(page, base_url)


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_create_policy(login_page, policies_page):
    """Test creating a policy."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create policy
    policy = {
        "name": f"Test Policy {int(time.time())}",
        "description": "Test policy created by automation",
        "policy_type": "row_level",
        "condition": "true",
        "data_sources": ["Test Data Source"],
    }
    
    try:
        result = policies_page.create_policy(policy)
        
        # Verify policy was created
        assert result["name"] == policy["name"]
        assert result["description"] == policy["description"]
        assert result["policy_type"] == policy["policy_type"]
        
        # Verify policy exists
        found_policy = policies_page.search_policy(policy["name"])
        assert found_policy is not None
        assert found_policy["name"] == policy["name"]
    finally:
        # Clean up
        try:
            policies_page.delete_policy(policy["name"])
        except Exception as e:
            print(f"Failed to clean up policy: {e}")


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_delete_policy(login_page, policies_page):
    """Test deleting a policy."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create policy
    policy = {
        "name": f"Test Policy Delete {int(time.time())}",
        "description": "Test policy for deletion",
        "policy_type": "row_level",
        "condition": "true",
        "data_sources": ["Test Data Source"],
    }
    
    # Create policy
    policies_page.create_policy(policy)
    
    # Delete policy
    result = policies_page.delete_policy(policy["name"])
    assert result is True
    
    # Verify policy no longer exists
    found_policy = policies_page.search_policy(policy["name"])
    assert found_policy is None
