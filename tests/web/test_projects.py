"""Tests for projects page."""

import os
import time
import pytest
from playwright.sync_api import expect

from immuta_toolkit.web.page_objects.projects_page import ProjectsPage


@pytest.fixture(scope="function")
def projects_page(page, base_url):
    """Fixture for projects page."""
    return ProjectsPage(page, base_url)


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_create_project(login_page, projects_page):
    """Test creating a project."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create project
    project = {
        "name": f"Test Project {int(time.time())}",
        "description": "Test project created by automation",
        "data_sources": ["Test Data Source"],
        "members": [
            {
                "email": username,
                "role": "OWNER",
            }
        ],
    }
    
    try:
        result = projects_page.create_project(project)
        
        # Verify project was created
        assert result["name"] == project["name"]
        assert result["description"] == project["description"]
        
        # Verify project exists
        found_project = projects_page.search_project(project["name"])
        assert found_project is not None
        assert found_project["name"] == project["name"]
    finally:
        # Clean up
        try:
            projects_page.delete_project(project["name"])
        except Exception as e:
            print(f"Failed to clean up project: {e}")


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_delete_project(login_page, projects_page):
    """Test deleting a project."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create project
    project = {
        "name": f"Test Project Delete {int(time.time())}",
        "description": "Test project for deletion",
        "data_sources": ["Test Data Source"],
    }
    
    # Create project
    projects_page.create_project(project)
    
    # Delete project
    result = projects_page.delete_project(project["name"])
    assert result is True
    
    # Verify project no longer exists
    found_project = projects_page.search_project(project["name"])
    assert found_project is None


@pytest.mark.skipif(
    not os.getenv("IMMUTA_BASE_URL") or not os.getenv("IMMUTA_USERNAME") or not os.getenv("IMMUTA_PASSWORD"),
    reason="Immuta credentials not set",
)
def test_add_project_member(login_page, projects_page):
    """Test adding a member to a project."""
    # Login
    username = os.getenv("IMMUTA_USERNAME")
    password = os.getenv("IMMUTA_PASSWORD")
    login_page.login(username, password)
    
    # Create project
    project = {
        "name": f"Test Project Members {int(time.time())}",
        "description": "Test project for adding members",
        "data_sources": ["Test Data Source"],
    }
    
    try:
        # Create project
        projects_page.create_project(project)
        
        # Add member
        result = projects_page.add_member(project["name"], username, "OWNER")
        assert result is True
    finally:
        # Clean up
        try:
            projects_page.delete_project(project["name"])
        except Exception as e:
            print(f"Failed to clean up project: {e}")
