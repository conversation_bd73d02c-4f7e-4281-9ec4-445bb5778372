"""Pytest configuration for web automation tests."""

import os
import pytest
from playwright.sync_api import sync_playwright

from immuta_toolkit.web.page_objects.login_page import LoginPage
from immuta_toolkit.web.page_objects.users_page import UsersPage
from immuta_toolkit.web.page_objects.data_sources_page import DataSourcesPage
from immuta_toolkit.web.page_objects.policies_page import PoliciesPage


@pytest.fixture(scope="session")
def playwright():
    """Fixture for Playwright instance."""
    with sync_playwright() as playwright:
        yield playwright


@pytest.fixture(scope="session")
def browser(playwright):
    """Fixture for browser instance."""
    browser = playwright.chromium.launch(headless=True)
    yield browser
    browser.close()


@pytest.fixture(scope="function")
def page(browser):
    """Fixture for page instance."""
    context = browser.new_context()
    page = context.new_page()
    yield page
    page.close()
    context.close()


@pytest.fixture(scope="session")
def base_url():
    """Fixture for base URL."""
    return os.getenv("IMMUTA_BASE_URL", "https://example.com")


@pytest.fixture(scope="function")
def login_page(page, base_url):
    """Fixture for login page."""
    return LoginPage(page, base_url)


@pytest.fixture(scope="function")
def users_page(page, base_url):
    """Fixture for users page."""
    return UsersPage(page, base_url)


@pytest.fixture(scope="function")
def data_sources_page(page, base_url):
    """Fixture for data sources page."""
    return DataSourcesPage(page, base_url)


@pytest.fixture(scope="function")
def policies_page(page, base_url):
    """Fixture for policies page."""
    return PoliciesPage(page, base_url)


@pytest.fixture(scope="function")
def projects_page(page, base_url):
    """Fixture for projects page."""
    return ProjectsPage(page, base_url)
