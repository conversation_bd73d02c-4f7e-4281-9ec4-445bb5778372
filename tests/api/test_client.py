"""Tests for the Immuta API client."""

import pytest
import responses
from unittest.mock import patch, MagicMock

from immuta_toolkit.api.client import ImmutaApiClient
from immuta_toolkit.api.exceptions import (
    ImmutaApiError,
    AuthenticationError,
    ResourceNotFoundError,
    ValidationError,
    RateLimitError,
    ServerError,
    CircuitBreakerError,
    TimeoutError,
    ConnectionError,
    ApiVersionError,
)


@pytest.fixture
def mock_client():
    """Create a mock API client."""
    return ImmutaApiClient(
        base_url="https://example.com",
        api_key="test_api_key",
        api_version="v1",
        timeout=1,
        max_retries=0,
        is_local=False,
    )


@pytest.fixture
def mock_local_client():
    """Create a mock local API client."""
    return ImmutaApiClient(
        base_url="https://example.com",
        api_key="test_api_key",
        api_version="v1",
        timeout=1,
        max_retries=0,
        is_local=True,
    )


class TestImmutaApiClient:
    """Tests for the ImmutaApiClient class."""

    def test_init(self):
        """Test client initialization."""
        client = ImmutaApiClient(
            base_url="https://example.com",
            api_key="test_api_key",
            api_version="v1",
        )
        assert client.base_url == "https://example.com"
        assert client.api_key == "test_api_key"
        assert client.api_version == "v1"
        assert client.is_local is False

    def test_init_with_invalid_version(self):
        """Test client initialization with invalid API version."""
        with pytest.raises(ApiVersionError):
            ImmutaApiClient(
                base_url="https://example.com",
                api_key="test_api_key",
                api_version="v2",
            )

    def test_init_local_mode(self):
        """Test client initialization in local mode."""
        client = ImmutaApiClient(
            base_url="https://example.com",
            api_key="test_api_key",
            api_version="v1",
            is_local=True,
        )
        assert client.is_local is True

    @responses.activate
    def test_get_request(self, mock_client):
        """Test GET request."""
        responses.add(
            responses.GET,
            "https://example.com/api/v1/test",
            json={"key": "value"},
            status=200,
        )

        response = mock_client.get("test")
        assert response == {"key": "value"}

    @responses.activate
    def test_post_request(self, mock_client):
        """Test POST request."""
        responses.add(
            responses.POST,
            "https://example.com/api/v1/test",
            json={"id": 1, "name": "Test"},
            status=201,
        )

        response = mock_client.post("test", json_data={"name": "Test"})
        assert response == {"id": 1, "name": "Test"}

    @responses.activate
    def test_put_request(self, mock_client):
        """Test PUT request."""
        responses.add(
            responses.PUT,
            "https://example.com/api/v1/test/1",
            json={"id": 1, "name": "Updated"},
            status=200,
        )

        response = mock_client.put("test/1", json_data={"name": "Updated"})
        assert response == {"id": 1, "name": "Updated"}

    @responses.activate
    def test_delete_request(self, mock_client):
        """Test DELETE request."""
        responses.add(
            responses.DELETE,
            "https://example.com/api/v1/test/1",
            status=204,
        )

        response = mock_client.delete("test/1")
        assert response == {}

    @responses.activate
    def test_patch_request(self, mock_client):
        """Test PATCH request."""
        responses.add(
            responses.PATCH,
            "https://example.com/api/v1/test/1",
            json={"id": 1, "name": "Patched"},
            status=200,
        )

        response = mock_client.patch("test/1", json_data={"name": "Patched"})
        assert response == {"id": 1, "name": "Patched"}

    @responses.activate
    def test_authentication_error(self, mock_client):
        """Test authentication error."""
        responses.add(
            responses.GET,
            "https://example.com/api/v1/test",
            json={"message": "Unauthorized"},
            status=401,
        )

        with pytest.raises(AuthenticationError) as excinfo:
            mock_client.get("test")
        assert "Unauthorized" in str(excinfo.value)

    @responses.activate
    def test_resource_not_found_error(self, mock_client):
        """Test resource not found error."""
        responses.add(
            responses.GET,
            "https://example.com/api/v1/test/999",
            json={"message": "Resource not found"},
            status=404,
        )

        with pytest.raises(ResourceNotFoundError) as excinfo:
            mock_client.get("test/999")
        assert "Resource not found" in str(excinfo.value)

    @responses.activate
    def test_validation_error(self, mock_client):
        """Test validation error."""
        responses.add(
            responses.POST,
            "https://example.com/api/v1/test",
            json={"message": "Validation failed"},
            status=422,
        )

        with pytest.raises(ValidationError) as excinfo:
            mock_client.post("test", json_data={"invalid": "data"})
        assert "Validation failed" in str(excinfo.value)

    @responses.activate
    def test_rate_limit_error(self, mock_client):
        """Test rate limit error."""
        responses.add(
            responses.GET,
            "https://example.com/api/v1/test",
            json={"message": "Rate limit exceeded"},
            status=429,
            headers={"Retry-After": "60"},
        )

        with pytest.raises(RateLimitError) as excinfo:
            mock_client.get("test")
        assert "Rate limit exceeded" in str(excinfo.value)

    @responses.activate
    def test_server_error(self, mock_client):
        """Test server error."""
        responses.add(
            responses.GET,
            "https://example.com/api/v1/test",
            json={"message": "Internal server error"},
            status=500,
        )

        with pytest.raises(ServerError) as excinfo:
            mock_client.get("test")
        assert "Internal server error" in str(excinfo.value)

    def test_local_mode(self, mock_local_client):
        """Test local mode."""
        response = mock_local_client.get("test")
        assert response == {}

    def test_circuit_breaker_state(self, mock_client):
        """Test circuit breaker state."""
        state = mock_client.get_circuit_breaker_state()
        assert state["name"] == "immuta-api"
        assert state["state"] == "closed"

    def test_reset_circuit_breaker(self, mock_client):
        """Test reset circuit breaker."""
        mock_client.reset_circuit_breaker()
        state = mock_client.get_circuit_breaker_state()
        assert state["state"] == "closed"

    def test_close(self, mock_client):
        """Test close."""
        mock_client.close()
        # No assertion needed, just make sure it doesn't raise an exception
