"""Retry utilities for the Immuta SRE Toolkit."""

import time
import random
import functools
from typing import Callable, Any, TypeVar, Optional, List, Dict, Union, Type, cast

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")


def retry(
    max_retries: int = 3,
    retry_delay: float = 1.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    max_delay: float = 30.0,
    exceptions_to_retry: Optional[List[Type[Exception]]] = None,
    exceptions_to_ignore: Optional[List[Type[Exception]]] = None,
    on_retry: Optional[Callable[[Exception, int, int], None]] = None,
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """Retry decorator for functions that might fail temporarily.

    Args:
        max_retries: Maximum number of retries.
        retry_delay: Initial delay between retries in seconds.
        backoff_factor: Factor to increase delay for each retry.
        jitter: Whether to add random jitter to delay.
        max_delay: Maximum delay between retries in seconds.
        exceptions_to_retry: List of exceptions to retry on. If None, retry on all exceptions.
        exceptions_to_ignore: List of exceptions to ignore (not retry).
        on_retry: Function to call on retry with (exception, attempt, max_retries) as arguments.

    Returns:
        Decorated function.
    """
    exceptions_to_retry = exceptions_to_retry or [Exception]
    exceptions_to_ignore = exceptions_to_ignore or []

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # Check if this exception should be ignored
                    if any(isinstance(e, exc_type) for exc_type in exceptions_to_ignore):
                        raise
                    
                    # Check if this exception should be retried
                    if not any(isinstance(e, exc_type) for exc_type in exceptions_to_retry):
                        raise
                    
                    last_exception = e
                    
                    # If this was the last attempt, re-raise the exception
                    if attempt >= max_retries:
                        logger.error(f"All {max_retries + 1} attempts failed: {str(e)}")
                        raise
                    
                    # Calculate delay with exponential backoff and optional jitter
                    delay = min(
                        retry_delay * (backoff_factor ** attempt),
                        max_delay,
                    )
                    
                    if jitter:
                        delay += random.random() * retry_delay
                    
                    # Call on_retry callback if provided
                    if on_retry:
                        on_retry(e, attempt + 1, max_retries + 1)
                    else:
                        logger.warning(
                            f"Attempt {attempt + 1}/{max_retries + 1} failed: {str(e)}. "
                            f"Retrying in {delay:.2f} seconds..."
                        )
                    
                    time.sleep(delay)
            
            # This should never happen, but makes the type checker happy
            assert last_exception is not None
            raise last_exception
        
        return wrapper
    
    return decorator


def with_retry(
    func: Callable[..., T],
    max_retries: int = 3,
    retry_delay: float = 1.0,
    backoff_factor: float = 2.0,
    jitter: bool = True,
    max_delay: float = 30.0,
    exceptions_to_retry: Optional[List[Type[Exception]]] = None,
    exceptions_to_ignore: Optional[List[Type[Exception]]] = None,
    on_retry: Optional[Callable[[Exception, int, int], None]] = None,
) -> Callable[..., T]:
    """Function version of the retry decorator.

    This is useful when you want to apply retry logic to a function
    without decorating it.

    Args:
        func: Function to retry.
        max_retries: Maximum number of retries.
        retry_delay: Initial delay between retries in seconds.
        backoff_factor: Factor to increase delay for each retry.
        jitter: Whether to add random jitter to delay.
        max_delay: Maximum delay between retries in seconds.
        exceptions_to_retry: List of exceptions to retry on. If None, retry on all exceptions.
        exceptions_to_ignore: List of exceptions to ignore (not retry).
        on_retry: Function to call on retry with (exception, attempt, max_retries) as arguments.

    Returns:
        Function with retry logic.
    """
    return retry(
        max_retries=max_retries,
        retry_delay=retry_delay,
        backoff_factor=backoff_factor,
        jitter=jitter,
        max_delay=max_delay,
        exceptions_to_retry=exceptions_to_retry,
        exceptions_to_ignore=exceptions_to_ignore,
        on_retry=on_retry,
    )(func)
