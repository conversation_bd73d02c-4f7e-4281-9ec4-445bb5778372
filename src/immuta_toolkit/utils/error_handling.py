"""Common error handling utilities for the Immuta SRE Toolkit."""

import os
import traceback
from typing import Dict, Optional, Any, List, Union, TypeVar, Type, cast
from datetime import datetime

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Type variable for exception classes
E = TypeVar("E", bound="BaseError")


class BaseError(Exception):
    """Base exception for all Immuta SRE Toolkit errors."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[Union[Dict[str, Any], str]] = None,
        request_info: Optional[Dict[str, Any]] = None,
        selector: Optional[str] = None,
        page_url: Optional[str] = None,
        screenshot_path: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            status_code: HTTP status code (for API errors).
            response_body: Response body (for API errors).
            request_info: Request information (for API errors).
            selector: CSS selector that caused the error (for web errors).
            page_url: URL of the page where the error occurred (for web errors).
            screenshot_path: Path to the screenshot taken when the error occurred (for web errors).
            context: Additional context information.
        """
        self.message = message
        self.status_code = status_code
        self.response_body = response_body
        self.request_info = request_info
        self.selector = selector
        self.page_url = page_url
        self.screenshot_path = screenshot_path
        self.context = context or {}
        super().__init__(self.message)

    def __str__(self) -> str:
        """Return string representation of the exception.

        Returns:
            String representation.
        """
        parts = [self.message]
        
        # API error information
        if self.status_code:
            parts.append(f"Status code: {self.status_code}")
        if self.response_body:
            parts.append(f"Response: {self.response_body}")
        if self.request_info:
            parts.append(f"Request: {self.request_info}")
            
        # Web error information
        if self.selector:
            parts.append(f"Selector: {self.selector}")
        if self.page_url:
            parts.append(f"URL: {self.page_url}")
        if self.screenshot_path:
            parts.append(f"Screenshot: {self.screenshot_path}")
            
        # Common information
        if self.context:
            parts.append(f"Context: {self.context}")
            
        return " | ".join(parts)


def take_screenshot(
    page: Any, 
    error_message: str, 
    screenshot_dir: Optional[str] = None
) -> str:
    """Take a screenshot and save it to a file.

    Args:
        page: Playwright page object.
        error_message: Error message to include in the filename.
        screenshot_dir: Directory to save the screenshot. If None, will use
            the current working directory.

    Returns:
        Path to the screenshot file.
    """
    # Create screenshots directory if it doesn't exist
    screenshots_dir = screenshot_dir or os.path.join(os.getcwd(), "screenshots")
    os.makedirs(screenshots_dir, exist_ok=True)

    # Generate filename with timestamp and error message
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    error_slug = error_message.lower().replace(" ", "_")[:50]
    filename = f"{timestamp}_{error_slug}.png"
    filepath = os.path.join(screenshots_dir, filename)

    # Take screenshot
    page.screenshot(path=filepath)
    logger.info(f"Screenshot saved to {filepath}")

    return filepath


def create_error_context(
    page: Any,
    error_message: str,
    selector: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    screenshot_dir: Optional[str] = None,
) -> Dict[str, Any]:
    """Create error context with screenshot and page information.

    Args:
        page: Playwright page object.
        error_message: Error message.
        selector: CSS selector that caused the error.
        context: Additional context information.
        screenshot_dir: Directory to save the screenshot. If None, will use
            the current working directory.

    Returns:
        Error context dictionary.
    """
    screenshot_path = take_screenshot(page, error_message, screenshot_dir)
    return {
        "selector": selector,
        "page_url": page.url,
        "screenshot_path": screenshot_path,
        "context": context or {},
    }


def handle_web_error(
    error: Exception,
    page: Any,
    error_message: str,
    error_class: Type[E],
    selector: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    screenshot_dir: Optional[str] = None,
) -> E:
    """Handle a web automation error.

    Args:
        error: Original exception.
        page: Playwright page object.
        error_message: Error message.
        error_class: Exception class to use.
        selector: CSS selector that caused the error.
        context: Additional context information.
        screenshot_dir: Directory to save the screenshot. If None, will use
            the current working directory.

    Returns:
        New exception with error context.
    """
    logger.error(f"{error_message}: {str(error)}")
    logger.debug(traceback.format_exc())
    
    # Take screenshot
    screenshot_path = take_screenshot(page, error_message, screenshot_dir)
    
    # Create new exception
    if isinstance(error, BaseError):
        # Update existing exception
        error.message = error_message
        error.selector = selector
        error.page_url = page.url
        error.screenshot_path = screenshot_path
        error.context = context or {}
        return cast(E, error)
    else:
        # Create new exception
        return error_class(
            message=error_message,
            selector=selector,
            page_url=page.url,
            screenshot_path=screenshot_path,
            context=context,
        )


def handle_api_error(
    error: Exception,
    error_message: str,
    error_class: Type[E],
    status_code: Optional[int] = None,
    response_body: Optional[Union[Dict[str, Any], str]] = None,
    request_info: Optional[Dict[str, Any]] = None,
    context: Optional[Dict[str, Any]] = None,
) -> E:
    """Handle an API error.

    Args:
        error: Original exception.
        error_message: Error message.
        error_class: Exception class to use.
        status_code: HTTP status code.
        response_body: Response body.
        request_info: Request information.
        context: Additional context information.

    Returns:
        New exception with error context.
    """
    logger.error(f"{error_message}: {str(error)}")
    logger.debug(traceback.format_exc())
    
    # Create new exception
    if isinstance(error, BaseError):
        # Update existing exception
        error.message = error_message
        error.status_code = status_code
        error.response_body = response_body
        error.request_info = request_info
        error.context = context or {}
        return cast(E, error)
    else:
        # Create new exception
        return error_class(
            message=error_message,
            status_code=status_code,
            response_body=response_body,
            request_info=request_info,
            context=context,
        )
