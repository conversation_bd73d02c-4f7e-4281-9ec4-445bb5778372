"""Storage utilities for the Immuta SRE Toolkit."""

import json
import os
import re
import shutil
import tempfile
from datetime import datetime
from typing import Dict, List, Optional, Union

from azure.storage.blob import (
    BlobServiceClient,
    ContentSettings,
)

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class StorageManager:
    """Storage manager for the Immuta SRE Toolkit.

    This class provides functionality for storing and retrieving files,
    including backups and configuration files.

    Attributes:
        is_local: Whether to use local storage instead of Azure Blob Storage.
        local_dir: Local directory for storage when is_local is True.
        connection_string: Azure Blob Storage connection string.
        container_name: Azure Blob Storage container name.
    """

    def __init__(
        self,
        is_local: bool = False,
        local_dir: str = None,
        connection_string: Optional[str] = None,
        container_name: str = "immuta-backups",
    ):
        """Initialize the storage manager.

        Args:
            is_local: Whether to use local storage instead of Azure Blob Storage.
            local_dir: Local directory for storage when is_local is True.
            connection_string: Azure Blob Storage connection string.
                Required when is_local is False.
            container_name: Azure Blob Storage container name.
        """
        # Use a secure temporary directory if none is provided
        if local_dir is None:
            local_dir = os.path.join(tempfile.gettempdir(), "immuta-backups")
        self.is_local = is_local
        self.local_dir = local_dir
        self.connection_string = connection_string
        self.container_name = container_name
        self.metadata_file = "metadata.jsonl"

        if is_local:
            os.makedirs(local_dir, exist_ok=True)
            logger.info(f"Using local storage at {local_dir}")
        else:
            if not connection_string:
                connection_string = os.getenv("BLOB_CONNECTION_STRING")
                if not connection_string:
                    raise ValueError(
                        "Connection string is required for Azure Blob Storage"
                    )
            self.blob_service_client = BlobServiceClient.from_connection_string(
                connection_string
            )
            # Ensure container exists
            try:
                self.blob_service_client.create_container(container_name)
                logger.info(f"Created container {container_name}")
            except Exception:
                # Container already exists
                logger.info(f"Using existing container {container_name}")

    def validate_metadata(self, metadata: Optional[Dict]) -> None:
        """Validate metadata keys and values.

        Args:
            metadata: Metadata dictionary to validate.

        Raises:
            ValueError: If metadata is invalid.
        """
        if not metadata:
            return

        for key, value in metadata.items():
            if not re.match(r"^[a-zA-Z0-9_-]+$", key):
                raise ValueError(
                    f"Invalid metadata key: {key}. Use alphanumeric, underscore, or hyphen."
                )
            if len(str(value)) > 1024:
                raise ValueError(f"Metadata value for {key} exceeds 1024 characters.")
            if not isinstance(value, str):
                raise ValueError(f"Metadata value for {key} must be a string.")

    def upload_file(
        self, file_path: str, blob_name: str, metadata: Optional[Dict] = None
    ) -> None:
        """Upload file to Blob Storage or local directory.

        Args:
            file_path: Path to the file to upload.
            blob_name: Name of the blob to create.
            metadata: Metadata to attach to the blob.

        Raises:
            FileNotFoundError: If the file does not exist.
            ValueError: If metadata is invalid.
        """
        self.validate_metadata(metadata)

        try:
            if self.is_local:
                shutil.copy(file_path, os.path.join(self.local_dir, blob_name))
                if metadata:
                    metadata_path = os.path.join(self.local_dir, self.metadata_file)
                    # Use JSONL format for metadata writes to reduce I/O overhead
                    with open(metadata_path, "a") as f:
                        entry = {
                            "blob_name": blob_name,
                            "timestamp": datetime.now().isoformat(),
                            **metadata,
                        }
                        f.write(json.dumps(entry) + "\n")
                logger.info(f"Uploaded {file_path} to {blob_name} (local)")
            else:
                blob_client = self.blob_service_client.get_blob_client(
                    container=self.container_name, blob=blob_name
                )
                with open(file_path, "rb") as data:
                    blob_client.upload_blob(
                        data,
                        overwrite=True,
                        metadata=metadata,
                        content_settings=ContentSettings(
                            content_type="application/octet-stream"
                        ),
                    )
                logger.info(f"Uploaded {file_path} to {blob_name} (Azure)")
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise
        except Exception as e:
            logger.error(f"Failed to upload file: {e}")
            raise

    def download_file(self, blob_name: str, file_path: str) -> None:
        """Download file from Blob Storage or local directory.

        Args:
            blob_name: Name of the blob to download.
            file_path: Path to save the downloaded file.

        Raises:
            FileNotFoundError: If the blob does not exist.
        """
        try:
            if self.is_local:
                shutil.copy(os.path.join(self.local_dir, blob_name), file_path)
                logger.info(f"Downloaded {blob_name} to {file_path} (local)")
            else:
                blob_client = self.blob_service_client.get_blob_client(
                    container=self.container_name, blob=blob_name
                )
                with open(file_path, "wb") as download_file:
                    download_file.write(blob_client.download_blob().readall())
                logger.info(f"Downloaded {blob_name} to {file_path} (Azure)")
        except FileNotFoundError:
            logger.error(f"Blob not found: {blob_name}")
            raise
        except Exception as e:
            logger.error(f"Failed to download file: {e}")
            raise

    def list_blobs(self, prefix: Optional[str] = None) -> List[Dict]:
        """List blobs in Blob Storage or local directory.

        Args:
            prefix: Prefix to filter blobs.

        Returns:
            List of blob metadata dictionaries.
        """
        try:
            if self.is_local:
                blobs = []
                for filename in os.listdir(self.local_dir):
                    if filename == self.metadata_file:
                        continue
                    if prefix and not filename.startswith(prefix):
                        continue
                    blob_path = os.path.join(self.local_dir, filename)
                    if os.path.isfile(blob_path):
                        # Get metadata from metadata file if available
                        metadata = {}
                        metadata_path = os.path.join(self.local_dir, self.metadata_file)
                        if os.path.exists(metadata_path):
                            with open(metadata_path, "r") as f:
                                for line in f:
                                    entry = json.loads(line.strip())
                                    if entry.get("blob_name") == filename:
                                        metadata = entry
                                        break
                        blobs.append(
                            {
                                "name": filename,
                                "size": os.path.getsize(blob_path),
                                "created": datetime.fromtimestamp(
                                    os.path.getctime(blob_path)
                                ).isoformat(),
                                "metadata": metadata,
                            }
                        )
                return blobs
            else:
                blobs = []
                container_client = self.blob_service_client.get_container_client(
                    self.container_name
                )
                for blob in container_client.list_blobs(name_starts_with=prefix):
                    blob_client = self.blob_service_client.get_blob_client(
                        container=self.container_name, blob=blob.name
                    )
                    properties = blob_client.get_blob_properties()
                    blobs.append(
                        {
                            "name": blob.name,
                            "size": properties.size,
                            "created": properties.creation_time.isoformat(),
                            "metadata": properties.metadata,
                        }
                    )
                return blobs
        except Exception as e:
            logger.error(f"Failed to list blobs: {e}")
            raise


class LocalStorageManager(StorageManager):
    """Local storage manager for the Immuta SRE Toolkit.

    This class provides a simplified interface for local storage operations.
    It's a convenience wrapper around StorageManager with is_local=True.

    Attributes:
        local_dir: Local directory for storage.
    """

    def __init__(self, local_dir: str = None):
        """Initialize the local storage manager.

        Args:
            local_dir: Local directory for storage. If None, a secure temporary directory will be used.
        """
        # Use a secure temporary directory if none is provided
        if local_dir is None:
            local_dir = os.path.join(tempfile.gettempdir(), "immuta-backups")

        super().__init__(is_local=True, local_dir=local_dir)
