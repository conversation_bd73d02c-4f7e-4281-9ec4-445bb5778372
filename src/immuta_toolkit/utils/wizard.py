"""Wizard-style interface for complex operations."""

import os
import sys
import json
import time
from typing import Dict, Any, List, Optional, Callable, Union, Tuple

import click
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.table import Table

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


class WizardStep:
    """A step in a wizard.
    
    Attributes:
        name: Step name.
        title: Step title.
        description: Step description.
        action: Action function.
        next_step: Next step name.
        previous_step: Previous step name.
        skip_condition: Condition function to skip this step.
    """
    
    def __init__(
        self,
        name: str,
        title: str,
        description: str,
        action: Callable[[Dict[str, Any]], Tuple[bool, Optional[str]]],
        next_step: Optional[str] = None,
        previous_step: Optional[str] = None,
        skip_condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
    ):
        """Initialize a wizard step.
        
        Args:
            name: Step name.
            title: Step title.
            description: Step description.
            action: Action function that takes context and returns (success, next_step).
            next_step: Next step name.
            previous_step: Previous step name.
            skip_condition: Condition function to skip this step.
        """
        self.name = name
        self.title = title
        self.description = description
        self.action = action
        self.next_step = next_step
        self.previous_step = previous_step
        self.skip_condition = skip_condition
    
    def should_skip(self, context: Dict[str, Any]) -> bool:
        """Check if this step should be skipped.
        
        Args:
            context: Wizard context.
            
        Returns:
            True if this step should be skipped, False otherwise.
        """
        if self.skip_condition is None:
            return False
        
        return self.skip_condition(context)
    
    def execute(self, context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Execute this step.
        
        Args:
            context: Wizard context.
            
        Returns:
            Tuple of (success, next_step).
        """
        # Display step information
        console.print(
            Panel(
                f"{self.description}",
                title=f"[bold cyan]{self.title}[/bold cyan]",
                subtitle=f"Step: {self.name}",
            )
        )
        
        # Execute action
        try:
            success, next_step = self.action(context)
            
            # Use default next step if not specified
            if success and next_step is None:
                next_step = self.next_step
            
            return success, next_step
        except Exception as e:
            logger.error(f"Error in step {self.name}: {e}", exc_info=True)
            console.print(f"[red]Error: {e}[/red]")
            
            # Ask if user wants to retry
            if Confirm.ask("Do you want to retry this step?"):
                return self.execute(context)
            
            return False, None


class Wizard:
    """Wizard-style interface for complex operations.
    
    Attributes:
        name: Wizard name.
        title: Wizard title.
        description: Wizard description.
        steps: Dictionary of steps.
        start_step: Start step name.
        context: Wizard context.
    """
    
    def __init__(
        self,
        name: str,
        title: str,
        description: str,
        steps: List[WizardStep],
        start_step: str,
    ):
        """Initialize a wizard.
        
        Args:
            name: Wizard name.
            title: Wizard title.
            description: Wizard description.
            steps: List of steps.
            start_step: Start step name.
        """
        self.name = name
        self.title = title
        self.description = description
        self.steps = {step.name: step for step in steps}
        self.start_step = start_step
        self.context: Dict[str, Any] = {}
    
    def run(self, initial_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Run the wizard.
        
        Args:
            initial_context: Initial context.
            
        Returns:
            Final context.
        """
        # Initialize context
        self.context = initial_context or {}
        
        # Display wizard information
        console.print(
            Panel(
                f"{self.description}",
                title=f"[bold green]{self.title}[/bold green]",
                subtitle=f"Wizard: {self.name}",
            )
        )
        
        # Start with the first step
        current_step_name = self.start_step
        
        while current_step_name:
            # Get current step
            current_step = self.steps.get(current_step_name)
            if not current_step:
                console.print(f"[red]Error: Step '{current_step_name}' not found[/red]")
                break
            
            # Check if step should be skipped
            if current_step.should_skip(self.context):
                logger.debug(f"Skipping step {current_step_name}")
                current_step_name = current_step.next_step
                continue
            
            # Execute step
            success, next_step_name = current_step.execute(self.context)
            
            if success:
                # Move to next step
                current_step_name = next_step_name
            else:
                # Ask if user wants to go back or exit
                choices = []
                
                if current_step.previous_step:
                    choices.append(("Back", current_step.previous_step))
                
                choices.append(("Exit", None))
                
                if choices:
                    choice = Prompt.ask(
                        "What would you like to do?",
                        choices=[c[0] for c in choices],
                    )
                    
                    for c, step_name in choices:
                        if c == choice:
                            current_step_name = step_name
                            break
                else:
                    current_step_name = None
        
        # Display completion message
        console.print(
            Panel(
                "Wizard completed successfully!",
                title=f"[bold green]{self.title}[/bold green]",
                subtitle=f"Wizard: {self.name}",
            )
        )
        
        return self.context


def create_input_step(
    name: str,
    title: str,
    description: str,
    prompts: List[Dict[str, Any]],
    next_step: Optional[str] = None,
    previous_step: Optional[str] = None,
    skip_condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
) -> WizardStep:
    """Create an input step.
    
    Args:
        name: Step name.
        title: Step title.
        description: Step description.
        prompts: List of prompt dictionaries.
        next_step: Next step name.
        previous_step: Previous step name.
        skip_condition: Condition function to skip this step.
        
    Returns:
        Wizard step.
    """
    def action(context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Input step action.
        
        Args:
            context: Wizard context.
            
        Returns:
            Tuple of (success, next_step).
        """
        for prompt in prompts:
            key = prompt["key"]
            message = prompt["message"]
            default = prompt.get("default")
            choices = prompt.get("choices")
            required = prompt.get("required", False)
            
            # Get default from context if available
            if default is None and key in context:
                default = context[key]
            
            # Convert default to string
            if default is not None:
                default = str(default)
            
            # Get input
            if choices:
                value = Prompt.ask(
                    message,
                    choices=choices,
                    default=default,
                )
            else:
                value = Prompt.ask(
                    message,
                    default=default,
                )
            
            # Validate required
            if required and not value:
                console.print(f"[red]Error: {message} is required[/red]")
                return False, None
            
            # Store in context
            context[key] = value
        
        return True, None
    
    return WizardStep(
        name=name,
        title=title,
        description=description,
        action=action,
        next_step=next_step,
        previous_step=previous_step,
        skip_condition=skip_condition,
    )


def create_confirmation_step(
    name: str,
    title: str,
    description: str,
    message: str,
    next_step: Optional[str] = None,
    previous_step: Optional[str] = None,
    skip_condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
) -> WizardStep:
    """Create a confirmation step.
    
    Args:
        name: Step name.
        title: Step title.
        description: Step description.
        message: Confirmation message.
        next_step: Next step name.
        previous_step: Previous step name.
        skip_condition: Condition function to skip this step.
        
    Returns:
        Wizard step.
    """
    def action(context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Confirmation step action.
        
        Args:
            context: Wizard context.
            
        Returns:
            Tuple of (success, next_step).
        """
        confirmed = Confirm.ask(message)
        context["confirmed"] = confirmed
        
        return confirmed, None
    
    return WizardStep(
        name=name,
        title=title,
        description=description,
        action=action,
        next_step=next_step,
        previous_step=previous_step,
        skip_condition=skip_condition,
    )


def create_summary_step(
    name: str,
    title: str,
    description: str,
    fields: List[Dict[str, Any]],
    next_step: Optional[str] = None,
    previous_step: Optional[str] = None,
    skip_condition: Optional[Callable[[Dict[str, Any]], bool]] = None,
) -> WizardStep:
    """Create a summary step.
    
    Args:
        name: Step name.
        title: Step title.
        description: Step description.
        fields: List of field dictionaries.
        next_step: Next step name.
        previous_step: Previous step name.
        skip_condition: Condition function to skip this step.
        
    Returns:
        Wizard step.
    """
    def action(context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Summary step action.
        
        Args:
            context: Wizard context.
            
        Returns:
            Tuple of (success, next_step).
        """
        # Create table
        table = Table(title="Summary")
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="green")
        
        # Add rows
        for field in fields:
            key = field["key"]
            label = field.get("label", key)
            
            if key in context:
                value = context[key]
                table.add_row(label, str(value))
        
        # Display table
        console.print(table)
        
        # Ask for confirmation
        confirmed = Confirm.ask("Is this information correct?")
        
        return confirmed, None
    
    return WizardStep(
        name=name,
        title=title,
        description=description,
        action=action,
        next_step=next_step,
        previous_step=previous_step,
        skip_condition=skip_condition,
    )
