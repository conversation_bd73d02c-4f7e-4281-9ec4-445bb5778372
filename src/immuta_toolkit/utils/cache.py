"""Enhanced caching mechanism with TTL, LRU, and memory management."""

import time
import threading
import functools
import json
import os
import pickle
from typing import Dict, Any, Optional, TypeVar, Generic, Callable, List, Tuple, Union
from dataclasses import dataclass
from pathlib import Path

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar('T')


@dataclass
class CacheEntry(Generic[T]):
    """Cache entry with value and expiration time.

    Attributes:
        value: Cached value.
        ttl_seconds: Time-to-live in seconds.
        created_at: Creation timestamp.
        last_accessed: Last access timestamp.
        access_count: Number of times the entry has been accessed.
    """

    value: T
    ttl_seconds: int
    created_at: float = time.time()
    last_accessed: float = time.time()
    access_count: int = 0

    def is_expired(self) -> bool:
        """Check if the entry is expired.

        Returns:
            True if the entry is expired, False otherwise.
        """
        return time.time() > self.created_at + self.ttl_seconds

    def access(self) -> None:
        """Update last access timestamp and access count."""
        self.last_accessed = time.time()
        self.access_count += 1


class Cache:
    """Enhanced cache with TTL, LRU, and memory management.

    This class provides a thread-safe cache with time-to-live (TTL) support,
    least recently used (LRU) eviction, and memory management.

    Attributes:
        name: Cache name.
        default_ttl_seconds: Default time-to-live in seconds.
        max_size: Maximum number of entries in the cache.
        entries: Dictionary of cache entries.
        lock: Thread lock for thread safety.
        stats: Cache statistics.
    """

    def __init__(
        self,
        name: str,
        default_ttl_seconds: int = 300,
        max_size: int = 1000,
        persistent: bool = False,
        cache_dir: Optional[str] = None,
    ):
        """Initialize the cache.

        Args:
            name: Cache name.
            default_ttl_seconds: Default time-to-live in seconds.
            max_size: Maximum number of entries in the cache.
            persistent: Whether to persist the cache to disk.
            cache_dir: Directory for persistent cache files.
        """
        self.name = name
        self.default_ttl_seconds = default_ttl_seconds
        self.max_size = max_size
        self.persistent = persistent
        self.cache_dir = cache_dir or os.path.join(os.path.expanduser("~"), ".immuta_toolkit", "cache")
        self.entries: Dict[str, CacheEntry[Any]] = {}
        self.lock = threading.Lock()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "expirations": 0,
        }

        # Create cache directory if persistent
        if self.persistent:
            os.makedirs(self.cache_dir, exist_ok=True)
            self._load_from_disk()

    def get(self, key: str) -> Optional[Any]:
        """Get a value from the cache.

        Args:
            key: Cache key.

        Returns:
            Cached value or None if not found or expired.
        """
        with self.lock:
            entry = self.entries.get(key)
            if entry and not entry.is_expired():
                # Update access information
                entry.access()
                self.stats["hits"] += 1
                logger.debug(f"Cache hit for {key} in {self.name}")
                return entry.value

            if entry:
                # Entry exists but is expired
                self.stats["expirations"] += 1
                logger.debug(f"Cache expired for {key} in {self.name}")
                del self.entries[key]
            else:
                logger.debug(f"Cache miss for {key} in {self.name}")

            self.stats["misses"] += 1
            return None

    def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None,
    ) -> None:
        """Set a value in the cache.

        Args:
            key: Cache key.
            value: Value to cache.
            ttl_seconds: Time-to-live in seconds. If None, default TTL is used.
        """
        ttl = ttl_seconds if ttl_seconds is not None else self.default_ttl_seconds

        with self.lock:
            # Check if we need to evict entries
            if len(self.entries) >= self.max_size and key not in self.entries:
                self._evict_entries()

            # Add or update entry
            self.entries[key] = CacheEntry(value, ttl)
            logger.debug(f"Cached {key} in {self.name} with TTL {ttl} seconds")

            # Persist to disk if enabled
            if self.persistent:
                self._save_to_disk()

    def delete(self, key: str) -> bool:
        """Delete a value from the cache.

        Args:
            key: Cache key.

        Returns:
            True if the key was found and deleted, False otherwise.
        """
        with self.lock:
            if key in self.entries:
                del self.entries[key]
                logger.debug(f"Deleted {key} from {self.name} cache")

                # Persist to disk if enabled
                if self.persistent:
                    self._save_to_disk()

                return True
            return False

    def clear(self) -> None:
        """Clear all entries from the cache."""
        with self.lock:
            self.entries.clear()
            logger.debug(f"Cleared {self.name} cache")

            # Persist to disk if enabled
            if self.persistent:
                self._save_to_disk()

    def get_or_set(
        self,
        key: str,
        value_func: Callable[[], T],
        ttl_seconds: Optional[int] = None,
    ) -> T:
        """Get a value from the cache or set it if not found.

        Args:
            key: Cache key.
            value_func: Function to call to get the value if not in cache.
            ttl_seconds: Time-to-live in seconds. If None, default TTL is used.

        Returns:
            Cached value or new value from value_func.
        """
        # First check if the value is in the cache
        value = self.get(key)
        if value is not None:
            return value

        # Value not in cache, get it from value_func
        value = value_func()

        # Cache the value
        self.set(key, value, ttl_seconds)

        return value

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics.

        Returns:
            Dictionary of cache statistics.
        """
        with self.lock:
            stats = {
                "name": self.name,
                "size": len(self.entries),
                "max_size": self.max_size,
                "default_ttl_seconds": self.default_ttl_seconds,
                "persistent": self.persistent,
                **self.stats,
            }

            # Calculate hit ratio
            total = stats["hits"] + stats["misses"]
            stats["hit_ratio"] = stats["hits"] / total if total > 0 else 0

            return stats

    def _evict_entries(self, count: int = 1) -> None:
        """Evict entries from the cache using LRU policy.

        Args:
            count: Number of entries to evict.
        """
        if not self.entries:
            return

        # Sort entries by last accessed time
        sorted_entries = sorted(
            self.entries.items(),
            key=lambda x: x[1].last_accessed,
        )

        # Evict oldest entries
        for i in range(min(count, len(sorted_entries))):
            key, _ = sorted_entries[i]
            del self.entries[key]
            self.stats["evictions"] += 1
            logger.debug(f"Evicted {key} from {self.name} cache (LRU)")

    def _save_to_disk(self) -> None:
        """Save cache to disk."""
        try:
            cache_file = os.path.join(self.cache_dir, f"{self.name}.cache")
            with open(cache_file, "wb") as f:
                pickle.dump(self.entries, f)
            logger.debug(f"Saved {self.name} cache to disk")
        except Exception as e:
            logger.error(f"Failed to save {self.name} cache to disk: {e}")

    def _load_from_disk(self) -> None:
        """Load cache from disk."""
        try:
            cache_file = os.path.join(self.cache_dir, f"{self.name}.cache")
            if os.path.exists(cache_file):
                with open(cache_file, "rb") as f:
                    self.entries = pickle.load(f)
                logger.debug(f"Loaded {self.name} cache from disk")

                # Remove expired entries
                self._remove_expired_entries()
        except Exception as e:
            logger.error(f"Failed to load {self.name} cache from disk: {e}")

    def _remove_expired_entries(self) -> None:
        """Remove expired entries from the cache."""
        with self.lock:
            expired_keys = [
                key for key, entry in self.entries.items() if entry.is_expired()
            ]

            for key in expired_keys:
                del self.entries[key]
                self.stats["expirations"] += 1
                logger.debug(f"Removed expired entry {key} from {self.name} cache")

    def keys(self) -> List[str]:
        """Get all keys in the cache.

        Returns:
            List of keys.
        """
        with self.lock:
            return list(self.entries.keys())

    def items(self) -> List[Tuple[str, Any]]:
        """Get all items in the cache.

        Returns:
            List of (key, value) tuples.
        """
        with self.lock:
            return [(key, entry.value) for key, entry in self.entries.items()]

    def cleanup(self) -> int:
        """Remove expired entries from the cache.

        Returns:
            Number of entries removed.
        """
        with self.lock:
            expired_keys = [
                key for key, entry in self.entries.items() if entry.is_expired()
            ]

            for key in expired_keys:
                del self.entries[key]
                self.stats["expirations"] += 1
                logger.debug(f"Removed expired entry {key} from {self.name} cache")

            return len(expired_keys)


class CacheManager:
    """Manager for multiple caches.

    This class provides a centralized manager for multiple caches.

    Attributes:
        caches: Dictionary of caches.
        lock: Thread lock for thread safety.
    """

    def __init__(self):
        """Initialize the cache manager."""
        self.caches: Dict[str, Cache] = {}
        self.lock = threading.Lock()

    def get_cache(
        self,
        name: str,
        default_ttl_seconds: int = 300,
        max_size: int = 1000,
        persistent: bool = False,
        cache_dir: Optional[str] = None,
    ) -> Cache:
        """Get a cache by name.

        Args:
            name: Cache name.
            default_ttl_seconds: Default time-to-live in seconds for new caches.
            max_size: Maximum number of entries in the cache.
            persistent: Whether to persist the cache to disk.
            cache_dir: Directory for persistent cache files.

        Returns:
            Cache instance.
        """
        with self.lock:
            if name not in self.caches:
                self.caches[name] = Cache(
                    name=name,
                    default_ttl_seconds=default_ttl_seconds,
                    max_size=max_size,
                    persistent=persistent,
                    cache_dir=cache_dir,
                )
            return self.caches[name]

    def clear_all(self) -> None:
        """Clear all caches."""
        with self.lock:
            for cache in self.caches.values():
                cache.clear()

    def get_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all caches.

        Returns:
            Dictionary of cache statistics.
        """
        with self.lock:
            return {name: cache.get_stats() for name, cache in self.caches.items()}


# Global cache manager instance
cache_manager = CacheManager()


def get_cache(
    name: str,
    default_ttl_seconds: int = 300,
    max_size: int = 1000,
    persistent: bool = False,
    cache_dir: Optional[str] = None,
) -> Cache:
    """Get a cache by name.

    Args:
        name: Cache name.
        default_ttl_seconds: Default time-to-live in seconds for new caches.
        max_size: Maximum number of entries in the cache.
        persistent: Whether to persist the cache to disk.
        cache_dir: Directory for persistent cache files.

    Returns:
        Cache instance.
    """
    return cache_manager.get_cache(
        name=name,
        default_ttl_seconds=default_ttl_seconds,
        max_size=max_size,
        persistent=persistent,
        cache_dir=cache_dir,
    )


def cached(
    ttl_seconds: int = 300,
    cache_name: Optional[str] = None,
    key_prefix: str = "",
):
    """Decorator to cache function results.

    Args:
        ttl_seconds: Time-to-live in seconds.
        cache_name: Name of the cache to use. If None, function name is used.
        key_prefix: Prefix for cache keys.

    Returns:
        Decorated function.
    """
    def decorator(func):
        # Use function name as cache name if not provided
        nonlocal cache_name
        if cache_name is None:
            cache_name = func.__name__

        # Get cache
        cache = get_cache(cache_name, default_ttl_seconds=ttl_seconds)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key_parts = [key_prefix, func.__name__]

            # Add args to key
            for arg in args:
                key_parts.append(str(arg))

            # Add kwargs to key (sorted for consistency)
            for k, v in sorted(kwargs.items()):
                key_parts.append(f"{k}={v}")

            # Join key parts
            key = ":".join(key_parts)

            # Get or set value in cache
            return cache.get_or_set(
                key=key,
                value_func=lambda: func(*args, **kwargs),
                ttl_seconds=ttl_seconds,
            )

        return wrapper

    return decorator
