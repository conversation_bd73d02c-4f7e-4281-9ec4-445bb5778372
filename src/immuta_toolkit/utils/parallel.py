"""Parallel processing utilities for batch operations."""

import time
import threading
import concurrent.futures
from typing import List, Callable, TypeVar, Any, Dict, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar('T')
R = TypeVar('R')


class ParallelProcessor:
    """Parallel processor for batch operations.
    
    This class provides utilities for processing batch operations in parallel
    using thread pools.
    
    Attributes:
        max_workers: Maximum number of worker threads.
        timeout: Timeout for operations in seconds.
    """
    
    def __init__(self, max_workers: int = 5, timeout: int = 60):
        """Initialize the parallel processor.
        
        Args:
            max_workers: Maximum number of worker threads.
            timeout: Timeout for operations in seconds.
        """
        self.max_workers = max_workers
        self.timeout = timeout
    
    def process(
        self,
        items: List[T],
        operation: Callable[[T], R],
        max_workers: Optional[int] = None,
        timeout: Optional[int] = None,
        show_progress: bool = False,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[R], List[Tuple[T, Exception]]]:
        """Process items in parallel.
        
        Args:
            items: List of items to process.
            operation: Function to apply to each item.
            max_workers: Maximum number of worker threads.
            timeout: Timeout for operations in seconds.
            show_progress: Whether to show progress.
            progress_callback: Callback function for progress updates.
            
        Returns:
            Tuple of (successful results, failed items with exceptions).
        """
        max_workers = max_workers or self.max_workers
        timeout = timeout or self.timeout
        
        # Adjust max_workers based on number of items
        max_workers = min(max_workers, len(items))
        
        # Initialize results
        results: List[R] = []
        failures: List[Tuple[T, Exception]] = []
        
        # Initialize progress tracking
        total_items = len(items)
        processed_items = 0
        
        # Process items in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_item = {
                executor.submit(operation, item): item for item in items
            }
            
            # Process results as they complete
            for future in as_completed(future_to_item):
                item = future_to_item[future]
                try:
                    result = future.result(timeout=timeout)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error processing item {item}: {e}")
                    failures.append((item, e))
                
                # Update progress
                processed_items += 1
                if show_progress and progress_callback:
                    progress_callback(processed_items, total_items)
        
        return results, failures
    
    def process_with_retry(
        self,
        items: List[T],
        operation: Callable[[T], R],
        max_workers: Optional[int] = None,
        timeout: Optional[int] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        show_progress: bool = False,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[R], List[Tuple[T, Exception]]]:
        """Process items in parallel with retry mechanism.
        
        Args:
            items: List of items to process.
            operation: Function to apply to each item.
            max_workers: Maximum number of worker threads.
            timeout: Timeout for operations in seconds.
            max_retries: Maximum number of retries for failed operations.
            retry_delay: Delay between retries in seconds.
            show_progress: Whether to show progress.
            progress_callback: Callback function for progress updates.
            
        Returns:
            Tuple of (successful results, failed items with exceptions).
        """
        max_workers = max_workers or self.max_workers
        timeout = timeout or self.timeout
        
        # Define retry wrapper
        def retry_operation(item: T) -> R:
            """Retry operation with exponential backoff."""
            retries = 0
            last_exception = None
            
            while retries <= max_retries:
                try:
                    return operation(item)
                except Exception as e:
                    last_exception = e
                    retries += 1
                    
                    if retries <= max_retries:
                        # Calculate exponential backoff delay
                        delay = retry_delay * (2 ** (retries - 1))
                        logger.warning(
                            f"Retry {retries}/{max_retries} for item {item} "
                            f"after {delay:.2f}s delay. Error: {e}"
                        )
                        time.sleep(delay)
                    else:
                        logger.error(
                            f"Failed to process item {item} after {max_retries} "
                            f"retries. Error: {e}"
                        )
            
            # If we get here, all retries failed
            if last_exception:
                raise last_exception
            
            # This should never happen
            raise RuntimeError("Unexpected error in retry_operation")
        
        # Process items with retry
        return self.process(
            items=items,
            operation=retry_operation,
            max_workers=max_workers,
            timeout=timeout,
            show_progress=show_progress,
            progress_callback=progress_callback,
        )
    
    def process_batched(
        self,
        items: List[T],
        operation: Callable[[List[T]], List[R]],
        batch_size: int = 10,
        max_workers: Optional[int] = None,
        timeout: Optional[int] = None,
        show_progress: bool = False,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> Tuple[List[R], List[Tuple[List[T], Exception]]]:
        """Process items in batches in parallel.
        
        Args:
            items: List of items to process.
            operation: Function to apply to each batch of items.
            batch_size: Size of each batch.
            max_workers: Maximum number of worker threads.
            timeout: Timeout for operations in seconds.
            show_progress: Whether to show progress.
            progress_callback: Callback function for progress updates.
            
        Returns:
            Tuple of (successful results, failed batches with exceptions).
        """
        max_workers = max_workers or self.max_workers
        timeout = timeout or self.timeout
        
        # Create batches
        batches = [
            items[i:i + batch_size]
            for i in range(0, len(items), batch_size)
        ]
        
        # Adjust max_workers based on number of batches
        max_workers = min(max_workers, len(batches))
        
        # Initialize results
        results: List[R] = []
        failures: List[Tuple[List[T], Exception]] = []
        
        # Initialize progress tracking
        total_batches = len(batches)
        processed_batches = 0
        
        # Process batches in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_batch = {
                executor.submit(operation, batch): batch for batch in batches
            }
            
            # Process results as they complete
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    batch_results = future.result(timeout=timeout)
                    results.extend(batch_results)
                except Exception as e:
                    logger.error(f"Error processing batch: {e}")
                    failures.append((batch, e))
                
                # Update progress
                processed_batches += 1
                if show_progress and progress_callback:
                    progress_callback(processed_batches, total_batches)
        
        return results, failures
