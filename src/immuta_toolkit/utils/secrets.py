"""Secrets management utilities for the Immuta SRE Toolkit."""

import os
import time
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any, List
import threading

from azure.identity import DefaultAzureCredential, ClientSecretCredential
from azure.keyvault.secrets import SecretClient

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class SecretsManager(ABC):
    """Abstract base class for secrets management."""

    @abstractmethod
    def get_secret(self, secret_name: str, default: Optional[str] = None) -> str:
        """Get a secret by name.

        Args:
            secret_name: Name of the secret.
            default: Default value to return if the secret is not found.

        Returns:
            Secret value or default if provided and secret not found.

        Raises:
            ValueError: If the secret is not found and no default is provided.
        """
        pass

    @abstractmethod
    def set_secret(self, secret_name: str, secret_value: str) -> None:
        """Set a secret.

        Args:
            secret_name: Name of the secret.
            secret_value: Value of the secret.

        Raises:
            ValueError: If the secret cannot be set.
        """
        pass

    @abstractmethod
    def list_secrets(self) -> List[str]:
        """List available secrets.

        Returns:
            List of secret names.
        """
        pass

    @abstractmethod
    def delete_secret(self, secret_name: str) -> None:
        """Delete a secret.

        Args:
            secret_name: Name of the secret.

        Raises:
            ValueError: If the secret cannot be deleted.
        """
        pass


class EnvironmentSecretsManager(SecretsManager):
    """Secrets manager that uses environment variables."""

    def get_secret(self, secret_name: str, default: Optional[str] = None) -> str:
        """Get a secret from environment variables.

        Args:
            secret_name: Name of the secret (environment variable).
            default: Default value to return if the secret is not found.

        Returns:
            Secret value or default if provided and secret not found.

        Raises:
            ValueError: If the secret is not found and no default is provided.
        """
        value = os.getenv(secret_name)
        if value is None:
            if default is not None:
                return default
            raise ValueError(f"Secret {secret_name} not found in environment variables")
        return value

    def set_secret(self, secret_name: str, secret_value: str) -> None:
        """Set a secret in environment variables.

        Args:
            secret_name: Name of the secret (environment variable).
            secret_value: Value of the secret.
        """
        os.environ[secret_name] = secret_value
        logger.info(f"Set environment variable {secret_name}")

    def list_secrets(self) -> List[str]:
        """List available secrets in environment variables.

        Returns:
            List of environment variable names.
        """
        # This returns all environment variables, which may not be ideal
        # for security reasons. Consider filtering to a specific prefix.
        return list(os.environ.keys())

    def delete_secret(self, secret_name: str) -> None:
        """Delete a secret from environment variables.

        Args:
            secret_name: Name of the secret (environment variable).

        Raises:
            ValueError: If the secret is not found.
        """
        if secret_name not in os.environ:
            raise ValueError(f"Secret {secret_name} not found in environment variables")
        del os.environ[secret_name]
        logger.info(f"Deleted environment variable {secret_name}")


class AzureKeyVaultSecretsManager(SecretsManager):
    """Secrets manager that uses Azure Key Vault."""

    def __init__(
        self,
        vault_url: str,
        credential: Optional[Any] = None,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        tenant_id: Optional[str] = None,
        cache_ttl_seconds: int = 300,
    ):
        """Initialize the Azure Key Vault secrets manager.

        Args:
            vault_url: URL of the Azure Key Vault.
            credential: Azure credential object. If None, DefaultAzureCredential is used.
            client_id: Azure client ID (for ClientSecretCredential).
            client_secret: Azure client secret (for ClientSecretCredential).
            tenant_id: Azure tenant ID (for ClientSecretCredential).
            cache_ttl_seconds: Time-to-live for cached secrets in seconds.
        """
        self.vault_url = vault_url

        # Use provided credential or create one
        if credential:
            self.credential = credential
        elif all([client_id, client_secret, tenant_id]):
            self.credential = ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret,
            )
        else:
            # Use DefaultAzureCredential which tries multiple authentication methods
            self.credential = DefaultAzureCredential()

        # Initialize the secret client
        self.client = SecretClient(vault_url=vault_url, credential=self.credential)

        # Initialize cache
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl_seconds = cache_ttl_seconds
        self.cache_lock = threading.Lock()

        logger.info(f"Initialized Azure Key Vault secrets manager for {vault_url}")

    def get_secret(self, secret_name: str, default: Optional[str] = None) -> str:
        """Get a secret from Azure Key Vault.

        Args:
            secret_name: Name of the secret.
            default: Default value to return if the secret is not found.

        Returns:
            Secret value or default if provided and secret not found.

        Raises:
            ValueError: If the secret is not found and no default is provided.
        """
        # Check cache first
        with self.cache_lock:
            if secret_name in self.cache:
                cache_entry = self.cache[secret_name]
                if time.time() - cache_entry["timestamp"] < self.cache_ttl_seconds:
                    logger.debug(f"Using cached secret {secret_name}")
                    return cache_entry["value"]

        try:
            # Get secret from Key Vault
            secret = self.client.get_secret(secret_name)

            # Update cache
            with self.cache_lock:
                self.cache[secret_name] = {
                    "value": secret.value,
                    "timestamp": time.time(),
                }

            return secret.value
        except Exception as e:
            logger.error(f"Failed to get secret {secret_name}: {e}")
            if default is not None:
                return default
            raise ValueError(f"Secret {secret_name} not found in Azure Key Vault")

    def set_secret(self, secret_name: str, secret_value: str) -> None:
        """Set a secret in Azure Key Vault.

        Args:
            secret_name: Name of the secret.
            secret_value: Value of the secret.

        Raises:
            ValueError: If the secret cannot be set.
        """
        try:
            self.client.set_secret(secret_name, secret_value)

            # Update cache
            with self.cache_lock:
                self.cache[secret_name] = {
                    "value": secret_value,
                    "timestamp": time.time(),
                }

            logger.info(f"Set secret {secret_name} in Azure Key Vault")
        except Exception as e:
            logger.error(f"Failed to set secret {secret_name}: {e}")
            raise ValueError(
                f"Failed to set secret {secret_name} in Azure Key Vault: {e}"
            )

    def list_secrets(self) -> List[str]:
        """List available secrets in Azure Key Vault.

        Returns:
            List of secret names.
        """
        try:
            return [secret.name for secret in self.client.list_properties_of_secrets()]
        except Exception as e:
            logger.error(f"Failed to list secrets: {e}")
            return []

    def delete_secret(self, secret_name: str) -> None:
        """Delete a secret from Azure Key Vault.

        Args:
            secret_name: Name of the secret.

        Raises:
            ValueError: If the secret cannot be deleted.
        """
        try:
            self.client.begin_delete_secret(secret_name)

            # Remove from cache
            with self.cache_lock:
                if secret_name in self.cache:
                    del self.cache[secret_name]

            logger.info(f"Deleted secret {secret_name} from Azure Key Vault")
        except Exception as e:
            logger.error(f"Failed to delete secret {secret_name}: {e}")
            raise ValueError(
                f"Failed to delete secret {secret_name} from Azure Key Vault: {e}"
            )


# Alias for backward compatibility
EnvSecretsManager = EnvironmentSecretsManager


def get_secrets_manager(
    provider: str = "env",
    vault_url: Optional[str] = None,
    client_id: Optional[str] = None,
    client_secret: Optional[str] = None,
    tenant_id: Optional[str] = None,
) -> SecretsManager:
    """Get a secrets manager instance.

    Args:
        provider: Secrets provider ("env" or "azure").
        vault_url: URL of the Azure Key Vault (required for "azure" provider).
        client_id: Azure client ID (for ClientSecretCredential).
        client_secret: Azure client secret (for ClientSecretCredential).
        tenant_id: Azure tenant ID (for ClientSecretCredential).

    Returns:
        SecretsManager instance.

    Raises:
        ValueError: If the provider is invalid or required parameters are missing.
    """
    if provider == "env":
        return EnvironmentSecretsManager()
    elif provider == "azure":
        if not vault_url:
            vault_url = os.getenv("AZURE_KEY_VAULT_URL")
            if not vault_url:
                raise ValueError("vault_url is required for Azure Key Vault")

        # Use environment variables if not provided
        client_id = client_id or os.getenv("AZURE_CLIENT_ID")
        client_secret = client_secret or os.getenv("AZURE_CLIENT_SECRET")
        tenant_id = tenant_id or os.getenv("AZURE_TENANT_ID")

        return AzureKeyVaultSecretsManager(
            vault_url=vault_url,
            client_id=client_id,
            client_secret=client_secret,
            tenant_id=tenant_id,
        )
    else:
        raise ValueError(f"Invalid secrets provider: {provider}")
