"""Notification utilities for the Immuta SRE Toolkit."""

import json
import os
import smtplib
import time
from datetime import datetime
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from typing import Dict, List, Optional, Union

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class NotificationConfig:
    """Configuration for notifications.

    Attributes:
        enabled: Whether notifications are enabled.
        email_enabled: Whether email notifications are enabled.
        slack_enabled: Whether Slack notifications are enabled.
        teams_enabled: Whether Microsoft Teams notifications are enabled.
        email_recipients: List of email recipients.
        slack_webhook: Slack webhook URL.
        teams_webhook: Microsoft Teams webhook URL.
        operations: List of operations to notify on.
        errors: List of errors to notify on.
        suppress_duration: Dictionary mapping operation/error to suppression duration in seconds.
    """

    def __init__(
        self,
        enabled: bool = True,
        email_enabled: bool = False,
        slack_enabled: bool = False,
        teams_enabled: bool = False,
        email_recipients: Optional[List[str]] = None,
        slack_webhook: Optional[str] = None,
        teams_webhook: Optional[str] = None,
        operations: Optional[List[str]] = None,
        errors: Optional[List[str]] = None,
        suppress_duration: Optional[Dict[str, int]] = None,
    ):
        """Initialize notification configuration.

        Args:
            enabled: Whether notifications are enabled.
            email_enabled: Whether email notifications are enabled.
            slack_enabled: Whether Slack notifications are enabled.
            teams_enabled: Whether Microsoft Teams notifications are enabled.
            email_recipients: List of email recipients.
            slack_webhook: Slack webhook URL.
            teams_webhook: Microsoft Teams webhook URL.
            operations: List of operations to notify on.
            errors: List of errors to notify on.
            suppress_duration: Dictionary mapping operation/error to suppression duration in seconds.
        """
        self.enabled = enabled
        self.email_enabled = email_enabled
        self.slack_enabled = slack_enabled
        self.teams_enabled = teams_enabled
        self.email_recipients = email_recipients or []
        self.slack_webhook = slack_webhook
        self.teams_webhook = teams_webhook
        self.operations = operations or []
        self.errors = errors or []
        self.suppress_duration = suppress_duration or {}

    @classmethod
    def from_dict(cls, config: Dict) -> "NotificationConfig":
        """Create notification configuration from dictionary.

        Args:
            config: Dictionary containing notification configuration.

        Returns:
            NotificationConfig instance.
        """
        return cls(
            enabled=config.get("enabled", True),
            email_enabled=config.get("email_enabled", False),
            slack_enabled=config.get("slack_enabled", False),
            teams_enabled=config.get("teams_enabled", False),
            email_recipients=config.get("email_recipients", []),
            slack_webhook=config.get("slack_webhook"),
            teams_webhook=config.get("teams_webhook"),
            operations=config.get("operations", []),
            errors=config.get("errors", []),
            suppress_duration=config.get("suppress_duration", {}),
        )

    @classmethod
    def from_env(cls) -> "NotificationConfig":
        """Create notification configuration from environment variables.

        Returns:
            NotificationConfig instance.
        """
        return cls(
            enabled=os.getenv("NOTIFICATION_ENABLED", "true").lower() == "true",
            email_enabled=os.getenv("EMAIL_ENABLED", "false").lower() == "true",
            slack_enabled=os.getenv("SLACK_ENABLED", "false").lower() == "true",
            teams_enabled=os.getenv("TEAMS_ENABLED", "false").lower() == "true",
            email_recipients=os.getenv("EMAIL_RECIPIENTS", "").split(","),
            slack_webhook=os.getenv("SLACK_WEBHOOK"),
            teams_webhook=os.getenv("TEAMS_WEBHOOK"),
            operations=os.getenv("NOTIFICATION_OPERATIONS", "").split(","),
            errors=os.getenv("NOTIFICATION_ERRORS", "").split(","),
            suppress_duration={},  # Not supported via env vars
        )


class NotificationManager:
    """Manager for sending notifications.

    This class provides functionality for sending notifications via email,
    Slack, and Microsoft Teams.

    Attributes:
        config: Notification configuration.
        suppress_cache: Cache of suppressed notifications.
    """

    def __init__(self, config: Optional[NotificationConfig] = None):
        """Initialize the notification manager.

        Args:
            config: Notification configuration. If None, configuration will be
                loaded from environment variables.
        """
        self.config = config or NotificationConfig.from_env()
        self.suppress_cache = {}  # {operation/error: timestamp}
        logger.info(
            f"Notification manager initialized (enabled: {self.config.enabled})"
        )

    def should_notify(self, operation: str, error: Optional[str] = None) -> bool:
        """Check if notification is needed.

        Args:
            operation: Operation name.
            error: Error message if any.

        Returns:
            Whether notification should be sent.
        """
        if not self.config.enabled:
            return False

        key = error or operation
        if key not in (self.config.errors + self.config.operations):
            return False

        suppress_duration = self.config.suppress_duration.get(key, 0)
        if suppress_duration and (
            time.time() - self.suppress_cache.get(key, 0) < suppress_duration
        ):
            logger.info(f"Notification suppressed for {key}")
            return False

        return True

    def format_message(
        self, operation: str, details: Dict, status: str, error: Optional[str] = None
    ) -> str:
        """Format notification message.

        Args:
            operation: Operation name.
            details: Operation details.
            status: Operation status.
            error: Error message if any.

        Returns:
            Formatted message.
        """
        message = f"Immuta SRE Toolkit: {operation} - {status}\n\n"

        if error:
            message += f"Error: {error}\n\n"

        message += "Details:\n"
        for key, value in details.items():
            message += f"- {key}: {value}\n"

        message += f"\nTimestamp: {datetime.now().isoformat()}"
        return message

    def send_notification(
        self, operation: str, details: Dict, status: str, error: Optional[str] = None
    ) -> None:
        """Send notifications.

        Args:
            operation: Operation name.
            details: Operation details.
            status: Operation status.
            error: Error message if any.
        """
        # Clean up old cache entries
        current_time = time.time()
        self.suppress_cache = {
            k: v for k, v in self.suppress_cache.items() if current_time - v < 3600
        }  # 1 hour TTL

        if not self.should_notify(operation, error):
            return

        message = self.format_message(operation, details, status, error)
        self.suppress_cache[error or operation] = current_time

        if self.config.email_enabled:
            self._send_email(
                subject=f"Immuta SRE Toolkit: {operation} - {status}",
                body=message,
            )

        if self.config.slack_enabled:
            self._send_slack(message)

        if self.config.teams_enabled:
            self._send_teams(
                title=f"Immuta SRE Toolkit: {operation} - {status}",
                text=message,
            )

    def _send_email(self, subject: str, body: str) -> None:
        """Send email notification.

        Args:
            subject: Email subject.
            body: Email body.
        """
        if not self.config.email_recipients:
            logger.warning("No email recipients configured")
            return

        try:
            smtp_server = os.getenv("SMTP_SERVER", "localhost")
            smtp_port = int(os.getenv("SMTP_PORT", "25"))
            smtp_user = os.getenv("SMTP_USER")
            smtp_password = os.getenv("SMTP_PASSWORD")

            msg = MIMEMultipart()
            msg["From"] = os.getenv("SMTP_FROM", "<EMAIL>")
            msg["To"] = ", ".join(self.config.email_recipients)
            msg["Subject"] = subject
            msg.attach(MIMEText(body, "plain"))

            server = smtplib.SMTP(smtp_server, smtp_port)
            if smtp_user and smtp_password:
                server.starttls()
                server.login(smtp_user, smtp_password)

            server.send_message(msg)
            server.quit()
            logger.info(f"Email notification sent to {self.config.email_recipients}")
        except Exception as e:
            logger.error(f"Failed to send email notification: {e}")

    def _send_slack(self, message: str) -> None:
        """Send Slack notification.

        Args:
            message: Message to send.
        """
        if not self.config.slack_webhook:
            logger.warning("No Slack webhook configured")
            return

        try:
            import requests

            payload = {"text": message}
            # Add timeout to prevent hanging requests
            response = requests.post(
                self.config.slack_webhook,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10,  # 10 seconds timeout
            )
            response.raise_for_status()
            logger.info("Slack notification sent")
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {e}")

    def _send_teams(self, title: str, text: str) -> None:
        """Send Microsoft Teams notification.

        Args:
            title: Card title.
            text: Card text.
        """
        if not self.config.teams_webhook:
            logger.warning("No Teams webhook configured")
            return

        try:
            import requests

            payload = {
                "@type": "MessageCard",
                "@context": "http://schema.org/extensions",
                "themeColor": "0076D7",
                "summary": title,
                "sections": [
                    {
                        "activityTitle": title,
                        "activitySubtitle": datetime.now().isoformat(),
                        "text": text,
                    }
                ],
            }
            # Add timeout to prevent hanging requests
            response = requests.post(
                self.config.teams_webhook,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=10,  # 10 seconds timeout
            )
            response.raise_for_status()
            logger.info("Teams notification sent")
        except Exception as e:
            logger.error(f"Failed to send Teams notification: {e}")
