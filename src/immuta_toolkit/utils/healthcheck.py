"""Healthcheck utilities for the Immuta SRE Toolkit."""

import json
import os
import threading
import time
from http.server import <PERSON><PERSON><PERSON><PERSON><PERSON>quest<PERSON><PERSON><PERSON>, HTTPServer
from typing import Any, Dict, List, Optional, Tuple

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Global health status
_health_status: Dict[str, Any] = {
    "status": "starting",
    "version": "0.0.0",
    "components": {
        "api": {"status": "unknown", "last_check": 0},
        "cache": {"status": "unknown", "last_check": 0},
        "storage": {"status": "unknown", "last_check": 0},
    },
    "uptime": 0,
    "start_time": time.time(),
}


class HealthcheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for healthcheck endpoints."""

    def do_GET(self) -> None:
        """Handle GET requests."""
        if self.path == "/health" or self.path == "/healthz":
            self._handle_health()
        elif self.path == "/metrics":
            self._handle_metrics()
        else:
            self.send_response(404)
            self.end_headers()
            self.wfile.write(b"Not Found")

    def _handle_health(self) -> None:
        """Handle health check requests."""
        global _health_status
        
        # Update uptime
        _health_status["uptime"] = int(time.time() - _health_status["start_time"])
        
        # Determine overall status
        component_statuses = [
            component["status"] for component in _health_status["components"].values()
        ]
        if "down" in component_statuses:
            _health_status["status"] = "down"
            status_code = 503  # Service Unavailable
        elif "degraded" in component_statuses:
            _health_status["status"] = "degraded"
            status_code = 200  # OK, but degraded
        elif all(status == "up" for status in component_statuses):
            _health_status["status"] = "up"
            status_code = 200  # OK
        else:
            _health_status["status"] = "unknown"
            status_code = 200  # OK, but unknown
        
        self.send_response(status_code)
        self.send_header("Content-Type", "application/json")
        self.end_headers()
        self.wfile.write(json.dumps(_health_status).encode())

    def _handle_metrics(self) -> None:
        """Handle metrics requests."""
        # This endpoint is handled by prometheus_client
        # Just return a 200 OK
        self.send_response(200)
        self.send_header("Content-Type", "text/plain")
        self.end_headers()
        self.wfile.write(b"See /metrics endpoint provided by prometheus_client")

    def log_message(self, format: str, *args: Any) -> None:
        """Override log_message to use our logger."""
        logger.debug(f"{self.client_address[0]} - {format % args}")


def update_component_status(
    component: str, status: str, details: Optional[Dict[str, Any]] = None
) -> None:
    """Update the status of a component.

    Args:
        component: Component name.
        status: Status of the component (up, down, degraded, unknown).
        details: Additional details about the component status.
    """
    global _health_status
    
    with threading.Lock():
        if component not in _health_status["components"]:
            _health_status["components"][component] = {}
        
        _health_status["components"][component]["status"] = status
        _health_status["components"][component]["last_check"] = int(time.time())
        
        if details:
            _health_status["components"][component].update(details)


def set_version(version: str) -> None:
    """Set the version of the application.

    Args:
        version: Version string.
    """
    global _health_status
    
    with threading.Lock():
        _health_status["version"] = version


def start_healthcheck_server(port: int = 8000) -> None:
    """Start the healthcheck server.

    Args:
        port: Port to listen on.
    """
    try:
        server = HTTPServer(("", port), HealthcheckHandler)
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        logger.info(f"Started healthcheck server on port {port}")
    except Exception as e:
        logger.error(f"Failed to start healthcheck server: {e}")


def check_api_health(client: Any) -> Tuple[str, Dict[str, Any]]:
    """Check the health of the Immuta API.

    Args:
        client: Immuta client instance.

    Returns:
        Tuple of (status, details).
    """
    try:
        # Try to make a simple API call
        response = client.make_request("GET", "version")
        if response.status_code == 200:
            return "up", {
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "version": response.json().get("version", "unknown"),
            }
        else:
            return "degraded", {
                "response_time_ms": int(response.elapsed.total_seconds() * 1000),
                "status_code": response.status_code,
                "reason": "Non-200 response",
            }
    except Exception as e:
        return "down", {"error": str(e)}


def check_storage_health(storage: Any) -> Tuple[str, Dict[str, Any]]:
    """Check the health of the storage.

    Args:
        storage: Storage manager instance.

    Returns:
        Tuple of (status, details).
    """
    try:
        # Try to list containers or check connection
        if hasattr(storage, "check_connection"):
            storage.check_connection()
        elif hasattr(storage, "list_containers"):
            storage.list_containers()
        elif hasattr(storage, "list_blobs"):
            storage.list_blobs()
        else:
            return "unknown", {"reason": "No health check method available"}
        
        return "up", {}
    except Exception as e:
        return "down", {"error": str(e)}


def check_cache_health(cache_manager: Any) -> Tuple[str, Dict[str, Any]]:
    """Check the health of the cache.

    Args:
        cache_manager: Cache manager instance.

    Returns:
        Tuple of (status, details).
    """
    try:
        # Get cache statistics
        cache_count = len(cache_manager.caches)
        total_entries = sum(len(cache.entries) for cache in cache_manager.caches.values())
        
        return "up", {
            "cache_count": cache_count,
            "total_entries": total_entries,
        }
    except Exception as e:
        return "down", {"error": str(e)}
