"""Enhanced error handling with suggestions and recovery options."""

import sys
import traceback
import json
import re
from typing import Dict, Any, List, Optional, Callable, Union, Tuple, Type

import requests
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.table import Table

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


class ErrorHandler:
    """Enhanced error handler with suggestions and recovery options.
    
    This class provides enhanced error handling with suggestions and recovery options
    for common errors.
    
    Attributes:
        error_patterns: Dictionary of error patterns and handlers.
    """
    
    def __init__(self):
        """Initialize the error handler."""
        self.error_patterns = self._get_error_patterns()
    
    def _get_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Get error patterns and handlers.
        
        Returns:
            Dictionary of error patterns and handlers.
        """
        return {
            # API errors
            r"401 Client Error: Unauthorized": {
                "type": "api_auth",
                "message": "Authentication failed. Your API key or credentials are invalid.",
                "suggestions": [
                    "Check your API key or credentials.",
                    "Ensure your API key has not expired.",
                    "Verify that you have the correct permissions.",
                ],
                "recovery": self._handle_auth_error,
            },
            r"403 Client Error: Forbidden": {
                "type": "api_permission",
                "message": "Permission denied. You don't have permission to access this resource.",
                "suggestions": [
                    "Check your permissions in Immuta.",
                    "Contact your administrator to request access.",
                    "Verify that you are using the correct API key.",
                ],
                "recovery": None,
            },
            r"404 Client Error: Not Found": {
                "type": "api_not_found",
                "message": "Resource not found. The requested resource does not exist.",
                "suggestions": [
                    "Check the resource ID or name.",
                    "Verify that the resource exists in Immuta.",
                    "Check the API endpoint URL.",
                ],
                "recovery": None,
            },
            r"429 Client Error: Too Many Requests": {
                "type": "api_rate_limit",
                "message": "Rate limit exceeded. You have made too many requests.",
                "suggestions": [
                    "Wait a few minutes and try again.",
                    "Reduce the number of concurrent requests.",
                    "Increase the rate limit in the configuration.",
                ],
                "recovery": self._handle_rate_limit_error,
            },
            r"500 Server Error": {
                "type": "api_server_error",
                "message": "Server error. The Immuta server encountered an error.",
                "suggestions": [
                    "Wait a few minutes and try again.",
                    "Check the Immuta server status.",
                    "Contact Immuta support if the issue persists.",
                ],
                "recovery": None,
            },
            r"Connection.*timed out": {
                "type": "api_timeout",
                "message": "Connection timed out. The request took too long to complete.",
                "suggestions": [
                    "Check your network connection.",
                    "Increase the timeout in the configuration.",
                    "Try again later when the server is less busy.",
                ],
                "recovery": self._handle_timeout_error,
            },
            r"Failed to establish a new connection": {
                "type": "api_connection",
                "message": "Connection failed. Could not connect to the Immuta server.",
                "suggestions": [
                    "Check your network connection.",
                    "Verify that the Immuta server is running.",
                    "Check the base URL in the configuration.",
                ],
                "recovery": self._handle_connection_error,
            },
            
            # Configuration errors
            r"No such file or directory.*\.env": {
                "type": "config_env_file",
                "message": "Environment file not found. The .env file does not exist.",
                "suggestions": [
                    "Create a .env file with your configuration.",
                    "Use the config command to create a configuration.",
                    "Specify the configuration file with --config.",
                ],
                "recovery": self._handle_env_file_error,
            },
            r"No such file or directory.*config": {
                "type": "config_file",
                "message": "Configuration file not found. The configuration file does not exist.",
                "suggestions": [
                    "Create a configuration file.",
                    "Use the config command to create a configuration.",
                    "Specify the configuration file with --config.",
                ],
                "recovery": self._handle_config_file_error,
            },
            r"Invalid configuration": {
                "type": "config_invalid",
                "message": "Invalid configuration. The configuration is invalid or incomplete.",
                "suggestions": [
                    "Check the configuration file format.",
                    "Ensure all required fields are present.",
                    "Use the config command to validate the configuration.",
                ],
                "recovery": None,
            },
            
            # Web automation errors
            r"Browser.*not found": {
                "type": "web_browser",
                "message": "Browser not found. The browser executable was not found.",
                "suggestions": [
                    "Install the required browser.",
                    "Run 'playwright install' to install browsers.",
                    "Specify a different browser with --browser.",
                ],
                "recovery": self._handle_browser_error,
            },
            r"Element.*not found": {
                "type": "web_element",
                "message": "Element not found. The web element was not found on the page.",
                "suggestions": [
                    "Check if the page structure has changed.",
                    "Update the selectors in the configuration.",
                    "Try running in non-headless mode to debug.",
                ],
                "recovery": None,
            },
            r"Timeout.*waiting for": {
                "type": "web_timeout",
                "message": "Timeout waiting for element. The element did not appear within the timeout.",
                "suggestions": [
                    "Increase the timeout in the configuration.",
                    "Check if the page is loading correctly.",
                    "Try running in non-headless mode to debug.",
                ],
                "recovery": self._handle_web_timeout_error,
            },
            
            # General errors
            r"Permission denied": {
                "type": "general_permission",
                "message": "Permission denied. You don't have permission to access a file or directory.",
                "suggestions": [
                    "Check the file or directory permissions.",
                    "Run the command with elevated privileges.",
                    "Ensure you have write access to the output directory.",
                ],
                "recovery": None,
            },
            r"Disk quota exceeded": {
                "type": "general_disk_quota",
                "message": "Disk quota exceeded. You have run out of disk space.",
                "suggestions": [
                    "Free up disk space.",
                    "Use a different output directory.",
                    "Reduce the amount of data being processed.",
                ],
                "recovery": None,
            },
            r"No space left on device": {
                "type": "general_disk_space",
                "message": "No space left on device. You have run out of disk space.",
                "suggestions": [
                    "Free up disk space.",
                    "Use a different output directory.",
                    "Reduce the amount of data being processed.",
                ],
                "recovery": None,
            },
        }
    
    def handle_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> bool:
        """Handle an error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        # Get error message
        error_message = str(error)
        
        # Log error
        logger.error(f"Error: {error_message}", exc_info=True)
        
        # Find matching error pattern
        for pattern, handler in self.error_patterns.items():
            if re.search(pattern, error_message, re.IGNORECASE):
                # Display error message
                console.print(
                    Panel(
                        f"[bold red]{handler['message']}[/bold red]\n\n"
                        f"[yellow]Error:[/yellow] {error_message}",
                        title="Error",
                        border_style="red",
                    )
                )
                
                # Display suggestions
                if handler["suggestions"]:
                    console.print("\n[bold cyan]Suggestions:[/bold cyan]")
                    for i, suggestion in enumerate(handler["suggestions"], 1):
                        console.print(f"  [cyan]{i}.[/cyan] {suggestion}")
                
                # Try recovery
                if handler["recovery"] and context:
                    if handler["recovery"](error, context):
                        return True
                
                return True
        
        # No matching pattern found, display generic error
        console.print(
            Panel(
                f"[bold red]An error occurred:[/bold red]\n\n"
                f"{error_message}",
                title="Error",
                border_style="red",
            )
        )
        
        # Display traceback
        console.print("\n[bold cyan]Traceback:[/bold cyan]")
        console.print(Syntax(traceback.format_exc(), "python", theme="monokai"))
        
        return False
    
    def _handle_auth_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle authentication error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Prompt
        
        # Ask for new API key
        console.print("\n[bold cyan]Would you like to enter a new API key?[/bold cyan]")
        new_api_key = Prompt.ask("Enter new API key", password=True)
        
        if new_api_key:
            # Update client
            if "client" in context:
                context["client"].api_key = new_api_key
                console.print("[green]API key updated.[/green]")
                return True
        
        return False
    
    def _handle_rate_limit_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle rate limit error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm
        import time
        
        # Ask to retry with backoff
        console.print("\n[bold cyan]Would you like to retry with exponential backoff?[/bold cyan]")
        retry = Confirm.ask("Retry?")
        
        if retry:
            # Get retry count and delay
            retry_count = context.get("retry_count", 0)
            delay = 2 ** retry_count
            
            if retry_count < 5:  # Max 5 retries
                # Update retry count
                context["retry_count"] = retry_count + 1
                
                # Wait with backoff
                console.print(f"[yellow]Waiting {delay} seconds before retrying...[/yellow]")
                time.sleep(delay)
                
                console.print("[green]Retrying...[/green]")
                return True
            else:
                console.print("[red]Maximum retry count reached.[/red]")
        
        return False
    
    def _handle_timeout_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle timeout error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm, IntPrompt
        
        # Ask to retry with increased timeout
        console.print("\n[bold cyan]Would you like to retry with increased timeout?[/bold cyan]")
        retry = Confirm.ask("Retry?")
        
        if retry:
            # Get current timeout
            current_timeout = context.get("timeout", 30)
            
            # Ask for new timeout
            new_timeout = IntPrompt.ask(
                "Enter new timeout (seconds)",
                default=current_timeout * 2,
            )
            
            if new_timeout > current_timeout:
                # Update timeout
                context["timeout"] = new_timeout
                
                # Update client
                if "client" in context:
                    context["client"].timeout = new_timeout
                    console.print(f"[green]Timeout increased to {new_timeout} seconds.[/green]")
                    return True
        
        return False
    
    def _handle_connection_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle connection error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Prompt, Confirm
        
        # Ask to retry with new base URL
        console.print("\n[bold cyan]Would you like to enter a new base URL?[/bold cyan]")
        retry = Confirm.ask("Enter new URL?")
        
        if retry:
            # Get current base URL
            current_base_url = context.get("base_url", "")
            
            # Ask for new base URL
            new_base_url = Prompt.ask(
                "Enter new base URL",
                default=current_base_url,
            )
            
            if new_base_url and new_base_url != current_base_url:
                # Update base URL
                context["base_url"] = new_base_url
                
                # Update client
                if "client" in context:
                    context["client"].base_url = new_base_url
                    console.print(f"[green]Base URL updated to {new_base_url}.[/green]")
                    return True
        
        return False
    
    def _handle_env_file_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle environment file error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm
        
        # Ask to create .env file
        console.print("\n[bold cyan]Would you like to create a .env file?[/bold cyan]")
        create = Confirm.ask("Create .env file?")
        
        if create:
            # Create .env file
            try:
                with open(".env", "w") as f:
                    f.write("# Immuta SRE Toolkit Configuration\n")
                    f.write("IMMUTA_API_KEY=\n")
                    f.write("IMMUTA_BASE_URL=\n")
                    f.write("IMMUTA_USERNAME=\n")
                    f.write("IMMUTA_PASSWORD=\n")
                
                console.print("[green].env file created. Please edit it to add your configuration.[/green]")
                return True
            except Exception as e:
                console.print(f"[red]Failed to create .env file: {e}[/red]")
        
        return False
    
    def _handle_config_file_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle configuration file error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm
        
        # Ask to create config file
        console.print("\n[bold cyan]Would you like to create a configuration file?[/bold cyan]")
        create = Confirm.ask("Create config file?")
        
        if create:
            # Create config file
            try:
                config_file = context.get("config_file", "config.yaml")
                
                with open(config_file, "w") as f:
                    f.write("# Immuta SRE Toolkit Configuration\n")
                    f.write("api:\n")
                    f.write("  key: \n")
                    f.write("  base_url: \n")
                    f.write("  username: \n")
                    f.write("  password: \n")
                
                console.print(f"[green]Configuration file {config_file} created. Please edit it to add your configuration.[/green]")
                return True
            except Exception as e:
                console.print(f"[red]Failed to create configuration file: {e}[/red]")
        
        return False
    
    def _handle_browser_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle browser error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm
        import subprocess
        
        # Ask to install browsers
        console.print("\n[bold cyan]Would you like to install Playwright browsers?[/bold cyan]")
        install = Confirm.ask("Install browsers?")
        
        if install:
            # Install browsers
            try:
                console.print("[yellow]Installing browsers...[/yellow]")
                subprocess.run(["playwright", "install", "--with-deps"], check=True)
                console.print("[green]Browsers installed successfully.[/green]")
                return True
            except Exception as e:
                console.print(f"[red]Failed to install browsers: {e}[/red]")
        
        return False
    
    def _handle_web_timeout_error(self, error: Exception, context: Dict[str, Any]) -> bool:
        """Handle web timeout error.
        
        Args:
            error: Exception to handle.
            context: Error context.
            
        Returns:
            True if the error was handled, False otherwise.
        """
        from rich.prompt import Confirm
        
        # Ask to retry with non-headless mode
        console.print("\n[bold cyan]Would you like to retry in non-headless mode?[/bold cyan]")
        retry = Confirm.ask("Retry in non-headless mode?")
        
        if retry:
            # Update headless mode
            context["headless"] = False
            
            # Update client
            if "client" in context:
                context["client"].headless = False
                console.print("[green]Switched to non-headless mode.[/green]")
                return True
        
        return False


# Global error handler instance
error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance.
    
    Returns:
        Error handler instance.
    """
    return error_handler


def handle_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> bool:
    """Handle an error.
    
    Args:
        error: Exception to handle.
        context: Error context.
        
    Returns:
        True if the error was handled, False otherwise.
    """
    return get_error_handler().handle_error(error, context)
