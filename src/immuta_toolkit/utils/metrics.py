"""Metrics utilities for the Immuta SRE Toolkit."""

import os
import time
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar, cast

from prometheus_client import Counter, Gauge, Histogram, Summary, start_http_server

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Type variable for function return type
T = TypeVar("T")

# Initialize metrics
API_REQUESTS = Counter(
    "immuta_api_requests_total",
    "Total number of API requests",
    ["method", "endpoint", "status"],
)

API_REQUEST_DURATION = Histogram(
    "immuta_api_request_duration_seconds",
    "API request duration in seconds",
    ["method", "endpoint"],
    buckets=(0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 120.0),
)

OPERATION_DURATION = Histogram(
    "immuta_operation_duration_seconds",
    "Operation duration in seconds",
    ["operation"],
    buckets=(0.1, 0.5, 1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0),
)

OPERATION_ERRORS = Counter(
    "immuta_operation_errors_total",
    "Total number of operation errors",
    ["operation", "error_type"],
)

CACHE_HITS = Counter(
    "immuta_cache_hits_total",
    "Total number of cache hits",
    ["cache_name"],
)

CACHE_MISSES = Counter(
    "immuta_cache_misses_total",
    "Total number of cache misses",
    ["cache_name"],
)

CACHE_SIZE = Gauge(
    "immuta_cache_size",
    "Current cache size",
    ["cache_name"],
)

BACKUP_SIZE = Gauge(
    "immuta_backup_size_bytes",
    "Backup size in bytes",
    ["backup_type"],
)

BACKUP_DURATION = Histogram(
    "immuta_backup_duration_seconds",
    "Backup duration in seconds",
    ["backup_type"],
    buckets=(1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0),
)

RESTORE_DURATION = Histogram(
    "immuta_restore_duration_seconds",
    "Restore duration in seconds",
    ["restore_type"],
    buckets=(1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0),
)


def start_metrics_server(port: int = 8000) -> None:
    """Start the Prometheus metrics server.

    Args:
        port: Port to listen on.
    """
    try:
        start_http_server(port)
        logger.info(f"Started metrics server on port {port}")
    except Exception as e:
        logger.error(f"Failed to start metrics server: {e}")


def track_operation_time(operation_name: str) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """Decorator to track operation time.

    Args:
        operation_name: Name of the operation.

    Returns:
        Decorated function.
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                OPERATION_DURATION.labels(operation=operation_name).observe(
                    time.time() - start_time
                )
                return result
            except Exception as e:
                OPERATION_ERRORS.labels(
                    operation=operation_name, error_type=type(e).__name__
                ).inc()
                raise

        return cast(Callable[..., T], wrapper)

    return decorator


def track_api_request(
    method: str, endpoint: str, status: str, duration: float
) -> None:
    """Track an API request.

    Args:
        method: HTTP method.
        endpoint: API endpoint.
        status: HTTP status code.
        duration: Request duration in seconds.
    """
    API_REQUESTS.labels(method=method, endpoint=endpoint, status=status).inc()
    API_REQUEST_DURATION.labels(method=method, endpoint=endpoint).observe(duration)


def track_cache_hit(cache_name: str) -> None:
    """Track a cache hit.

    Args:
        cache_name: Name of the cache.
    """
    CACHE_HITS.labels(cache_name=cache_name).inc()


def track_cache_miss(cache_name: str) -> None:
    """Track a cache miss.

    Args:
        cache_name: Name of the cache.
    """
    CACHE_MISSES.labels(cache_name=cache_name).inc()


def update_cache_size(cache_name: str, size: int) -> None:
    """Update cache size.

    Args:
        cache_name: Name of the cache.
        size: Current cache size.
    """
    CACHE_SIZE.labels(cache_name=cache_name).set(size)


def track_backup(backup_type: str, size_bytes: int, duration: float) -> None:
    """Track a backup operation.

    Args:
        backup_type: Type of backup.
        size_bytes: Size of the backup in bytes.
        duration: Duration of the backup operation in seconds.
    """
    BACKUP_SIZE.labels(backup_type=backup_type).set(size_bytes)
    BACKUP_DURATION.labels(backup_type=backup_type).observe(duration)


def track_restore(restore_type: str, duration: float) -> None:
    """Track a restore operation.

    Args:
        restore_type: Type of restore.
        duration: Duration of the restore operation in seconds.
    """
    RESTORE_DURATION.labels(restore_type=restore_type).observe(duration)


class MetricsCollector:
    """Enhanced metrics collector for the hybrid client."""

    def __init__(self):
        """Initialize the metrics collector."""
        self.operation_metrics: List[Dict[str, Any]] = []
        self.start_time = time.time()

    def record_operation(self, metrics: Any) -> None:
        """Record operation metrics.

        Args:
            metrics: Operation metrics object.
        """
        # Convert metrics to dictionary for storage
        metrics_dict = {
            "operation_name": metrics.operation_name,
            "api_attempts": metrics.api_attempts,
            "web_attempts": metrics.web_attempts,
            "total_time": metrics.total_time,
            "api_time": metrics.api_time,
            "web_time": metrics.web_time,
            "success": metrics.success,
            "fallback_used": metrics.fallback_used,
            "error_message": metrics.error_message,
            "timestamp": time.time(),
        }

        self.operation_metrics.append(metrics_dict)

        # Update Prometheus metrics
        OPERATION_DURATION.labels(operation=metrics.operation_name).observe(
            metrics.total_time
        )

        if not metrics.success:
            error_type = "unknown"
            if metrics.error_message:
                # Extract error type from error message
                if "timeout" in metrics.error_message.lower():
                    error_type = "timeout"
                elif "connection" in metrics.error_message.lower():
                    error_type = "connection"
                elif "validation" in metrics.error_message.lower():
                    error_type = "validation"
                elif "authentication" in metrics.error_message.lower():
                    error_type = "authentication"

            OPERATION_ERRORS.labels(
                operation=metrics.operation_name, error_type=error_type
            ).inc()

    def get_metrics(self) -> Dict[str, Any]:
        """Get collected metrics.

        Returns:
            Dictionary with metrics data.
        """
        total_operations = len(self.operation_metrics)
        successful_operations = sum(1 for m in self.operation_metrics if m["success"])
        failed_operations = total_operations - successful_operations

        api_operations = sum(1 for m in self.operation_metrics if m["api_attempts"] > 0)
        web_operations = sum(1 for m in self.operation_metrics if m["web_attempts"] > 0)
        fallback_operations = sum(1 for m in self.operation_metrics if m["fallback_used"])

        total_time = sum(m["total_time"] for m in self.operation_metrics)
        avg_time = total_time / total_operations if total_operations > 0 else 0

        return {
            "summary": {
                "total_operations": total_operations,
                "successful_operations": successful_operations,
                "failed_operations": failed_operations,
                "success_rate": successful_operations / total_operations if total_operations > 0 else 0,
                "api_operations": api_operations,
                "web_operations": web_operations,
                "fallback_operations": fallback_operations,
                "fallback_rate": fallback_operations / total_operations if total_operations > 0 else 0,
                "total_time": total_time,
                "average_time": avg_time,
                "uptime": time.time() - self.start_time,
            },
            "operations": self.operation_metrics,
        }

    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """Get statistics for a specific operation.

        Args:
            operation_name: Name of the operation.

        Returns:
            Dictionary with operation statistics.
        """
        operation_metrics = [
            m for m in self.operation_metrics if m["operation_name"] == operation_name
        ]

        if not operation_metrics:
            return {"error": f"No metrics found for operation: {operation_name}"}

        total = len(operation_metrics)
        successful = sum(1 for m in operation_metrics if m["success"])
        failed = total - successful

        times = [m["total_time"] for m in operation_metrics]
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        return {
            "operation_name": operation_name,
            "total_executions": total,
            "successful_executions": successful,
            "failed_executions": failed,
            "success_rate": successful / total,
            "average_time": avg_time,
            "min_time": min_time,
            "max_time": max_time,
            "fallback_usage": sum(1 for m in operation_metrics if m["fallback_used"]),
        }

    def clear_metrics(self) -> None:
        """Clear all collected metrics."""
        self.operation_metrics.clear()
        self.start_time = time.time()

    def export_metrics(self, format: str = "json") -> str:
        """Export metrics in the specified format.

        Args:
            format: Export format (json, csv).

        Returns:
            Exported metrics as a string.
        """
        metrics = self.get_metrics()

        if format.lower() == "json":
            import json
            return json.dumps(metrics, indent=2)
        elif format.lower() == "csv":
            import csv
            import io

            output = io.StringIO()
            writer = csv.writer(output)

            # Write header
            writer.writerow([
                "operation_name", "api_attempts", "web_attempts", "total_time",
                "api_time", "web_time", "success", "fallback_used", "error_message", "timestamp"
            ])

            # Write data
            for metric in metrics["operations"]:
                writer.writerow([
                    metric["operation_name"],
                    metric["api_attempts"],
                    metric["web_attempts"],
                    metric["total_time"],
                    metric["api_time"],
                    metric["web_time"],
                    metric["success"],
                    metric["fallback_used"],
                    metric["error_message"] or "",
                    metric["timestamp"],
                ])

            return output.getvalue()
        else:
            raise ValueError(f"Unsupported export format: {format}")
