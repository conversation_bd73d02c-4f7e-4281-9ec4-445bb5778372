"""Circuit breaker implementation for the Immuta SRE Toolkit."""

import time
import threading
from enum import Enum
from typing import Callable, Any, Dict, Optional, List, Tuple, TypeVar, Generic, cast
from functools import wraps

from immuta_toolkit.api.exceptions import CircuitBreakerError
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")


class CircuitState(Enum):
    """Circuit breaker states."""

    CLOSED = "closed"  # Normal operation, requests are allowed
    OPEN = "open"  # Circuit is open, requests are blocked
    HALF_OPEN = "half_open"  # Testing if the circuit can be closed again


class CircuitBreaker:
    """Circuit breaker implementation.

    This class implements the circuit breaker pattern to prevent cascading failures
    when a service is unavailable or experiencing high error rates.

    Attributes:
        name: Name of the circuit breaker.
        failure_threshold: Number of failures before opening the circuit.
        reset_timeout: Seconds to wait before attempting to close the circuit.
        half_open_max_calls: Maximum number of calls allowed in half-open state.
        exclude_exceptions: Exceptions that should not count as failures.
        state: Current state of the circuit breaker.
        failure_count: Number of consecutive failures.
        last_failure_time: Timestamp of the last failure.
        successful_calls: Number of successful calls in half-open state.
        lock: Lock for thread safety.
    """

    def __init__(
        self,
        name: str,
        failure_threshold: int = 5,
        reset_timeout: int = 60,
        half_open_max_calls: int = 3,
        exclude_exceptions: Optional[List[type]] = None,
    ):
        """Initialize the circuit breaker.

        Args:
            name: Name of the circuit breaker.
            failure_threshold: Number of failures before opening the circuit.
            reset_timeout: Seconds to wait before attempting to close the circuit.
            half_open_max_calls: Maximum number of calls allowed in half-open state.
            exclude_exceptions: Exceptions that should not count as failures.
        """
        self.name = name
        self.failure_threshold = failure_threshold
        self.reset_timeout = reset_timeout
        self.half_open_max_calls = half_open_max_calls
        self.exclude_exceptions = exclude_exceptions or []

        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.successful_calls = 0
        self.lock = threading.RLock()

    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """Decorate a function with circuit breaker.

        Args:
            func: Function to decorate.

        Returns:
            Decorated function.
        """
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            return self.call(func, *args, **kwargs)
        return wrapper

    def call(self, func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
        """Call a function with circuit breaker protection.

        Args:
            func: Function to call.
            *args: Positional arguments.
            **kwargs: Keyword arguments.

        Returns:
            Function result.

        Raises:
            CircuitBreakerError: If the circuit is open.
            Exception: Any exception raised by the function.
        """
        with self.lock:
            if self.state == CircuitState.OPEN:
                if time.time() - self.last_failure_time >= self.reset_timeout:
                    logger.info(f"Circuit {self.name} transitioning from OPEN to HALF_OPEN")
                    self.state = CircuitState.HALF_OPEN
                    self.successful_calls = 0
                else:
                    time_remaining = self.reset_timeout - (time.time() - self.last_failure_time)
                    raise CircuitBreakerError(
                        f"Circuit {self.name} is OPEN",
                        reset_timeout=int(time_remaining),
                    )

            if self.state == CircuitState.HALF_OPEN and self.successful_calls >= self.half_open_max_calls:
                logger.info(f"Circuit {self.name} transitioning from HALF_OPEN to CLOSED")
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.successful_calls = 0

        try:
            result = func(*args, **kwargs)

            with self.lock:
                if self.state == CircuitState.HALF_OPEN:
                    self.successful_calls += 1
                    logger.debug(f"Circuit {self.name} successful call in HALF_OPEN state: {self.successful_calls}/{self.half_open_max_calls}")

                if self.failure_count > 0:
                    self.failure_count = 0
                    logger.debug(f"Circuit {self.name} reset failure count")

            return result
        except Exception as e:
            # Check if this exception should be excluded
            if any(isinstance(e, exc_type) for exc_type in self.exclude_exceptions):
                logger.debug(f"Circuit {self.name} excluded exception: {type(e).__name__}")
                raise

            with self.lock:
                self.failure_count += 1
                self.last_failure_time = time.time()
                logger.debug(f"Circuit {self.name} failure: {self.failure_count}/{self.failure_threshold}")

                if self.state == CircuitState.CLOSED and self.failure_count >= self.failure_threshold:
                    logger.warning(f"Circuit {self.name} transitioning from CLOSED to OPEN")
                    self.state = CircuitState.OPEN
                elif self.state == CircuitState.HALF_OPEN:
                    logger.warning(f"Circuit {self.name} transitioning from HALF_OPEN to OPEN")
                    self.state = CircuitState.OPEN

            raise

    def reset(self) -> None:
        """Reset the circuit breaker to closed state."""
        with self.lock:
            logger.info(f"Circuit {self.name} manually reset to CLOSED")
            self.state = CircuitState.CLOSED
            self.failure_count = 0
            self.successful_calls = 0

    def get_state(self) -> Dict[str, Any]:
        """Get the current state of the circuit breaker.

        Returns:
            Dictionary with circuit breaker state information.
        """
        with self.lock:
            time_remaining = 0
            if self.state == CircuitState.OPEN:
                time_remaining = max(0, self.reset_timeout - (time.time() - self.last_failure_time))

            return {
                "name": self.name,
                "state": self.state.value,
                "failure_count": self.failure_count,
                "failure_threshold": self.failure_threshold,
                "reset_timeout": self.reset_timeout,
                "time_remaining": int(time_remaining),
                "successful_calls": self.successful_calls,
                "half_open_max_calls": self.half_open_max_calls,
            }
