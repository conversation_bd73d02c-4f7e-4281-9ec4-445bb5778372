"""Enhanced rate limiter for API calls with adaptive throttling."""

import time
import threading
import functools
from typing import Any, Callable, TypeVar, Dict, Optional, List, Tuple
from dataclasses import dataclass
from collections import deque

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")


@dataclass
class RateLimitConfig:
    """Rate limit configuration.

    Attributes:
        calls_per_second: Maximum number of calls allowed per second.
        burst_size: Maximum number of calls allowed in a burst.
        adaptive: Whether to use adaptive rate limiting.
        min_calls_per_second: Minimum number of calls allowed per second.
        max_calls_per_second: Maximum number of calls allowed per second.
        backoff_factor: Factor to reduce rate limit after a 429 response.
        recovery_factor: Factor to increase rate limit after successful responses.
    """

    calls_per_second: int = 10
    burst_size: int = 20
    adaptive: bool = True
    min_calls_per_second: int = 1
    max_calls_per_second: int = 50
    backoff_factor: float = 0.5
    recovery_factor: float = 1.1


class TokenBucket:
    """Token bucket rate limiter.

    This class implements a token bucket algorithm for rate limiting.

    Attributes:
        rate: Rate at which tokens are added to the bucket (tokens per second).
        max_tokens: Maximum number of tokens in the bucket.
        tokens: Current number of tokens in the bucket.
        last_refill: Timestamp of the last token refill.
        lock: Thread lock for thread safety.
    """

    def __init__(self, rate: float, max_tokens: int):
        """Initialize the token bucket.

        Args:
            rate: Rate at which tokens are added to the bucket (tokens per second).
            max_tokens: Maximum number of tokens in the bucket.
        """
        self.rate = rate
        self.max_tokens = max_tokens
        self.tokens = max_tokens
        self.last_refill = time.time()
        self.lock = threading.Lock()

    def consume(self, tokens: int = 1) -> bool:
        """Consume tokens from the bucket.

        Args:
            tokens: Number of tokens to consume.

        Returns:
            True if tokens were consumed, False otherwise.
        """
        with self.lock:
            self._refill()

            if self.tokens >= tokens:
                self.tokens -= tokens
                return True

            return False

    def _refill(self) -> None:
        """Refill tokens in the bucket based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill

        # Calculate new tokens
        new_tokens = elapsed * self.rate

        # Update tokens
        self.tokens = min(self.tokens + new_tokens, self.max_tokens)
        self.last_refill = now

    def set_rate(self, rate: float) -> None:
        """Set the token refill rate.

        Args:
            rate: New rate in tokens per second.
        """
        with self.lock:
            self._refill()  # Refill tokens before changing rate
            self.rate = rate
            logger.debug(f"Token bucket rate set to {rate} tokens/second")


class RateLimiter:
    """Enhanced rate limiter for API calls with adaptive throttling.

    This class provides rate limiting functionality to prevent API rate limit
    errors when making frequent API calls. It supports adaptive throttling
    based on response status codes.

    Attributes:
        config: Rate limit configuration.
        bucket: Token bucket for rate limiting.
        response_times: Queue of recent response times.
        error_count: Count of recent errors.
        lock: Thread lock for thread safety.
    """

    def __init__(
        self, calls_per_second: int = 10, config: Optional[RateLimitConfig] = None
    ):
        """Initialize the rate limiter.

        Args:
            calls_per_second: Maximum number of calls allowed per second.
            config: Rate limit configuration.
        """
        if config is None:
            config = RateLimitConfig(calls_per_second=calls_per_second)
        elif isinstance(config, int):
            # Handle the case where config is an integer (for backward compatibility)
            config = RateLimitConfig(calls_per_second=config)
        self.config = config
        self.bucket = TokenBucket(
            rate=self.config.calls_per_second,
            max_tokens=self.config.burst_size,
        )
        self.response_times: deque = deque(maxlen=100)
        self.error_count = 0
        self.lock = threading.Lock()

    def call(self, func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
        """Execute a function with rate limiting.

        Args:
            func: Function to execute.
            *args: Positional arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.

        Returns:
            Result of the function call.

        Raises:
            Exception: If the function call fails.
        """
        # Wait for token
        self._wait_for_token()

        # Execute function
        start_time = time.time()
        try:
            result = func(*args, **kwargs)

            # Record response time
            response_time = time.time() - start_time
            self._record_success(response_time)

            return result
        except Exception as e:
            # Record error
            self._record_error(e)
            raise

    def acquire(self) -> None:
        """Acquire a token from the bucket."""
        self._wait_for_token()

    def _wait_for_token(self) -> None:
        """Wait for a token to be available."""
        while not self.bucket.consume():
            # Sleep for a short time to avoid busy waiting
            time.sleep(0.01)

    def _record_success(self, response_time: float) -> None:
        """Record a successful response.

        Args:
            response_time: Response time in seconds.
        """
        with self.lock:
            self.response_times.append(response_time)

            # Adjust rate if adaptive rate limiting is enabled
            if self.config.adaptive and self.error_count == 0:
                # Increase rate if we have enough successful responses
                if len(self.response_times) >= 10:
                    new_rate = min(
                        self.bucket.rate * self.config.recovery_factor,
                        self.config.max_calls_per_second,
                    )
                    if new_rate > self.bucket.rate:
                        self.bucket.set_rate(new_rate)
                        logger.debug(f"Increased rate limit to {new_rate} calls/second")

    def _record_error(self, error: Exception) -> None:
        """Record an error response.

        Args:
            error: Exception from the API call.
        """
        with self.lock:
            self.error_count += 1

            # Adjust rate if adaptive rate limiting is enabled
            if self.config.adaptive:
                # Check if error is a rate limit error (429)
                is_rate_limit_error = (
                    hasattr(error, "response")
                    and getattr(error.response, "status_code", 0) == 429
                )

                if is_rate_limit_error:
                    # Reduce rate
                    new_rate = max(
                        self.bucket.rate * self.config.backoff_factor,
                        self.config.min_calls_per_second,
                    )
                    self.bucket.set_rate(new_rate)
                    logger.warning(
                        f"Rate limit exceeded. Reduced rate to {new_rate} calls/second"
                    )

                # Reset error count after some time
                if self.error_count >= 5:
                    self.error_count = 0


def with_rate_limiting(
    calls_per_second: int = 10,
    burst_size: int = 20,
    adaptive: bool = True,
) -> Callable:
    """Decorator to apply rate limiting to a function.

    Args:
        calls_per_second: Maximum number of calls allowed per second.
        burst_size: Maximum number of calls allowed in a burst.
        adaptive: Whether to use adaptive rate limiting.

    Returns:
        Decorated function with rate limiting.
    """

    def decorator(func: Callable) -> Callable:
        """Decorator function."""
        config = RateLimitConfig(
            calls_per_second=calls_per_second,
            burst_size=burst_size,
            adaptive=adaptive,
        )
        limiter = RateLimiter(config=config)

        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            """Wrapper function."""
            return limiter.call(func, *args, **kwargs)

        return wrapper

    return decorator
