"""Snowflake utilities for the Immuta SRE Toolkit."""

import json
import re
import threading
import time
from typing import Dict, Optional, Any

try:
    import snowflake.connector
    from snowflake.connector.connection import SnowflakeConnection

    SNOWFLAKE_AVAILABLE = True
except ImportError:
    # Define a mock SnowflakeConnection type for type hints
    class SnowflakeConnection:
        """Mock Snowflake connection class for type hints."""

        pass

    SNOWFLAKE_AVAILABLE = False

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class SnowflakeConnectionPool:
    """Connection pool for Snowflake.

    This class provides a thread-safe connection pool for Snowflake,
    with support for connection reuse, connection validation, and
    connection cleanup.

    Attributes:
        credentials: Snowflake credentials.
        max_connections: Maximum number of connections in the pool.
        connection_timeout: Connection timeout in seconds.
        idle_timeout: Idle timeout in seconds.
        connections: Dictionary of connections.
        connection_locks: Dictionary of connection locks.
        pool_lock: Lock for the connection pool.
    """

    def __init__(
        self,
        credentials: Dict[str, str],
        max_connections: int = 10,
        connection_timeout: int = 10,
        idle_timeout: int = 300,
    ):
        """Initialize the Snowflake connection pool.

        Args:
            credentials: Snowflake credentials.
            max_connections: Maximum number of connections in the pool.
            connection_timeout: Connection timeout in seconds.
            idle_timeout: Idle timeout in seconds.

        Raises:
            ImportError: If Snowflake connector is not available.
        """
        if not SNOWFLAKE_AVAILABLE:
            raise ImportError(
                "Snowflake connector is not available. Please install it with 'pip install snowflake-connector-python'."
            )

        self.credentials = credentials
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.idle_timeout = idle_timeout
        self.connections = (
            {}
        )  # {connection_id: {"connection": conn, "last_used": timestamp}}
        self.connection_locks = {}  # {connection_id: lock}
        self.pool_lock = threading.Lock()

        # Validate credentials
        required_keys = [
            "account",
            "user",
            "password",
            "database",
            "schema",
            "warehouse",
        ]
        missing_keys = [key for key in required_keys if key not in credentials]
        if missing_keys:
            raise ValueError(f"Missing Snowflake credentials: {missing_keys}")

        logger.info(
            f"Initialized Snowflake connection pool with max {max_connections} connections"
        )

    def get_connection(self) -> SnowflakeConnection:
        """Get a connection from the pool.

        Returns:
            Snowflake connection.

        Raises:
            Exception: If unable to get a connection.
        """
        with self.pool_lock:
            # Clean up idle connections
            self._cleanup_idle_connections()

            # Try to reuse an existing connection
            for connection_id, conn_info in self.connections.items():
                if not self.connection_locks[connection_id].locked():
                    # Validate connection before reusing
                    if self._validate_connection(conn_info["connection"]):
                        logger.debug(f"Reusing connection {connection_id}")
                        self.connection_locks[connection_id].acquire()
                        conn_info["last_used"] = time.time()
                        return conn_info["connection"]
                    else:
                        # Connection is invalid, remove it
                        logger.debug(f"Removing invalid connection {connection_id}")
                        self._close_connection(conn_info["connection"])
                        del self.connections[connection_id]
                        del self.connection_locks[connection_id]

            # Create a new connection if pool is not full
            if len(self.connections) < self.max_connections:
                connection_id = len(self.connections)
                logger.debug(f"Creating new connection {connection_id}")
                connection = self._create_connection()
                self.connections[connection_id] = {
                    "connection": connection,
                    "last_used": time.time(),
                }
                self.connection_locks[connection_id] = threading.Lock()
                self.connection_locks[connection_id].acquire()
                return connection

            # Wait for a connection to become available
            logger.debug("Waiting for a connection to become available")
            start_time = time.time()
            while time.time() - start_time < self.connection_timeout:
                for connection_id, conn_info in self.connections.items():
                    if not self.connection_locks[connection_id].locked():
                        # Validate connection before reusing
                        if self._validate_connection(conn_info["connection"]):
                            logger.debug(
                                f"Reusing connection {connection_id} after waiting"
                            )
                            self.connection_locks[connection_id].acquire()
                            conn_info["last_used"] = time.time()
                            return conn_info["connection"]
                        else:
                            # Connection is invalid, remove it
                            logger.debug(f"Removing invalid connection {connection_id}")
                            self._close_connection(conn_info["connection"])
                            del self.connections[connection_id]
                            del self.connection_locks[connection_id]

                            # Create a new connection
                            connection_id = len(self.connections)
                            logger.debug(f"Creating new connection {connection_id}")
                            connection = self._create_connection()
                            self.connections[connection_id] = {
                                "connection": connection,
                                "last_used": time.time(),
                            }
                            self.connection_locks[connection_id] = threading.Lock()
                            self.connection_locks[connection_id].acquire()
                            return connection
                time.sleep(0.1)

            # Timeout waiting for a connection
            raise Exception("Timeout waiting for a Snowflake connection")

    def release_connection(self, connection: SnowflakeConnection) -> None:
        """Release a connection back to the pool.

        Args:
            connection: Snowflake connection to release.
        """
        with self.pool_lock:
            for connection_id, conn_info in self.connections.items():
                if conn_info["connection"] == connection:
                    logger.debug(f"Releasing connection {connection_id}")
                    conn_info["last_used"] = time.time()
                    self.connection_locks[connection_id].release()
                    return

            logger.warning("Attempted to release a connection not in the pool")

    def _create_connection(self) -> SnowflakeConnection:
        """Create a new Snowflake connection.

        Returns:
            Snowflake connection.

        Raises:
            Exception: If unable to create a connection.
        """
        try:
            connection = snowflake.connector.connect(
                account=self.credentials["account"],
                user=self.credentials["user"],
                password=self.credentials["password"],
                database=self.credentials["database"],
                schema=self.credentials["schema"],
                warehouse=self.credentials["warehouse"],
                role=self.credentials.get("role"),
                autocommit=True,
            )
            return connection
        except Exception as e:
            logger.error(f"Failed to create Snowflake connection: {e}")
            raise

    def _validate_connection(self, connection: SnowflakeConnection) -> bool:
        """Validate a Snowflake connection.

        Args:
            connection: Snowflake connection to validate.

        Returns:
            Whether the connection is valid.
        """
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception as e:
            logger.warning(f"Invalid Snowflake connection: {e}")
            return False

    def _close_connection(self, connection: SnowflakeConnection) -> None:
        """Close a Snowflake connection.

        Args:
            connection: Snowflake connection to close.
        """
        try:
            connection.close()
        except Exception as e:
            logger.warning(f"Failed to close Snowflake connection: {e}")

    def _cleanup_idle_connections(self) -> None:
        """Clean up idle connections."""
        current_time = time.time()
        connection_ids_to_remove = []

        for connection_id, conn_info in self.connections.items():
            if (
                current_time - conn_info["last_used"] > self.idle_timeout
                and not self.connection_locks[connection_id].locked()
            ):
                connection_ids_to_remove.append(connection_id)

        for connection_id in connection_ids_to_remove:
            logger.debug(f"Removing idle connection {connection_id}")
            self._close_connection(self.connections[connection_id]["connection"])
            del self.connections[connection_id]
            del self.connection_locks[connection_id]

    def close_all_connections(self) -> None:
        """Close all connections in the pool."""
        with self.pool_lock:
            for connection_id, conn_info in self.connections.items():
                logger.debug(f"Closing connection {connection_id}")
                self._close_connection(conn_info["connection"])

            self.connections = {}
            self.connection_locks = {}

        logger.info("Closed all Snowflake connections")


class SnowflakeAuditLogger:
    """Snowflake audit logger.

    This class provides functionality for logging audit events to Snowflake.

    Attributes:
        connection_pool: Snowflake connection pool.
        table_name: Snowflake table name for audit logs.
    """

    def __init__(
        self,
        connection_pool: SnowflakeConnectionPool,
        table_name: str = "IMMUTA_AUDIT_LOGS",
    ):
        """Initialize the Snowflake audit logger.

        Args:
            connection_pool: Snowflake connection pool.
            table_name: Snowflake table name for audit logs.
        """
        self.connection_pool = connection_pool
        self.table_name = table_name

        # Ensure audit table exists
        self._ensure_audit_table_exists()

        logger.info(f"Initialized Snowflake audit logger with table {table_name}")

    def _ensure_audit_table_exists(self) -> None:
        """Ensure the audit table exists."""
        connection = self.connection_pool.get_connection()
        try:
            cursor = connection.cursor()

            # Validate table name to ensure it only contains allowed characters
            table_name = self.table_name
            if not re.match(r"^[A-Za-z0-9_]+$", table_name):
                raise ValueError(f"Invalid table name: {table_name}")

            # Use a safer approach with identifier quoting
            # This avoids SQL injection by properly quoting the table identifier
            # nosec B608 - table_name is validated above with regex
            cursor.execute(  # nosec
                f'CREATE TABLE IF NOT EXISTS "{table_name}" ('  # nosec
                f"    ID NUMBER AUTOINCREMENT,"
                f"    TIMESTAMP TIMESTAMP_NTZ DEFAULT CURRENT_TIMESTAMP(),"
                f"    OPERATION VARCHAR,"
                f"    STATUS VARCHAR,"
                f"    DETAILS VARIANT,"
                f"    ERROR VARCHAR,"
                f"    USER_ID VARCHAR,"
                f"    CLIENT_IP VARCHAR"
                f")"
            )

            cursor.close()
        finally:
            self.connection_pool.release_connection(connection)

    def log_audit_event(
        self,
        operation: str,
        status: str,
        details: Dict[str, Any],
        error: Optional[str] = None,
        user_id: Optional[str] = None,
        client_ip: Optional[str] = None,
    ) -> None:
        """Log an audit event to Snowflake.

        Args:
            operation: Operation name.
            status: Operation status.
            details: Operation details.
            error: Error message if any.
            user_id: User ID if available.
            client_ip: Client IP if available.
        """
        # Sanitize details to remove sensitive information
        sanitized_details = self._sanitize_details(details)

        connection = self.connection_pool.get_connection()
        try:
            cursor = connection.cursor()
            # Validate table name to ensure it only contains allowed characters
            table_name = self.table_name
            if not re.match(r"^[A-Za-z0-9_]+$", table_name):
                raise ValueError(f"Invalid table name: {table_name}")

            # Use a safer approach with identifier quoting
            # This avoids SQL injection by properly quoting the table identifier
            # and not using string concatenation in the SQL statement
            # nosec B608 - table_name is validated above with regex
            cursor.execute(  # nosec
                f'INSERT INTO "{table_name}" (OPERATION, STATUS, DETAILS, ERROR, USER_ID, CLIENT_IP) VALUES (%s, %s, %s, %s, %s, %s)',  # nosec
                (
                    operation,
                    status,
                    json.dumps(sanitized_details),
                    error,
                    user_id,
                    client_ip,
                ),
            )
            cursor.close()
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
        finally:
            self.connection_pool.release_connection(connection)

    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize details to remove sensitive information.

        Args:
            details: Operation details.

        Returns:
            Sanitized details.
        """
        # Create a copy of the details to avoid modifying the original
        sanitized = details.copy()

        # Sanitize email addresses
        if "email" in sanitized:
            sanitized["email"] = self._sanitize_email(sanitized["email"])

        # Sanitize user IDs
        if "user_id" in sanitized:
            sanitized["user_id"] = f"user_{hash(str(sanitized['user_id'])) % 10000}"

        # Sanitize nested dictionaries
        for key, value in sanitized.items():
            if isinstance(value, dict):
                sanitized[key] = self._sanitize_details(value)

        return sanitized

    def _sanitize_email(self, email: str) -> str:
        """Sanitize an email address.

        Args:
            email: Email address.

        Returns:
            Sanitized email address.
        """
        if "@" in email:
            username, domain = email.split("@", 1)
            return f"{username[0]}***@{domain}"
        return email
