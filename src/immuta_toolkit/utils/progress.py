"""Progress visualization utilities."""

import time
import threading
from typing import Optional, Dict, Any, List, Tuple, Callable, Union

from rich.console import Console
from rich.progress import (
    Progress,
    BarColumn,
    TextColumn,
    TimeElapsedColumn,
    TimeRemainingColumn,
    SpinnerColumn,
    ProgressColumn,
)
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.live import Live

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


class ProgressManager:
    """Progress manager for batch operations.
    
    This class provides a centralized manager for progress visualization
    of batch operations.
    
    Attributes:
        progress: Rich progress instance.
        tasks: Dictionary of tasks.
        lock: Thread lock for thread safety.
    """
    
    def __init__(self):
        """Initialize the progress manager."""
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("({task.completed}/{task.total})"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
        )
        self.tasks: Dict[str, int] = {}
        self.lock = threading.Lock()
    
    def add_task(
        self,
        description: str,
        total: Optional[int] = None,
        task_id: Optional[str] = None,
    ) -> str:
        """Add a task to the progress manager.
        
        Args:
            description: Task description.
            total: Total number of steps.
            task_id: Task ID. If None, a unique ID will be generated.
            
        Returns:
            Task ID.
        """
        with self.lock:
            # Generate task ID if not provided
            if task_id is None:
                task_id = f"task_{len(self.tasks) + 1}"
            
            # Add task
            task = self.progress.add_task(description, total=total)
            self.tasks[task_id] = task
            
            return task_id
    
    def update_task(
        self,
        task_id: str,
        advance: Optional[int] = None,
        completed: Optional[int] = None,
        total: Optional[int] = None,
        description: Optional[str] = None,
    ) -> None:
        """Update a task in the progress manager.
        
        Args:
            task_id: Task ID.
            advance: Number of steps to advance.
            completed: Number of completed steps.
            total: Total number of steps.
            description: Task description.
        """
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                kwargs = {}
                
                if advance is not None:
                    kwargs["advance"] = advance
                if completed is not None:
                    kwargs["completed"] = completed
                if total is not None:
                    kwargs["total"] = total
                if description is not None:
                    kwargs["description"] = description
                
                self.progress.update(task, **kwargs)
    
    def remove_task(self, task_id: str) -> None:
        """Remove a task from the progress manager.
        
        Args:
            task_id: Task ID.
        """
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                self.progress.remove_task(task)
                del self.tasks[task_id]
    
    def __enter__(self):
        """Enter context manager."""
        self.progress.__enter__()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.progress.__exit__(exc_type, exc_val, exc_tb)


class BatchProgressCallback:
    """Batch progress callback for parallel processing.
    
    This class provides a callback function for parallel processing
    that updates a progress bar.
    
    Attributes:
        progress_manager: Progress manager instance.
        task_id: Task ID.
    """
    
    def __init__(
        self,
        progress_manager: ProgressManager,
        task_id: str,
    ):
        """Initialize the batch progress callback.
        
        Args:
            progress_manager: Progress manager instance.
            task_id: Task ID.
        """
        self.progress_manager = progress_manager
        self.task_id = task_id
    
    def __call__(self, processed: int, total: int) -> None:
        """Update progress.
        
        Args:
            processed: Number of processed items.
            total: Total number of items.
        """
        self.progress_manager.update_task(
            task_id=self.task_id,
            completed=processed,
            total=total,
        )


class StatusTable:
    """Status table for batch operations.
    
    This class provides a live-updating status table for batch operations.
    
    Attributes:
        title: Table title.
        columns: Table columns.
        rows: Table rows.
        lock: Thread lock for thread safety.
        live: Rich live instance.
    """
    
    def __init__(
        self,
        title: str,
        columns: List[Tuple[str, str]],
    ):
        """Initialize the status table.
        
        Args:
            title: Table title.
            columns: List of (name, style) tuples for columns.
        """
        self.title = title
        self.columns = columns
        self.rows: List[List[str]] = []
        self.lock = threading.Lock()
        self.live = Live(self._generate_table(), refresh_per_second=4)
    
    def _generate_table(self) -> Table:
        """Generate the table.
        
        Returns:
            Rich table instance.
        """
        table = Table(title=self.title)
        
        # Add columns
        for name, style in self.columns:
            table.add_column(name, style=style)
        
        # Add rows
        for row in self.rows:
            table.add_row(*row)
        
        return table
    
    def add_row(self, *values: str) -> None:
        """Add a row to the table.
        
        Args:
            *values: Row values.
        """
        with self.lock:
            self.rows.append(list(values))
            self.live.update(self._generate_table())
    
    def update_row(self, row_index: int, column_index: int, value: str) -> None:
        """Update a cell in the table.
        
        Args:
            row_index: Row index.
            column_index: Column index.
            value: New value.
        """
        with self.lock:
            if 0 <= row_index < len(self.rows) and 0 <= column_index < len(self.columns):
                self.rows[row_index][column_index] = value
                self.live.update(self._generate_table())
    
    def clear(self) -> None:
        """Clear the table."""
        with self.lock:
            self.rows.clear()
            self.live.update(self._generate_table())
    
    def __enter__(self):
        """Enter context manager."""
        self.live.__enter__()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        self.live.__exit__(exc_type, exc_val, exc_tb)


# Global progress manager instance
progress_manager = ProgressManager()


def get_progress_manager() -> ProgressManager:
    """Get the global progress manager instance.
    
    Returns:
        Progress manager instance.
    """
    return progress_manager


def create_progress_callback(
    description: str,
    total: Optional[int] = None,
    task_id: Optional[str] = None,
) -> Tuple[BatchProgressCallback, ProgressManager]:
    """Create a progress callback for parallel processing.
    
    Args:
        description: Task description.
        total: Total number of steps.
        task_id: Task ID. If None, a unique ID will be generated.
        
    Returns:
        Tuple of (progress callback, progress manager).
    """
    manager = get_progress_manager()
    task_id = manager.add_task(description, total=total, task_id=task_id)
    callback = BatchProgressCallback(manager, task_id)
    
    return callback, manager
