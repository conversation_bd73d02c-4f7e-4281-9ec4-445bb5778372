"""Backup utilities for the Immuta SRE Toolkit."""

import time
from typing import Any, Dict, Optional, Union, List, Callable

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def create_backup(
    storage_client: Any,
    data: Union[Dict[str, Any], List[Dict[str, Any]]],
    resource_type: str,
    resource_id: Optional[Union[int, str]] = None,
    operation: str = "update",
    timestamp: Optional[int] = None,
    metadata: Optional[Dict[str, str]] = None,
) -> Optional[str]:
    """Create a backup of a resource.

    Args:
        storage_client: Storage client to use for backup.
        data: Data to backup.
        resource_type: Type of resource (e.g., "user", "data_source").
        resource_id: ID of the resource. If None, will backup all resources of the type.
        operation: Operation being performed (e.g., "create", "update", "delete").
        timestamp: Timestamp to use for the backup. If None, will use current time.
        metadata: Additional metadata to store with the backup.

    Returns:
        Blob name if backup was created, None otherwise.
    """
    if not storage_client:
        logger.debug("No storage client provided, skipping backup")
        return None

    # Generate blob name
    timestamp = timestamp or int(time.time())
    if resource_id is not None:
        blob_name = f"{resource_type}_{resource_id}_{timestamp}.json"
    else:
        blob_name = f"{resource_type}s_{timestamp}.json"

    # Create metadata
    backup_metadata = {
        "type": resource_type,
        "operation": f"{resource_type}_{operation}",
    }

    if resource_id is not None:
        backup_metadata["id"] = str(resource_id)

    # Add additional metadata
    if metadata:
        backup_metadata.update(metadata)

    try:
        storage_client.upload_file(
            data,
            blob_name,
            metadata=backup_metadata,
        )
        logger.info(f"Created backup {blob_name}")
        return blob_name
    except Exception as e:
        logger.warning(f"Failed to create backup: {e}")
        return None


def with_backup(
    func: Callable,
    storage_client: Any,
    resource_type: str,
    get_resource_func: Optional[Callable] = None,
    resource_id_arg_name: str = "id",
    operation: str = "update",
) -> Callable:
    """Decorator to create a backup before executing a function.

    Args:
        func: Function to decorate.
        storage_client: Storage client to use for backup.
        resource_type: Type of resource (e.g., "user", "data_source").
        get_resource_func: Function to get the resource data. If None, will use the first argument.
        resource_id_arg_name: Name of the argument that contains the resource ID.
        operation: Operation being performed (e.g., "create", "update", "delete").

    Returns:
        Decorated function.
    """
    def wrapper(*args, **kwargs):
        # Check if backup is enabled
        backup_enabled = kwargs.get("backup", True)
        if not backup_enabled or not storage_client:
            return func(*args, **kwargs)

        # Get resource ID
        resource_id = kwargs.get(resource_id_arg_name)
        if resource_id is None and args and hasattr(args[0], "id"):
            resource_id = args[0].id

        # Get resource data
        resource_data = None
        if get_resource_func and resource_id is not None:
            try:
                resource_data = get_resource_func(resource_id)
            except Exception as e:
                logger.warning(f"Failed to get resource data for backup: {e}")

        # Create backup
        blob_name = None
        if resource_data:
            blob_name = create_backup(
                storage_client=storage_client,
                data=resource_data,
                resource_type=resource_type,
                resource_id=resource_id,
                operation=operation,
            )

        # Add backup blob name to kwargs
        if blob_name:
            kwargs["backup_blob_name"] = blob_name

        # Execute function
        return func(*args, **kwargs)

    return wrapper
