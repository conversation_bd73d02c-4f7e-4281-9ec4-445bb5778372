"""Logging utilities for the Immuta SRE Toolkit."""

import logging
import os
import sys
from typing import Optional, Union

from rich.console import Console
from rich.logging import RichHandler

# Create console for rich output
console = Console()

# Configure logging
LOG_LEVEL = os.getenv("IMMUTA_LOG_LEVEL", "INFO").upper()
LOG_FORMAT = "%(message)s"
LOG_DATE_FORMAT = "[%Y-%m-%d %H:%M:%S]"


def get_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """Get a logger with the specified name and level.

    Args:
        name: Logger name.
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
            Defaults to the value of the IMMUTA_LOG_LEVEL environment variable,
            or INFO if not set.

    Returns:
        Logger instance.
    """
    # Use the specified level or the default
    log_level = level.upper() if level else LOG_LEVEL

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level))

    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Add rich handler for console output
    rich_handler = RichHandler(
        rich_tracebacks=True,
        console=console,
        show_time=False,
        show_path=False,
    )
    rich_handler.setFormatter(logging.Formatter(LOG_FORMAT, datefmt=LOG_DATE_FORMAT))
    logger.addHandler(rich_handler)

    # Add file handler if log file is specified
    log_file = os.getenv("IMMUTA_LOG_FILE")
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                datefmt=LOG_DATE_FORMAT,
            )
        )
        logger.addHandler(file_handler)

    return logger


def setup_logging(level: str = "INFO", log_file: Optional[str] = None) -> None:
    """Set up logging for the application.

    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL).
        log_file: Path to log file. If None, logs will only be output to console.
    """
    # Set environment variables for get_logger
    os.environ["IMMUTA_LOG_LEVEL"] = level.upper()
    if log_file:
        os.environ["IMMUTA_LOG_FILE"] = log_file

    # Configure root logger
    root_logger = get_logger("immuta_toolkit")

    # Log the configuration
    root_logger.info(f"Logging initialized at level {level}")
    if log_file:
        root_logger.info(f"Logging to file: {log_file}")

    # Suppress verbose logging from libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("azure").setLevel(logging.WARNING)


def set_log_level(logger_name: str, level: Union[str, int]) -> None:
    """Set the log level for a specific logger.

    Args:
        logger_name: Name of the logger to set the level for.
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL) or logging level constant.
    """
    if isinstance(level, str):
        level = getattr(logging, level.upper())

    logger = logging.getLogger(logger_name)
    logger.setLevel(level)

    # Update handlers to match the new level
    for handler in logger.handlers:
        handler.setLevel(level)
