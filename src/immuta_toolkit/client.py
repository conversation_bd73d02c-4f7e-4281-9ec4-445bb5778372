"""Mock client module for testing."""

from immuta_toolkit.services.backup_service import BackupService
from immuta_toolkit.services.project_service import ProjectService

class ImmutaClient:
    """Mock Immuta client for testing."""

    def __init__(self, api_key=None, base_url=None, is_local=False, notifier=None, storage=None):
        """Initialize the client."""
        self.api_key = api_key
        self.base_url = base_url
        self.is_local = is_local
        self.notifier = notifier
        self.storage = storage

        # Initialize services
        self.backup_service = BackupService(self)
        self.project_service = ProjectService(self)

    def make_request(self, method, endpoint, data=None, params=None, headers=None):
        """Make a request to the Immuta API."""
        class MockResponse:
            def json(self):
                return data or {}

        return MockResponse()

    def get_users(self, **kwargs):
        """Get users."""
        return []

    def get_user(self, user_id):
        """Get a user."""
        return {"id": user_id, "username": f"user_{user_id}"}

    def create_user(self, **kwargs):
        """Create a user."""
        return {"id": 1, "username": kwargs.get("username", "new_user")}

    def update_user(self, user_id, **kwargs):
        """Update a user."""
        return {"id": user_id, "username": kwargs.get("username", f"user_{user_id}")}

    def delete_user(self, user_id):
        """Delete a user."""
        return True

    def get_data_sources(self, **kwargs):
        """Get data sources."""
        return []

    def get_data_source(self, data_source_id):
        """Get a data source."""
        return {"id": data_source_id, "name": f"data_source_{data_source_id}"}

    def create_data_source(self, **kwargs):
        """Create a data source."""
        return {"id": 1, "name": kwargs.get("name", "new_data_source")}

    def update_data_source(self, data_source_id, **kwargs):
        """Update a data source."""
        return {"id": data_source_id, "name": kwargs.get("name", f"data_source_{data_source_id}")}

    def delete_data_source(self, data_source_id):
        """Delete a data source."""
        return True

    def get_policies(self, **kwargs):
        """Get policies."""
        return []

    def get_policy(self, policy_id):
        """Get a policy."""
        return {"id": policy_id, "name": f"policy_{policy_id}"}

    def create_policy(self, **kwargs):
        """Create a policy."""
        return {"id": 1, "name": kwargs.get("name", "new_policy")}

    def update_policy(self, policy_id, **kwargs):
        """Update a policy."""
        return {"id": policy_id, "name": kwargs.get("name", f"policy_{policy_id}")}

    def delete_policy(self, policy_id):
        """Delete a policy."""
        return True

    def get_projects(self, **kwargs):
        """Get projects."""
        return []

    def get_project(self, project_id):
        """Get a project."""
        return {"id": project_id, "name": f"project_{project_id}"}

    def create_project(self, **kwargs):
        """Create a project."""
        return {"id": 1, "name": kwargs.get("name", "new_project")}

    def update_project(self, project_id, **kwargs):
        """Update a project."""
        return {"id": project_id, "name": kwargs.get("name", f"project_{project_id}")}

    def delete_project(self, project_id):
        """Delete a project."""
        return True

    def get_project_members(self, project_id):
        """Get project members."""
        return [{"id": 1, "name": "User 1", "role": "MEMBER"}]

    def get_project_data_sources(self, project_id):
        """Get project data sources."""
        return [{"id": 1, "name": "Data Source 1"}]

    def get_project_purposes(self, project_id):
        """Get project purposes."""
        return [{"id": 1, "name": "Purpose 1"}]

    def add_project_member(self, project_id, user_id, role):
        """Add a member to a project."""
        return {"success": True, "project_id": project_id, "user_id": user_id}

    def add_data_source_to_project(self, project_id, data_source_id):
        """Add a data source to a project."""
        return {"success": True, "project_id": project_id, "data_source_id": data_source_id}

    def add_purpose_to_project(self, project_id, purpose_id):
        """Add a purpose to a project."""
        return {"success": True, "project_id": project_id, "purpose_id": purpose_id}
