"""FastAPI application for Immuta SRE Toolkit local service."""

import os
from typing import Optional

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.service.routes import (
    users_router,
    data_sources_router,
    policies_router,
    projects_router,
    operations_router,
)
from immuta_toolkit.service.middleware.logging import LoggingMiddleware
from immuta_toolkit.service.middleware.error_handler import ErrorHandlerMiddleware

logger = get_logger(__name__)


def create_app(
    title: str = "Immuta SRE Toolkit API",
    description: str = "Local API for Immuta SRE Toolkit",
    version: str = "0.1.0",
    debug: bool = False,
) -> FastAPI:
    """Create a FastAPI application.
    
    Args:
        title: API title.
        description: API description.
        version: API version.
        debug: Whether to enable debug mode.
        
    Returns:
        FastAPI application.
    """
    app = FastAPI(
        title=title,
        description=description,
        version=version,
        debug=debug,
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add custom middleware
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(ErrorHandlerMiddleware)
    
    # Include routers
    app.include_router(users_router, prefix="/api")
    app.include_router(data_sources_router, prefix="/api")
    app.include_router(policies_router, prefix="/api")
    app.include_router(projects_router, prefix="/api")
    app.include_router(operations_router, prefix="/api")
    
    @app.get("/")
    def read_root():
        """Root endpoint.
        
        Returns:
            Welcome message.
        """
        return {
            "message": "Welcome to Immuta SRE Toolkit API",
            "version": version,
            "docs_url": "/docs",
            "redoc_url": "/redoc",
        }
    
    @app.get("/health")
    def health_check():
        """Health check endpoint.
        
        Returns:
            Health status.
        """
        return {"status": "healthy"}
    
    return app
