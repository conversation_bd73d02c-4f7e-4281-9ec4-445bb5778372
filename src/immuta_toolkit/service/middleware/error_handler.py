"""Error handler middleware for Immuta SRE Toolkit local service."""

import traceback
from typing import Callable

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware for handling errors.
    
    This middleware catches unhandled exceptions and returns a JSON response
    with error details.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process a request and handle errors.
        
        Args:
            request: Incoming request.
            call_next: Function to call the next middleware or route handler.
            
        Returns:
            Response from the next middleware or route handler, or an error response.
        """
        try:
            return await call_next(request)
        except Exception as e:
            # Log the exception
            logger.error(f"Unhandled exception: {str(e)}")
            logger.debug(traceback.format_exc())
            
            # Return a JSON response with error details
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Internal server error",
                    "message": str(e),
                },
            )
