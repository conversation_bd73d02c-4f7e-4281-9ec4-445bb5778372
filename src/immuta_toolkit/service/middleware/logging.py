"""Logging middleware for Immuta SRE Toolkit local service."""

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging requests and responses.
    
    This middleware logs information about incoming requests and outgoing
    responses, including method, path, status code, and execution time.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process a request and log information.
        
        Args:
            request: Incoming request.
            call_next: Function to call the next middleware or route handler.
            
        Returns:
            Response from the next middleware or route handler.
        """
        start_time = time.time()
        
        # Log request
        logger.info(f"Request: {request.method} {request.url.path}")
        
        # Process request
        try:
            response = await call_next(request)
            
            # Log response
            execution_time = time.time() - start_time
            logger.info(
                f"Response: {request.method} {request.url.path} - "
                f"Status: {response.status_code} - "
                f"Time: {execution_time:.3f}s"
            )
            
            return response
        except Exception as e:
            # Log exception
            execution_time = time.time() - start_time
            logger.error(
                f"Exception: {request.method} {request.url.path} - "
                f"Error: {str(e)} - "
                f"Time: {execution_time:.3f}s"
            )
            raise
