"""Policy routes for Immuta SRE Toolkit local service."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models.policy import PolicyModel, PolicyQueryParameters
from immuta_toolkit.operations.policies import (
    ListPoliciesOperation,
    GetPolicyOperation,
    CreatePolicyOperation,
    UpdatePolicyOperation,
    DeletePolicyOperation,
)
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/policies",
    tags=["policies"],
)


def get_operation_engine() -> OperationEngine:
    """Get or create the operation engine.

    Returns:
        Operation engine instance.
    """
    return OperationEngine.get_instance()


@router.get("/", response_model=List[PolicyModel])
def list_policies(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    policy_type: Optional[str] = None,
    data_source_id: Optional[str] = None,
    engine: OperationEngine = Depends(get_operation_engine),
) -> List[PolicyModel]:
    """List policies.

    Args:
        limit: Maximum number of policies to return.
        offset: Offset for pagination.
        search: Search term.
        policy_type: Filter by policy type.
        data_source_id: Filter by data source ID.
        engine: Operation engine.

    Returns:
        List of policies.
    """
    operation = engine.get_operation(ListPoliciesOperation)
    params = PolicyQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
        policy_type=policy_type,
        data_source_id=data_source_id,
    )
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list policies: {result.error}",
        )
    
    return result.data


@router.get("/{policy_id}", response_model=PolicyModel)
def get_policy(
    policy_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> PolicyModel:
    """Get a policy by ID.

    Args:
        policy_id: Policy ID.
        engine: Operation engine.

    Returns:
        Policy.

    Raises:
        HTTPException: If the policy is not found.
    """
    operation = engine.get_operation(GetPolicyOperation)
    result = operation.execute(policy_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found: {result.error}",
        )
    
    return result.data


@router.post("/", response_model=PolicyModel, status_code=status.HTTP_201_CREATED)
def create_policy(
    policy: PolicyModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> PolicyModel:
    """Create a policy.

    Args:
        policy: Policy to create.
        engine: Operation engine.

    Returns:
        Created policy.
    """
    operation = engine.get_operation(CreatePolicyOperation)
    result = operation.execute(policy)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create policy: {result.error}",
        )
    
    return result.data


@router.put("/{policy_id}", response_model=PolicyModel)
def update_policy(
    policy_id: str,
    policy: PolicyModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> PolicyModel:
    """Update a policy.

    Args:
        policy_id: Policy ID.
        policy: Updated policy.
        engine: Operation engine.

    Returns:
        Updated policy.

    Raises:
        HTTPException: If the policy is not found.
    """
    operation = engine.get_operation(UpdatePolicyOperation)
    params = {
        "policy_id": policy_id,
        "policy": policy,
    }
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found or update failed: {result.error}",
        )
    
    return result.data


@router.delete("/{policy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policy(
    policy_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> None:
    """Delete a policy.

    Args:
        policy_id: Policy ID.
        engine: Operation engine.

    Raises:
        HTTPException: If the policy is not found.
    """
    operation = engine.get_operation(DeletePolicyOperation)
    result = operation.execute(policy_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found or deletion failed: {result.error}",
        )
