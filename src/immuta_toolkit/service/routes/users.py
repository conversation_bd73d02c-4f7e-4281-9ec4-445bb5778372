"""User routes for Immuta SRE Toolkit local service."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models.user import UserModel, UserQueryParameters
from immuta_toolkit.operations.users import (
    ListUsersOperation,
    GetUserOperation,
    CreateUserOperation,
    UpdateUserOperation,
    DeleteUserOperation,
)
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/users",
    tags=["users"],
)


def get_operation_engine() -> OperationEngine:
    """Get or create the operation engine.

    Returns:
        Operation engine instance.
    """
    return OperationEngine.get_instance()


@router.get("/", response_model=List[UserModel])
def list_users(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    engine: OperationEngine = Depends(get_operation_engine),
) -> List[UserModel]:
    """List users.

    Args:
        limit: Maximum number of users to return.
        offset: Offset for pagination.
        search: Search term.
        engine: Operation engine.

    Returns:
        List of users.
    """
    operation = engine.get_operation(ListUsersOperation)
    params = UserQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
    )
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list users: {result.error}",
        )
    
    return result.data


@router.get("/{user_id}", response_model=UserModel)
def get_user(
    user_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> UserModel:
    """Get a user by ID.

    Args:
        user_id: User ID.
        engine: Operation engine.

    Returns:
        User.

    Raises:
        HTTPException: If the user is not found.
    """
    operation = engine.get_operation(GetUserOperation)
    result = operation.execute(user_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found: {result.error}",
        )
    
    return result.data


@router.post("/", response_model=UserModel, status_code=status.HTTP_201_CREATED)
def create_user(
    user: UserModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> UserModel:
    """Create a user.

    Args:
        user: User to create.
        engine: Operation engine.

    Returns:
        Created user.
    """
    operation = engine.get_operation(CreateUserOperation)
    result = operation.execute(user)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create user: {result.error}",
        )
    
    return result.data


@router.put("/{user_id}", response_model=UserModel)
def update_user(
    user_id: str,
    user: UserModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> UserModel:
    """Update a user.

    Args:
        user_id: User ID.
        user: Updated user.
        engine: Operation engine.

    Returns:
        Updated user.

    Raises:
        HTTPException: If the user is not found.
    """
    operation = engine.get_operation(UpdateUserOperation)
    params = {
        "user_id": user_id,
        "user": user,
    }
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found or update failed: {result.error}",
        )
    
    return result.data


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(
    user_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> None:
    """Delete a user.

    Args:
        user_id: User ID.
        engine: Operation engine.

    Raises:
        HTTPException: If the user is not found.
    """
    operation = engine.get_operation(DeleteUserOperation)
    result = operation.execute(user_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found or deletion failed: {result.error}",
        )
