"""Project routes for Immuta SRE Toolkit local service."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models.project import ProjectModel, ProjectQueryParameters
from immuta_toolkit.operations.projects import (
    ListProjectsOperation,
    GetProjectOperation,
    CreateProjectOperation,
    UpdateProjectOperation,
    DeleteProjectOperation,
)
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/projects",
    tags=["projects"],
)


def get_operation_engine() -> OperationEngine:
    """Get or create the operation engine.

    Returns:
        Operation engine instance.
    """
    return OperationEngine.get_instance()


@router.get("/", response_model=List[ProjectModel])
def list_projects(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    engine: OperationEngine = Depends(get_operation_engine),
) -> List[ProjectModel]:
    """List projects.

    Args:
        limit: Maximum number of projects to return.
        offset: Offset for pagination.
        search: Search term.
        engine: Operation engine.

    Returns:
        List of projects.
    """
    operation = engine.get_operation(ListProjectsOperation)
    params = ProjectQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
    )
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list projects: {result.error}",
        )
    
    return result.data


@router.get("/{project_id}", response_model=ProjectModel)
def get_project(
    project_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> ProjectModel:
    """Get a project by ID.

    Args:
        project_id: Project ID.
        engine: Operation engine.

    Returns:
        Project.

    Raises:
        HTTPException: If the project is not found.
    """
    operation = engine.get_operation(GetProjectOperation)
    result = operation.execute(project_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found: {result.error}",
        )
    
    return result.data


@router.post("/", response_model=ProjectModel, status_code=status.HTTP_201_CREATED)
def create_project(
    project: ProjectModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> ProjectModel:
    """Create a project.

    Args:
        project: Project to create.
        engine: Operation engine.

    Returns:
        Created project.
    """
    operation = engine.get_operation(CreateProjectOperation)
    result = operation.execute(project)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create project: {result.error}",
        )
    
    return result.data


@router.put("/{project_id}", response_model=ProjectModel)
def update_project(
    project_id: str,
    project: ProjectModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> ProjectModel:
    """Update a project.

    Args:
        project_id: Project ID.
        project: Updated project.
        engine: Operation engine.

    Returns:
        Updated project.

    Raises:
        HTTPException: If the project is not found.
    """
    operation = engine.get_operation(UpdateProjectOperation)
    params = {
        "project_id": project_id,
        "project": project,
    }
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found or update failed: {result.error}",
        )
    
    return result.data


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_project(
    project_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> None:
    """Delete a project.

    Args:
        project_id: Project ID.
        engine: Operation engine.

    Raises:
        HTTPException: If the project is not found.
    """
    operation = engine.get_operation(DeleteProjectOperation)
    result = operation.execute(project_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found or deletion failed: {result.error}",
        )
