"""Operation routes for Immuta SRE Toolkit local service."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.operations.base import OperationResult
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/operations",
    tags=["operations"],
)


def get_operation_engine() -> OperationEngine:
    """Get or create the operation engine.

    Returns:
        Operation engine instance.
    """
    return OperationEngine.get_instance()


@router.get("/", response_model=List[str])
def list_operations(
    engine: OperationEngine = Depends(get_operation_engine),
) -> List[str]:
    """List available operations.

    Args:
        engine: Operation engine.

    Returns:
        List of operation names.
    """
    return engine.list_operations()


@router.post("/{operation_name}", response_model=Dict[str, Any])
def execute_operation(
    operation_name: str,
    params: Dict[str, Any],
    engine: OperationEngine = Depends(get_operation_engine),
) -> Dict[str, Any]:
    """Execute an operation.

    Args:
        operation_name: Name of the operation to execute.
        params: Operation parameters.
        engine: Operation engine.

    Returns:
        Operation result.

    Raises:
        HTTPException: If the operation is not found or execution fails.
    """
    if operation_name not in engine.list_operations():
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Operation {operation_name} not found",
        )
    
    result = engine.execute_operation(operation_name, params)
    
    if result is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to execute operation {operation_name}",
        )
    
    # Convert OperationResult to dict
    return {
        "status": result.status,
        "data": result.data,
        "error": result.error,
        "execution_time": result.execution_time,
        "api_used": result.api_used,
        "web_used": result.web_used,
        "attempts": result.attempts,
        "timestamp": result.timestamp,
    }
