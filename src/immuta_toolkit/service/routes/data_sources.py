"""Data source routes for Immuta SRE Toolkit local service."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models.data_source import DataSourceModel, DataSourceQueryParameters
from immuta_toolkit.operations.data_sources import (
    ListDataSourcesOperation,
    GetDataSourceOperation,
    CreateDataSourceOperation,
    UpdateDataSourceOperation,
    DeleteDataSourceOperation,
)
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/data-sources",
    tags=["data_sources"],
)


def get_operation_engine() -> OperationEngine:
    """Get or create the operation engine.

    Returns:
        Operation engine instance.
    """
    return OperationEngine.get_instance()


@router.get("/", response_model=List[DataSourceModel])
def list_data_sources(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    tag: Optional[str] = None,
    engine: OperationEngine = Depends(get_operation_engine),
) -> List[DataSourceModel]:
    """List data sources.

    Args:
        limit: Maximum number of data sources to return.
        offset: Offset for pagination.
        search: Search term.
        tag: Filter by tag.
        engine: Operation engine.

    Returns:
        List of data sources.
    """
    operation = engine.get_operation(ListDataSourcesOperation)
    params = DataSourceQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
        tag=tag,
    )
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list data sources: {result.error}",
        )
    
    return result.data


@router.get("/{data_source_id}", response_model=DataSourceModel)
def get_data_source(
    data_source_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> DataSourceModel:
    """Get a data source by ID.

    Args:
        data_source_id: Data source ID.
        engine: Operation engine.

    Returns:
        Data source.

    Raises:
        HTTPException: If the data source is not found.
    """
    operation = engine.get_operation(GetDataSourceOperation)
    result = operation.execute(data_source_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found: {result.error}",
        )
    
    return result.data


@router.post("/", response_model=DataSourceModel, status_code=status.HTTP_201_CREATED)
def create_data_source(
    data_source: DataSourceModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> DataSourceModel:
    """Create a data source.

    Args:
        data_source: Data source to create.
        engine: Operation engine.

    Returns:
        Created data source.
    """
    operation = engine.get_operation(CreateDataSourceOperation)
    result = operation.execute(data_source)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create data source: {result.error}",
        )
    
    return result.data


@router.put("/{data_source_id}", response_model=DataSourceModel)
def update_data_source(
    data_source_id: str,
    data_source: DataSourceModel,
    engine: OperationEngine = Depends(get_operation_engine),
) -> DataSourceModel:
    """Update a data source.

    Args:
        data_source_id: Data source ID.
        data_source: Updated data source.
        engine: Operation engine.

    Returns:
        Updated data source.

    Raises:
        HTTPException: If the data source is not found.
    """
    operation = engine.get_operation(UpdateDataSourceOperation)
    params = {
        "data_source_id": data_source_id,
        "data_source": data_source,
    }
    result = operation.execute(params)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found or update failed: {result.error}",
        )
    
    return result.data


@router.delete("/{data_source_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_data_source(
    data_source_id: str,
    engine: OperationEngine = Depends(get_operation_engine),
) -> None:
    """Delete a data source.

    Args:
        data_source_id: Data source ID.
        engine: Operation engine.

    Raises:
        HTTPException: If the data source is not found.
    """
    operation = engine.get_operation(DeleteDataSourceOperation)
    result = operation.execute(data_source_id)
    
    if result.status != "success":
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found or deletion failed: {result.error}",
        )
