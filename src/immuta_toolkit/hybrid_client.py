"""Hybrid client for Immuta combining API and web automation."""

import time
import random
from typing import Dict, List, Optional, Union, Any, Callable

try:
    from immuta_toolkit.api.client import ImmutaApiClient
    from immuta_toolkit.models import (
        UserModel,
        DataSourceModel,
        PolicyModel,
        ProjectModel,
    )
    from immuta_toolkit.utils.logging import get_logger
    from immuta_toolkit.web.client import ImmutaWebClient

    logger = get_logger(__name__)
except ImportError:
    # Mock imports for testing
    class UserModel:
        pass

    class DataSourceModel:
        pass

    class PolicyModel:
        pass

    class ProjectModel:
        pass

    class ImmutaApiClient:
        def __init__(self, **kwargs):
            pass

    class ImmutaWebClient:
        def __init__(self, **kwargs):
            pass

    class MockLogger:
        def info(self, msg):
            pass

        def warning(self, msg):
            pass

        def error(self, msg):
            pass

    logger = MockLogger()


class ImmutaHybridClient:
    """Hybrid client for Immuta combining API and web automation.

    This client provides a high-level interface for managing Immuta resources
    using the API with automatic fallback to web automation when API operations fail.

    Attributes:
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        use_web_fallback: bool = True,
        headless: bool = True,
        max_retries: int = 3,
        retry_delay: int = 2,
        is_local: bool = False,
        timeout: int = 30,
    ):
        """Initialize the Immuta hybrid client.

        Args:
            api_key: Immuta API key. If None, will be retrieved from environment.
            base_url: Base URL of the Immuta instance. If None, will be retrieved from environment.
            username: Username for web authentication. If None, will be retrieved from environment.
            password: Password for web authentication. If None, will be retrieved from environment.
            use_web_fallback: Whether to use web automation as a fallback.
            headless: Whether to run the browser in headless mode.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
            is_local: Whether to run in local mode (mock API calls).
            timeout: Default timeout for operations in seconds.
        """
        self.use_web_fallback = use_web_fallback
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Initialize API client
        self.api_client = ImmutaApiClient(
            api_key=api_key,
            base_url=base_url,
            is_local=is_local,
            timeout=timeout,
        )

        # Initialize web client if fallback is enabled
        self.web_client = None
        if use_web_fallback and not is_local:
            try:
                self.web_client = ImmutaWebClient(
                    base_url=base_url,
                    username=username,
                    password=password,
                    headless=headless,
                    timeout=timeout * 1000,  # Convert to milliseconds
                )
            except Exception as e:
                logger.warning(f"Failed to initialize web client: {e}")
                logger.warning("Web fallback will not be available")

    def close(self) -> None:
        """Close the client and release resources."""
        if hasattr(self, "api_client") and self.api_client:
            self.api_client.close()

        if hasattr(self, "web_client") and self.web_client:
            self.web_client.close()

        logger.info("Closed Immuta hybrid client")

    def _with_fallback(
        self,
        api_func: Callable,
        web_func: Optional[Callable] = None,
        *args,
        **kwargs,
    ) -> Any:
        """Execute a function with fallback.

        Args:
            api_func: API function to execute.
            web_func: Web function to execute as fallback.
            *args: Positional arguments to pass to the functions.
            **kwargs: Keyword arguments to pass to the functions.

        Returns:
            Result of the function execution.

        Raises:
            Exception: If both API and web functions fail.
        """
        api_errors = []

        # Try API function first
        for retry in range(self.max_retries):
            try:
                return api_func(*args, **kwargs)
            except Exception as e:
                error_msg = f"API operation failed (attempt {retry + 1}/{self.max_retries}): {str(e)}"
                logger.warning(error_msg)
                api_errors.append(error_msg)

                if retry < self.max_retries - 1:
                    # Use exponential backoff with jitter for retries
                    delay = min(
                        self.retry_delay * (2**retry)
                        + (random.random() * self.retry_delay),
                        30,
                    )
                    logger.info(f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)

        # If API function failed and web fallback is enabled, try web function
        if self.use_web_fallback and self.web_client and web_func:
            logger.info("Falling back to web automation")
            try:
                result = web_func(*args, **kwargs)
                logger.info("Web fallback succeeded")
                return result
            except Exception as e:
                web_error = f"Web fallback failed: {str(e)}"
                logger.error(web_error)

                # Combine API and web errors for better debugging
                error_details = "\n".join(
                    [
                        "Operation failed with both API and web automation:",
                        "API Errors:",
                        *[f"  - {err}" for err in api_errors],
                        "Web Error:",
                        f"  - {web_error}",
                    ]
                )

                raise Exception(error_details)
        else:
            # No web fallback available
            error_details = "\n".join(
                [
                    f"API operation failed after {self.max_retries} retries and no web fallback available:",
                    *[f"  - {err}" for err in api_errors],
                ]
            )

            raise Exception(error_details)

    # User operations with fallback
    def list_users(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List users in Immuta.

        Args:
            limit: Maximum number of users to return.
            offset: Offset for pagination.

        Returns:
            List of user dictionaries.
        """
        return self._with_fallback(
            self.api_client.user_service.list_users,
            None,  # No web fallback for listing users
            limit=limit,
            offset=offset,
        )

    def get_user(self, user_id: Union[int, str]) -> Dict:
        """Get user by ID or email.

        Args:
            user_id: User ID or email.

        Returns:
            User dictionary.
        """
        return self._with_fallback(
            self.api_client.user_service.get_user,
            self.web_client.get_user if self.web_client else None,
            user_id,
        )

    def create_user(
        self, user: UserModel, backup: bool = True, dry_run: bool = False
    ) -> Dict:
        """Create a new user.

        Args:
            user: User model.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created user dictionary.
        """
        return self._with_fallback(
            self.api_client.user_service.create_user,
            self.web_client.create_user if self.web_client else None,
            user,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_user(
        self,
        user_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a user.

        Args:
            user_id: User ID or email.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If user_id is an email and we're using web fallback, we need to handle it differently
        if isinstance(user_id, str) and "@" in user_id and self.web_client:
            return self._with_fallback(
                self.api_client.user_service.delete_user,
                self.web_client.delete_user,
                user_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.user_service.delete_user,
                None,  # No web fallback for deleting by ID
                user_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Data source operations with fallback
    def list_data_sources(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List data sources in Immuta.

        Args:
            limit: Maximum number of data sources to return.
            offset: Offset for pagination.

        Returns:
            List of data source dictionaries.
        """
        return self._with_fallback(
            self.api_client.data_source_service.list_data_sources,
            None,  # No web fallback for listing data sources
            limit=limit,
            offset=offset,
        )

    def get_data_source(self, data_source_id: Union[int, str]) -> Dict:
        """Get data source by ID or name.

        Args:
            data_source_id: Data source ID or name.

        Returns:
            Data source dictionary.
        """
        # If data_source_id is a string and we're using web fallback, assume it's a name
        if isinstance(data_source_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.data_source_service.get_data_source,
                self.web_client.get_data_source,
                data_source_id,
            )
        else:
            return self._with_fallback(
                self.api_client.data_source_service.get_data_source,
                None,  # No web fallback for getting by ID
                data_source_id,
            )

    def create_data_source(
        self,
        data_source: Union[DataSourceModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new data source.

        Args:
            data_source: Data source model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created data source dictionary.
        """
        return self._with_fallback(
            self.api_client.data_source_service.create_data_source,
            self.web_client.create_data_source if self.web_client else None,
            data_source,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_data_source(
        self,
        data_source_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a data source.

        Args:
            data_source_id: Data source ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If data_source_id is a string and we're using web fallback, assume it's a name
        if isinstance(data_source_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.data_source_service.delete_data_source,
                self.web_client.delete_data_source,
                data_source_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.data_source_service.delete_data_source,
                None,  # No web fallback for deleting by ID
                data_source_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Policy operations with fallback
    def list_policies(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List policies in Immuta.

        Args:
            limit: Maximum number of policies to return.
            offset: Offset for pagination.

        Returns:
            List of policy dictionaries.
        """
        return self._with_fallback(
            self.api_client.policy_service.list_policies,
            None,  # No web fallback for listing policies
            limit=limit,
            offset=offset,
        )

    def get_policy(self, policy_id: Union[int, str]) -> Dict:
        """Get policy by ID or name.

        Args:
            policy_id: Policy ID or name.

        Returns:
            Policy dictionary.
        """
        # If policy_id is a string and we're using web fallback, assume it's a name
        if isinstance(policy_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.policy_service.get_policy,
                self.web_client.get_policy,
                policy_id,
            )
        else:
            return self._with_fallback(
                self.api_client.policy_service.get_policy,
                None,  # No web fallback for getting by ID
                policy_id,
            )

    def create_policy(
        self,
        policy: Union[PolicyModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new policy.

        Args:
            policy: Policy model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created policy dictionary.
        """
        return self._with_fallback(
            self.api_client.policy_service.create_policy,
            self.web_client.create_policy if self.web_client else None,
            policy,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_policy(
        self,
        policy_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a policy.

        Args:
            policy_id: Policy ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If policy_id is a string and we're using web fallback, assume it's a name
        if isinstance(policy_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.policy_service.delete_policy,
                self.web_client.delete_policy,
                policy_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.policy_service.delete_policy,
                None,  # No web fallback for deleting by ID
                policy_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Project operations with fallback
    def list_projects(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List projects in Immuta.

        Args:
            limit: Maximum number of projects to return.
            offset: Offset for pagination.

        Returns:
            List of project dictionaries.
        """
        return self._with_fallback(
            self.api_client.project_service.list_projects,
            None,  # No web fallback for listing projects
            limit=limit,
            offset=offset,
        )

    def get_project(self, project_id: Union[int, str]) -> Dict:
        """Get project by ID or name.

        Args:
            project_id: Project ID or name.

        Returns:
            Project dictionary.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.get_project,
                self.web_client.get_project,
                project_id,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.get_project,
                None,  # No web fallback for getting by ID
                project_id,
            )

    def create_project(
        self,
        project: Union[ProjectModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new project.

        Args:
            project: Project model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created project dictionary.
        """
        return self._with_fallback(
            self.api_client.project_service.create_project,
            self.web_client.create_project if self.web_client else None,
            project,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_project(
        self,
        project_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a project.

        Args:
            project_id: Project ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.delete_project,
                self.web_client.delete_project,
                project_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.delete_project,
                None,  # No web fallback for deleting by ID
                project_id,
                backup=backup,
                dry_run=dry_run,
            )

    def add_project_member(
        self,
        project_id: Union[int, str],
        email: str,
        role: str,
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Add a member to a project.

        Args:
            project_id: Project ID or name.
            email: Member email.
            role: Member role.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with status.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.add_member,
                self.web_client.add_project_member,
                project_id,
                email,
                role,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.add_member,
                None,  # No web fallback for adding by ID
                project_id,
                email,
                role,
                backup=backup,
                dry_run=dry_run,
            )
