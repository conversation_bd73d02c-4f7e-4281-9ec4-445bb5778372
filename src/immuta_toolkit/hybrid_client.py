"""Enhanced hybrid client for Immuta combining API and web automation."""

import time
import random
import asyncio
from typing import Dict, List, Optional, Union, Any, Callable, Tuple
from enum import Enum
from dataclasses import dataclass

try:
    from immuta_toolkit.api.client import ImmutaApiClient
    from immuta_toolkit.models import (
        UserModel,
        DataSourceModel,
        PolicyModel,
        ProjectModel,
    )
    from immuta_toolkit.utils.logging import get_logger
    from immuta_toolkit.web.client import ImmutaWebClient
    from immuta_toolkit.operations.engine import OperationEngine
    from immuta_toolkit.validation.base import ValidationResult
    from immuta_toolkit.reporting.manager import ReportManager
    from immuta_toolkit.utils.metrics import MetricsCollector

    logger = get_logger(__name__)
except ImportError:
    # Mock imports for testing
    class UserModel:
        pass

    class DataSourceModel:
        pass

    class PolicyModel:
        pass

    class ProjectModel:
        pass

    class ImmutaApiClient:
        def __init__(self, **kwargs):
            pass

    class ImmutaWebClient:
        def __init__(self, **kwargs):
            pass

    class OperationEngine:
        def __init__(self, **kwargs):
            pass

    class ValidationResult:
        pass

    class ReportManager:
        def __init__(self, **kwargs):
            pass

    class MetricsCollector:
        def __init__(self, **kwargs):
            pass

    class MockLogger:
        def info(self, msg):
            pass

        def warning(self, msg):
            pass

        def error(self, msg):
            pass

    logger = MockLogger()


class FallbackStrategy(Enum):
    """Strategy for handling API failures."""

    IMMEDIATE = "immediate"  # Fallback immediately on API failure
    RETRY_THEN_FALLBACK = "retry_then_fallback"  # Retry API, then fallback
    API_ONLY = "api_only"  # Never fallback to web automation
    WEB_ONLY = "web_only"  # Use only web automation


@dataclass
class OperationMetrics:
    """Metrics for operation execution."""

    operation_name: str
    api_attempts: int = 0
    web_attempts: int = 0
    total_time: float = 0.0
    api_time: float = 0.0
    web_time: float = 0.0
    success: bool = False
    fallback_used: bool = False
    error_message: Optional[str] = None


class ImmutaHybridClient:
    """Enhanced hybrid client for Immuta combining API and web automation.

    This client provides a high-level interface for managing Immuta resources
    using the API with automatic fallback to web automation when API operations fail.
    It includes comprehensive validation, reporting, and metrics collection.

    Attributes:
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        operation_engine: Operation execution engine.
        report_manager: Report manager for operation tracking.
        metrics_collector: Metrics collector for performance monitoring.
        fallback_strategy: Strategy for handling API failures.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
        validation_enabled: Whether to enable validation.
        reporting_enabled: Whether to enable reporting.
        metrics_enabled: Whether to enable metrics collection.
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        use_web_fallback: bool = True,
        headless: bool = True,
        max_retries: int = 3,
        retry_delay: int = 2,
        is_local: bool = False,
        timeout: int = 30,
        fallback_strategy: FallbackStrategy = FallbackStrategy.RETRY_THEN_FALLBACK,
        validation_enabled: bool = True,
        reporting_enabled: bool = True,
        metrics_enabled: bool = True,
        report_storage_dir: Optional[str] = None,
    ):
        """Initialize the enhanced Immuta hybrid client.

        Args:
            api_key: Immuta API key. If None, will be retrieved from environment.
            base_url: Base URL of the Immuta instance. If None, will be retrieved from environment.
            username: Username for web authentication. If None, will be retrieved from environment.
            password: Password for web authentication. If None, will be retrieved from environment.
            use_web_fallback: Whether to use web automation as a fallback.
            headless: Whether to run the browser in headless mode.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
            is_local: Whether to run in local mode (mock API calls).
            timeout: Default timeout for operations in seconds.
            fallback_strategy: Strategy for handling API failures.
            validation_enabled: Whether to enable validation.
            reporting_enabled: Whether to enable reporting.
            metrics_enabled: Whether to enable metrics collection.
            report_storage_dir: Directory to store reports.
        """
        # Store configuration
        self.use_web_fallback = use_web_fallback
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.fallback_strategy = fallback_strategy
        self.validation_enabled = validation_enabled
        self.reporting_enabled = reporting_enabled
        self.metrics_enabled = metrics_enabled
        self.is_local = is_local

        # Initialize API client
        self.api_client = ImmutaApiClient(
            api_key=api_key,
            base_url=base_url,
            is_local=is_local,
            timeout=timeout,
        )

        # Initialize web client if fallback is enabled
        self.web_client = None
        if (use_web_fallback or fallback_strategy == FallbackStrategy.WEB_ONLY) and not is_local:
            try:
                self.web_client = ImmutaWebClient(
                    base_url=base_url,
                    username=username,
                    password=password,
                    headless=headless,
                    timeout=timeout * 1000,  # Convert to milliseconds
                )
            except Exception as e:
                logger.warning(f"Failed to initialize web client: {e}")
                logger.warning("Web fallback will not be available")

        # Initialize operation engine
        self.operation_engine = OperationEngine(
            api_client=self.api_client,
            web_client=self.web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

        # Initialize report manager if reporting is enabled
        self.report_manager = None
        if reporting_enabled:
            self.report_manager = ReportManager(storage_dir=report_storage_dir)

        # Initialize metrics collector if metrics are enabled
        self.metrics_collector = None
        if metrics_enabled:
            self.metrics_collector = MetricsCollector()

        logger.info(f"Initialized enhanced hybrid client with strategy: {fallback_strategy.value}")

    def close(self) -> None:
        """Close the client and release resources."""
        if hasattr(self, "api_client") and self.api_client:
            self.api_client.close()

        if hasattr(self, "web_client") and self.web_client:
            self.web_client.close()

        logger.info("Closed Immuta hybrid client")

    # Enhanced utility methods
    def get_metrics(self) -> Optional[Dict[str, Any]]:
        """Get collected metrics.

        Returns:
            Dictionary with metrics data if metrics are enabled, None otherwise.
        """
        if self.metrics_enabled and self.metrics_collector:
            return self.metrics_collector.get_metrics()
        return None

    def get_reports(self) -> List[Dict[str, Any]]:
        """Get list of available reports.

        Returns:
            List of report summaries if reporting is enabled, empty list otherwise.
        """
        if self.reporting_enabled and self.report_manager:
            return self.report_manager.list_reports()
        return []

    def export_report(
        self,
        report_id: str,
        format: str,
        output_path: str,
        summary_only: bool = False,
    ) -> Optional[str]:
        """Export a report to a file.

        Args:
            report_id: Report ID.
            format: Export format (json, csv, html, pdf).
            output_path: Path to export the report to.
            summary_only: Whether to export only the report summary.

        Returns:
            Path to the exported report if successful, None otherwise.
        """
        if self.reporting_enabled and self.report_manager:
            return self.report_manager.export_report(
                format=format,
                output_path=output_path,
                report_id=report_id,
                summary_only=summary_only,
            )
        return None

    def set_fallback_strategy(self, strategy: FallbackStrategy) -> None:
        """Set the fallback strategy.

        Args:
            strategy: New fallback strategy.
        """
        self.fallback_strategy = strategy
        logger.info(f"Updated fallback strategy to: {strategy.value}")

    def enable_validation(self, enabled: bool = True) -> None:
        """Enable or disable validation.

        Args:
            enabled: Whether to enable validation.
        """
        self.validation_enabled = enabled
        logger.info(f"Validation {'enabled' if enabled else 'disabled'}")

    def enable_reporting(self, enabled: bool = True) -> None:
        """Enable or disable reporting.

        Args:
            enabled: Whether to enable reporting.
        """
        self.reporting_enabled = enabled
        logger.info(f"Reporting {'enabled' if enabled else 'disabled'}")

    def enable_metrics(self, enabled: bool = True) -> None:
        """Enable or disable metrics collection.

        Args:
            enabled: Whether to enable metrics collection.
        """
        self.metrics_enabled = enabled
        logger.info(f"Metrics collection {'enabled' if enabled else 'disabled'}")

    def get_client_status(self) -> Dict[str, Any]:
        """Get the status of the hybrid client.

        Returns:
            Dictionary with client status information.
        """
        status = {
            "api_client_available": self.api_client is not None,
            "web_client_available": self.web_client is not None,
            "fallback_strategy": self.fallback_strategy.value,
            "validation_enabled": self.validation_enabled,
            "reporting_enabled": self.reporting_enabled,
            "metrics_enabled": self.metrics_enabled,
            "is_local": self.is_local,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
        }

        # Add API client status if available
        if self.api_client and hasattr(self.api_client, "get_circuit_breaker_state"):
            status["api_circuit_breaker"] = self.api_client.get_circuit_breaker_state()

        return status

    async def execute_batch_operations(
        self,
        operations: List[Dict[str, Any]],
        max_concurrent: int = 5,
        fail_fast: bool = False,
    ) -> List[Dict[str, Any]]:
        """Execute multiple operations concurrently.

        Args:
            operations: List of operation dictionaries with 'type', 'params', etc.
            max_concurrent: Maximum number of concurrent operations.
            fail_fast: Whether to stop on first failure.

        Returns:
            List of operation results.
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        results = []

        async def execute_operation(operation: Dict[str, Any]) -> Dict[str, Any]:
            async with semaphore:
                try:
                    # This is a simplified example - in practice, you'd map operation types
                    # to actual method calls
                    operation_type = operation.get("type")
                    params = operation.get("params", {})

                    if operation_type == "get_user":
                        result = self.get_user(**params)
                    elif operation_type == "create_user":
                        result = self.create_user(**params)
                    # Add more operation types as needed
                    else:
                        raise ValueError(f"Unknown operation type: {operation_type}")

                    return {
                        "operation": operation,
                        "success": True,
                        "result": result,
                        "error": None,
                    }
                except Exception as e:
                    error_result = {
                        "operation": operation,
                        "success": False,
                        "result": None,
                        "error": str(e),
                    }
                    if fail_fast:
                        raise
                    return error_result

        # Execute operations concurrently
        tasks = [execute_operation(op) for op in operations]
        results = await asyncio.gather(*tasks, return_exceptions=not fail_fast)

        return results

    def _with_fallback(
        self,
        operation_name: str,
        api_func: Callable,
        web_func: Optional[Callable] = None,
        validator: Optional[Callable] = None,
        *args,
        **kwargs,
    ) -> Any:
        """Execute a function with enhanced fallback and monitoring.

        Args:
            operation_name: Name of the operation for logging and metrics.
            api_func: API function to execute.
            web_func: Web function to execute as fallback.
            validator: Optional validator function for results.
            *args: Positional arguments to pass to the functions.
            **kwargs: Keyword arguments to pass to the functions.

        Returns:
            Result of the function execution.

        Raises:
            Exception: If both API and web functions fail.
        """
        start_time = time.time()
        metrics = OperationMetrics(operation_name=operation_name)
        api_errors = []
        web_errors = []

        # Determine execution strategy based on fallback strategy
        should_try_api = self.fallback_strategy != FallbackStrategy.WEB_ONLY
        should_try_web = (
            self.fallback_strategy != FallbackStrategy.API_ONLY
            and self.web_client
            and web_func
        )

        # Try API function first if strategy allows
        if should_try_api:
            api_start_time = time.time()

            for retry in range(self.max_retries):
                metrics.api_attempts += 1
                try:
                    logger.info(f"Executing {operation_name} using API (attempt {retry + 1})")
                    result = api_func(*args, **kwargs)

                    # Validate result if validator is provided
                    if validator and self.validation_enabled:
                        validation_result = validator(result)
                        if not validation_result:
                            raise ValueError("API result validation failed")

                    metrics.api_time = time.time() - api_start_time
                    metrics.total_time = time.time() - start_time
                    metrics.success = True

                    # Record metrics if enabled
                    if self.metrics_enabled and self.metrics_collector:
                        self.metrics_collector.record_operation(metrics)

                    logger.info(f"API operation {operation_name} succeeded")
                    return result

                except Exception as e:
                    error_msg = f"API operation failed (attempt {retry + 1}/{self.max_retries}): {str(e)}"
                    logger.warning(error_msg)
                    api_errors.append(error_msg)

                    # Check if we should retry or fallback immediately
                    if self.fallback_strategy == FallbackStrategy.IMMEDIATE and should_try_web:
                        break

                    if retry < self.max_retries - 1:
                        # Use exponential backoff with jitter for retries
                        delay = min(
                            self.retry_delay * (2**retry)
                            + (random.random() * self.retry_delay),
                            30,
                        )
                        logger.info(f"Retrying in {delay:.2f} seconds...")
                        time.sleep(delay)

            metrics.api_time = time.time() - api_start_time

        # Try web automation if strategy allows and API failed
        if should_try_web and (not should_try_api or api_errors):
            web_start_time = time.time()
            metrics.web_attempts += 1
            metrics.fallback_used = True

            logger.info(f"Falling back to web automation for {operation_name}")
            try:
                result = web_func(*args, **kwargs)

                # Validate result if validator is provided
                if validator and self.validation_enabled:
                    validation_result = validator(result)
                    if not validation_result:
                        raise ValueError("Web result validation failed")

                metrics.web_time = time.time() - web_start_time
                metrics.total_time = time.time() - start_time
                metrics.success = True

                # Record metrics if enabled
                if self.metrics_enabled and self.metrics_collector:
                    self.metrics_collector.record_operation(metrics)

                logger.info(f"Web fallback for {operation_name} succeeded")
                return result

            except Exception as e:
                web_error = f"Web fallback failed: {str(e)}"
                logger.error(web_error)
                web_errors.append(web_error)
                metrics.web_time = time.time() - web_start_time

        # Both methods failed
        metrics.total_time = time.time() - start_time
        metrics.success = False

        # Combine all errors for better debugging
        all_errors = []
        if api_errors:
            all_errors.extend([f"API: {err}" for err in api_errors])
        if web_errors:
            all_errors.extend([f"Web: {err}" for err in web_errors])

        error_message = f"Operation {operation_name} failed: " + "; ".join(all_errors)
        metrics.error_message = error_message

        # Record failed metrics if enabled
        if self.metrics_enabled and self.metrics_collector:
            self.metrics_collector.record_operation(metrics)

        raise Exception(error_message)

    # Enhanced user operations with fallback
    def list_users(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List users in Immuta.

        Args:
            limit: Maximum number of users to return.
            offset: Offset for pagination.

        Returns:
            List of user dictionaries.
        """
        return self._with_fallback(
            operation_name="list_users",
            api_func=self.api_client.user_service.list_users,
            web_func=None,  # No web fallback for listing users
            limit=limit,
            offset=offset,
        )

    def get_user(self, user_id: Union[int, str]) -> Dict:
        """Get user by ID or email.

        Args:
            user_id: User ID or email.

        Returns:
            User dictionary.
        """
        return self._with_fallback(
            operation_name="get_user",
            api_func=self.api_client.user_service.get_user,
            web_func=self.web_client.get_user if self.web_client else None,
            user_id=user_id,
        )

    def create_user(
        self, user: UserModel, backup: bool = True, dry_run: bool = False
    ) -> Dict:
        """Create a new user.

        Args:
            user: User model.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created user dictionary.
        """
        return self._with_fallback(
            operation_name="create_user",
            api_func=self.api_client.user_service.create_user,
            web_func=self.web_client.create_user if self.web_client else None,
            user=user,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_user(
        self,
        user_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a user.

        Args:
            user_id: User ID or email.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If user_id is an email and we're using web fallback, we need to handle it differently
        if isinstance(user_id, str) and "@" in user_id and self.web_client:
            return self._with_fallback(
                self.api_client.user_service.delete_user,
                self.web_client.delete_user,
                user_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.user_service.delete_user,
                None,  # No web fallback for deleting by ID
                user_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Data source operations with fallback
    def list_data_sources(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List data sources in Immuta.

        Args:
            limit: Maximum number of data sources to return.
            offset: Offset for pagination.

        Returns:
            List of data source dictionaries.
        """
        return self._with_fallback(
            self.api_client.data_source_service.list_data_sources,
            None,  # No web fallback for listing data sources
            limit=limit,
            offset=offset,
        )

    def get_data_source(self, data_source_id: Union[int, str]) -> Dict:
        """Get data source by ID or name.

        Args:
            data_source_id: Data source ID or name.

        Returns:
            Data source dictionary.
        """
        # If data_source_id is a string and we're using web fallback, assume it's a name
        if isinstance(data_source_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.data_source_service.get_data_source,
                self.web_client.get_data_source,
                data_source_id,
            )
        else:
            return self._with_fallback(
                self.api_client.data_source_service.get_data_source,
                None,  # No web fallback for getting by ID
                data_source_id,
            )

    def create_data_source(
        self,
        data_source: Union[DataSourceModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new data source.

        Args:
            data_source: Data source model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created data source dictionary.
        """
        return self._with_fallback(
            self.api_client.data_source_service.create_data_source,
            self.web_client.create_data_source if self.web_client else None,
            data_source,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_data_source(
        self,
        data_source_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a data source.

        Args:
            data_source_id: Data source ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If data_source_id is a string and we're using web fallback, assume it's a name
        if isinstance(data_source_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.data_source_service.delete_data_source,
                self.web_client.delete_data_source,
                data_source_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.data_source_service.delete_data_source,
                None,  # No web fallback for deleting by ID
                data_source_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Policy operations with fallback
    def list_policies(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List policies in Immuta.

        Args:
            limit: Maximum number of policies to return.
            offset: Offset for pagination.

        Returns:
            List of policy dictionaries.
        """
        return self._with_fallback(
            self.api_client.policy_service.list_policies,
            None,  # No web fallback for listing policies
            limit=limit,
            offset=offset,
        )

    def get_policy(self, policy_id: Union[int, str]) -> Dict:
        """Get policy by ID or name.

        Args:
            policy_id: Policy ID or name.

        Returns:
            Policy dictionary.
        """
        # If policy_id is a string and we're using web fallback, assume it's a name
        if isinstance(policy_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.policy_service.get_policy,
                self.web_client.get_policy,
                policy_id,
            )
        else:
            return self._with_fallback(
                self.api_client.policy_service.get_policy,
                None,  # No web fallback for getting by ID
                policy_id,
            )

    def create_policy(
        self,
        policy: Union[PolicyModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new policy.

        Args:
            policy: Policy model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created policy dictionary.
        """
        return self._with_fallback(
            self.api_client.policy_service.create_policy,
            self.web_client.create_policy if self.web_client else None,
            policy,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_policy(
        self,
        policy_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a policy.

        Args:
            policy_id: Policy ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If policy_id is a string and we're using web fallback, assume it's a name
        if isinstance(policy_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.policy_service.delete_policy,
                self.web_client.delete_policy,
                policy_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.policy_service.delete_policy,
                None,  # No web fallback for deleting by ID
                policy_id,
                backup=backup,
                dry_run=dry_run,
            )

    # Project operations with fallback
    def list_projects(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List projects in Immuta.

        Args:
            limit: Maximum number of projects to return.
            offset: Offset for pagination.

        Returns:
            List of project dictionaries.
        """
        return self._with_fallback(
            self.api_client.project_service.list_projects,
            None,  # No web fallback for listing projects
            limit=limit,
            offset=offset,
        )

    def get_project(self, project_id: Union[int, str]) -> Dict:
        """Get project by ID or name.

        Args:
            project_id: Project ID or name.

        Returns:
            Project dictionary.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.get_project,
                self.web_client.get_project,
                project_id,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.get_project,
                None,  # No web fallback for getting by ID
                project_id,
            )

    def create_project(
        self,
        project: Union[ProjectModel, Dict],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Create a new project.

        Args:
            project: Project model or dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created project dictionary.
        """
        return self._with_fallback(
            self.api_client.project_service.create_project,
            self.web_client.create_project if self.web_client else None,
            project,
            backup=backup,
            dry_run=dry_run,
        )

    def delete_project(
        self,
        project_id: Union[int, str],
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a project.

        Args:
            project_id: Project ID or name.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.delete_project,
                self.web_client.delete_project,
                project_id,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.delete_project,
                None,  # No web fallback for deleting by ID
                project_id,
                backup=backup,
                dry_run=dry_run,
            )

    def add_project_member(
        self,
        project_id: Union[int, str],
        email: str,
        role: str,
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Add a member to a project.

        Args:
            project_id: Project ID or name.
            email: Member email.
            role: Member role.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with status.
        """
        # If project_id is a string and we're using web fallback, assume it's a name
        if isinstance(project_id, str) and self.web_client:
            return self._with_fallback(
                self.api_client.project_service.add_member,
                self.web_client.add_project_member,
                project_id,
                email,
                role,
                backup=backup,
                dry_run=dry_run,
            )
        else:
            return self._with_fallback(
                self.api_client.project_service.add_member,
                None,  # No web fallback for adding by ID
                project_id,
                email,
                role,
                backup=backup,
                dry_run=dry_run,
            )
