#!/usr/bin/env python3
"""Example script for adding users to a group in Immuta."""

import os
import sys
import argparse
from typing import List

from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.models.user import UserModel, GroupModel
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Add users to a group in Immuta")
    parser.add_argument(
        "--api-key",
        help="Immuta API key",
        default=os.getenv("IMMUTA_API_KEY"),
    )
    parser.add_argument(
        "--base-url",
        help="Immuta base URL",
        default=os.getenv("IMMUTA_BASE_URL"),
    )
    parser.add_argument(
        "--group-name",
        required=True,
        help="Name of the group to add users to",
    )
    parser.add_argument(
        "--users",
        required=True,
        nargs="+",
        help="List of user emails to add to the group",
    )
    parser.add_argument(
        "--create-group",
        action="store_true",
        help="Create the group if it doesn't exist",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run without making changes",
    )
    return parser.parse_args()


def add_users_to_group(
    client: ImmutaHybridClient,
    group_name: str,
    user_emails: List[str],
    create_group: bool = False,
    dry_run: bool = False,
) -> bool:
    """Add users to a group.
    
    Args:
        client: Immuta client.
        group_name: Name of the group to add users to.
        user_emails: List of user emails to add to the group.
        create_group: Whether to create the group if it doesn't exist.
        dry_run: Whether to perform a dry run without making changes.
        
    Returns:
        True if successful, False otherwise.
    """
    # Get the group
    group = client.get_group(group_name)
    
    # Create the group if it doesn't exist and create_group is True
    if group is None:
        if create_group:
            logger.info(f"Group {group_name} does not exist, creating it")
            if not dry_run:
                group = client.create_group(GroupModel(name=group_name))
                logger.info(f"Created group {group_name}")
            else:
                logger.info(f"Dry run: Would create group {group_name}")
                # Create a dummy group for dry run
                group = GroupModel(name=group_name)
        else:
            logger.error(f"Group {group_name} does not exist")
            return False
    
    # Get the users
    users = []
    for email in user_emails:
        user = client.get_user(email)
        if user is None:
            logger.error(f"User {email} does not exist")
            continue
        users.append(user)
    
    if not users:
        logger.error("No valid users to add to the group")
        return False
    
    # Add users to the group
    for user in users:
        logger.info(f"Adding user {user.email} to group {group_name}")
        if not dry_run:
            success = client.add_user_to_group(user.id, group.id)
            if success:
                logger.info(f"Added user {user.email} to group {group_name}")
            else:
                logger.error(f"Failed to add user {user.email} to group {group_name}")
        else:
            logger.info(f"Dry run: Would add user {user.email} to group {group_name}")
    
    return True


def main():
    """Main entry point."""
    args = parse_args()
    
    # Initialize client
    client = ImmutaHybridClient(
        api_key=args.api_key,
        base_url=args.base_url,
    )
    
    try:
        # Add users to group
        success = add_users_to_group(
            client=client,
            group_name=args.group_name,
            user_emails=args.users,
            create_group=args.create_group,
            dry_run=args.dry_run,
        )
        
        if success:
            logger.info("Operation completed successfully")
            return 0
        else:
            logger.error("Operation failed")
            return 1
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    finally:
        client.close()


if __name__ == "__main__":
    sys.exit(main())
