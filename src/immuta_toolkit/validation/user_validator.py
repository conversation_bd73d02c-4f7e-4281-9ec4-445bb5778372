"""User validators for Immuta automation."""

import re
from typing import Dict, Any, List

from immuta_toolkit.models import UserModel, UserRole
from immuta_toolkit.validation.base import ModelValidator, DictValidator
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class UserModelValidator(ModelValidator):
    """Validator for user models."""

    def validate_business_rules(self, model: UserModel) -> bool:
        """Validate business rules for a user model.
        
        Args:
            model: User model to validate.
            
        Returns:
            True if the model passes all business rules, False otherwise.
        """
        is_valid = True
        
        # Validate email format (Pydantic already does this, but we can add custom rules)
        if not model.email or "@" not in model.email:
            self.add_error("Email must be a valid email address")
            is_valid = False
        
        # Validate name
        if not model.name or len(model.name.strip()) < 2:
            self.add_error("Name must be at least 2 characters long")
            is_valid = False
        
        # Validate role
        if not model.attributes or not model.attributes.role:
            self.add_error("Role is required")
            is_valid = False
        
        # Validate groups
        if model.groups:
            for group in model.groups:
                if not group or len(group.strip()) < 2:
                    self.add_error(f"Group name '{group}' must be at least 2 characters long")
                    is_valid = False
        
        return is_valid


class UserDictValidator(DictValidator):
    """Validator for user dictionaries."""

    def validate_fields(self, data: Dict[str, Any]) -> bool:
        """Validate fields in a user dictionary.
        
        Args:
            data: User dictionary to validate.
            
        Returns:
            True if all fields are valid, False otherwise.
        """
        is_valid = True
        
        # Check required fields
        required_fields = ["email", "name", "attributes"]
        for field in required_fields:
            if field not in data:
                self.add_error(f"Missing required field: {field}")
                is_valid = False
        
        # If any required fields are missing, return early
        if not is_valid:
            return False
        
        # Validate email
        if not data.get("email") or not isinstance(data["email"], str) or "@" not in data["email"]:
            self.add_error("Email must be a valid email address")
            is_valid = False
        
        # Validate name
        if not data.get("name") or not isinstance(data["name"], str) or len(data["name"].strip()) < 2:
            self.add_error("Name must be at least 2 characters long")
            is_valid = False
        
        # Validate attributes
        attributes = data.get("attributes", {})
        if not attributes or not isinstance(attributes, dict):
            self.add_error("Attributes must be a dictionary")
            is_valid = False
        elif "role" not in attributes:
            self.add_error("Role is required in attributes")
            is_valid = False
        elif attributes["role"] not in [role.value for role in UserRole]:
            self.add_error(f"Invalid role: {attributes['role']}")
            is_valid = False
        
        # Validate groups
        groups = data.get("groups", [])
        if groups and not isinstance(groups, list):
            self.add_error("Groups must be a list")
            is_valid = False
        elif groups:
            for group in groups:
                if not isinstance(group, str) or len(group.strip()) < 2:
                    self.add_error(f"Group name '{group}' must be at least 2 characters long")
                    is_valid = False
        
        return is_valid
    
    def validate_business_rules(self, data: Dict[str, Any]) -> bool:
        """Validate business rules for a user dictionary.
        
        Args:
            data: User dictionary to validate.
            
        Returns:
            True if the dictionary passes all business rules, False otherwise.
        """
        is_valid = True
        
        # Validate email domain
        email = data.get("email", "")
        if email and "@" in email:
            domain = email.split("@")[1]
            if domain.lower() in ["example.com", "test.com"]:
                self.add_error(f"Email domain {domain} is not allowed in production")
                is_valid = False
        
        # Validate role-specific rules
        attributes = data.get("attributes", {})
        role = attributes.get("role")
        
        if role == UserRole.ADMIN.value:
            # Add special validation for admin users
            if not data.get("groups") or "Administrators" not in data["groups"]:
                self.add_error("Admin users must be in the Administrators group")
                is_valid = False
        
        return is_valid
