"""Data source validator for Immuta automation."""

import re
from typing import Dict, List, Optional, Any, Union, Set

from immuta_toolkit.models.data_source import DataSourceModel, DataSourceType, Column
from immuta_toolkit.validation.base import Val<PERSON><PERSON>, ValidationResult, ValidationLevel
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class DataSourceValidator(Validator[DataSourceModel]):
    """Data source validator.
    
    This validator validates data source models against business rules.
    
    Attributes:
        name: Validator name.
        description: Validator description.
        level: Validation level.
        reserved_names: Set of reserved data source names.
        reserved_name_patterns: List of reserved data source name patterns.
        max_name_length: Maximum length of data source names.
        max_description_length: Maximum length of data source descriptions.
        max_columns: Maximum number of columns in a data source.
    """
    
    def __init__(
        self,
        name: str = "data_source_validator",
        description: str = "Validates data source models against business rules",
        level: ValidationLevel = ValidationLevel.ERROR,
        reserved_names: Optional[Set[str]] = None,
        reserved_name_patterns: Optional[List[str]] = None,
        max_name_length: int = 128,
        max_description_length: int = 1024,
        max_columns: int = 1000,
    ):
        """Initialize the data source validator.
        
        Args:
            name: Validator name.
            description: Validator description.
            level: Validation level.
            reserved_names: Set of reserved data source names.
            reserved_name_patterns: List of reserved data source name patterns.
            max_name_length: Maximum length of data source names.
            max_description_length: Maximum length of data source descriptions.
            max_columns: Maximum number of columns in a data source.
        """
        super().__init__(name, description, level)
        self.reserved_names = reserved_names or {"admin", "system", "immuta", "internal"}
        self.reserved_name_patterns = reserved_name_patterns or [r"^_.*", r".*_$", r".*\s{2,}.*"]
        self.max_name_length = max_name_length
        self.max_description_length = max_description_length
        self.max_columns = max_columns
    
    def validate(self, data_source: DataSourceModel) -> ValidationResult:
        """Validate a data source model.
        
        Args:
            data_source: Data source model to validate.
            
        Returns:
            Validation result.
        """
        result = ValidationResult(valid=True)
        
        # Validate name
        if not data_source.name:
            result.add_error("Name is required")
        elif len(data_source.name) > self.max_name_length:
            result.add_error(f"Name exceeds maximum length of {self.max_name_length} characters")
        elif data_source.name.lower() in self.reserved_names:
            result.add_error(f"Name '{data_source.name}' is reserved")
        else:
            for pattern in self.reserved_name_patterns:
                if re.match(pattern, data_source.name):
                    result.add_error(f"Name '{data_source.name}' matches reserved pattern '{pattern}'")
                    break
        
        # Validate description
        if data_source.description and len(data_source.description) > self.max_description_length:
            result.add_error(f"Description exceeds maximum length of {self.max_description_length} characters")
        
        # Validate type
        if not data_source.type:
            result.add_error("Type is required")
        
        # Validate connection parameters
        if not data_source.connection_parameters:
            result.add_error("Connection parameters are required")
        else:
            # Validate connection parameters based on data source type
            if data_source.type == DataSourceType.JDBC:
                if not data_source.connection_parameters.connection_string:
                    result.add_error("Connection string is required for JDBC data sources")
            elif data_source.type == DataSourceType.S3:
                if not data_source.connection_parameters.custom_parameters.get("bucket"):
                    result.add_error("Bucket is required for S3 data sources")
            elif data_source.type == DataSourceType.SNOWFLAKE:
                if not data_source.connection_parameters.host:
                    result.add_error("Host is required for Snowflake data sources")
                if not data_source.connection_parameters.database:
                    result.add_error("Database is required for Snowflake data sources")
                if not data_source.connection_parameters.schema:
                    result.add_error("Schema is required for Snowflake data sources")
            elif data_source.type == DataSourceType.REDSHIFT:
                if not data_source.connection_parameters.host:
                    result.add_error("Host is required for Redshift data sources")
                if not data_source.connection_parameters.database:
                    result.add_error("Database is required for Redshift data sources")
            elif data_source.type == DataSourceType.BIGQUERY:
                if not data_source.connection_parameters.custom_parameters.get("project_id"):
                    result.add_error("Project ID is required for BigQuery data sources")
                if not data_source.connection_parameters.custom_parameters.get("dataset"):
                    result.add_error("Dataset is required for BigQuery data sources")
        
        # Validate columns
        if len(data_source.columns) > self.max_columns:
            result.add_error(f"Number of columns exceeds maximum of {self.max_columns}")
        
        # Validate column names
        column_names = set()
        for column in data_source.columns:
            if not column.name:
                result.add_error("Column name is required")
            elif column.name in column_names:
                result.add_error(f"Duplicate column name: {column.name}")
            else:
                column_names.add(column.name)
        
        return result
    
    def validate_batch(self, data_sources: List[DataSourceModel]) -> List[ValidationResult]:
        """Validate a batch of data source models.
        
        Args:
            data_sources: List of data source models to validate.
            
        Returns:
            List of validation results.
        """
        results = []
        
        # Check for duplicate names across all data sources
        names = set()
        for data_source in data_sources:
            if data_source.name in names:
                result = ValidationResult(valid=False)
                result.add_error(f"Duplicate data source name across batch: {data_source.name}")
                results.append(result)
            else:
                names.add(data_source.name)
                results.append(self.validate(data_source))
        
        return results
