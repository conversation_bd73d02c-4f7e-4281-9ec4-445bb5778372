"""Project validator for Immuta automation."""

import re
from typing import Dict, List, Optional, Any, Union, Set, Tuple

from immuta_toolkit.models.project import (
    ProjectModel,
    ProjectMember,
    ProjectQueryParameters,
)
from immuta_toolkit.validation.base import <PERSON><PERSON><PERSON>, ValidationResult, ValidationLevel
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ProjectValidator(Validator[ProjectModel]):
    """Project validator.

    This validator validates project models against business rules.

    Attributes:
        name: Validator name.
        description: Validator description.
        level: Validation level.
        reserved_names: Set of reserved project names.
        reserved_name_patterns: List of reserved project name patterns.
        max_name_length: Maximum length of project names.
        max_description_length: Maximum length of project descriptions.
        max_members: Maximum number of members in a project.
        max_data_sources: Maximum number of data sources in a project.
    """

    def __init__(
        self,
        name: str = "project_validator",
        description: str = "Validates project models against business rules",
        level: ValidationLevel = ValidationLevel.ERROR,
        reserved_names: Optional[Set[str]] = None,
        reserved_name_patterns: Optional[List[str]] = None,
        max_name_length: int = 128,
        max_description_length: int = 1024,
        max_members: int = 1000,
        max_data_sources: int = 1000,
    ):
        """Initialize the project validator.

        Args:
            name: Validator name.
            description: Validator description.
            level: Validation level.
            reserved_names: Set of reserved project names.
            reserved_name_patterns: List of reserved project name patterns.
            max_name_length: Maximum length of project names.
            max_description_length: Maximum length of project descriptions.
            max_members: Maximum number of members in a project.
            max_data_sources: Maximum number of data sources in a project.
        """
        super().__init__(name, description, level)
        self.reserved_names = reserved_names or {
            "admin",
            "system",
            "immuta",
            "internal",
        }
        self.reserved_name_patterns = reserved_name_patterns or [
            r"^_.*",
            r".*_$",
            r".*\s{2,}.*",
        ]
        self.max_name_length = max_name_length
        self.max_description_length = max_description_length
        self.max_members = max_members
        self.max_data_sources = max_data_sources

    def validate(self, project: ProjectModel) -> ValidationResult:
        """Validate a project model.

        Args:
            project: Project model to validate.

        Returns:
            Validation result.
        """
        result = ValidationResult(valid=True)

        # Validate name
        if not project.name:
            result.add_error("Name is required")
        elif len(project.name) > self.max_name_length:
            result.add_error(
                f"Name exceeds maximum length of {self.max_name_length} characters"
            )
        elif project.name.lower() in self.reserved_names:
            result.add_error(f"Name '{project.name}' is reserved")
        else:
            for pattern in self.reserved_name_patterns:
                if re.match(pattern, project.name):
                    result.add_error(
                        f"Name '{project.name}' matches reserved pattern '{pattern}'"
                    )
                    break

        # Validate description
        if (
            project.description
            and len(project.description) > self.max_description_length
        ):
            result.add_error(
                f"Description exceeds maximum length of {self.max_description_length} characters"
            )

        # Validate members
        if len(project.members) > self.max_members:
            result.add_error(f"Number of members exceeds maximum of {self.max_members}")

        # Validate member emails
        member_emails = set()
        for member in project.members:
            if not member.email:
                result.add_error("Member email is required")
            elif member.email in member_emails:
                result.add_error(f"Duplicate member email: {member.email}")
            else:
                member_emails.add(member.email)

        # Validate data sources
        if len(project.data_sources) > self.max_data_sources:
            result.add_error(
                f"Number of data sources exceeds maximum of {self.max_data_sources}"
            )

        # Validate data source IDs
        data_source_ids = set()
        for data_source in project.data_sources:
            if not data_source.id:
                result.add_error("Data source ID is required")
            elif data_source.id in data_source_ids:
                result.add_error(f"Duplicate data source ID: {data_source.id}")
            else:
                data_source_ids.add(data_source.id)

        return result

    def validate_batch(self, projects: List[ProjectModel]) -> List[ValidationResult]:
        """Validate a batch of project models.

        Args:
            projects: List of project models to validate.

        Returns:
            List of validation results.
        """
        results = []

        # Check for duplicate names across all projects
        names = set()
        for project in projects:
            if project.name in names:
                result = ValidationResult(valid=False)
                result.add_error(f"Duplicate project name across batch: {project.name}")
                results.append(result)
            else:
                names.add(project.name)
                results.append(self.validate(project))

        return results

    def validate_query_parameters(
        self, params: ProjectQueryParameters
    ) -> ValidationResult:
        """Validate project query parameters.

        Args:
            params: Query parameters to validate.

        Returns:
            Validation result.
        """
        result = ValidationResult(valid=True)

        # Validate limit
        if params.limit is not None and params.limit < 0:
            result.add_error("Limit must be a positive integer")

        # Validate offset
        if params.offset is not None and params.offset < 0:
            result.add_error("Offset must be a positive integer")

        # Validate search
        if params.search is not None and len(params.search) > 255:
            result.add_error("Search term must be less than 255 characters")

        return result

    def validate_project_id(self, project_id: str) -> ValidationResult:
        """Validate a project ID.

        Args:
            project_id: Project ID to validate.

        Returns:
            Validation result.
        """
        result = ValidationResult(valid=True)

        if not project_id:
            result.add_error("Project ID is required")

        # Validate ID format (assuming it's a string)
        if not isinstance(project_id, str):
            result.add_error("Project ID must be a string")

        return result

    def validate_project_update(
        self, project_id: str, project: ProjectModel
    ) -> ValidationResult:
        """Validate a project update.

        Args:
            project_id: Project ID to update.
            project: Updated project model.

        Returns:
            Validation result.
        """
        # Validate project ID
        id_result = self.validate_project_id(project_id)

        # Validate project model
        project_result = self.validate(project)

        # Combine results
        result = ValidationResult(valid=id_result.valid and project_result.valid)
        result.errors.extend(id_result.errors)
        result.errors.extend(project_result.errors)
        result.warnings.extend(id_result.warnings)
        result.warnings.extend(project_result.warnings)

        return result

    def validate_project_delete(self, project_id: str) -> ValidationResult:
        """Validate a project deletion.

        Args:
            project_id: Project ID to delete.

        Returns:
            Validation result.
        """
        return self.validate_project_id(project_id)

    def validate_add_data_source(
        self, project_id: str, data_source_id: str
    ) -> ValidationResult:
        """Validate adding a data source to a project.

        Args:
            project_id: Project ID.
            data_source_id: Data source ID to add.

        Returns:
            Validation result.
        """
        # Validate project ID
        project_result = self.validate_project_id(project_id)

        # Validate data source ID
        result = ValidationResult(valid=project_result.valid)
        result.errors.extend(project_result.errors)
        result.warnings.extend(project_result.warnings)

        if not data_source_id:
            result.add_error("Data source ID is required")

        return result

    def validate_remove_data_source(
        self, project_id: str, data_source_id: str
    ) -> ValidationResult:
        """Validate removing a data source from a project.

        Args:
            project_id: Project ID.
            data_source_id: Data source ID to remove.

        Returns:
            Validation result.
        """
        return self.validate_add_data_source(project_id, data_source_id)

    def validate_add_user(
        self, project_id: str, user_id: str, role: str
    ) -> ValidationResult:
        """Validate adding a user to a project.

        Args:
            project_id: Project ID.
            user_id: User ID to add.
            role: Role to assign to the user.

        Returns:
            Validation result.
        """
        # Validate project ID
        project_result = self.validate_project_id(project_id)

        # Validate user ID and role
        result = ValidationResult(valid=project_result.valid)
        result.errors.extend(project_result.errors)
        result.warnings.extend(project_result.warnings)

        if not user_id:
            result.add_error("User ID is required")

        if not role:
            result.add_error("Role is required")
        else:
            # Validate role value
            valid_roles = ["OWNER", "CONTRIBUTOR", "VIEWER"]
            if role not in valid_roles:
                result.add_error(f"Role must be one of {valid_roles}")

        return result

    def validate_remove_user(self, project_id: str, user_id: str) -> ValidationResult:
        """Validate removing a user from a project.

        Args:
            project_id: Project ID.
            user_id: User ID to remove.

        Returns:
            Validation result.
        """
        # Validate project ID
        project_result = self.validate_project_id(project_id)

        # Validate user ID
        result = ValidationResult(valid=project_result.valid)
        result.errors.extend(project_result.errors)
        result.warnings.extend(project_result.warnings)

        if not user_id:
            result.add_error("User ID is required")

        return result
