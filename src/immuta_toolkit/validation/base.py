"""Base validation classes for Immuta automation."""

from abc import ABC, abstractmethod
from enum import Enum, auto
from typing import Any, Dict, List, Optional, Tuple, TypeVar, Generic

from pydantic import BaseModel

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")  # Input type for validation


class ValidationLevel(Enum):
    """Validation level for validation results."""

    INFO = auto()
    WARNING = auto()
    ERROR = auto()


class ValidationResult(BaseModel):
    """Result of a validation operation.

    Attributes:
        is_valid: Whether the validation passed.
        level: Validation level (info, warning, error).
        message: Validation message.
        details: Additional details about the validation result.
    """

    is_valid: bool
    level: ValidationLevel
    message: str
    details: Optional[Dict[str, Any]] = None


class Validator(ABC, Generic[T]):
    """Base class for validators.

    This class provides a common interface for all validators, including
    validation methods and error reporting.

    Attributes:
        name: Validator name.
        description: Validator description.
    """

    def __init__(self):
        """Initialize the validator."""
        self.name = self.__class__.__name__
        self.description = self.__doc__ or "No description available"
        self.errors: List[str] = []

    @abstractmethod
    def validate(self, value: T) -> bool:
        """Validate a value.

        Args:
            value: Value to validate.

        Returns:
            True if the value is valid, False otherwise.
        """
        pass

    def add_error(self, error: str) -> None:
        """Add an error message.

        Args:
            error: Error message.
        """
        logger.debug(f"Validation error in {self.name}: {error}")
        self.errors.append(error)

    def get_errors(self) -> List[str]:
        """Get all error messages.

        Returns:
            List of error messages.
        """
        return self.errors

    def clear_errors(self) -> None:
        """Clear all error messages."""
        self.errors = []

    def validate_with_errors(self, value: T) -> Tuple[bool, List[str]]:
        """Validate a value and return errors.

        Args:
            value: Value to validate.

        Returns:
            Tuple of (is_valid, error_messages).
        """
        self.clear_errors()
        is_valid = self.validate(value)
        return is_valid, self.get_errors()


class ModelValidator(Validator[BaseModel]):
    """Base class for model validators.

    This class provides validation for Pydantic models, including
    field validation and business rule validation.
    """

    def validate(self, model: BaseModel) -> bool:
        """Validate a model.

        Args:
            model: Model to validate.

        Returns:
            True if the model is valid, False otherwise.
        """
        # Pydantic handles basic validation
        # This method is for additional business rule validation
        return self.validate_business_rules(model)

    def validate_business_rules(self, model: BaseModel) -> bool:
        """Validate business rules for a model.

        Args:
            model: Model to validate.

        Returns:
            True if the model passes all business rules, False otherwise.
        """
        # Subclasses should override this method to add custom validation
        return True


class DictValidator(Validator[Dict[str, Any]]):
    """Base class for dictionary validators.

    This class provides validation for dictionaries, including
    field validation and business rule validation.
    """

    def validate(self, data: Dict[str, Any]) -> bool:
        """Validate a dictionary.

        Args:
            data: Dictionary to validate.

        Returns:
            True if the dictionary is valid, False otherwise.
        """
        return self.validate_fields(data) and self.validate_business_rules(data)

    def validate_fields(self, data: Dict[str, Any]) -> bool:
        """Validate fields in a dictionary.

        Args:
            data: Dictionary to validate.

        Returns:
            True if all fields are valid, False otherwise.
        """
        # Subclasses should override this method to add field validation
        return True

    def validate_business_rules(self, data: Dict[str, Any]) -> bool:
        """Validate business rules for a dictionary.

        Args:
            data: Dictionary to validate.

        Returns:
            True if the dictionary passes all business rules, False otherwise.
        """
        # Subclasses should override this method to add custom validation
        return True


class ListValidator(Validator[List[T]]):
    """Base class for list validators.

    This class provides validation for lists, including
    item validation and list-level validation.
    """

    def __init__(self, item_validator: Optional[Validator[T]] = None):
        """Initialize the list validator.

        Args:
            item_validator: Validator for list items.
        """
        super().__init__()
        self.item_validator = item_validator

    def validate(self, items: List[T]) -> bool:
        """Validate a list.

        Args:
            items: List to validate.

        Returns:
            True if the list is valid, False otherwise.
        """
        # Validate each item if item validator is provided
        if self.item_validator:
            for i, item in enumerate(items):
                if not self.item_validator.validate(item):
                    for error in self.item_validator.get_errors():
                        self.add_error(f"Item {i}: {error}")
                    return False

        # Validate list-level rules
        return self.validate_list_rules(items)

    def validate_list_rules(self, items: List[T]) -> bool:
        """Validate list-level rules.

        Args:
            items: List to validate.

        Returns:
            True if the list passes all list-level rules, False otherwise.
        """
        # Subclasses should override this method to add custom validation
        return True
