"""Policy validators for Immuta automation."""

from typing import Dict, Any, List

from immuta_toolkit.models import PolicyModel
from immuta_toolkit.validation.base import ModelValidator, DictValidator
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class PolicyModelValidator(ModelValidator):
    """Validator for policy models."""

    def validate_business_rules(self, model: PolicyModel) -> bool:
        """Validate business rules for a policy model.
        
        Args:
            model: Policy model to validate.
            
        Returns:
            True if the model passes all business rules, False otherwise.
        """
        is_valid = True
        
        # Validate name
        if not model.name or len(model.name.strip()) < 3:
            self.add_error("Policy name must be at least 3 characters long")
            is_valid = False
        
        # Validate actions
        if not model.actions:
            self.add_error("Policy must have at least one action")
            is_valid = False
        else:
            for i, action in enumerate(model.actions):
                if not isinstance(action, dict):
                    self.add_error(f"Action {i} must be a dictionary")
                    is_valid = False
                elif "type" not in action:
                    self.add_error(f"Action {i} must have a type")
                    is_valid = False
        
        # Validate rules
        if not model.rules:
            self.add_error("Policy must have at least one rule")
            is_valid = False
        else:
            for i, rule in enumerate(model.rules):
                if not isinstance(rule, dict):
                    self.add_error(f"Rule {i} must be a dictionary")
                    is_valid = False
                elif "type" not in rule:
                    self.add_error(f"Rule {i} must have a type")
                    is_valid = False
        
        return is_valid


class PolicyDictValidator(DictValidator):
    """Validator for policy dictionaries."""

    def validate_fields(self, data: Dict[str, Any]) -> bool:
        """Validate fields in a policy dictionary.
        
        Args:
            data: Policy dictionary to validate.
            
        Returns:
            True if all fields are valid, False otherwise.
        """
        is_valid = True
        
        # Check required fields
        required_fields = ["name", "actions", "rules"]
        for field in required_fields:
            if field not in data:
                self.add_error(f"Missing required field: {field}")
                is_valid = False
        
        # If any required fields are missing, return early
        if not is_valid:
            return False
        
        # Validate name
        if not data.get("name") or not isinstance(data["name"], str) or len(data["name"].strip()) < 3:
            self.add_error("Policy name must be at least 3 characters long")
            is_valid = False
        
        # Validate actions
        actions = data.get("actions", [])
        if not actions or not isinstance(actions, list):
            self.add_error("Actions must be a non-empty list")
            is_valid = False
        else:
            for i, action in enumerate(actions):
                if not isinstance(action, dict):
                    self.add_error(f"Action {i} must be a dictionary")
                    is_valid = False
                elif "type" not in action:
                    self.add_error(f"Action {i} must have a type")
                    is_valid = False
        
        # Validate rules
        rules = data.get("rules", [])
        if not rules or not isinstance(rules, list):
            self.add_error("Rules must be a non-empty list")
            is_valid = False
        else:
            for i, rule in enumerate(rules):
                if not isinstance(rule, dict):
                    self.add_error(f"Rule {i} must be a dictionary")
                    is_valid = False
                elif "type" not in rule:
                    self.add_error(f"Rule {i} must have a type")
                    is_valid = False
        
        return is_valid
    
    def validate_business_rules(self, data: Dict[str, Any]) -> bool:
        """Validate business rules for a policy dictionary.
        
        Args:
            data: Policy dictionary to validate.
            
        Returns:
            True if the dictionary passes all business rules, False otherwise.
        """
        is_valid = True
        
        # Validate policy name uniqueness (would require access to existing policies)
        # This is just a placeholder for where you would add that logic
        
        # Validate action types
        actions = data.get("actions", [])
        valid_action_types = ["mask", "filter", "redact", "allow", "deny"]
        for i, action in enumerate(actions):
            action_type = action.get("type")
            if action_type not in valid_action_types:
                self.add_error(f"Action {i} has invalid type: {action_type}")
                is_valid = False
            
            # Validate action-specific fields
            if action_type == "mask":
                if "fields" not in action or not isinstance(action["fields"], list):
                    self.add_error(f"Mask action {i} must have a fields list")
                    is_valid = False
        
        # Validate rule types
        rules = data.get("rules", [])
        valid_rule_types = ["tag", "column", "purpose", "user", "group"]
        for i, rule in enumerate(rules):
            rule_type = rule.get("type")
            if rule_type not in valid_rule_types:
                self.add_error(f"Rule {i} has invalid type: {rule_type}")
                is_valid = False
            
            # Validate rule-specific fields
            if rule_type == "tag" and "value" not in rule:
                self.add_error(f"Tag rule {i} must have a value")
                is_valid = False
        
        return is_valid
