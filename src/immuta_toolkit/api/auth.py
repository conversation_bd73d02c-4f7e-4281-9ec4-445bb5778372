"""Authentication for the API service."""

import os
from datetime import datetime, timedelta
from typing import Dict, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import <PERSON>pt<PERSON>ontext

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Secret key and algorithm
SECRET_KEY = os.getenv("API_SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# Simple user database (in a real app, this would be a database)
USERS = {
    "admin": {
        "username": "admin",
        "hashed_password": pwd_context.hash("password"),
        "disabled": False,
    }
}


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash.

    Args:
        plain_password: Plain text password.
        hashed_password: Hashed password.

    Returns:
        True if the password matches the hash, False otherwise.
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Get password hash.

    Args:
        password: Plain text password.

    Returns:
        Hashed password.
    """
    return pwd_context.hash(password)


def get_user(username: str) -> Optional[Dict]:
    """Get user from database.

    Args:
        username: Username.

    Returns:
        User dictionary if found, None otherwise.
    """
    if username in USERS:
        user = USERS[username]
        return user
    return None


def authenticate_user(username: str, password: str) -> Optional[Dict]:
    """Authenticate user.

    Args:
        username: Username.
        password: Password.

    Returns:
        User dictionary if authentication is successful, None otherwise.
    """
    user = get_user(username)
    if not user:
        return None
    if not verify_password(password, user["hashed_password"]):
        return None
    return user


def create_access_token(data: Dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create access token.

    Args:
        data: Data to encode in the token.
        expires_delta: Token expiration time.

    Returns:
        Encoded JWT token.
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=15))
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme)) -> Dict:
    """Get current user from token.

    Args:
        token: JWT token.

    Returns:
        User dictionary.

    Raises:
        HTTPException: If the token is invalid.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = get_user(username)
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(current_user: Dict = Depends(get_current_user)) -> Dict:
    """Get current active user.

    Args:
        current_user: Current user dictionary.

    Returns:
        User dictionary.

    Raises:
        HTTPException: If the user is disabled.
    """
    if current_user.get("disabled"):
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
