"""Authentication models for Immuta SRE Toolkit API."""

from typing import Optional
from pydantic import BaseModel


class Token(BaseModel):
    """Token model.

    Attributes:
        access_token: JWT access token.
        token_type: Token type.
    """

    access_token: str
    token_type: str


class TokenData(BaseModel):
    """Token data model.

    Attributes:
        username: Username.
        scopes: Token scopes.
    """

    username: Optional[str] = None
    scopes: list[str] = []
