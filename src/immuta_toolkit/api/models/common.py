"""Common models for Immuta SRE Toolkit API."""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel


class ErrorResponse(BaseModel):
    """Error response model.

    Attributes:
        detail: Error detail.
    """

    detail: str


class SuccessResponse(BaseModel):
    """Success response model.

    Attributes:
        message: Success message.
    """

    message: str
