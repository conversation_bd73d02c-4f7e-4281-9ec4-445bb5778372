"""Client factory for Immuta SRE Toolkit API."""

import os
from typing import Optional

from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Client instance
_client = None


def get_client() -> ImmutaHybridClient:
    """Get or create the Immuta hybrid client.

    Returns:
        Immuta hybrid client instance.
    """
    global _client
    if _client is None:
        _client = ImmutaHybridClient(
            api_key=os.getenv("IMMUTA_API_KEY"),
            base_url=os.getenv("IMMUTA_BASE_URL"),
            username=os.getenv("IMMUTA_USERNAME"),
            password=os.getenv("IMMUTA_PASSWORD"),
            use_web_fallback=os.getenv("IMMUTA_USE_WEB_FALLBACK", "true").lower()
            == "true",
            headless=os.getenv("IMMUTA_HEADLESS", "true").lower() == "true",
            is_local=os.getenv("IMMUTA_LOCAL", "false").lower() == "true",
        )
    return _client


def close_client() -> None:
    """Close the Immuta hybrid client."""
    global _client
    if _client:
        _client.close()
        _client = None
        logger.info("Closed Immuta client")
