"""Middleware for the API service."""

import time
from typing import Di<PERSON>, <PERSON><PERSON>, Callable, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Simple in-memory cache
cache: Dict[str, Tuple[float, any]] = {}
cache_ttl = 60  # seconds

# Rate limiting
rate_limit: Dict[str, Tuple[int, float]] = {}
rate_limit_max = 10  # requests per minute
rate_limit_window = 60  # seconds


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware.

    This middleware limits the number of requests per client IP address.

    Attributes:
        max_requests: Maximum number of requests per window.
        window_size: Window size in seconds.
    """

    def __init__(
        self,
        app,
        max_requests: int = rate_limit_max,
        window_size: int = rate_limit_window,
    ):
        """Initialize the rate limiting middleware.

        Args:
            app: FastAPI application.
            max_requests: Maximum number of requests per window.
            window_size: Window size in seconds.
        """
        super().__init__(app)
        self.max_requests = max_requests
        self.window_size = window_size
        logger.info(
            f"Rate limiting middleware initialized with {max_requests} requests per {window_size} seconds"
        )

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Dispatch request with rate limiting.

        Args:
            request: FastAPI request.
            call_next: Next middleware or endpoint.

        Returns:
            FastAPI response.
        """
        # Skip rate limiting for certain paths
        if request.url.path in ["/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)

        # Get client IP
        client_ip = request.client.host if request.client else "unknown"
        
        # Check rate limit
        now = time.time()
        if client_ip in rate_limit:
            count, window_start = rate_limit[client_ip]
            if now - window_start > self.window_size:
                # Reset window
                rate_limit[client_ip] = (1, now)
                logger.debug(f"Rate limit window reset for {client_ip}")
            elif count >= self.max_requests:
                # Rate limit exceeded
                logger.warning(f"Rate limit exceeded for {client_ip}")
                return Response(
                    content="Rate limit exceeded",
                    status_code=HTTP_429_TOO_MANY_REQUESTS,
                )
            else:
                # Increment count
                rate_limit[client_ip] = (count + 1, window_start)
                logger.debug(f"Request count for {client_ip}: {count + 1}")
        else:
            # First request
            rate_limit[client_ip] = (1, now)
            logger.debug(f"First request for {client_ip}")
        
        # Process request
        return await call_next(request)


class CacheMiddleware(BaseHTTPMiddleware):
    """Cache middleware.

    This middleware caches responses for GET requests.

    Attributes:
        ttl: Cache TTL in seconds.
    """

    def __init__(self, app, ttl: int = cache_ttl):
        """Initialize the cache middleware.

        Args:
            app: FastAPI application.
            ttl: Cache TTL in seconds.
        """
        super().__init__(app)
        self.ttl = ttl
        logger.info(f"Cache middleware initialized with TTL {ttl} seconds")

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Dispatch request with caching.

        Args:
            request: FastAPI request.
            call_next: Next middleware or endpoint.

        Returns:
            FastAPI response.
        """
        # Only cache GET requests
        if request.method != "GET":
            return await call_next(request)
        
        # Skip caching for certain paths
        if request.url.path in ["/", "/docs", "/redoc", "/openapi.json"]:
            return await call_next(request)
        
        # Generate cache key
        cache_key = f"{request.method}:{request.url.path}:{request.query_params}"
        
        # Check cache
        now = time.time()
        if cache_key in cache and now - cache[cache_key][0] < self.ttl:
            logger.debug(f"Cache hit for {cache_key}")
            return cache[cache_key][1]
        
        # Process request
        response = await call_next(request)
        
        # Cache response
        if response.status_code == 200:
            logger.debug(f"Caching response for {cache_key}")
            cache[cache_key] = (now, response)
        
        return response
