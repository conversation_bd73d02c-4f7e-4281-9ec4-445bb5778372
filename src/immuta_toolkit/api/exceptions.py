"""Exceptions for the Immuta API client."""

from typing import Dict, Optional, Any, List, Union

from immuta_toolkit.utils.error_handling import BaseError


class ImmutaApiError(BaseError):
    """Base exception for Immuta API errors."""

    pass


class AuthenticationError(ImmutaApiError):
    """Exception raised when authentication fails."""

    pass


class AuthorizationError(ImmutaApiError):
    """Exception raised when authorization fails."""

    pass


class ResourceNotFoundError(ImmutaApiError):
    """Exception raised when a resource is not found."""

    pass


class ValidationError(ImmutaApiError):
    """Exception raised when validation fails."""

    pass


class RateLimitError(ImmutaApiError):
    """Exception raised when rate limit is exceeded."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[Union[Dict[str, Any], str]] = None,
        request_info: Optional[Dict[str, Any]] = None,
        retry_after: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            status_code: HTTP status code.
            response_body: Response body.
            request_info: Request information.
            retry_after: Seconds to wait before retrying.
            context: Additional context information.
        """
        super().__init__(message, status_code, response_body, request_info, context=context)
        self.retry_after = retry_after


class ServerError(ImmutaApiError):
    """Exception raised when a server error occurs."""

    pass


class CircuitBreakerError(ImmutaApiError):
    """Exception raised when the circuit breaker is open."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[Union[Dict[str, Any], str]] = None,
        request_info: Optional[Dict[str, Any]] = None,
        reset_timeout: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            status_code: HTTP status code.
            response_body: Response body.
            request_info: Request information.
            reset_timeout: Seconds until the circuit breaker resets.
            context: Additional context information.
        """
        super().__init__(message, status_code, response_body, request_info, context=context)
        self.reset_timeout = reset_timeout


class TimeoutError(ImmutaApiError):
    """Exception raised when a request times out."""

    pass


class ConnectionError(ImmutaApiError):
    """Exception raised when a connection error occurs."""

    pass


class ApiVersionError(ImmutaApiError):
    """Exception raised when API version is incompatible."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        response_body: Optional[Union[Dict[str, Any], str]] = None,
        request_info: Optional[Dict[str, Any]] = None,
        current_version: Optional[str] = None,
        supported_versions: Optional[List[str]] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            status_code: HTTP status code.
            response_body: Response body.
            request_info: Request information.
            current_version: Current API version.
            supported_versions: Supported API versions.
            context: Additional context information.
        """
        super().__init__(message, status_code, response_body, request_info, context=context)
        self.current_version = current_version
        self.supported_versions = supported_versions


def map_http_error_to_exception(
    status_code: int,
    message: str,
    response_body: Optional[Union[Dict[str, Any], str]] = None,
    request_info: Optional[Dict[str, Any]] = None,
) -> ImmutaApiError:
    """Map HTTP error to appropriate exception.

    Args:
        status_code: HTTP status code.
        message: Error message.
        response_body: Response body.
        request_info: Request information.

    Returns:
        Appropriate exception.
    """
    if status_code == 401:
        return AuthenticationError(message, status_code, response_body, request_info)
    elif status_code == 403:
        return AuthorizationError(message, status_code, response_body, request_info)
    elif status_code == 404:
        return ResourceNotFoundError(message, status_code, response_body, request_info)
    elif status_code == 422:
        return ValidationError(message, status_code, response_body, request_info)
    elif status_code == 429:
        retry_after = None
        if isinstance(response_body, dict) and "retry-after" in response_body:
            retry_after = response_body["retry-after"]
        return RateLimitError(
            message, status_code, response_body, request_info, retry_after
        )
    elif 500 <= status_code < 600:
        return ServerError(message, status_code, response_body, request_info)
    else:
        return ImmutaApiError(message, status_code, response_body, request_info)
