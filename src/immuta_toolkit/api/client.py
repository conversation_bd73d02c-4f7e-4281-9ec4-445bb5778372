"""API client for Immuta SRE Toolkit."""

import requests
from typing import Dict, List, Optional, Union, Any, TypeVar
from urllib.parse import urljoin
from requests.exceptions import (
    RequestException,
    Timeout,
    ConnectionError as RequestsConnectionError,
)

from immuta_toolkit.utils.retry import with_retry
from immuta_toolkit.utils.error_handling import handle_api_error

from immuta_toolkit.api.exceptions import (
    ImmutaApiError,
    ResourceNotFoundError,
    ValidationError,
    CircuitBreakerError,
    TimeoutError,
    ConnectionError,
    ApiVersionError,
    map_http_error_to_exception,
)
from immuta_toolkit.utils.circuit_breaker import CircuitBreaker
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.rate_limiter import RateLimiter

logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar("T")


class ImmutaApiClient:
    """API client for Immuta.

    This client provides a high-level interface for interacting with the
    Immuta API with support for versioning, error handling, rate limiting,
    and circuit breaking.

    Attributes:
        base_url: Base URL of the Immuta API.
        api_key: API key for authentication.
        api_version: API version to use.
        timeout: Default timeout for API requests in seconds.
        rate_limiter: Rate limiter for API requests.
        circuit_breaker: Circuit breaker for API calls.
        is_local: Whether to run in local mode (mock API calls).
    """

    # Supported API versions
    SUPPORTED_VERSIONS = ["v1"]

    # Default API version
    DEFAULT_VERSION = "v1"

    def __init__(
        self,
        base_url: str,
        api_key: Optional[str] = None,
        api_version: Optional[str] = None,
        timeout: int = 30,
        max_retries: int = 3,
        backoff_factor: float = 0.5,
        rate_limit: int = 10,
        rate_limit_window: int = 1,
        circuit_breaker_threshold: int = 5,
        circuit_breaker_timeout: int = 60,
        is_local: bool = False,
    ):
        """Initialize the API client.

        Args:
            base_url: Base URL of the Immuta API.
            api_key: API key for authentication.
            api_version: API version to use. If None, will use the default version.
            timeout: Default timeout for API requests in seconds.
            max_retries: Maximum number of retries for failed requests.
            backoff_factor: Backoff factor for retries.
            rate_limit: Maximum number of requests per second.
            rate_limit_window: Window size for rate limiting in seconds.
            circuit_breaker_threshold: Number of failures before opening the circuit.
            circuit_breaker_timeout: Seconds to wait before attempting to close the circuit.
            is_local: Whether to run in local mode (mock API calls).

        Raises:
            ApiVersionError: If the API version is not supported.
        """
        # Normalize base URL
        self.base_url = base_url.rstrip("/")

        # Set API version
        self.api_version = api_version or self.DEFAULT_VERSION
        if self.api_version not in self.SUPPORTED_VERSIONS:
            raise ApiVersionError(
                f"Unsupported API version: {self.api_version}",
                current_version=self.api_version,
                supported_versions=self.SUPPORTED_VERSIONS,
            )

        # Set other attributes
        self.api_key = api_key
        self.timeout = timeout
        self.max_retries = max_retries
        self.is_local = is_local

        # Initialize rate limiter
        self.rate_limiter = RateLimiter(rate_limit, rate_limit_window)

        # Initialize circuit breaker
        self.circuit_breaker = CircuitBreaker(
            name="immuta-api",
            failure_threshold=circuit_breaker_threshold,
            reset_timeout=circuit_breaker_timeout,
            exclude_exceptions=[ResourceNotFoundError, ValidationError],
        )

        # Set up session
        self.session = requests.Session()

        # We'll use our custom retry utility instead of the built-in requests retry mechanism
        self.backoff_factor = backoff_factor

        # Set up authentication
        if api_key:
            self.session.headers.update({"Authorization": f"Bearer {api_key}"})

        # Set up common headers
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "immuta-sre-toolkit/1.0",
            }
        )

    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        use_rate_limiter: bool = True,
        use_circuit_breaker: bool = True,
        raw_response: bool = False,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]], requests.Response]:
        """Make an API request with rate limiting and circuit breaking.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE, PATCH).
            endpoint: API endpoint (without base URL and version).
            params: Query parameters.
            data: Form data (for POST/PUT/PATCH).
            json_data: JSON data (for POST/PUT/PATCH).
            headers: Additional headers.
            use_rate_limiter: Whether to use rate limiting.
            use_circuit_breaker: Whether to use circuit breaking.
            raw_response: Whether to return the raw response object.

        Returns:
            Response data as dictionary or list, or raw response if raw_response=True.

        Raises:
            ImmutaApiError: If the request fails.
        """
        # Normalize endpoint and build URL
        endpoint = endpoint.lstrip("/")

        # Add API version to the URL if not already included
        if self.api_version and f"/api/{self.api_version}/" not in endpoint:
            url = urljoin(self.base_url, f"api/{self.api_version}/{endpoint}")
        else:
            url = urljoin(self.base_url, endpoint)

        # Merge headers
        request_headers = headers or {}

        # Handle mock mode
        if self.is_local:
            logger.info(f"Mock request: {method} {url}")
            # Create a mock response
            mock_response = requests.Response()
            mock_response.status_code = 200
            mock_response._content = b"{}"
            return mock_response if raw_response else {}

        # Define the request function
        def request_func() -> requests.Response:
            try:
                return self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    data=data,
                    json=json_data,
                    headers=request_headers,
                    timeout=self.timeout,
                )
            except Timeout as e:
                raise handle_api_error(
                    error=e,
                    error_message=f"Request timed out: {str(e)}",
                    error_class=TimeoutError,
                    request_info={
                        "url": url,
                        "method": method,
                        "timeout": self.timeout,
                    },
                )
            except RequestsConnectionError as e:
                raise handle_api_error(
                    error=e,
                    error_message=f"Connection error: {str(e)}",
                    error_class=ConnectionError,
                    request_info={"url": url, "method": method},
                )
            except RequestException as e:
                raise handle_api_error(
                    error=e,
                    error_message=f"Request failed: {str(e)}",
                    error_class=ImmutaApiError,
                    request_info={"url": url, "method": method},
                )

        # Apply retry logic to the request function
        retryable_request_func = with_retry(
            request_func,
            max_retries=self.max_retries,
            retry_delay=1.0,
            backoff_factor=self.backoff_factor,
            jitter=True,
            exceptions_to_retry=[RequestException],
            exceptions_to_ignore=[ImmutaApiError, TimeoutError, ConnectionError],
        )

        # Apply rate limiting if enabled
        if use_rate_limiter:
            self.rate_limiter.acquire()

        # Apply circuit breaking if enabled
        if use_circuit_breaker:
            try:
                response = self.circuit_breaker.call(retryable_request_func)
            except CircuitBreakerError as e:
                raise handle_api_error(
                    error=e,
                    error_message=f"Circuit breaker is open: {str(e)}",
                    error_class=CircuitBreakerError,
                    request_info={"url": url, "method": method},
                    context={"reset_timeout": getattr(e, "reset_timeout", None)},
                )
        else:
            response = retryable_request_func()

        # Handle error responses
        if response.status_code >= 400:
            try:
                response_body = response.json()
            except ValueError:
                response_body = response.text

            error_message = f"API request failed: {method} {url}"
            if isinstance(response_body, dict) and "message" in response_body:
                error_message = f"{error_message} - {response_body['message']}"

            # Use the common error handling utility
            exception_class = map_http_error_to_exception(
                response.status_code,
                error_message,
                response_body,
                {"url": url, "method": method},
            ).__class__

            raise handle_api_error(
                error=Exception(error_message),
                error_message=error_message,
                error_class=exception_class,
                status_code=response.status_code,
                response_body=response_body,
                request_info={"url": url, "method": method},
            )

        # Return raw response if requested
        if raw_response:
            return response

        # Parse response
        if response.status_code == 204 or not response.content:
            return {}

        try:
            return response.json()
        except ValueError:
            return response.text

    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Make a GET request.

        Args:
            endpoint: API endpoint.
            params: Query parameters.
            headers: Additional headers.
            **kwargs: Additional arguments to pass to _make_request.

        Returns:
            Response data.
        """
        return self._make_request(
            method="GET",
            endpoint=endpoint,
            params=params,
            headers=headers,
            **kwargs,
        )

    def post(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Make a POST request.

        Args:
            endpoint: API endpoint.
            json_data: JSON data.
            data: Form data.
            params: Query parameters.
            headers: Additional headers.
            **kwargs: Additional arguments to pass to _make_request.

        Returns:
            Response data.
        """
        return self._make_request(
            method="POST",
            endpoint=endpoint,
            json_data=json_data,
            data=data,
            params=params,
            headers=headers,
            **kwargs,
        )

    def put(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Make a PUT request.

        Args:
            endpoint: API endpoint.
            json_data: JSON data.
            data: Form data.
            params: Query parameters.
            headers: Additional headers.
            **kwargs: Additional arguments to pass to _make_request.

        Returns:
            Response data.
        """
        return self._make_request(
            method="PUT",
            endpoint=endpoint,
            json_data=json_data,
            data=data,
            params=params,
            headers=headers,
            **kwargs,
        )

    def delete(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Make a DELETE request.

        Args:
            endpoint: API endpoint.
            params: Query parameters.
            headers: Additional headers.
            **kwargs: Additional arguments to pass to _make_request.

        Returns:
            Response data.
        """
        return self._make_request(
            method="DELETE",
            endpoint=endpoint,
            params=params,
            headers=headers,
            **kwargs,
        )

    def patch(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs,
    ) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
        """Make a PATCH request.

        Args:
            endpoint: API endpoint.
            json_data: JSON data.
            data: Form data.
            params: Query parameters.
            headers: Additional headers.
            **kwargs: Additional arguments to pass to _make_request.

        Returns:
            Response data.
        """
        return self._make_request(
            method="PATCH",
            endpoint=endpoint,
            json_data=json_data,
            data=data,
            params=params,
            headers=headers,
            **kwargs,
        )

    def get_circuit_breaker_state(self) -> Dict[str, Any]:
        """Get the current state of the circuit breaker.

        Returns:
            Dictionary with circuit breaker state information.
        """
        return self.circuit_breaker.get_state()

    def reset_circuit_breaker(self) -> None:
        """Reset the circuit breaker to closed state."""
        self.circuit_breaker.reset()
        logger.info("Reset circuit breaker to CLOSED state")

    def close(self):
        """Close the client and release resources."""
        # Close the session
        if hasattr(self, "session"):
            self.session.close()

        # Reset the circuit breaker
        if hasattr(self, "circuit_breaker"):
            self.circuit_breaker.reset()

        logger.info("Closed Immuta API client")
