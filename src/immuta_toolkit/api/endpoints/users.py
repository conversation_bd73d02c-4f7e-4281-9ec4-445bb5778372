"""User endpoints for Immuta SRE Toolkit API."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models import UserModel
from immuta_toolkit.api.auth import get_current_active_user
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.api.client_factory import get_client
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/users",
    tags=["users"],
    dependencies=[Depends(get_current_active_user)],
)


@router.get("/", response_model=List[UserModel])
def list_users(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> List[UserModel]:
    """List users.

    Args:
        limit: Maximum number of users to return.
        offset: Offset for pagination.
        search: Search term.
        current_user: Current user.

    Returns:
        List of users.
    """
    client = get_client()
    return client.list_users(limit=limit, offset=offset, search=search)


@router.get("/{user_id}", response_model=UserModel)
def get_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> UserModel:
    """Get a user by ID.

    Args:
        user_id: User ID.
        current_user: Current user.

    Returns:
        User.

    Raises:
        HTTPException: If the user is not found.
    """
    client = get_client()
    user = client.get_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found",
        )
    return user


@router.post("/", response_model=UserModel, status_code=status.HTTP_201_CREATED)
def create_user(
    user: UserModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> UserModel:
    """Create a user.

    Args:
        user: User to create.
        current_user: Current user.

    Returns:
        Created user.
    """
    client = get_client()
    return client.create_user(user)


@router.put("/{user_id}", response_model=UserModel)
def update_user(
    user_id: str,
    user: UserModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> UserModel:
    """Update a user.

    Args:
        user_id: User ID.
        user: Updated user.
        current_user: Current user.

    Returns:
        Updated user.

    Raises:
        HTTPException: If the user is not found.
    """
    client = get_client()
    updated_user = client.update_user(user_id, user)
    if not updated_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found",
        )
    return updated_user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(
    user_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> None:
    """Delete a user.

    Args:
        user_id: User ID.
        current_user: Current user.

    Raises:
        HTTPException: If the user is not found.
    """
    client = get_client()
    success = client.delete_user(user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User {user_id} not found",
        )
