"""Data source endpoints for Immuta SRE Toolkit API."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models import DataSourceModel, DataSourceQueryParameters
from immuta_toolkit.api.auth import get_current_active_user
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.api.client_factory import get_client
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/data-sources",
    tags=["data_sources"],
    dependencies=[Depends(get_current_active_user)],
)


@router.get("/", response_model=List[DataSourceModel])
def list_data_sources(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    tag: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> List[DataSourceModel]:
    """List data sources.

    Args:
        limit: Maximum number of data sources to return.
        offset: Offset for pagination.
        search: Search term.
        tag: Filter by tag.
        current_user: Current user.

    Returns:
        List of data sources.
    """
    client = get_client()
    query_params = DataSourceQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
        tag=tag,
    )
    return client.list_data_sources(query_params=query_params)


@router.get("/{data_source_id}", response_model=DataSourceModel)
def get_data_source(
    data_source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> DataSourceModel:
    """Get a data source by ID.

    Args:
        data_source_id: Data source ID.
        current_user: Current user.

    Returns:
        Data source.

    Raises:
        HTTPException: If the data source is not found.
    """
    client = get_client()
    data_source = client.get_data_source(data_source_id)
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found",
        )
    return data_source


@router.post("/", response_model=DataSourceModel, status_code=status.HTTP_201_CREATED)
def create_data_source(
    data_source: DataSourceModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> DataSourceModel:
    """Create a data source.

    Args:
        data_source: Data source to create.
        current_user: Current user.

    Returns:
        Created data source.
    """
    client = get_client()
    return client.create_data_source(data_source)


@router.put("/{data_source_id}", response_model=DataSourceModel)
def update_data_source(
    data_source_id: str,
    data_source: DataSourceModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> DataSourceModel:
    """Update a data source.

    Args:
        data_source_id: Data source ID.
        data_source: Updated data source.
        current_user: Current user.

    Returns:
        Updated data source.

    Raises:
        HTTPException: If the data source is not found.
    """
    client = get_client()
    updated_data_source = client.update_data_source(data_source_id, data_source)
    if not updated_data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found",
        )
    return updated_data_source


@router.delete("/{data_source_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_data_source(
    data_source_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> None:
    """Delete a data source.

    Args:
        data_source_id: Data source ID.
        current_user: Current user.

    Raises:
        HTTPException: If the data source is not found.
    """
    client = get_client()
    success = client.delete_data_source(data_source_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Data source {data_source_id} not found",
        )
