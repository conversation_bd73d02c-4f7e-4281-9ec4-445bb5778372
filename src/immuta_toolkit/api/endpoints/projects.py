"""Project endpoints for Immuta SRE Toolkit API."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models import ProjectModel
from immuta_toolkit.api.auth import get_current_active_user
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.api.client_factory import get_client
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/projects",
    tags=["projects"],
    dependencies=[Depends(get_current_active_user)],
)


@router.get("/", response_model=List[ProjectModel])
def list_projects(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> List[ProjectModel]:
    """List projects.

    Args:
        limit: Maximum number of projects to return.
        offset: Offset for pagination.
        search: Search term.
        current_user: Current user.

    Returns:
        List of projects.
    """
    client = get_client()
    return client.list_projects(limit=limit, offset=offset, search=search)


@router.get("/{project_id}", response_model=ProjectModel)
def get_project(
    project_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> ProjectModel:
    """Get a project by ID.

    Args:
        project_id: Project ID.
        current_user: Current user.

    Returns:
        Project.

    Raises:
        HTTPException: If the project is not found.
    """
    client = get_client()
    project = client.get_project(project_id)
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found",
        )
    return project


@router.post("/", response_model=ProjectModel, status_code=status.HTTP_201_CREATED)
def create_project(
    project: ProjectModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> ProjectModel:
    """Create a project.

    Args:
        project: Project to create.
        current_user: Current user.

    Returns:
        Created project.
    """
    client = get_client()
    return client.create_project(project)


@router.put("/{project_id}", response_model=ProjectModel)
def update_project(
    project_id: str,
    project: ProjectModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> ProjectModel:
    """Update a project.

    Args:
        project_id: Project ID.
        project: Updated project.
        current_user: Current user.

    Returns:
        Updated project.

    Raises:
        HTTPException: If the project is not found.
    """
    client = get_client()
    updated_project = client.update_project(project_id, project)
    if not updated_project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found",
        )
    return updated_project


@router.delete("/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_project(
    project_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> None:
    """Delete a project.

    Args:
        project_id: Project ID.
        current_user: Current user.

    Raises:
        HTTPException: If the project is not found.
    """
    client = get_client()
    success = client.delete_project(project_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Project {project_id} not found",
        )
