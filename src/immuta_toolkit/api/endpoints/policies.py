"""Policy endpoints for Immuta SRE Toolkit API."""

from typing import Dict, List, Optional, Union, Any
from fastapi import APIRouter, HTTPException, Depends, status

from immuta_toolkit.models import PolicyModel, PolicyQueryParameters
from immuta_toolkit.api.auth import get_current_active_user
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.api.client_factory import get_client
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/policies",
    tags=["policies"],
    dependencies=[Depends(get_current_active_user)],
)


@router.get("/", response_model=List[PolicyModel])
def list_policies(
    limit: int = 100,
    offset: int = 0,
    search: Optional[str] = None,
    policy_type: Optional[str] = None,
    data_source_id: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> List[PolicyModel]:
    """List policies.

    Args:
        limit: Maximum number of policies to return.
        offset: Offset for pagination.
        search: Search term.
        policy_type: Filter by policy type.
        data_source_id: Filter by data source ID.
        current_user: Current user.

    Returns:
        List of policies.
    """
    client = get_client()
    query_params = PolicyQueryParameters(
        limit=limit,
        offset=offset,
        search=search,
        policy_type=policy_type,
        data_source_id=data_source_id,
    )
    return client.list_policies(query_params=query_params)


@router.get("/{policy_id}", response_model=PolicyModel)
def get_policy(
    policy_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> PolicyModel:
    """Get a policy by ID.

    Args:
        policy_id: Policy ID.
        current_user: Current user.

    Returns:
        Policy.

    Raises:
        HTTPException: If the policy is not found.
    """
    client = get_client()
    policy = client.get_policy(policy_id)
    if not policy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found",
        )
    return policy


@router.post("/", response_model=PolicyModel, status_code=status.HTTP_201_CREATED)
def create_policy(
    policy: PolicyModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> PolicyModel:
    """Create a policy.

    Args:
        policy: Policy to create.
        current_user: Current user.

    Returns:
        Created policy.
    """
    client = get_client()
    return client.create_policy(policy)


@router.put("/{policy_id}", response_model=PolicyModel)
def update_policy(
    policy_id: str,
    policy: PolicyModel,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> PolicyModel:
    """Update a policy.

    Args:
        policy_id: Policy ID.
        policy: Updated policy.
        current_user: Current user.

    Returns:
        Updated policy.

    Raises:
        HTTPException: If the policy is not found.
    """
    client = get_client()
    updated_policy = client.update_policy(policy_id, policy)
    if not updated_policy:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found",
        )
    return updated_policy


@router.delete("/{policy_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_policy(
    policy_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
) -> None:
    """Delete a policy.

    Args:
        policy_id: Policy ID.
        current_user: Current user.

    Raises:
        HTTPException: If the policy is not found.
    """
    client = get_client()
    success = client.delete_policy(policy_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Policy {policy_id} not found",
        )
