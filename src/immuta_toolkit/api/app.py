"""FastAPI application for Immuta SRE Toolkit."""

import os
from datetime import timedel<PERSON>

from fastapi import FastAP<PERSON>, HTTPException, Depends, status
from fastapi.security import OAuth2PasswordRequestForm

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.api.auth import (
    authenticate_user,
    create_access_token,
    ACCESS_TOKEN_EXPIRE_MINUTES,
)
from immuta_toolkit.api.models.auth import Token
from immuta_toolkit.api.client_factory import get_client, close_client

logger = get_logger(__name__)

app = FastAPI(
    title="Immuta SRE Toolkit API",
    description="API for managing Immuta resources",
    version="0.2.0",
)

# Import middleware
from immuta_toolkit.api.middleware import RateLimitMiddleware, CacheMiddleware

# Add middleware
app.add_middleware(RateLimitMiddleware, max_requests=20, window_size=60)
app.add_middleware(CacheMiddleware, ttl=60)

# Import routers
from immuta_toolkit.api.endpoints.users import router as users_router
from immuta_toolkit.api.endpoints.data_sources import router as data_sources_router
from immuta_toolkit.api.endpoints.policies import router as policies_router
from immuta_toolkit.api.endpoints.projects import router as projects_router

# Include routers
app.include_router(users_router)
app.include_router(data_sources_router)
app.include_router(policies_router)
app.include_router(projects_router)


@app.get("/")
def read_root():
    """Root endpoint.

    Returns:
        Welcome message.
    """
    return {"message": "Welcome to Immuta SRE Toolkit API"}


@app.post("/token", response_model=Token)
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    """Get access token.

    Args:
        form_data: Form data with username and password.

    Returns:
        Access token.

    Raises:
        HTTPException: If authentication fails.
    """
    user = authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user["username"]}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}


@app.on_event("shutdown")
def shutdown_event():
    """Shutdown event handler.

    Closes the client when the application shuts down.
    """
    close_client()
    logger.info("Closed Immuta client on shutdown")
