"""Configuration models for Immuta automation."""

import os
from enum import Enum
from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field, field_validator


class EnvironmentType(str, Enum):
    """Environment type."""

    LOCAL = "local"
    DEV = "dev"
    TEST = "test"
    PROD = "prod"


class ApiConfig(BaseModel):
    """API configuration."""

    base_url: str = Field(..., description="Base URL of the Immuta API")
    api_key: Optional[str] = Field(None, description="API key for authentication")
    timeout: int = Field(30, description="Default timeout for API calls in seconds")
    max_retries: int = Field(3, description="Maximum number of retries for API calls")
    retry_delay: float = Field(1.0, description="Delay between retries in seconds")


class WebConfig(BaseModel):
    """Web automation configuration."""

    base_url: str = Field(..., description="Base URL of the Immuta web UI")
    username: Optional[str] = Field(None, description="Username for authentication")
    password: Optional[str] = Field(None, description="Password for authentication")
    headless: bool = Field(True, description="Whether to run the browser in headless mode")
    browser_type: str = Field("chromium", description="Browser type (chromium, firefox, webkit)")
    timeout: int = Field(30000, description="Default timeout for actions in milliseconds")


class SecretsConfig(BaseModel):
    """Secrets configuration."""

    provider: str = Field("env", description="Secrets provider (env, azure)")
    vault_url: Optional[str] = Field(None, description="URL of the Azure Key Vault")
    key_prefix: str = Field("IMMUTA_", description="Prefix for environment variables")


class StorageConfig(BaseModel):
    """Storage configuration."""

    provider: str = Field("file", description="Storage provider (file, azure)")
    connection_string: Optional[str] = Field(None, description="Azure Storage connection string")
    container_name: Optional[str] = Field(None, description="Azure Storage container name")
    local_path: str = Field("./data", description="Local storage path")


class LoggingConfig(BaseModel):
    """Logging configuration."""

    level: str = Field("INFO", description="Logging level")
    format: str = Field(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Logging format",
    )
    file: Optional[str] = Field(None, description="Log file path")
    use_structlog: bool = Field(True, description="Whether to use structlog")


class ReportingConfig(BaseModel):
    """Reporting configuration."""

    output_dir: str = Field("./reports", description="Output directory for reports")
    default_format: str = Field("json", description="Default report format")
    include_timestamp: bool = Field(True, description="Whether to include timestamp in report filenames")


class OperationConfig(BaseModel):
    """Operation configuration."""

    use_web_fallback: bool = Field(True, description="Whether to use web automation as a fallback")
    max_retries: int = Field(3, description="Maximum number of retries for operations")
    retry_delay: float = Field(1.0, description="Delay between retries in seconds")
    batch_size: int = Field(10, description="Default batch size for batch operations")


class Config(BaseModel):
    """Configuration model."""

    environment: EnvironmentType = Field(EnvironmentType.DEV, description="Environment type")
    api: ApiConfig = Field(..., description="API configuration")
    web: WebConfig = Field(..., description="Web automation configuration")
    secrets: SecretsConfig = Field(default_factory=SecretsConfig, description="Secrets configuration")
    storage: StorageConfig = Field(default_factory=StorageConfig, description="Storage configuration")
    logging: LoggingConfig = Field(default_factory=LoggingConfig, description="Logging configuration")
    reporting: ReportingConfig = Field(default_factory=ReportingConfig, description="Reporting configuration")
    operation: OperationConfig = Field(default_factory=OperationConfig, description="Operation configuration")
    custom: Dict[str, Any] = Field(default_factory=dict, description="Custom configuration")

    @classmethod
    def from_env(cls) -> "Config":
        """Create a configuration from environment variables.
        
        Returns:
            Configuration instance.
        """
        return cls(
            environment=os.getenv("IMMUTA_ENVIRONMENT", EnvironmentType.DEV),
            api=ApiConfig(
                base_url=os.getenv("IMMUTA_BASE_URL", ""),
                api_key=os.getenv("IMMUTA_API_KEY"),
                timeout=int(os.getenv("IMMUTA_API_TIMEOUT", "30")),
                max_retries=int(os.getenv("IMMUTA_API_MAX_RETRIES", "3")),
                retry_delay=float(os.getenv("IMMUTA_API_RETRY_DELAY", "1.0")),
            ),
            web=WebConfig(
                base_url=os.getenv("IMMUTA_BASE_URL", ""),
                username=os.getenv("IMMUTA_USERNAME"),
                password=os.getenv("IMMUTA_PASSWORD"),
                headless=os.getenv("IMMUTA_HEADLESS", "true").lower() == "true",
                browser_type=os.getenv("IMMUTA_BROWSER_TYPE", "chromium"),
                timeout=int(os.getenv("IMMUTA_WEB_TIMEOUT", "30000")),
            ),
            secrets=SecretsConfig(
                provider=os.getenv("IMMUTA_SECRETS_PROVIDER", "env"),
                vault_url=os.getenv("IMMUTA_VAULT_URL"),
                key_prefix=os.getenv("IMMUTA_KEY_PREFIX", "IMMUTA_"),
            ),
            storage=StorageConfig(
                provider=os.getenv("IMMUTA_STORAGE_PROVIDER", "file"),
                connection_string=os.getenv("IMMUTA_STORAGE_CONNECTION_STRING"),
                container_name=os.getenv("IMMUTA_STORAGE_CONTAINER_NAME"),
                local_path=os.getenv("IMMUTA_STORAGE_LOCAL_PATH", "./data"),
            ),
            logging=LoggingConfig(
                level=os.getenv("IMMUTA_LOG_LEVEL", "INFO"),
                format=os.getenv(
                    "IMMUTA_LOG_FORMAT",
                    "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                ),
                file=os.getenv("IMMUTA_LOG_FILE"),
                use_structlog=os.getenv("IMMUTA_USE_STRUCTLOG", "true").lower() == "true",
            ),
            reporting=ReportingConfig(
                output_dir=os.getenv("IMMUTA_REPORT_OUTPUT_DIR", "./reports"),
                default_format=os.getenv("IMMUTA_REPORT_DEFAULT_FORMAT", "json"),
                include_timestamp=os.getenv("IMMUTA_REPORT_INCLUDE_TIMESTAMP", "true").lower()
                == "true",
            ),
            operation=OperationConfig(
                use_web_fallback=os.getenv("IMMUTA_USE_WEB_FALLBACK", "true").lower() == "true",
                max_retries=int(os.getenv("IMMUTA_OPERATION_MAX_RETRIES", "3")),
                retry_delay=float(os.getenv("IMMUTA_OPERATION_RETRY_DELAY", "1.0")),
                batch_size=int(os.getenv("IMMUTA_OPERATION_BATCH_SIZE", "10")),
            ),
        )

    @field_validator("api")
    @classmethod
    def validate_api_config(cls, v: ApiConfig) -> ApiConfig:
        """Validate API configuration.
        
        Args:
            v: API configuration.
            
        Returns:
            Validated API configuration.
            
        Raises:
            ValueError: If the configuration is invalid.
        """
        if not v.base_url:
            raise ValueError("API base URL is required")
        return v

    @field_validator("web")
    @classmethod
    def validate_web_config(cls, v: WebConfig) -> WebConfig:
        """Validate web automation configuration.
        
        Args:
            v: Web automation configuration.
            
        Returns:
            Validated web automation configuration.
            
        Raises:
            ValueError: If the configuration is invalid.
        """
        if not v.base_url:
            raise ValueError("Web base URL is required")
        return v
