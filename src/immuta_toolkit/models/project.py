"""Project models for Immuta automation."""

from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field, EmailStr


class ProjectMemberRole(str, Enum):
    """Project member role."""

    OWNER = "owner"
    CONTRIBUTOR = "contributor"
    CONSUMER = "consumer"
    VIEWER = "viewer"


class ProjectMember(BaseModel):
    """Project member model."""

    email: EmailStr = Field(..., description="Member email")
    role: ProjectMemberRole = Field(..., description="Member role")
    name: Optional[str] = Field(None, description="Member name")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Member metadata")


class ProjectDataSource(BaseModel):
    """Project data source model."""

    id: Union[int, str] = Field(..., description="Data source ID or name")
    name: Optional[str] = Field(None, description="Data source name")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Data source metadata")


class ProjectAttributes(BaseModel):
    """Project attributes."""

    owner: Optional[str] = Field(None, description="Project owner")
    department: Optional[str] = Field(None, description="Department")
    tags: List[str] = Field(default_factory=list, description="Tags for the project")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Project metadata")


class ProjectModel(BaseModel):
    """Project model."""

    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    members: List[ProjectMember] = Field(default_factory=list, description="Project members")
    data_sources: List[ProjectDataSource] = Field(default_factory=list, description="Project data sources")
    attributes: ProjectAttributes = Field(default_factory=ProjectAttributes, description="Project attributes")
    enabled: bool = Field(True, description="Whether the project is enabled")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Project metadata")


class ProjectQueryParameters(BaseModel):
    """Query parameters for listing projects."""

    limit: int = Field(100, description="Maximum number of projects to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")
    owner: Optional[str] = Field(None, description="Filter by owner")
    member: Optional[str] = Field(None, description="Filter by member")
    data_source: Optional[str] = Field(None, description="Filter by data source")
    enabled: Optional[bool] = Field(None, description="Filter by enabled status")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")
