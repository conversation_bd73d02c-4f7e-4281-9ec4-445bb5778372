"""Data source models for Immuta automation."""

from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field


class DataSourceType(str, Enum):
    """Data source type."""

    JDBC = "jdbc"
    S3 = "s3"
    SNOWFLAKE = "snowflake"
    REDSHIFT = "redshift"
    BIGQUERY = "bigquery"
    AZURE_BLOB = "azure_blob"
    AZURE_SYNAPSE = "azure_synapse"
    CUSTOM = "custom"


class ColumnType(str, Enum):
    """Column type."""

    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATE = "date"
    TIMESTAMP = "timestamp"
    ARRAY = "array"
    STRUCT = "struct"
    MAP = "map"
    BINARY = "binary"
    UNKNOWN = "unknown"


class ColumnAttributes(BaseModel):
    """Column attributes."""

    pii: bool = Field(False, description="Whether the column contains PII")
    masked: bool = Field(False, description="Whether the column is masked")
    tags: List[str] = Field(default_factory=list, description="Tags for the column")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Column metadata")


class Column(BaseModel):
    """Data source column."""

    name: str = Field(..., description="Column name")
    type: ColumnType = Field(..., description="Column type")
    description: Optional[str] = Field(None, description="Column description")
    nullable: bool = Field(True, description="Whether the column is nullable")
    attributes: ColumnAttributes = Field(default_factory=ColumnAttributes, description="Column attributes")


class DataSourceAttributes(BaseModel):
    """Data source attributes."""

    owner: Optional[str] = Field(None, description="Data source owner")
    department: Optional[str] = Field(None, description="Department")
    sensitivity: Optional[str] = Field(None, description="Sensitivity level")
    tags: List[str] = Field(default_factory=list, description="Tags for the data source")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Data source metadata")


class ConnectionParameters(BaseModel):
    """Connection parameters for a data source."""

    host: Optional[str] = Field(None, description="Host name")
    port: Optional[int] = Field(None, description="Port number")
    database: Optional[str] = Field(None, description="Database name")
    schema: Optional[str] = Field(None, description="Schema name")
    table: Optional[str] = Field(None, description="Table name")
    username: Optional[str] = Field(None, description="Username")
    password: Optional[str] = Field(None, description="Password")
    connection_string: Optional[str] = Field(None, description="Connection string")
    ssl_enabled: bool = Field(False, description="Whether SSL is enabled")
    custom_parameters: Dict[str, Any] = Field(default_factory=dict, description="Custom connection parameters")


class DataSourceModel(BaseModel):
    """Data source model."""

    name: str = Field(..., description="Data source name")
    description: Optional[str] = Field(None, description="Data source description")
    type: DataSourceType = Field(..., description="Data source type")
    connection_parameters: ConnectionParameters = Field(..., description="Connection parameters")
    columns: List[Column] = Field(default_factory=list, description="Columns in the data source")
    attributes: DataSourceAttributes = Field(default_factory=DataSourceAttributes, description="Data source attributes")
    enabled: bool = Field(True, description="Whether the data source is enabled")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Data source metadata")


class DataSourceQueryParameters(BaseModel):
    """Query parameters for listing data sources."""

    limit: int = Field(100, description="Maximum number of data sources to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")
    type: Optional[DataSourceType] = Field(None, description="Filter by data source type")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    enabled: Optional[bool] = Field(None, description="Filter by enabled status")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")


class DataSourceTag(BaseModel):
    """Data source tag."""

    name: str = Field(..., description="Tag name")
    description: Optional[str] = Field(None, description="Tag description")
    created_at: Optional[str] = Field(None, description="Creation timestamp")
    created_by: Optional[str] = Field(None, description="User who created the tag")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Tag metadata")
