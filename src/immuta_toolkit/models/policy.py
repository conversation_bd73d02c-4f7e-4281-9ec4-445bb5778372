"""Policy models for Immuta automation."""

from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field


class PolicyType(str, Enum):
    """Policy type."""

    GLOBAL = "global"
    DATA_SOURCE = "data_source"
    COLUMN = "column"
    ROW = "row"
    PURPOSE = "purpose"
    SUBSCRIPTION = "subscription"
    CUSTOM = "custom"


class ActionType(str, Enum):
    """Action type."""

    MASK = "mask"
    FILTER = "filter"
    REDACT = "redact"
    ALLOW = "allow"
    DENY = "deny"


class RuleType(str, Enum):
    """Rule type."""

    TAG = "tag"
    COLUMN = "column"
    PURPOSE = "purpose"
    USER = "user"
    GROUP = "group"
    ATTRIBUTE = "attribute"
    CUSTOM = "custom"


class MaskingType(str, Enum):
    """Masking type."""

    HASH = "hash"
    REDACT = "redact"
    REPLACE = "replace"
    FORMAT_PRESERVING = "format_preserving"
    DATE_SHIFT = "date_shift"
    ROUNDING = "rounding"
    REGEX = "regex"
    CUSTOM = "custom"


class MaskingAction(BaseModel):
    """Masking action."""

    type: ActionType = Field(ActionType.MASK, description="Action type")
    fields: List[str] = Field(..., description="Fields to mask")
    masking_type: MaskingType = Field(..., description="Masking type")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Masking parameters")


class FilterAction(BaseModel):
    """Filter action."""

    type: ActionType = Field(ActionType.FILTER, description="Action type")
    condition: str = Field(..., description="Filter condition")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Filter parameters")


class RedactAction(BaseModel):
    """Redact action."""

    type: ActionType = Field(ActionType.REDACT, description="Action type")
    fields: List[str] = Field(..., description="Fields to redact")
    replacement: Optional[str] = Field(None, description="Replacement value")


class AllowAction(BaseModel):
    """Allow action."""

    type: ActionType = Field(ActionType.ALLOW, description="Action type")
    permissions: List[str] = Field(..., description="Permissions to allow")


class DenyAction(BaseModel):
    """Deny action."""

    type: ActionType = Field(ActionType.DENY, description="Action type")
    permissions: List[str] = Field(..., description="Permissions to deny")


class TagRule(BaseModel):
    """Tag rule."""

    type: RuleType = Field(RuleType.TAG, description="Rule type")
    value: str = Field(..., description="Tag value")
    operator: str = Field("equals", description="Operator (equals, contains, etc.)")


class ColumnRule(BaseModel):
    """Column rule."""

    type: RuleType = Field(RuleType.COLUMN, description="Rule type")
    column_name: str = Field(..., description="Column name")
    data_source_id: Optional[int] = Field(None, description="Data source ID")
    data_source_name: Optional[str] = Field(None, description="Data source name")


class PurposeRule(BaseModel):
    """Purpose rule."""

    type: RuleType = Field(RuleType.PURPOSE, description="Rule type")
    purpose_id: Union[int, str] = Field(..., description="Purpose ID or name")


class UserRule(BaseModel):
    """User rule."""

    type: RuleType = Field(RuleType.USER, description="Rule type")
    user_id: Union[int, str] = Field(..., description="User ID or email")


class GroupRule(BaseModel):
    """Group rule."""

    type: RuleType = Field(RuleType.GROUP, description="Rule type")
    group_id: Union[int, str] = Field(..., description="Group ID or name")


class AttributeRule(BaseModel):
    """Attribute rule."""

    type: RuleType = Field(RuleType.ATTRIBUTE, description="Rule type")
    attribute_name: str = Field(..., description="Attribute name")
    attribute_value: str = Field(..., description="Attribute value")
    operator: str = Field("equals", description="Operator (equals, contains, etc.)")


class CustomRule(BaseModel):
    """Custom rule."""

    type: RuleType = Field(RuleType.CUSTOM, description="Rule type")
    condition: str = Field(..., description="Custom condition")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Custom parameters")


class PolicyModel(BaseModel):
    """Policy model."""

    name: str = Field(..., description="Policy name")
    description: Optional[str] = Field(None, description="Policy description")
    type: PolicyType = Field(..., description="Policy type")
    actions: List[Dict[str, Any]] = Field(..., description="Policy actions")
    rules: List[Dict[str, Any]] = Field(..., description="Policy rules")
    enabled: bool = Field(True, description="Whether the policy is enabled")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Policy metadata")


class PolicyQueryParameters(BaseModel):
    """Query parameters for listing policies."""

    limit: int = Field(100, description="Maximum number of policies to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")
    type: Optional[PolicyType] = Field(None, description="Filter by policy type")
    enabled: Optional[bool] = Field(None, description="Filter by enabled status")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")
