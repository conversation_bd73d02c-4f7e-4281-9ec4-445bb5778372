"""User models for Immuta automation."""

from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field, EmailStr


class UserRole(str, Enum):
    """User role."""

    ADMIN = "admin"
    DATA_OWNER = "data_owner"
    DATA_SCIENTIST = "data_scientist"
    DATA_STEWARD = "data_steward"
    CUSTOM = "custom"


class UserStatus(str, Enum):
    """User status."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    LOCKED = "locked"


class AuthenticationType(str, Enum):
    """Authentication type."""

    LOCAL = "local"
    LDAP = "ldap"
    SAML = "saml"
    OAUTH = "oauth"
    CUSTOM = "custom"


class UserAttributes(BaseModel):
    """User attributes."""

    role: UserRole = Field(..., description="User role")
    department: Optional[str] = Field(None, description="Department")
    title: Optional[str] = Field(None, description="Job title")
    location: Optional[str] = Field(None, description="Location")
    manager: Optional[str] = Field(None, description="Manager")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="User metadata")


class UserModel(BaseModel):
    """User model."""

    email: EmailStr = Field(..., description="User email")
    name: str = Field(..., description="User name")
    attributes: UserAttributes = Field(..., description="User attributes")
    groups: List[str] = Field(default_factory=list, description="Groups the user belongs to")
    status: UserStatus = Field(UserStatus.ACTIVE, description="User status")
    authentication_type: AuthenticationType = Field(AuthenticationType.LOCAL, description="Authentication type")
    password: Optional[str] = Field(None, description="Password (only for local authentication)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="User metadata")


class UserQueryParameters(BaseModel):
    """Query parameters for listing users."""

    limit: int = Field(100, description="Maximum number of users to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")
    role: Optional[UserRole] = Field(None, description="Filter by role")
    status: Optional[UserStatus] = Field(None, description="Filter by status")
    group: Optional[str] = Field(None, description="Filter by group")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")


class GroupModel(BaseModel):
    """Group model."""

    name: str = Field(..., description="Group name")
    description: Optional[str] = Field(None, description="Group description")
    members: List[Union[int, str]] = Field(default_factory=list, description="Group members (user IDs or emails)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Group metadata")


class GroupQueryParameters(BaseModel):
    """Query parameters for listing groups."""

    limit: int = Field(100, description="Maximum number of groups to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")
    sort_by: Optional[str] = Field(None, description="Field to sort by")
    sort_order: Optional[str] = Field("asc", description="Sort order (asc or desc)")
