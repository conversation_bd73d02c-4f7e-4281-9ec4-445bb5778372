"""Configuration module for the Immuta SRE Toolkit."""

from typing import Any, List, Optional, Dict, Union
import argparse

from immuta_toolkit.config.formats import ConfigFormat
from immuta_toolkit.config.manager import ConfigManager
from immuta_toolkit.config.schema import ConfigSchema
from immuta_toolkit.config.sources import (
    ConfigSource,
    FileConfigSource,
    EnvConfigSource,
    ArgParseConfigSource,
    SecretsConfigSource,
)
from immuta_toolkit.config.profiles import (
    ConfigProfile,
    ProfileManager,
    ProfileType,
)
from immuta_toolkit.config.validation import (
    ConfigValidator,
    ValidationResult,
    ValidationLevel,
)

# Create a global configuration manager instance
_config_manager = None


def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance.

    Returns:
        Configuration manager instance.
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def load_config(
    config_file: str = None,
    env_prefix: str = "IMMUTA_",
    secrets_provider: str = None,
    arg_parser: Optional[argparse.ArgumentParser] = None,
    arg_namespace: Optional[argparse.Namespace] = None,
    **kwargs,
) -> None:
    """Load configuration from various sources.

    Args:
        config_file: Path to the configuration file.
        env_prefix: Prefix for environment variables.
        secrets_provider: Secrets provider name.
        arg_parser: ArgumentParser instance for command-line arguments.
        arg_namespace: Parsed arguments for command-line arguments.
        **kwargs: Additional arguments for the secrets provider.
    """
    # Get configuration manager
    manager = get_config_manager()

    # Add sources in order of precedence (lowest to highest)

    # Load from configuration file if provided (lowest precedence)
    if config_file:
        manager.from_file(config_file)

    # Load from environment variables (medium precedence)
    manager.from_env(env_prefix)

    # Load from secrets provider if provided (medium-high precedence)
    if secrets_provider:
        manager.from_secrets(secrets_provider, **kwargs)

    # Load from command-line arguments if provided (highest precedence)
    if arg_parser or arg_namespace:
        manager.from_args(arg_parser, arg_namespace)

    # Load configuration
    manager.load()


def load_args(
    parser: Optional[argparse.ArgumentParser] = None,
    args: Optional[argparse.Namespace] = None,
    prefix: str = "",
) -> None:
    """Load configuration from command-line arguments.

    Args:
        parser: ArgumentParser instance. If None, a new parser will be created.
        args: Parsed arguments. If None, arguments will be parsed from sys.argv.
        prefix: Prefix for configuration keys.
    """
    # Get configuration manager
    manager = get_config_manager()

    # Load from command-line arguments
    manager.from_args(parser, args, prefix)

    # Load configuration
    manager.load()


def get_config() -> Any:
    """Get the current configuration.

    Returns:
        Current configuration.
    """
    return get_config_manager().config


def get_config_value(key: str, default: Any = None) -> Any:
    """Get a configuration value.

    Args:
        key: Configuration key (dot-separated path).
        default: Default value to return if the key is not found.

    Returns:
        Configuration value.
    """
    return get_config_manager().get(key, default)


def set_config_value(key: str, value: Any) -> None:
    """Set a configuration value.

    Args:
        key: Configuration key (dot-separated path).
        value: Configuration value.
    """
    get_config_manager().set(key, value)


def save_config(path: str, format: ConfigFormat = ConfigFormat.JSON) -> None:
    """Save configuration to a file.

    Args:
        path: Path to save the configuration to.
        format: Configuration file format.
    """
    get_config_manager().save(path, format)


def validate_config() -> List[ValidationResult]:
    """Validate the current configuration.

    Returns:
        List of validation results.
    """
    validator = ConfigValidator()
    return validator.validate(get_config_manager().config.model_dump())


__all__ = [
    "ConfigManager",
    "ConfigFormat",
    "ConfigSchema",
    "ConfigSource",
    "FileConfigSource",
    "EnvConfigSource",
    "ArgParseConfigSource",
    "SecretsConfigSource",
    "ConfigProfile",
    "ProfileManager",
    "ProfileType",
    "ConfigValidator",
    "ValidationResult",
    "ValidationLevel",
    "get_config_manager",
    "load_config",
    "load_args",
    "get_config",
    "get_config_value",
    "set_config_value",
    "save_config",
    "validate_config",
]
