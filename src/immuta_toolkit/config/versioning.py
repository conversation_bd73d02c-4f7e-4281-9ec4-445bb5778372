"""Configuration versioning for the Immuta SRE Toolkit."""

import os
import json
import yaml
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Callable

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.config.formats import ConfigFormat

logger = get_logger(__name__)


class ConfigVersion:
    """Configuration version.

    This class represents a version of a configuration file.

    Attributes:
        version: Version string.
        timestamp: Timestamp of the version.
        description: Description of the version.
        changes: Changes made in this version.
    """

    def __init__(
        self,
        version: str,
        timestamp: Optional[datetime] = None,
        description: Optional[str] = None,
        changes: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the configuration version.

        Args:
            version: Version string.
            timestamp: Timestamp of the version. If None, the current time is used.
            description: Description of the version.
            changes: Changes made in this version.
        """
        self.version = version
        self.timestamp = timestamp or datetime.now()
        self.description = description or ""
        self.changes = changes or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the version to a dictionary.

        Returns:
            Dictionary representation of the version.
        """
        return {
            "version": self.version,
            "timestamp": self.timestamp.isoformat(),
            "description": self.description,
            "changes": self.changes,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConfigVersion":
        """Create a version from a dictionary.

        Args:
            data: Dictionary representation of the version.

        Returns:
            Configuration version.
        """
        return cls(
            version=data["version"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            description=data.get("description", ""),
            changes=data.get("changes", {}),
        )


class ConfigVersionManager:
    """Configuration version manager.

    This class manages versions of configuration files.

    Attributes:
        config_dir: Directory containing configuration files.
        versions_dir: Directory containing version history.
        current_version: Current version of the configuration.
        version_history: List of previous versions.
    """

    def __init__(
        self,
        config_dir: str,
        versions_dir: Optional[str] = None,
        max_versions: int = 10,
    ):
        """Initialize the configuration version manager.

        Args:
            config_dir: Directory containing configuration files.
            versions_dir: Directory containing version history. If None, a
                subdirectory named "versions" will be created in the config_dir.
            max_versions: Maximum number of versions to keep.
        """
        self.config_dir = Path(config_dir)
        self.versions_dir = Path(versions_dir) if versions_dir else self.config_dir / "versions"
        self.max_versions = max_versions
        self.current_version: Optional[ConfigVersion] = None
        self.version_history: List[ConfigVersion] = []

        # Create directories if they don't exist
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.versions_dir, exist_ok=True)

        # Load version history
        self._load_version_history()

    def _load_version_history(self) -> None:
        """Load version history from the versions directory."""
        # Check if version history file exists
        history_file = self.versions_dir / "history.json"
        if not history_file.exists():
            return

        # Load version history
        try:
            with open(history_file, "r") as f:
                history_data = json.load(f)

            # Parse version history
            self.version_history = [
                ConfigVersion.from_dict(version_data)
                for version_data in history_data.get("versions", [])
            ]

            # Set current version
            current_version_data = history_data.get("current_version")
            if current_version_data:
                self.current_version = ConfigVersion.from_dict(current_version_data)

            logger.debug(f"Loaded version history: {len(self.version_history)} versions")
        except Exception as e:
            logger.error(f"Failed to load version history: {e}")

    def _save_version_history(self) -> None:
        """Save version history to the versions directory."""
        # Create history data
        history_data = {
            "current_version": self.current_version.to_dict() if self.current_version else None,
            "versions": [version.to_dict() for version in self.version_history],
        }

        # Save history data
        history_file = self.versions_dir / "history.json"
        try:
            with open(history_file, "w") as f:
                json.dump(history_data, f, indent=2)

            logger.debug(f"Saved version history: {len(self.version_history)} versions")
        except Exception as e:
            logger.error(f"Failed to save version history: {e}")

    def create_version(
        self,
        version: str,
        description: Optional[str] = None,
        changes: Optional[Dict[str, Any]] = None,
    ) -> ConfigVersion:
        """Create a new version of the configuration.

        Args:
            version: Version string.
            description: Description of the version.
            changes: Changes made in this version.

        Returns:
            New configuration version.
        """
        # Create new version
        new_version = ConfigVersion(
            version=version,
            description=description,
            changes=changes,
        )

        # Backup current configuration files
        self._backup_config_files(new_version)

        # Update version history
        self.version_history.append(new_version)
        self.current_version = new_version

        # Trim version history if needed
        if len(self.version_history) > self.max_versions:
            # Remove oldest versions
            removed_versions = self.version_history[: len(self.version_history) - self.max_versions]
            self.version_history = self.version_history[-self.max_versions:]

            # Remove backup files for removed versions
            for version in removed_versions:
                version_dir = self.versions_dir / version.version
                if version_dir.exists():
                    shutil.rmtree(version_dir)

        # Save version history
        self._save_version_history()

        return new_version

    def _backup_config_files(self, version: ConfigVersion) -> None:
        """Backup configuration files for a version.

        Args:
            version: Configuration version.
        """
        # Create version directory
        version_dir = self.versions_dir / version.version
        os.makedirs(version_dir, exist_ok=True)

        # Copy configuration files
        for file_path in self.config_dir.glob("*.json"):
            if file_path.name == "history.json":
                continue
            shutil.copy2(file_path, version_dir / file_path.name)

        for file_path in self.config_dir.glob("*.yaml"):
            shutil.copy2(file_path, version_dir / file_path.name)

        for file_path in self.config_dir.glob("*.yml"):
            shutil.copy2(file_path, version_dir / file_path.name)

        for file_path in self.config_dir.glob("*.toml"):
            shutil.copy2(file_path, version_dir / file_path.name)

        logger.debug(f"Backed up configuration files for version {version.version}")

    def restore_version(self, version: str) -> Optional[ConfigVersion]:
        """Restore a previous version of the configuration.

        Args:
            version: Version string.

        Returns:
            Restored configuration version, or None if the version was not found.
        """
        # Find the version
        for v in self.version_history:
            if v.version == version:
                # Restore configuration files
                version_dir = self.versions_dir / version
                if not version_dir.exists():
                    logger.error(f"Version directory not found: {version_dir}")
                    return None

                # Copy configuration files back to config directory
                for file_path in version_dir.glob("*"):
                    shutil.copy2(file_path, self.config_dir / file_path.name)

                # Update current version
                self.current_version = v

                # Save version history
                self._save_version_history()

                logger.debug(f"Restored configuration version: {version}")
                return v

        logger.error(f"Version not found: {version}")
        return None

    def get_version(self, version: str) -> Optional[ConfigVersion]:
        """Get a specific version of the configuration.

        Args:
            version: Version string.

        Returns:
            Configuration version, or None if the version was not found.
        """
        for v in self.version_history:
            if v.version == version:
                return v
        return None

    def get_version_history(self) -> List[ConfigVersion]:
        """Get the version history.

        Returns:
            List of configuration versions.
        """
        return self.version_history

    def get_current_version(self) -> Optional[ConfigVersion]:
        """Get the current version of the configuration.

        Returns:
            Current configuration version, or None if no version exists.
        """
        return self.current_version
