"""Configuration migration for the Immuta SRE Toolkit."""

import os
import json
import yaml
import toml
from typing import Dict, List, Optional, Union, Any, Callable
from enum import Enum
from pathlib import Path

from immuta_toolkit.config.manager import ConfigFormat
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class MigrationResult:
    """Migration result.
    
    This class represents the result of a configuration migration.
    
    Attributes:
        success: Whether the migration was successful.
        changes: List of changes made during the migration.
        errors: List of errors encountered during the migration.
        warnings: List of warnings encountered during the migration.
    """
    
    def __init__(self):
        """Initialize the migration result."""
        self.success = True
        self.changes: List[str] = []
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def add_change(self, message: str) -> None:
        """Add a change message.
        
        Args:
            message: Change message.
        """
        self.changes.append(message)
        logger.info(f"Migration change: {message}")
    
    def add_error(self, message: str) -> None:
        """Add an error message.
        
        Args:
            message: Error message.
        """
        self.errors.append(message)
        self.success = False
        logger.error(f"Migration error: {message}")
    
    def add_warning(self, message: str) -> None:
        """Add a warning message.
        
        Args:
            message: Warning message.
        """
        self.warnings.append(message)
        logger.warning(f"Migration warning: {message}")
    
    def __str__(self) -> str:
        """Get a string representation of the migration result.
        
        Returns:
            String representation.
        """
        return f"MigrationResult(success={self.success}, changes={len(self.changes)}, errors={len(self.errors)}, warnings={len(self.warnings)})"


class ConfigMigration:
    """Configuration migration.
    
    This class provides methods for migrating configuration files
    from one version to another.
    
    Attributes:
        migrations: Dictionary of migration functions, keyed by version.
    """
    
    def __init__(self):
        """Initialize the configuration migration."""
        self.migrations: Dict[str, Callable[[Dict[str, Any]], Dict[str, Any]]] = {}
        
        # Initialize default migrations
        self._init_default_migrations()
    
    def add_migration(self, version: str, migration: Callable[[Dict[str, Any]], Dict[str, Any]]) -> None:
        """Add a migration function.
        
        Args:
            version: Target version.
            migration: Migration function.
        """
        self.migrations[version] = migration
        logger.debug(f"Added migration for version {version}")
    
    def migrate(
        self,
        config: Dict[str, Any],
        target_version: str,
        current_version: Optional[str] = None,
    ) -> MigrationResult:
        """Migrate a configuration to a target version.
        
        Args:
            config: Configuration dictionary.
            target_version: Target version.
            current_version: Current version. If None, will be determined from the configuration.
            
        Returns:
            Migration result.
        """
        result = MigrationResult()
        
        # Determine current version if not provided
        if current_version is None:
            current_version = config.get("version", "0.0.0")
            
        # Check if migration is needed
        if current_version == target_version:
            result.add_warning(f"Configuration is already at version {target_version}")
            return result
            
        # Check if target version is supported
        if target_version not in self.migrations:
            result.add_error(f"Unsupported target version: {target_version}")
            return result
            
        # Apply migration
        try:
            # Make a copy of the configuration
            config_copy = config.copy()
            
            # Apply migration function
            migrated_config = self.migrations[target_version](config_copy)
            
            # Update version
            migrated_config["version"] = target_version
            
            # Update original configuration
            config.clear()
            config.update(migrated_config)
            
            result.add_change(f"Migrated configuration from version {current_version} to {target_version}")
            
            return result
        except Exception as e:
            result.add_error(f"Failed to migrate configuration: {e}")
            return result
    
    def migrate_file(
        self,
        path: Union[str, Path],
        target_version: str,
        format: Optional[ConfigFormat] = None,
        backup: bool = True,
    ) -> MigrationResult:
        """Migrate a configuration file to a target version.
        
        Args:
            path: Path to the configuration file.
            target_version: Target version.
            format: Configuration file format. If None, will be determined from the file extension.
            backup: Whether to create a backup of the original file.
            
        Returns:
            Migration result.
        """
        result = MigrationResult()
        
        # Determine format from file extension if not provided
        if format is None:
            ext = os.path.splitext(path)[1].lower()
            if ext == ".json":
                format = ConfigFormat.JSON
            elif ext in (".yaml", ".yml"):
                format = ConfigFormat.YAML
            elif ext == ".toml":
                format = ConfigFormat.TOML
            else:
                result.add_error(f"Cannot determine format from file extension: {ext}")
                return result
                
        # Check if file exists
        if not os.path.exists(path):
            result.add_error(f"Configuration file not found: {path}")
            return result
            
        # Load configuration from file
        try:
            with open(path, "r") as f:
                if format == ConfigFormat.JSON:
                    config = json.load(f)
                elif format == ConfigFormat.YAML:
                    config = yaml.safe_load(f)
                elif format == ConfigFormat.TOML:
                    config = toml.load(f)
                else:
                    result.add_error(f"Unsupported format: {format}")
                    return result
        except Exception as e:
            result.add_error(f"Failed to load configuration from {path}: {e}")
            return result
            
        # Create backup if requested
        if backup:
            backup_path = f"{path}.bak"
            try:
                with open(backup_path, "w") as f:
                    if format == ConfigFormat.JSON:
                        json.dump(config, f, indent=2)
                    elif format == ConfigFormat.YAML:
                        yaml.dump(config, f, default_flow_style=False)
                    elif format == ConfigFormat.TOML:
                        toml.dump(config, f)
                        
                result.add_change(f"Created backup at {backup_path}")
            except Exception as e:
                result.add_warning(f"Failed to create backup: {e}")
                
        # Migrate configuration
        migration_result = self.migrate(config, target_version)
        
        # Update result
        result.success = migration_result.success
        result.changes.extend(migration_result.changes)
        result.errors.extend(migration_result.errors)
        result.warnings.extend(migration_result.warnings)
        
        # Save migrated configuration
        if result.success:
            try:
                with open(path, "w") as f:
                    if format == ConfigFormat.JSON:
                        json.dump(config, f, indent=2)
                    elif format == ConfigFormat.YAML:
                        yaml.dump(config, f, default_flow_style=False)
                    elif format == ConfigFormat.TOML:
                        toml.dump(config, f)
                        
                result.add_change(f"Saved migrated configuration to {path}")
            except Exception as e:
                result.add_error(f"Failed to save migrated configuration: {e}")
                
        return result
    
    def _init_default_migrations(self) -> None:
        """Initialize default migrations."""
        # Migration to version 1.0.0
        self.add_migration("1.0.0", self._migrate_to_1_0_0)
    
    def _migrate_to_1_0_0(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Migrate configuration to version 1.0.0.
        
        Args:
            config: Configuration dictionary.
            
        Returns:
            Migrated configuration dictionary.
        """
        # Create a new configuration dictionary
        migrated_config = {
            "version": "1.0.0",
            "environment": config.get("environment", "dev"),
        }
        
        # Migrate API configuration
        if "api" in config:
            migrated_config["api"] = {
                "base_url": config["api"].get("base_url"),
                "api_key": config["api"].get("api_key"),
                "timeout": config["api"].get("timeout", 30),
                "max_retries": config["api"].get("max_retries", 3),
                "retry_delay": config["api"].get("retry_delay", 1.0),
                "rate_limit": config["api"].get("rate_limit", 10),
                "rate_limit_window": config["api"].get("rate_limit_window", 1),
            }
        else:
            # Create default API configuration
            migrated_config["api"] = {
                "base_url": None,
                "api_key": None,
                "timeout": 30,
                "max_retries": 3,
                "retry_delay": 1.0,
                "rate_limit": 10,
                "rate_limit_window": 1,
            }
            
        # Migrate web configuration
        if "web" in config:
            migrated_config["web"] = {
                "base_url": config["web"].get("base_url"),
                "username": config["web"].get("username"),
                "password": config["web"].get("password"),
                "headless": config["web"].get("headless", True),
                "browser_type": config["web"].get("browser_type", "chromium"),
                "timeout": config["web"].get("timeout", 30000),
                "screenshot_dir": config["web"].get("screenshot_dir", "./screenshots"),
            }
        else:
            # Create default web configuration
            migrated_config["web"] = {
                "base_url": None,
                "username": None,
                "password": None,
                "headless": True,
                "browser_type": "chromium",
                "timeout": 30000,
                "screenshot_dir": "./screenshots",
            }
            
        # Migrate secrets configuration
        if "secrets" in config:
            migrated_config["secrets"] = {
                "provider": config["secrets"].get("provider", "env"),
                "vault_url": config["secrets"].get("vault_url"),
                "key_prefix": config["secrets"].get("key_prefix", "IMMUTA_"),
                "client_id": config["secrets"].get("client_id"),
                "client_secret": config["secrets"].get("client_secret"),
                "tenant_id": config["secrets"].get("tenant_id"),
                "region": config["secrets"].get("region"),
            }
        else:
            # Create default secrets configuration
            migrated_config["secrets"] = {
                "provider": "env",
                "vault_url": None,
                "key_prefix": "IMMUTA_",
                "client_id": None,
                "client_secret": None,
                "tenant_id": None,
                "region": None,
            }
            
        # Migrate storage configuration
        if "storage" in config:
            migrated_config["storage"] = {
                "provider": config["storage"].get("provider", "file"),
                "connection_string": config["storage"].get("connection_string"),
                "container_name": config["storage"].get("container_name"),
                "local_path": config["storage"].get("local_path", "./data"),
                "database_url": config["storage"].get("database_url"),
            }
        else:
            # Create default storage configuration
            migrated_config["storage"] = {
                "provider": "file",
                "connection_string": None,
                "container_name": None,
                "local_path": "./data",
                "database_url": None,
            }
            
        # Migrate logging configuration
        if "logging" in config:
            migrated_config["logging"] = {
                "level": config["logging"].get("level", "INFO"),
                "format": config["logging"].get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"),
                "file": config["logging"].get("file"),
                "use_structlog": config["logging"].get("use_structlog", True),
                "max_size": config["logging"].get("max_size", 10485760),
                "backup_count": config["logging"].get("backup_count", 5),
            }
        else:
            # Create default logging configuration
            migrated_config["logging"] = {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": None,
                "use_structlog": True,
                "max_size": 10485760,
                "backup_count": 5,
            }
            
        # Migrate operation configuration
        if "operation" in config:
            migrated_config["operation"] = {
                "use_web_fallback": config["operation"].get("use_web_fallback", True),
                "max_retries": config["operation"].get("max_retries", 3),
                "retry_delay": config["operation"].get("retry_delay", 1.0),
                "batch_size": config["operation"].get("batch_size", 10),
                "parallel": config["operation"].get("parallel", False),
                "max_workers": config["operation"].get("max_workers", 4),
            }
        else:
            # Create default operation configuration
            migrated_config["operation"] = {
                "use_web_fallback": True,
                "max_retries": 3,
                "retry_delay": 1.0,
                "batch_size": 10,
                "parallel": False,
                "max_workers": 4,
            }
            
        # Migrate features
        if "features" in config:
            migrated_config["features"] = config["features"]
        else:
            migrated_config["features"] = {}
            
        # Migrate profiles
        if "profiles" in config:
            migrated_config["profiles"] = config["profiles"]
        else:
            migrated_config["profiles"] = {}
            
        # Migrate custom configuration
        if "custom" in config:
            migrated_config["custom"] = config["custom"]
        else:
            migrated_config["custom"] = {}
            
        return migrated_config
