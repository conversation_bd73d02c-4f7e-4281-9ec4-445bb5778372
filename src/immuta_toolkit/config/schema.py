"""Configuration schema for the Immuta SRE Toolkit."""

from typing import Dict, List, Optional, Union, Any
from enum import Enum


class ConfigSchema:
    """Configuration schema for the Immuta SRE Toolkit.
    
    This class defines the schema for the configuration, including
    the available properties, their types, and default values.
    """
    
    def __init__(self):
        """Initialize the configuration schema."""
        self.title = "ImmutaConfig"
        self.description = "Configuration for the Immuta SRE Toolkit"
        self.version = "1.0.0"
        
        # Define schema properties
        self.properties = {
            "environment": {
                "type": str,
                "enum": ["local", "dev", "test", "prod"],
                "default": "dev",
                "description": "Environment type",
            },
            "api": {
                "type": Dict[str, Any],
                "properties": {
                    "base_url": {
                        "type": str,
                        "description": "Base URL of the Immuta API",
                    },
                    "api_key": {
                        "type": Optional[str],
                        "default": None,
                        "description": "API key for authentication",
                    },
                    "timeout": {
                        "type": int,
                        "default": 30,
                        "description": "Default timeout for API calls in seconds",
                    },
                    "max_retries": {
                        "type": int,
                        "default": 3,
                        "description": "Maximum number of retries for API calls",
                    },
                    "retry_delay": {
                        "type": float,
                        "default": 1.0,
                        "description": "Delay between retries in seconds",
                    },
                    "rate_limit": {
                        "type": int,
                        "default": 10,
                        "description": "Maximum number of requests per second",
                    },
                    "rate_limit_window": {
                        "type": int,
                        "default": 1,
                        "description": "Rate limit window in seconds",
                    },
                },
                "required": ["base_url"],
                "description": "API configuration",
            },
            "web": {
                "type": Dict[str, Any],
                "properties": {
                    "base_url": {
                        "type": str,
                        "description": "Base URL of the Immuta web UI",
                    },
                    "username": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Username for authentication",
                    },
                    "password": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Password for authentication",
                    },
                    "headless": {
                        "type": bool,
                        "default": True,
                        "description": "Whether to run the browser in headless mode",
                    },
                    "browser_type": {
                        "type": str,
                        "enum": ["chromium", "firefox", "webkit"],
                        "default": "chromium",
                        "description": "Browser type",
                    },
                    "timeout": {
                        "type": int,
                        "default": 30000,
                        "description": "Default timeout for actions in milliseconds",
                    },
                    "screenshot_dir": {
                        "type": str,
                        "default": "./screenshots",
                        "description": "Directory to save screenshots",
                    },
                },
                "required": ["base_url"],
                "description": "Web automation configuration",
            },
            "secrets": {
                "type": Dict[str, Any],
                "properties": {
                    "provider": {
                        "type": str,
                        "enum": ["env", "azure", "aws", "hashicorp"],
                        "default": "env",
                        "description": "Secrets provider",
                    },
                    "vault_url": {
                        "type": Optional[str],
                        "default": None,
                        "description": "URL of the secrets vault",
                    },
                    "key_prefix": {
                        "type": str,
                        "default": "IMMUTA_",
                        "description": "Prefix for environment variables",
                    },
                    "client_id": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Client ID for authentication",
                    },
                    "client_secret": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Client secret for authentication",
                    },
                    "tenant_id": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Tenant ID for authentication",
                    },
                    "region": {
                        "type": Optional[str],
                        "default": None,
                        "description": "AWS region",
                    },
                },
                "description": "Secrets configuration",
            },
            "storage": {
                "type": Dict[str, Any],
                "properties": {
                    "provider": {
                        "type": str,
                        "enum": ["file", "azure", "aws", "sqlite"],
                        "default": "file",
                        "description": "Storage provider",
                    },
                    "connection_string": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Connection string for cloud storage",
                    },
                    "container_name": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Container name for cloud storage",
                    },
                    "local_path": {
                        "type": str,
                        "default": "./data",
                        "description": "Local storage path",
                    },
                    "database_url": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Database URL for SQLite storage",
                    },
                },
                "description": "Storage configuration",
            },
            "logging": {
                "type": Dict[str, Any],
                "properties": {
                    "level": {
                        "type": str,
                        "enum": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                        "default": "INFO",
                        "description": "Logging level",
                    },
                    "format": {
                        "type": str,
                        "default": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                        "description": "Logging format",
                    },
                    "file": {
                        "type": Optional[str],
                        "default": None,
                        "description": "Log file path",
                    },
                    "use_structlog": {
                        "type": bool,
                        "default": True,
                        "description": "Whether to use structlog",
                    },
                    "max_size": {
                        "type": int,
                        "default": 10485760,  # 10 MB
                        "description": "Maximum log file size in bytes",
                    },
                    "backup_count": {
                        "type": int,
                        "default": 5,
                        "description": "Number of backup log files to keep",
                    },
                },
                "description": "Logging configuration",
            },
            "reporting": {
                "type": Dict[str, Any],
                "properties": {
                    "output_dir": {
                        "type": str,
                        "default": "./reports",
                        "description": "Output directory for reports",
                    },
                    "default_format": {
                        "type": str,
                        "enum": ["json", "csv", "html", "pdf"],
                        "default": "json",
                        "description": "Default report format",
                    },
                    "include_timestamp": {
                        "type": bool,
                        "default": True,
                        "description": "Whether to include timestamp in report filenames",
                    },
                    "email": {
                        "type": Dict[str, Any],
                        "properties": {
                            "smtp_server": {
                                "type": str,
                                "default": "smtp.gmail.com",
                                "description": "SMTP server address",
                            },
                            "smtp_port": {
                                "type": int,
                                "default": 587,
                                "description": "SMTP server port",
                            },
                            "username": {
                                "type": Optional[str],
                                "default": None,
                                "description": "SMTP username",
                            },
                            "password": {
                                "type": Optional[str],
                                "default": None,
                                "description": "SMTP password",
                            },
                            "use_tls": {
                                "type": bool,
                                "default": True,
                                "description": "Whether to use TLS",
                            },
                            "default_sender": {
                                "type": Optional[str],
                                "default": None,
                                "description": "Default sender email address",
                            },
                        },
                        "description": "Email configuration for report delivery",
                    },
                },
                "description": "Reporting configuration",
            },
            "operation": {
                "type": Dict[str, Any],
                "properties": {
                    "use_web_fallback": {
                        "type": bool,
                        "default": True,
                        "description": "Whether to use web automation as a fallback",
                    },
                    "max_retries": {
                        "type": int,
                        "default": 3,
                        "description": "Maximum number of retries for operations",
                    },
                    "retry_delay": {
                        "type": float,
                        "default": 1.0,
                        "description": "Delay between retries in seconds",
                    },
                    "batch_size": {
                        "type": int,
                        "default": 10,
                        "description": "Default batch size for batch operations",
                    },
                    "parallel": {
                        "type": bool,
                        "default": False,
                        "description": "Whether to run operations in parallel",
                    },
                    "max_workers": {
                        "type": int,
                        "default": 4,
                        "description": "Maximum number of worker threads",
                    },
                },
                "description": "Operation configuration",
            },
            "features": {
                "type": Dict[str, bool],
                "default": {},
                "description": "Feature flags",
            },
            "profiles": {
                "type": Dict[str, Dict[str, Any]],
                "default": {},
                "description": "Configuration profiles",
            },
            "custom": {
                "type": Dict[str, Any],
                "default": {},
                "description": "Custom configuration",
            },
        }
        
        # Define required properties
        self.required = ["environment", "api", "web"]
