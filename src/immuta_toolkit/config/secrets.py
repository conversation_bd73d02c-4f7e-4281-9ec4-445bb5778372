"""Secrets management for the Immuta SRE Toolkit configuration."""

import os
import json
from abc import ABC, abstractmethod
from typing import Optional, List

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def create_secrets_provider(provider: str, **kwargs) -> "SecretsProvider":
    """Create a secrets provider.

    Args:
        provider: Provider name.
        **kwargs: Additional arguments for the provider.

    Returns:
        Secrets provider instance.

    Raises:
        ValueError: If the provider is not supported.
    """
    if provider == "env":
        return EnvironmentSecretsProvider(**kwargs)
    elif provider == "azure":
        return AzureKeyVaultSecretsProvider(**kwargs)
    elif provider == "aws":
        return AWSSecretsManagerProvider(**kwargs)
    elif provider == "hashicorp":
        return HashiCorpVaultProvider(**kwargs)
    else:
        raise ValueError(f"Unsupported secrets provider: {provider}")


class SecretsProvider(ABC):
    """Base class for secrets providers.

    This class defines the interface for secrets providers, which
    provide secure storage and retrieval of sensitive configuration values.
    """

    @abstractmethod
    def get_secret(self, key: str) -> Optional[str]:
        """Get a secret value.

        Args:
            key: Secret key.

        Returns:
            Secret value, or None if the secret does not exist.
        """
        pass

    @abstractmethod
    def set_secret(self, key: str, value: str) -> None:
        """Set a secret value.

        Args:
            key: Secret key.
            value: Secret value.
        """
        pass

    @abstractmethod
    def list_secrets(self, prefix: str = "") -> List[str]:
        """List available secrets.

        Args:
            prefix: Prefix to filter secrets by.

        Returns:
            List of secret keys.
        """
        pass

    @abstractmethod
    def delete_secret(self, key: str) -> None:
        """Delete a secret.

        Args:
            key: Secret key.
        """
        pass


class EnvironmentSecretsProvider(SecretsProvider):
    """Environment variable secrets provider.

    This class provides secrets from environment variables.

    Attributes:
        prefix: Prefix for environment variables.
    """

    def __init__(self, prefix: str = "IMMUTA_"):
        """Initialize the environment variable secrets provider.

        Args:
            prefix: Prefix for environment variables.
        """
        self.prefix = prefix

    def get_secret(self, key: str) -> Optional[str]:
        """Get a secret value from an environment variable.

        Args:
            key: Secret key.

        Returns:
            Secret value, or None if the environment variable does not exist.
        """
        env_key = f"{self.prefix}{key}"
        return os.environ.get(env_key)

    def set_secret(self, key: str, value: str) -> None:
        """Set a secret value in an environment variable.

        Args:
            key: Secret key.
            value: Secret value.
        """
        env_key = f"{self.prefix}{key}"
        os.environ[env_key] = value

    def list_secrets(self, prefix: str = "") -> List[str]:
        """List available secrets from environment variables.

        Args:
            prefix: Prefix to filter secrets by.

        Returns:
            List of secret keys.
        """
        secrets = []
        full_prefix = f"{self.prefix}{prefix}"
        for key in os.environ:
            if key.startswith(full_prefix):
                # Remove the prefix
                secret_key = key[len(self.prefix) :]
                secrets.append(secret_key)
        return secrets

    def delete_secret(self, key: str) -> None:
        """Delete a secret from environment variables.

        Args:
            key: Secret key.
        """
        env_key = f"{self.prefix}{key}"
        if env_key in os.environ:
            del os.environ[env_key]


class AzureKeyVaultSecretsProvider(SecretsProvider):
    """Azure Key Vault secrets provider.

    This class provides secrets from Azure Key Vault.

    Attributes:
        client: Azure Key Vault client.
        vault_url: URL of the Azure Key Vault.
    """

    def __init__(self, vault_url: str, **kwargs):
        """Initialize the Azure Key Vault secrets provider.

        Args:
            vault_url: URL of the Azure Key Vault.
            **kwargs: Additional arguments for the Azure Key Vault client.

        Raises:
            ImportError: If the required packages are not installed.
            ValueError: If the vault URL is not provided.
        """
        self.vault_url = vault_url

        # Import Azure Key Vault client
        try:
            from azure.identity import DefaultAzureCredential
            from azure.keyvault.secrets import SecretClient

            # Create credential
            credential = DefaultAzureCredential()

            # Create client
            self.client = SecretClient(vault_url=vault_url, credential=credential)

            logger.debug(f"Initialized Azure Key Vault provider: {vault_url}")
        except ImportError:
            logger.error(
                "Azure Key Vault provider requires azure-identity and azure-keyvault-secrets packages"
            )
            raise

    def get_secret(self, key: str) -> Optional[str]:
        """Get a secret value from Azure Key Vault.

        Args:
            key: Secret key.

        Returns:
            Secret value, or None if the secret does not exist.
        """
        try:
            secret = self.client.get_secret(key)
            return secret.value
        except Exception as e:
            logger.debug(f"Failed to get secret {key} from Azure Key Vault: {e}")
            return None

    def set_secret(self, key: str, value: str) -> None:
        """Set a secret value in Azure Key Vault.

        Args:
            key: Secret key.
            value: Secret value.
        """
        try:
            self.client.set_secret(key, value)
        except Exception as e:
            logger.error(f"Failed to set secret {key} in Azure Key Vault: {e}")
            raise

    def list_secrets(self, prefix: str = "") -> List[str]:
        """List available secrets from Azure Key Vault.

        Args:
            prefix: Prefix to filter secrets by.

        Returns:
            List of secret keys.
        """
        try:
            secrets = []
            for secret in self.client.list_properties_of_secrets():
                if secret.name.startswith(prefix):
                    secrets.append(secret.name)
            return secrets
        except Exception as e:
            logger.error(f"Failed to list secrets from Azure Key Vault: {e}")
            return []

    def delete_secret(self, key: str) -> None:
        """Delete a secret from Azure Key Vault.

        Args:
            key: Secret key.
        """
        try:
            self.client.begin_delete_secret(key)
        except Exception as e:
            logger.error(f"Failed to delete secret {key} from Azure Key Vault: {e}")
            raise


class AWSSecretsManagerProvider(SecretsProvider):
    """AWS Secrets Manager provider.

    This class provides secrets from AWS Secrets Manager.

    Attributes:
        client: AWS Secrets Manager client.
        region: AWS region.
        secret_name: Name of the secret to use for storing multiple key-value pairs.
    """

    def __init__(self, region: str, secret_name: Optional[str] = None, **kwargs):
        """Initialize the AWS Secrets Manager provider.

        Args:
            region: AWS region.
            secret_name: Name of the secret to use for storing multiple key-value pairs.
                If provided, all secrets will be stored in this secret as a JSON object.
                If not provided, each secret will be stored as a separate secret.
            **kwargs: Additional arguments for the AWS Secrets Manager client.

        Raises:
            ImportError: If the required packages are not installed.
            ValueError: If the region is not provided.
        """
        self.region = region
        self.secret_name = secret_name

        # Import AWS Secrets Manager client
        try:
            import boto3

            # Create client
            self.client = boto3.client("secretsmanager", region_name=region)

            logger.debug(f"Initialized AWS Secrets Manager provider: {region}")
        except ImportError:
            logger.error("AWS Secrets Manager provider requires boto3 package")
            raise

    def get_secret(self, key: str) -> Optional[str]:
        """Get a secret value from AWS Secrets Manager.

        Args:
            key: Secret key.

        Returns:
            Secret value, or None if the secret does not exist.
        """
        try:
            if self.secret_name:
                # Get the entire secret
                response = self.client.get_secret_value(SecretId=self.secret_name)

                # Parse the secret value
                if "SecretString" in response:
                    secrets = json.loads(response["SecretString"])
                    return secrets.get(key)
                return None
            else:
                # Get the individual secret
                response = self.client.get_secret_value(SecretId=key)

                # Parse the secret value
                if "SecretString" in response:
                    return response["SecretString"]
                return None
        except Exception as e:
            logger.debug(f"Failed to get secret {key} from AWS Secrets Manager: {e}")
            return None

    def set_secret(self, key: str, value: str) -> None:
        """Set a secret value in AWS Secrets Manager.

        Args:
            key: Secret key.
            value: Secret value.
        """
        try:
            if self.secret_name:
                # Get the entire secret
                try:
                    response = self.client.get_secret_value(SecretId=self.secret_name)
                    if "SecretString" in response:
                        secrets = json.loads(response["SecretString"])
                    else:
                        secrets = {}
                except:
                    secrets = {}

                # Update the secret
                secrets[key] = value

                # Save the secret
                self.client.put_secret_value(
                    SecretId=self.secret_name, SecretString=json.dumps(secrets)
                )
            else:
                # Create or update the individual secret
                try:
                    # Try to update the secret
                    self.client.put_secret_value(SecretId=key, SecretString=value)
                except:
                    # Create the secret if it doesn't exist
                    self.client.create_secret(Name=key, SecretString=value)
        except Exception as e:
            logger.error(f"Failed to set secret {key} in AWS Secrets Manager: {e}")
            raise

    def list_secrets(self, prefix: str = "") -> List[str]:
        """List available secrets from AWS Secrets Manager.

        Args:
            prefix: Prefix to filter secrets by.

        Returns:
            List of secret keys.
        """
        try:
            if self.secret_name:
                # Get the entire secret
                response = self.client.get_secret_value(SecretId=self.secret_name)

                # Parse the secret value
                if "SecretString" in response:
                    secrets = json.loads(response["SecretString"])
                    return [k for k in secrets.keys() if k.startswith(prefix)]
                return []
            else:
                # List all secrets
                secrets = []
                paginator = self.client.get_paginator("list_secrets")

                # Iterate through all secrets
                for page in paginator.paginate():
                    for secret in page["SecretList"]:
                        if secret["Name"].startswith(prefix):
                            secrets.append(secret["Name"])

                return secrets
        except Exception as e:
            logger.error(f"Failed to list secrets from AWS Secrets Manager: {e}")
            return []

    def delete_secret(self, key: str) -> None:
        """Delete a secret from AWS Secrets Manager.

        Args:
            key: Secret key.
        """
        try:
            if self.secret_name:
                # Get the entire secret
                response = self.client.get_secret_value(SecretId=self.secret_name)

                # Parse the secret value
                if "SecretString" in response:
                    secrets = json.loads(response["SecretString"])

                    # Remove the key
                    if key in secrets:
                        del secrets[key]

                        # Save the secret
                        self.client.put_secret_value(
                            SecretId=self.secret_name, SecretString=json.dumps(secrets)
                        )
            else:
                # Delete the individual secret
                self.client.delete_secret(SecretId=key, ForceDeleteWithoutRecovery=True)
        except Exception as e:
            logger.error(f"Failed to delete secret {key} from AWS Secrets Manager: {e}")
            raise


class HashiCorpVaultProvider(SecretsProvider):
    """HashiCorp Vault provider.

    This class provides secrets from HashiCorp Vault.

    Attributes:
        client: HashiCorp Vault client.
        vault_url: URL of the HashiCorp Vault.
        token: Authentication token for HashiCorp Vault.
        mount_point: Mount point for the KV secrets engine.
    """

    def __init__(
        self,
        vault_url: str,
        token: Optional[str] = None,
        mount_point: str = "secret",
        **kwargs,
    ):
        """Initialize the HashiCorp Vault provider.

        Args:
            vault_url: URL of the HashiCorp Vault.
            token: Authentication token for HashiCorp Vault.
                If not provided, will try to get it from the VAULT_TOKEN environment variable.
            mount_point: Mount point for the KV secrets engine.
            **kwargs: Additional arguments for the HashiCorp Vault client.

        Raises:
            ImportError: If the required packages are not installed.
            ValueError: If the vault URL is not provided.
        """
        self.vault_url = vault_url
        self.mount_point = mount_point
        self.token = token or os.environ.get("VAULT_TOKEN")

        if not self.vault_url:
            raise ValueError("HashiCorp Vault URL is required")

        if not self.token:
            raise ValueError(
                "HashiCorp Vault token is required (either as parameter or VAULT_TOKEN environment variable)"
            )

        # Import HashiCorp Vault client
        try:
            import hvac

            # Create client
            self.client = hvac.Client(url=self.vault_url, token=self.token)

            # Check if client is authenticated
            if not self.client.is_authenticated():
                raise ValueError("Failed to authenticate with HashiCorp Vault")

            logger.debug(f"Initialized HashiCorp Vault provider: {self.vault_url}")
        except ImportError:
            logger.error("HashiCorp Vault provider requires hvac package")
            raise

    def get_secret(self, key: str) -> Optional[str]:
        """Get a secret value from HashiCorp Vault.

        Args:
            key: Secret key.

        Returns:
            Secret value, or None if the secret does not exist.
        """
        try:
            # Split key into path and key
            path_parts = key.split("/")
            if len(path_parts) > 1:
                # Last part is the key, the rest is the path
                secret_key = path_parts[-1]
                secret_path = "/".join(path_parts[:-1])
            else:
                # No path, just key
                secret_key = key
                secret_path = ""

            # Get secret from Vault
            try:
                # Try KV version 2 first
                response = self.client.secrets.kv.v2.read_secret_version(
                    path=secret_path or key, mount_point=self.mount_point
                )
                if response and "data" in response and "data" in response["data"]:
                    # KV v2 response
                    if secret_path:
                        # Return specific key from secret
                        return response["data"]["data"].get(secret_key)
                    else:
                        # Return entire secret as JSON string
                        return json.dumps(response["data"]["data"])
            except Exception:
                # Try KV version 1
                try:
                    response = self.client.secrets.kv.v1.read_secret(
                        path=secret_path or key, mount_point=self.mount_point
                    )
                    if response and "data" in response:
                        # KV v1 response
                        if secret_path:
                            # Return specific key from secret
                            return response["data"].get(secret_key)
                        else:
                            # Return entire secret as JSON string
                            return json.dumps(response["data"])
                except Exception as e:
                    logger.debug(
                        f"Failed to get secret {key} from HashiCorp Vault: {e}"
                    )
                    return None

            return None
        except Exception as e:
            logger.debug(f"Failed to get secret {key} from HashiCorp Vault: {e}")
            return None

    def set_secret(self, key: str, value: str) -> None:
        """Set a secret value in HashiCorp Vault.

        Args:
            key: Secret key.
            value: Secret value.
        """
        try:
            # Split key into path and key
            path_parts = key.split("/")
            if len(path_parts) > 1:
                # Last part is the key, the rest is the path
                secret_key = path_parts[-1]
                secret_path = "/".join(path_parts[:-1])
            else:
                # No path, just key
                secret_key = key
                secret_path = ""

            # Try to parse value as JSON
            try:
                json_value = json.loads(value)
                is_json = True
            except:
                json_value = value
                is_json = False

            # Set secret in Vault
            try:
                # Try KV version 2 first
                if secret_path:
                    # Get existing secret if it exists
                    try:
                        response = self.client.secrets.kv.v2.read_secret_version(
                            path=secret_path, mount_point=self.mount_point
                        )
                        if (
                            response
                            and "data" in response
                            and "data" in response["data"]
                        ):
                            # Update existing secret
                            secret_data = response["data"]["data"]
                            secret_data[secret_key] = json_value if is_json else value

                            # Write updated secret
                            self.client.secrets.kv.v2.create_or_update_secret(
                                path=secret_path,
                                secret=secret_data,
                                mount_point=self.mount_point,
                            )
                        else:
                            # Create new secret
                            self.client.secrets.kv.v2.create_or_update_secret(
                                path=secret_path,
                                secret={secret_key: json_value if is_json else value},
                                mount_point=self.mount_point,
                            )
                    except:
                        # Create new secret
                        self.client.secrets.kv.v2.create_or_update_secret(
                            path=secret_path,
                            secret={secret_key: json_value if is_json else value},
                            mount_point=self.mount_point,
                        )
                else:
                    # Write directly to the key
                    self.client.secrets.kv.v2.create_or_update_secret(
                        path=key,
                        secret=json_value if is_json else {"value": value},
                        mount_point=self.mount_point,
                    )
            except Exception:
                # Try KV version 1
                try:
                    if secret_path:
                        # Get existing secret if it exists
                        try:
                            response = self.client.secrets.kv.v1.read_secret(
                                path=secret_path, mount_point=self.mount_point
                            )
                            if response and "data" in response:
                                # Update existing secret
                                secret_data = response["data"]
                                secret_data[secret_key] = (
                                    json_value if is_json else value
                                )

                                # Write updated secret
                                self.client.secrets.kv.v1.create_or_update_secret(
                                    path=secret_path,
                                    secret=secret_data,
                                    mount_point=self.mount_point,
                                )
                            else:
                                # Create new secret
                                self.client.secrets.kv.v1.create_or_update_secret(
                                    path=secret_path,
                                    secret={
                                        secret_key: json_value if is_json else value
                                    },
                                    mount_point=self.mount_point,
                                )
                        except:
                            # Create new secret
                            self.client.secrets.kv.v1.create_or_update_secret(
                                path=secret_path,
                                secret={secret_key: json_value if is_json else value},
                                mount_point=self.mount_point,
                            )
                    else:
                        # Write directly to the key
                        self.client.secrets.kv.v1.create_or_update_secret(
                            path=key,
                            secret=json_value if is_json else {"value": value},
                            mount_point=self.mount_point,
                        )
                except Exception as e:
                    logger.error(f"Failed to set secret {key} in HashiCorp Vault: {e}")
                    raise
        except Exception as e:
            logger.error(f"Failed to set secret {key} in HashiCorp Vault: {e}")
            raise

    def list_secrets(self, prefix: str = "") -> List[str]:
        """List available secrets from HashiCorp Vault.

        Args:
            prefix: Prefix to filter secrets by.

        Returns:
            List of secret keys.
        """
        try:
            secrets = []

            # Try KV version 2 first
            try:
                # List secrets at the mount point
                response = self.client.secrets.kv.v2.list_secrets(
                    path=prefix, mount_point=self.mount_point
                )

                if response and "data" in response and "keys" in response["data"]:
                    # Add keys to the list
                    for key in response["data"]["keys"]:
                        if key.endswith("/"):
                            # This is a directory, recursively list secrets
                            sub_prefix = (
                                f"{prefix}/{key.rstrip('/')}"
                                if prefix
                                else key.rstrip("/")
                            )
                            secrets.extend(self.list_secrets(sub_prefix))
                        else:
                            # This is a secret
                            secrets.append(f"{prefix}/{key}" if prefix else key)
            except Exception:
                # Try KV version 1
                try:
                    # List secrets at the mount point
                    response = self.client.secrets.kv.v1.list_secrets(
                        path=prefix, mount_point=self.mount_point
                    )

                    if response and "data" in response and "keys" in response["data"]:
                        # Add keys to the list
                        for key in response["data"]["keys"]:
                            if key.endswith("/"):
                                # This is a directory, recursively list secrets
                                sub_prefix = (
                                    f"{prefix}/{key.rstrip('/')}"
                                    if prefix
                                    else key.rstrip("/")
                                )
                                secrets.extend(self.list_secrets(sub_prefix))
                            else:
                                # This is a secret
                                secrets.append(f"{prefix}/{key}" if prefix else key)
                except Exception as e:
                    logger.debug(f"Failed to list secrets from HashiCorp Vault: {e}")

            return secrets
        except Exception as e:
            logger.error(f"Failed to list secrets from HashiCorp Vault: {e}")
            return []

    def delete_secret(self, key: str) -> None:
        """Delete a secret from HashiCorp Vault.

        Args:
            key: Secret key.
        """
        try:
            # Split key into path and key
            path_parts = key.split("/")
            if len(path_parts) > 1:
                # Last part is the key, the rest is the path
                secret_key = path_parts[-1]
                secret_path = "/".join(path_parts[:-1])
            else:
                # No path, just key
                secret_key = key
                secret_path = ""

            # Delete secret from Vault
            try:
                # Try KV version 2 first
                if secret_path:
                    # Get existing secret if it exists
                    try:
                        response = self.client.secrets.kv.v2.read_secret_version(
                            path=secret_path, mount_point=self.mount_point
                        )
                        if (
                            response
                            and "data" in response
                            and "data" in response["data"]
                        ):
                            # Update existing secret
                            secret_data = response["data"]["data"]

                            # Remove the key
                            if secret_key in secret_data:
                                del secret_data[secret_key]

                                # Write updated secret
                                self.client.secrets.kv.v2.create_or_update_secret(
                                    path=secret_path,
                                    secret=secret_data,
                                    mount_point=self.mount_point,
                                )
                    except Exception:
                        pass
                else:
                    # Delete the entire secret
                    self.client.secrets.kv.v2.delete_metadata_and_all_versions(
                        path=key, mount_point=self.mount_point
                    )
            except Exception:
                # Try KV version 1
                try:
                    if secret_path:
                        # Get existing secret if it exists
                        try:
                            response = self.client.secrets.kv.v1.read_secret(
                                path=secret_path, mount_point=self.mount_point
                            )
                            if response and "data" in response:
                                # Update existing secret
                                secret_data = response["data"]

                                # Remove the key
                                if secret_key in secret_data:
                                    del secret_data[secret_key]

                                    # Write updated secret
                                    self.client.secrets.kv.v1.create_or_update_secret(
                                        path=secret_path,
                                        secret=secret_data,
                                        mount_point=self.mount_point,
                                    )
                        except Exception:
                            pass
                    else:
                        # Delete the entire secret
                        self.client.secrets.kv.v1.delete_secret(
                            path=key, mount_point=self.mount_point
                        )
                except Exception as e:
                    logger.error(
                        f"Failed to delete secret {key} from HashiCorp Vault: {e}"
                    )
                    raise
        except Exception as e:
            logger.error(f"Failed to delete secret {key} from HashiCorp Vault: {e}")
            raise
