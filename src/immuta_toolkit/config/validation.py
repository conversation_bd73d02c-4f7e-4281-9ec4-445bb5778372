"""Configuration validation for the Immuta SRE Toolkit."""

import os
import re
import json
import socket
import jsonschema
from typing import Dict, List, Optional, Union, Any, Callable
from enum import Enum

from pydantic import BaseModel, Field, ValidationError

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ValidationLevel(str, Enum):
    """Validation level."""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


class ValidationResult(BaseModel):
    """Validation result.

    This class represents the result of a validation check.

    Attributes:
        key: Configuration key.
        level: Validation level.
        message: Validation message.
        value: Configuration value.
    """

    key: str = Field(..., description="Configuration key")
    level: ValidationLevel = Field(..., description="Validation level")
    message: str = Field(..., description="Validation message")
    value: Any = Field(None, description="Configuration value")


class ConfigValidator:
    """Configuration validator.

    This class provides methods for validating configuration values.

    Attributes:
        rules: Dictionary of validation rules, keyed by configuration key.
    """

    def __init__(self, init_default_rules: bool = True):
        """Initialize the configuration validator.

        Args:
            init_default_rules: Whether to initialize default validation rules.
        """
        self.rules: Dict[str, List[Callable[[Any], Optional[ValidationResult]]]] = {}

        # Initialize default validation rules if requested
        if init_default_rules:
            self._init_default_rules()

    def add_rule(
        self, key: str, rule: Callable[[Any], Optional[ValidationResult]]
    ) -> None:
        """Add a validation rule.

        Args:
            key: Configuration key (dot-separated path).
            rule: Validation rule function.
        """
        if key not in self.rules:
            self.rules[key] = []
        self.rules[key].append(rule)
        logger.debug(f"Added validation rule for {key}")

    def validate(self, config: Dict[str, Any]) -> List[ValidationResult]:
        """Validate a configuration.

        Args:
            config: Configuration dictionary.

        Returns:
            List of validation results.
        """
        results = []

        # Apply validation rules
        for key, rules in self.rules.items():
            # Get configuration value
            value = self._get_value(config, key)

            # Apply rules
            for rule in rules:
                result = rule(value)
                if result:
                    results.append(result)

        return results

    def validate_key(self, config: Dict[str, Any], key: str) -> List[ValidationResult]:
        """Validate a specific configuration key.

        Args:
            config: Configuration dictionary.
            key: Configuration key (dot-separated path).

        Returns:
            List of validation results.
        """
        results = []

        # Get configuration value
        value = self._get_value(config, key)

        # Apply rules for the key
        if key in self.rules:
            for rule in self.rules[key]:
                result = rule(value)
                if result:
                    results.append(result)

        return results

    def _get_value(self, config: Dict[str, Any], key: str) -> Any:
        """Get a configuration value.

        Args:
            config: Configuration dictionary.
            key: Configuration key (dot-separated path).

        Returns:
            Configuration value.
        """
        # Split key into parts
        parts = key.split(".")

        # Get configuration value
        value = config
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return None

        return value

    def add_schema_rule(self, key: str, schema: Dict[str, Any]) -> None:
        """Add a JSON Schema validation rule.

        Args:
            key: Configuration key (dot-separated path).
            schema: JSON Schema.
        """
        self.add_rule(key, lambda value: self._validate_schema(key, value, schema))
        logger.debug(f"Added JSON Schema validation rule for {key}")

    def _validate_schema(
        self, key: str, value: Any, schema: Dict[str, Any]
    ) -> Optional[ValidationResult]:
        """Validate a value against a JSON Schema.

        Args:
            key: Configuration key.
            value: Value to validate.
            schema: JSON Schema.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if value is None:
            return None

        try:
            jsonschema.validate(value, schema)
        except jsonschema.exceptions.ValidationError as e:
            return ValidationResult(
                key=key,
                level=ValidationLevel.ERROR,
                message=f"Schema validation failed: {e.message}",
                value=value,
            )

        return None

    def _init_default_rules(self) -> None:
        """Initialize default validation rules."""
        # API base URL
        self.add_rule("api.base_url", self._validate_url)

        # API timeout
        self.add_rule("api.timeout", self._validate_positive_integer)

        # API max retries
        self.add_rule("api.max_retries", self._validate_positive_integer)

        # API rate limit
        self.add_rule("api.rate_limit", self._validate_positive_integer)

        # Web base URL
        self.add_rule("web.base_url", self._validate_url)

        # Web timeout
        self.add_rule("web.timeout", self._validate_positive_integer)

        # Storage local path
        self.add_rule("storage.local_path", self._validate_directory)

        # Logging level
        self.add_rule("logging.level", self._validate_log_level)

        # Logging file
        self.add_rule("logging.file", self._validate_file_path)

        # Reporting output directory
        self.add_rule("reporting.output_dir", self._validate_directory)

        # Operation max retries
        self.add_rule("operation.max_retries", self._validate_positive_integer)

        # Operation batch size
        self.add_rule("operation.batch_size", self._validate_positive_integer)

        # SMTP server
        self.add_rule("reporting.email.smtp_server", self._validate_smtp_server)

        # SMTP port
        self.add_rule("reporting.email.smtp_port", self._validate_port)

        # Add JSON Schema validation for configuration
        self.add_schema_rule(
            "config",
            {
                "type": "object",
                "properties": {
                    "api": {
                        "type": "object",
                        "properties": {
                            "base_url": {"type": "string", "format": "uri"},
                            "timeout": {"type": "integer", "minimum": 1},
                            "max_retries": {"type": "integer", "minimum": 0},
                            "rate_limit": {"type": "integer", "minimum": 1},
                        },
                    },
                    "web": {
                        "type": "object",
                        "properties": {
                            "base_url": {"type": "string", "format": "uri"},
                            "timeout": {"type": "integer", "minimum": 1},
                            "headless": {"type": "boolean"},
                        },
                    },
                    "storage": {
                        "type": "object",
                        "properties": {
                            "local_path": {"type": "string"},
                            "remote_url": {"type": "string", "format": "uri"},
                        },
                    },
                    "logging": {
                        "type": "object",
                        "properties": {
                            "level": {
                                "type": "string",
                                "enum": [
                                    "DEBUG",
                                    "INFO",
                                    "WARNING",
                                    "ERROR",
                                    "CRITICAL",
                                ],
                            },
                            "file": {"type": "string"},
                        },
                    },
                    "operation": {
                        "type": "object",
                        "properties": {
                            "max_retries": {"type": "integer", "minimum": 0},
                            "batch_size": {"type": "integer", "minimum": 1},
                        },
                    },
                },
            },
        )

    def _validate_url(self, value: Any) -> Optional[ValidationResult]:
        """Validate a URL.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        # Skip validation if value is None
        if value is None:
            return None

        if not value:
            return ValidationResult(
                key="api.base_url",
                level=ValidationLevel.ERROR,
                message="URL is required",
                value=value,
            )

        if not isinstance(value, str):
            return ValidationResult(
                key="api.base_url",
                level=ValidationLevel.ERROR,
                message="URL must be a string",
                value=value,
            )

        # Check if URL is valid
        url_pattern = r"^https?://[^\s/$.?#].[^\s]*$"
        if not re.match(url_pattern, value):
            return ValidationResult(
                key="api.base_url",
                level=ValidationLevel.ERROR,
                message="Invalid URL format",
                value=value,
            )

        return None

    def _validate_positive_integer(self, value: Any) -> Optional[ValidationResult]:
        """Validate a positive integer.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if value is None:
            return None

        if not isinstance(value, int):
            return ValidationResult(
                key="api.timeout",
                level=ValidationLevel.ERROR,
                message="Value must be an integer",
                value=value,
            )

        if value <= 0:
            return ValidationResult(
                key="api.timeout",
                level=ValidationLevel.ERROR,
                message="Value must be positive",
                value=value,
            )

        return None

    def _validate_directory(self, value: Any) -> Optional[ValidationResult]:
        """Validate a directory path.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if not value:
            return None

        if not isinstance(value, str):
            return ValidationResult(
                key="storage.local_path",
                level=ValidationLevel.ERROR,
                message="Directory path must be a string",
                value=value,
            )

        # Check if directory exists
        if not os.path.exists(value):
            return ValidationResult(
                key="storage.local_path",
                level=ValidationLevel.WARNING,
                message="Directory does not exist",
                value=value,
            )

        # Check if path is a directory
        if os.path.exists(value) and not os.path.isdir(value):
            return ValidationResult(
                key="storage.local_path",
                level=ValidationLevel.ERROR,
                message="Path exists but is not a directory",
                value=value,
            )

        return None

    def _validate_log_level(self, value: Any) -> Optional[ValidationResult]:
        """Validate a logging level.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if not value:
            return None

        if not isinstance(value, str):
            return ValidationResult(
                key="logging.level",
                level=ValidationLevel.ERROR,
                message="Logging level must be a string",
                value=value,
            )

        # Check if logging level is valid
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if value.upper() not in valid_levels:
            return ValidationResult(
                key="logging.level",
                level=ValidationLevel.ERROR,
                message=f"Invalid logging level. Valid levels: {valid_levels}",
                value=value,
            )

        return None

    def _validate_file_path(self, value: Any) -> Optional[ValidationResult]:
        """Validate a file path.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if not value:
            return None

        if not isinstance(value, str):
            return ValidationResult(
                key="logging.file",
                level=ValidationLevel.ERROR,
                message="File path must be a string",
                value=value,
            )

        # Check if directory exists
        directory = os.path.dirname(value)
        if directory and not os.path.exists(directory):
            return ValidationResult(
                key="logging.file",
                level=ValidationLevel.WARNING,
                message="Directory does not exist",
                value=value,
            )

        return None

    def _validate_smtp_server(self, value: Any) -> Optional[ValidationResult]:
        """Validate an SMTP server.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if not value:
            return None

        if not isinstance(value, str):
            return ValidationResult(
                key="reporting.email.smtp_server",
                level=ValidationLevel.ERROR,
                message="SMTP server must be a string",
                value=value,
            )

        # Try to resolve the hostname
        try:
            socket.gethostbyname(value)
        except socket.gaierror:
            return ValidationResult(
                key="reporting.email.smtp_server",
                level=ValidationLevel.WARNING,
                message="Could not resolve SMTP server hostname",
                value=value,
            )

        return None

    def _validate_port(self, value: Any) -> Optional[ValidationResult]:
        """Validate a port number.

        Args:
            value: Value to validate.

        Returns:
            Validation result if invalid, None otherwise.
        """
        if value is None:
            return None

        if not isinstance(value, int):
            return ValidationResult(
                key="reporting.email.smtp_port",
                level=ValidationLevel.ERROR,
                message="Port must be an integer",
                value=value,
            )

        if value < 1 or value > 65535:
            return ValidationResult(
                key="reporting.email.smtp_port",
                level=ValidationLevel.ERROR,
                message="Port must be between 1 and 65535",
                value=value,
            )

        return None
