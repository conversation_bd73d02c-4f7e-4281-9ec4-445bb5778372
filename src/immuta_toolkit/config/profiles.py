"""Configuration profiles for the Immuta SRE Toolkit."""

from typing import Dict, List, Optional, Union, Any
from enum import Enum

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ProfileType(str, Enum):
    """Profile type."""

    ENVIRONMENT = "environment"
    ROLE = "role"
    FEATURE = "feature"
    CUSTOM = "custom"


class ConfigProfile:
    """Configuration profile.

    This class represents a configuration profile, which is a set of
    configuration values that can be applied to the configuration.

    Attributes:
        name: Profile name.
        type: Profile type.
        description: Profile description.
        values: Profile configuration values.
        enabled: Whether the profile is enabled.
        parent: Parent profile name.
    """

    def __init__(
        self,
        name: str,
        type: Union[ProfileType, str],
        description: Optional[str] = None,
        values: Optional[Dict[str, Any]] = None,
        enabled: bool = True,
        parent: Optional[str] = None,
    ):
        """Initialize the configuration profile.

        Args:
            name: Profile name.
            type: Profile type.
            description: Profile description.
            values: Profile configuration values.
            enabled: Whether the profile is enabled.
            parent: Parent profile name.
        """
        self.name = name
        self.type = type if isinstance(type, ProfileType) else ProfileType(type)
        self.description = description
        self.values = values or {}
        self.enabled = enabled
        self.parent = parent

    def to_dict(self) -> Dict[str, Any]:
        """Convert the profile to a dictionary.

        Returns:
            Dictionary representation of the profile.
        """
        return {
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "values": self.values,
            "enabled": self.enabled,
            "parent": self.parent,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ConfigProfile":
        """Create a profile from a dictionary.

        Args:
            data: Dictionary representation of the profile.

        Returns:
            Configuration profile.
        """
        return cls(
            name=data["name"],
            type=data["type"],
            description=data.get("description"),
            values=data.get("values", {}),
            enabled=data.get("enabled", True),
            parent=data.get("parent"),
        )


class ProfileManager:
    """Profile manager.

    This class manages configuration profiles, which are sets of
    configuration values that can be applied to the configuration.

    Attributes:
        profiles: Dictionary of profiles, keyed by name.
    """

    def __init__(self):
        """Initialize the profile manager."""
        self.profiles: Dict[str, ConfigProfile] = {}

        # Initialize default profiles
        self._init_default_profiles()

    def add_profile(self, profile: ConfigProfile) -> None:
        """Add a profile.

        Args:
            profile: Profile to add.
        """
        self.profiles[profile.name] = profile
        logger.debug(f"Added profile: {profile.name}")

    def get_profile(self, name: str) -> Optional[ConfigProfile]:
        """Get a profile by name.

        Args:
            name: Profile name.

        Returns:
            Profile if found, None otherwise.
        """
        return self.profiles.get(name)

    def remove_profile(self, name: str) -> bool:
        """Remove a profile.

        Args:
            name: Profile name.

        Returns:
            True if the profile was removed, False otherwise.
        """
        if name in self.profiles:
            del self.profiles[name]
            logger.debug(f"Removed profile: {name}")
            return True
        return False

    def enable_profile(self, name: str) -> bool:
        """Enable a profile.

        Args:
            name: Profile name.

        Returns:
            True if the profile was enabled, False otherwise.
        """
        profile = self.get_profile(name)
        if profile:
            profile.enabled = True
            logger.debug(f"Enabled profile: {name}")
            return True
        return False

    def disable_profile(self, name: str) -> bool:
        """Disable a profile.

        Args:
            name: Profile name.

        Returns:
            True if the profile was disabled, False otherwise.
        """
        profile = self.get_profile(name)
        if profile:
            profile.enabled = False
            logger.debug(f"Disabled profile: {name}")
            return True
        return False

    def get_profiles_by_type(
        self, type: Union[ProfileType, str]
    ) -> List[ConfigProfile]:
        """Get profiles by type.

        Args:
            type: Profile type.

        Returns:
            List of profiles with the specified type.
        """
        type_str = type if isinstance(type, str) else type.value
        return [p for p in self.profiles.values() if p.type == type_str]

    def get_enabled_profiles(self) -> List[ConfigProfile]:
        """Get enabled profiles.

        Returns:
            List of enabled profiles.
        """
        return [p for p in self.profiles.values() if p.enabled]

    def apply_profile(
        self, config: Dict[str, Any], name: str, inherit: bool = True
    ) -> Dict[str, Any]:
        """Apply a profile to a configuration.

        Args:
            config: Configuration dictionary.
            name: Profile name.
            inherit: Whether to inherit values from parent profiles.

        Returns:
            Updated configuration dictionary.

        Raises:
            ValueError: If the profile is not found.
        """
        profile = self.get_profile(name)
        if not profile:
            raise ValueError(f"Profile not found: {name}")

        # Apply parent profiles if requested
        if inherit and profile.parent:
            try:
                # Apply parent profile first
                self.apply_profile(config, profile.parent, inherit=True)
            except ValueError as e:
                logger.warning(f"Failed to apply parent profile: {e}")

        # Apply profile values
        self._apply_values(config, profile.values)

        logger.debug(f"Applied profile: {name}")
        return config

    def apply_profiles(
        self,
        config: Dict[str, Any],
        types: Optional[List[Union[ProfileType, str]]] = None,
    ) -> Dict[str, Any]:
        """Apply enabled profiles to a configuration.

        Args:
            config: Configuration dictionary.
            types: List of profile types to apply. If None, all enabled profiles will be applied.

        Returns:
            Updated configuration dictionary.
        """
        # Get enabled profiles
        profiles = self.get_enabled_profiles()

        # Filter by type if specified
        if types:
            type_strs = [t if isinstance(t, str) else t.value for t in types]
            profiles = [p for p in profiles if p.type in type_strs]

        # Apply profiles
        for profile in profiles:
            self._apply_values(config, profile.values)
            logger.debug(f"Applied profile: {profile.name}")

        return config

    def _apply_values(self, config: Dict[str, Any], values: Dict[str, Any]) -> None:
        """Apply values to a configuration.

        Args:
            config: Configuration dictionary.
            values: Values to apply.
        """
        # Apply values recursively
        for key, value in values.items():
            if (
                isinstance(value, dict)
                and key in config
                and isinstance(config[key], dict)
            ):
                # Recursively update nested dictionaries
                self._apply_values(config[key], value)
            else:
                # Set value
                config[key] = value

    def get_inheritance_chain(self, name: str) -> List[str]:
        """Get the inheritance chain for a profile.

        Args:
            name: Profile name.

        Returns:
            List of profile names in the inheritance chain, starting with the
            most distant ancestor and ending with the specified profile.

        Raises:
            ValueError: If the profile is not found.
        """
        profile = self.get_profile(name)
        if not profile:
            raise ValueError(f"Profile not found: {name}")

        # Build inheritance chain
        chain = []
        visited = set()
        current_name = name

        while current_name and current_name not in visited:
            # Add profile to chain
            chain.append(current_name)
            visited.add(current_name)

            # Get parent profile
            current_profile = self.get_profile(current_name)
            if not current_profile or not current_profile.parent:
                break

            # Move to parent
            current_name = current_profile.parent

        # Reverse chain to get correct order
        chain.reverse()
        return chain

    def get_merged_values(self, name: str) -> Dict[str, Any]:
        """Get the merged values for a profile.

        This method merges the values of a profile with its parent profiles.

        Args:
            name: Profile name.

        Returns:
            Merged values dictionary.

        Raises:
            ValueError: If the profile is not found.
        """
        profile = self.get_profile(name)
        if not profile:
            raise ValueError(f"Profile not found: {name}")

        # Get inheritance chain
        chain = self.get_inheritance_chain(name)

        # Merge values
        merged_values = {}
        for profile_name in chain:
            profile = self.get_profile(profile_name)
            if profile:
                self._apply_values(merged_values, profile.values)

        return merged_values

    def _init_default_profiles(self) -> None:
        """Initialize default profiles."""
        # Development environment profile
        self.add_profile(
            ConfigProfile(
                name="dev",
                type=ProfileType.ENVIRONMENT,
                description="Development environment profile",
                values={
                    "environment": "dev",
                    "logging": {
                        "level": "DEBUG",
                    },
                    "web": {
                        "headless": False,
                    },
                    "operation": {
                        "max_retries": 3,
                    },
                },
            )
        )

        # Test environment profile
        self.add_profile(
            ConfigProfile(
                name="test",
                type=ProfileType.ENVIRONMENT,
                description="Test environment profile",
                values={
                    "environment": "test",
                    "logging": {
                        "level": "INFO",
                    },
                    "web": {
                        "headless": True,
                    },
                    "operation": {
                        "max_retries": 3,
                    },
                },
                enabled=False,
            )
        )

        # Production environment profile
        self.add_profile(
            ConfigProfile(
                name="prod",
                type=ProfileType.ENVIRONMENT,
                description="Production environment profile",
                values={
                    "environment": "prod",
                    "logging": {
                        "level": "WARNING",
                    },
                    "web": {
                        "headless": True,
                    },
                    "operation": {
                        "max_retries": 5,
                    },
                },
                enabled=False,
            )
        )

        # Administrator role profile
        self.add_profile(
            ConfigProfile(
                name="admin",
                type=ProfileType.ROLE,
                description="Administrator role profile",
                values={
                    "features": {
                        "advanced_operations": True,
                        "backup_restore": True,
                        "schema_evolution": True,
                    },
                },
                enabled=False,
            )
        )

        # Operator role profile
        self.add_profile(
            ConfigProfile(
                name="operator",
                type=ProfileType.ROLE,
                description="Operator role profile",
                values={
                    "features": {
                        "advanced_operations": False,
                        "backup_restore": True,
                        "schema_evolution": False,
                    },
                },
                enabled=False,
            )
        )

        # Viewer role profile
        self.add_profile(
            ConfigProfile(
                name="viewer",
                type=ProfileType.ROLE,
                description="Viewer role profile",
                values={
                    "features": {
                        "advanced_operations": False,
                        "backup_restore": False,
                        "schema_evolution": False,
                    },
                },
                enabled=False,
            )
        )
