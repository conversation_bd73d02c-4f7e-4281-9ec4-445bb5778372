"""Configuration sources for the Immuta SRE Toolkit."""

import os
import json
import yaml
import toml
import argparse
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.config.formats import ConfigFormat

logger = get_logger(__name__)


class ConfigSource(ABC):
    """Base class for configuration sources.

    This class defines the interface for configuration sources, which
    provide configuration values from different sources.
    """

    @abstractmethod
    def load(self) -> Dict[str, Any]:
        """Load configuration from the source.

        Returns:
            Configuration dictionary.

        Raises:
            Exception: If the configuration cannot be loaded.
        """
        pass

    @abstractmethod
    def __str__(self) -> str:
        """Get a string representation of the source.

        Returns:
            String representation.
        """
        pass


class FileConfigSource(ConfigSource):
    """File configuration source.

    This class provides configuration values from a file.

    Attributes:
        path: Path to the configuration file.
        format: Configuration file format.
        required: Whether the file is required to exist.
    """

    def __init__(
        self, path: Union[str, Path], format: ConfigFormat, required: bool = False
    ):
        """Initialize the file configuration source.

        Args:
            path: Path to the configuration file.
            format: Configuration file format.
            required: Whether the file is required to exist.
        """
        self.path = Path(path)
        self.format = format
        self.required = required

    def load(self) -> Dict[str, Any]:
        """Load configuration from the file.

        Returns:
            Configuration dictionary.

        Raises:
            FileNotFoundError: If the file does not exist and is required.
            ValueError: If the file format is not supported.
            Exception: If the configuration cannot be loaded.
        """
        # Check if the file exists
        if not self.path.exists():
            if self.required:
                raise FileNotFoundError(f"Configuration file not found: {self.path}")
            return {}

        # Load configuration from file
        try:
            with open(self.path, "r") as f:
                if self.format == ConfigFormat.JSON:
                    return json.load(f)
                elif self.format == ConfigFormat.YAML:
                    return yaml.safe_load(f)
                elif self.format == ConfigFormat.TOML:
                    return toml.load(f)
                else:
                    raise ValueError(f"Unsupported format: {self.format}")
        except Exception as e:
            logger.error(f"Failed to load configuration from {self.path}: {e}")
            raise

    def __str__(self) -> str:
        """Get a string representation of the source.

        Returns:
            String representation.
        """
        return f"FileConfigSource({self.path}, {self.format})"


class EnvConfigSource(ConfigSource):
    """Environment variable configuration source.

    This class provides configuration values from environment variables.

    Attributes:
        prefix: Prefix for environment variables.
        lowercase: Whether to convert environment variable names to lowercase.
    """

    def __init__(self, prefix: str = "IMMUTA_", lowercase: bool = True):
        """Initialize the environment variable configuration source.

        Args:
            prefix: Prefix for environment variables.
            lowercase: Whether to convert environment variable names to lowercase.
        """
        self.prefix = prefix
        self.lowercase = lowercase

    def load(self) -> Dict[str, Any]:
        """Load configuration from environment variables.

        Returns:
            Configuration dictionary.
        """
        config = {}

        # Get all environment variables with the prefix
        for key, value in os.environ.items():
            if key.startswith(self.prefix):
                # Remove prefix
                key = key[len(self.prefix) :]

                # Convert to lowercase if requested
                if self.lowercase:
                    key = key.lower()

                # Convert value to appropriate type
                value = self._convert_value(value)

                # Add to configuration
                self._set_nested_value(config, key, value)

        return config

    def _convert_value(self, value: str) -> Any:
        """Convert a string value to an appropriate type.

        Args:
            value: String value to convert.

        Returns:
            Converted value.
        """
        # Try to convert to boolean
        if value.lower() in ("true", "yes", "1"):
            return True
        elif value.lower() in ("false", "no", "0"):
            return False

        # Try to convert to integer
        try:
            return int(value)
        except ValueError:
            pass

        # Try to convert to float
        try:
            return float(value)
        except ValueError:
            pass

        # Return as string
        return value

    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any) -> None:
        """Set a nested value in a dictionary.

        Args:
            config: Dictionary to set the value in.
            key: Key to set (can be dot-separated for nested dictionaries).
            value: Value to set.
        """
        # Split key into parts
        parts = key.split("_")

        # Set value in nested dictionary
        current = config
        for i, part in enumerate(parts[:-1]):
            if part not in current:
                current[part] = {}
            current = current[part]

        # Set the value
        current[parts[-1]] = value

    def __str__(self) -> str:
        """Get a string representation of the source.

        Returns:
            String representation.
        """
        return f"EnvConfigSource({self.prefix})"


class ArgParseConfigSource(ConfigSource):
    """Command-line argument configuration source.

    This class provides configuration values from command-line arguments.

    Attributes:
        parser: ArgumentParser instance.
        args: Parsed arguments.
        prefix: Prefix for configuration keys.
    """

    def __init__(
        self,
        parser: Optional[argparse.ArgumentParser] = None,
        args: Optional[argparse.Namespace] = None,
        prefix: str = "",
    ):
        """Initialize the command-line argument configuration source.

        Args:
            parser: ArgumentParser instance. If None, a new parser will be created.
            args: Parsed arguments. If None, arguments will be parsed from sys.argv.
            prefix: Prefix for configuration keys.
        """
        self.parser = parser or argparse.ArgumentParser()
        self.args = args
        self.prefix = prefix

    def load(self) -> Dict[str, Any]:
        """Load configuration from command-line arguments.

        Returns:
            Configuration dictionary.
        """
        config = {}

        # Parse arguments if not already parsed
        if self.args is None:
            self.args = self.parser.parse_args()

        # Convert arguments to dictionary
        args_dict = vars(self.args)

        # Add to configuration
        for key, value in args_dict.items():
            # Skip None values
            if value is None:
                continue

            # Add prefix if provided
            if self.prefix:
                key = f"{self.prefix}.{key}"

            # Add to configuration
            self._set_nested_value(config, key, value)

        return config

    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any) -> None:
        """Set a nested value in a dictionary.

        Args:
            config: Dictionary to set the value in.
            key: Key to set (can be dot-separated for nested dictionaries).
            value: Value to set.
        """
        # Split key into parts
        parts = key.split(".")

        # Set value in nested dictionary
        current = config
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # Set the value
        current[parts[-1]] = value

    def __str__(self) -> str:
        """Get a string representation of the source.

        Returns:
            String representation.
        """
        return f"ArgParseConfigSource({self.prefix})"


class SecretsConfigSource(ConfigSource):
    """Secrets manager configuration source.

    This class provides configuration values from a secrets manager.

    Attributes:
        provider: Secrets provider name.
        kwargs: Additional arguments for the secrets provider.
    """

    def __init__(self, provider: str, **kwargs):
        """Initialize the secrets manager configuration source.

        Args:
            provider: Secrets provider name.
            **kwargs: Additional arguments for the secrets provider.

        Raises:
            ValueError: If the provider is not supported.
        """
        self.provider = provider
        self.kwargs = kwargs

        # Initialize secrets provider
        try:
            from immuta_toolkit.config.secrets import create_secrets_provider

            self.secrets_provider = create_secrets_provider(provider, **kwargs)
            logger.debug(f"Initialized {provider} secrets provider")
        except ImportError:
            logger.error(f"Failed to import secrets provider: {provider}")
            raise
        except ValueError as e:
            logger.error(f"Failed to initialize secrets provider: {e}")
            raise

    def load(self) -> Dict[str, Any]:
        """Load configuration from the secrets manager.

        Returns:
            Configuration dictionary.

        Raises:
            Exception: If the configuration cannot be loaded.
        """
        config = {}

        # Get prefix for filtering secrets
        prefix = self.kwargs.get("key_prefix", "")

        try:
            # List all secrets with the prefix
            secrets = self.secrets_provider.list_secrets(prefix)

            # Get each secret value
            for key in secrets:
                # Get secret value
                value = self.secrets_provider.get_secret(key)

                # Skip None values
                if value is None:
                    continue

                # Remove prefix if needed
                if prefix and key.startswith(prefix):
                    key = key[len(prefix) :]

                # Convert value to appropriate type
                value = self._convert_value(value)

                # Add to configuration
                self._set_nested_value(config, key, value)

            return config
        except Exception as e:
            logger.error(
                f"Failed to load configuration from {self.provider} secrets provider: {e}"
            )
            raise

    def _init_azure(self) -> None:
        """Initialize Azure Key Vault provider."""
        # Import Azure Key Vault client
        try:
            from azure.identity import DefaultAzureCredential
            from azure.keyvault.secrets import SecretClient

            # Get vault URL
            vault_url = self.kwargs.get("vault_url")
            if not vault_url:
                raise ValueError("Azure Key Vault URL is required")

            # Create credential
            credential = DefaultAzureCredential()

            # Create client
            self.client = SecretClient(vault_url=vault_url, credential=credential)

            logger.debug(f"Initialized Azure Key Vault provider: {vault_url}")
        except ImportError:
            logger.error(
                "Azure Key Vault provider requires azure-identity and azure-keyvault-secrets packages"
            )
            raise

    def _load_azure(self) -> Dict[str, Any]:
        """Load configuration from Azure Key Vault.

        Returns:
            Configuration dictionary.

        Raises:
            Exception: If the configuration cannot be loaded.
        """
        config = {}

        # Get prefix
        prefix = self.kwargs.get("key_prefix", "")

        try:
            # Get all secrets with the prefix
            for secret in self.client.list_properties_of_secrets():
                if secret.name.startswith(prefix):
                    # Remove prefix
                    key = secret.name[len(prefix) :]

                    # Get secret value
                    value = self.client.get_secret(secret.name).value

                    # Convert value to appropriate type
                    value = self._convert_value(value)

                    # Add to configuration
                    self._set_nested_value(config, key, value)

            return config
        except Exception as e:
            logger.error(f"Failed to load configuration from Azure Key Vault: {e}")
            raise

    def _init_aws(self) -> None:
        """Initialize AWS Secrets Manager provider."""
        # Import AWS Secrets Manager client
        try:
            import boto3

            # Get region
            region = self.kwargs.get("region")
            if not region:
                raise ValueError("AWS region is required")

            # Create client
            self.client = boto3.client("secretsmanager", region_name=region)

            logger.debug(f"Initialized AWS Secrets Manager provider: {region}")
        except ImportError:
            logger.error("AWS Secrets Manager provider requires boto3 package")
            raise

    def _load_aws(self) -> Dict[str, Any]:
        """Load configuration from AWS Secrets Manager.

        Returns:
            Configuration dictionary.

        Raises:
            Exception: If the configuration cannot be loaded.
        """
        config = {}

        # Get prefix for filtering secrets
        prefix = self.kwargs.get("key_prefix", "")

        # Check if we're loading a single secret or multiple secrets
        secret_name = self.kwargs.get("secret_name")

        if secret_name:
            # Load a single secret
            try:
                # Get secret value
                response = self.client.get_secret_value(SecretId=secret_name)

                # Parse secret value
                if "SecretString" in response:
                    secret = json.loads(response["SecretString"])

                    # Add to configuration
                    for key, value in secret.items():
                        # Convert value to appropriate type
                        value = self._convert_value(value)

                        # Add to configuration
                        self._set_nested_value(config, key, value)

                logger.debug(
                    f"Loaded configuration from AWS Secrets Manager: {secret_name}"
                )
            except Exception as e:
                logger.error(
                    f"Failed to load configuration from AWS Secrets Manager: {e}"
                )
                raise
        else:
            # Load multiple secrets with the prefix
            try:
                # List all secrets
                paginator = self.client.get_paginator("list_secrets")

                # Iterate through all secrets
                for page in paginator.paginate():
                    for secret in page["SecretList"]:
                        # Check if secret name starts with prefix
                        if prefix and not secret["Name"].startswith(prefix):
                            continue

                        try:
                            # Get secret value
                            response = self.client.get_secret_value(
                                SecretId=secret["ARN"]
                            )

                            # Parse secret value
                            if "SecretString" in response:
                                # Remove prefix from secret name
                                key = secret["Name"]
                                if prefix and key.startswith(prefix):
                                    key = key[len(prefix) :]

                                # Try to parse as JSON
                                try:
                                    secret_value = json.loads(response["SecretString"])

                                    # If it's a dictionary, add each key-value pair
                                    if isinstance(secret_value, dict):
                                        for k, v in secret_value.items():
                                            # Convert value to appropriate type
                                            v = self._convert_value(v)

                                            # Add to configuration
                                            self._set_nested_value(
                                                config, f"{key}.{k}" if key else k, v
                                            )
                                    else:
                                        # Add as a single value
                                        value = self._convert_value(secret_value)
                                        self._set_nested_value(config, key, value)
                                except json.JSONDecodeError:
                                    # Not JSON, treat as string
                                    value = self._convert_value(
                                        response["SecretString"]
                                    )
                                    self._set_nested_value(config, key, value)
                        except Exception as e:
                            logger.warning(
                                f"Failed to load secret {secret['Name']}: {e}"
                            )

                logger.debug(
                    f"Loaded configuration from AWS Secrets Manager with prefix: {prefix}"
                )
            except Exception as e:
                logger.error(f"Failed to list secrets from AWS Secrets Manager: {e}")
                raise

        return config

    def _init_hashicorp(self) -> None:
        """Initialize HashiCorp Vault provider."""
        # Import HashiCorp Vault client
        try:
            import hvac

            # Get vault URL
            vault_url = self.kwargs.get("vault_url")
            if not vault_url:
                raise ValueError("HashiCorp Vault URL is required")

            # Create client
            self.client = hvac.Client(url=vault_url)

            # Authenticate based on provided method
            auth_method = self.kwargs.get("auth_method", "token")

            if auth_method == "token":
                # Token authentication
                token = self.kwargs.get("token")
                if token:
                    self.client.token = token
                else:
                    # Try to get token from environment variable
                    token = os.getenv("VAULT_TOKEN")
                    if token:
                        self.client.token = token
                    else:
                        raise ValueError("Token is required for token authentication")

            elif auth_method == "approle":
                # AppRole authentication
                role_id = self.kwargs.get("role_id") or os.getenv("VAULT_ROLE_ID")
                secret_id = self.kwargs.get("secret_id") or os.getenv("VAULT_SECRET_ID")

                if not role_id or not secret_id:
                    raise ValueError(
                        "role_id and secret_id are required for AppRole authentication"
                    )

                # Authenticate with AppRole
                self.client.auth.approle.login(role_id=role_id, secret_id=secret_id)

            elif auth_method == "userpass":
                # Username/password authentication
                username = self.kwargs.get("username") or os.getenv("VAULT_USERNAME")
                password = self.kwargs.get("password") or os.getenv("VAULT_PASSWORD")

                if not username or not password:
                    raise ValueError(
                        "username and password are required for userpass authentication"
                    )

                # Authenticate with username/password
                self.client.auth.userpass.login(username=username, password=password)

            elif auth_method == "kubernetes":
                # Kubernetes authentication
                role = self.kwargs.get("role") or os.getenv("VAULT_ROLE")
                jwt = self.kwargs.get("jwt")

                if not role:
                    raise ValueError("role is required for Kubernetes authentication")

                if not jwt:
                    # Try to read from the default service account token path
                    token_path = "/var/run/secrets/kubernetes.io/serviceaccount/token"
                    try:
                        with open(token_path, "r") as f:
                            jwt = f.read().strip()
                    except Exception as e:
                        raise ValueError(
                            f"Failed to read Kubernetes service account token: {e}"
                        )

                # Authenticate with Kubernetes
                self.client.auth.kubernetes.login(role=role, jwt=jwt)

            else:
                raise ValueError(f"Unsupported authentication method: {auth_method}")

            # Verify authentication
            if not self.client.is_authenticated():
                raise ValueError("Failed to authenticate with HashiCorp Vault")

            logger.debug(
                f"Initialized HashiCorp Vault provider: {vault_url} with {auth_method} authentication"
            )
        except ImportError:
            logger.error("HashiCorp Vault provider requires hvac package")
            raise

    def _load_hashicorp(self) -> Dict[str, Any]:
        """Load configuration from HashiCorp Vault.

        Returns:
            Configuration dictionary.

        Raises:
            Exception: If the configuration cannot be loaded.
        """
        config = {}

        # Get secret engine type
        engine_type = self.kwargs.get("engine_type", "kv")
        engine_version = self.kwargs.get("engine_version", 2)

        # Get path and prefix
        path = self.kwargs.get("path")
        prefix = self.kwargs.get("key_prefix", "")

        # Check if we're loading a single path or multiple paths
        if path:
            # Load a single path
            try:
                if engine_type == "kv":
                    # KV secrets engine
                    if engine_version == 1:
                        # KV version 1
                        secret = self.client.secrets.kv.v1.read_secret(path=path)
                        if "data" in secret:
                            secret_data = secret["data"]
                    else:
                        # KV version 2
                        response = self.client.secrets.kv.v2.read_secret_version(
                            path=path
                        )
                        if "data" in response and "data" in response["data"]:
                            secret_data = response["data"]["data"]
                        else:
                            secret_data = {}
                elif engine_type == "database":
                    # Database secrets engine
                    secret = self.client.secrets.database.generate_credentials(
                        name=path
                    )
                    if "data" in secret:
                        secret_data = secret["data"]
                    else:
                        secret_data = {}
                elif engine_type == "aws":
                    # AWS secrets engine
                    secret = self.client.secrets.aws.generate_credentials(name=path)
                    if "data" in secret:
                        secret_data = secret["data"]
                    else:
                        secret_data = {}
                else:
                    raise ValueError(f"Unsupported engine type: {engine_type}")

                # Add to configuration
                for key, value in secret_data.items():
                    # Convert value to appropriate type
                    value = self._convert_value(value)

                    # Add to configuration
                    self._set_nested_value(config, key, value)

                logger.debug(f"Loaded configuration from HashiCorp Vault: {path}")
            except Exception as e:
                logger.error(f"Failed to load configuration from HashiCorp Vault: {e}")
                raise
        else:
            # Load multiple paths with the prefix (only supported for KV engine)
            if engine_type != "kv":
                raise ValueError(
                    f"Listing secrets is only supported for KV engine, not {engine_type}"
                )

            try:
                # Get mount point
                mount_point = self.kwargs.get("mount_point", "secret")

                # List all secrets
                list_response = self.client.secrets.kv.v2.list_secrets(
                    path=prefix, mount_point=mount_point
                )

                if "data" in list_response and "keys" in list_response["data"]:
                    # Iterate through all secrets
                    for key in list_response["data"]["keys"]:
                        # Skip directories (keys ending with /)
                        if key.endswith("/"):
                            continue

                        try:
                            # Get full path
                            full_path = f"{prefix}/{key}" if prefix else key

                            # Get secret value
                            if engine_version == 1:
                                # KV version 1
                                secret = self.client.secrets.kv.v1.read_secret(
                                    path=full_path, mount_point=mount_point
                                )
                                if "data" in secret:
                                    secret_data = secret["data"]
                                else:
                                    secret_data = {}
                            else:
                                # KV version 2
                                response = (
                                    self.client.secrets.kv.v2.read_secret_version(
                                        path=full_path, mount_point=mount_point
                                    )
                                )
                                if "data" in response and "data" in response["data"]:
                                    secret_data = response["data"]["data"]
                                else:
                                    secret_data = {}

                            # Add to configuration
                            for k, v in secret_data.items():
                                # Convert value to appropriate type
                                v = self._convert_value(v)

                                # Add to configuration
                                self._set_nested_value(config, f"{key}.{k}", v)
                        except Exception as e:
                            logger.warning(f"Failed to load secret {key}: {e}")

                logger.debug(
                    f"Loaded configuration from HashiCorp Vault with prefix: {prefix}"
                )
            except Exception as e:
                logger.error(f"Failed to list secrets from HashiCorp Vault: {e}")
                raise

        return config

    def _convert_value(self, value: str) -> Any:
        """Convert a string value to an appropriate type.

        Args:
            value: String value to convert.

        Returns:
            Converted value.
        """
        # If value is not a string, return as is
        if not isinstance(value, str):
            return value

        # Try to parse as JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            pass

        # Try to convert to boolean
        if value.lower() in ("true", "yes", "1"):
            return True
        elif value.lower() in ("false", "no", "0"):
            return False

        # Try to convert to integer
        try:
            return int(value)
        except ValueError:
            pass

        # Try to convert to float
        try:
            return float(value)
        except ValueError:
            pass

        # Return as string
        return value

    def _set_nested_value(self, config: Dict[str, Any], key: str, value: Any) -> None:
        """Set a nested value in a dictionary.

        Args:
            config: Dictionary to set the value in.
            key: Key to set (can be dot-separated for nested dictionaries).
            value: Value to set.
        """
        # Split key into parts (support both dot and underscore separators)
        if "." in key:
            parts = key.split(".")
        else:
            parts = key.split("_")

        # Set value in nested dictionary
        current = config
        for part in parts[:-1]:
            if part not in current:
                current[part] = {}
            current = current[part]

        # Set the value
        current[parts[-1]] = value

    def __str__(self) -> str:
        """Get a string representation of the source.

        Returns:
            String representation.
        """
        return f"SecretsConfigSource({self.provider})"
