"""Configuration manager for the Immuta SRE Toolkit."""

import os
import json
import yaml
import toml
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Union, Any, Callable, TypeVar, Type, cast
from enum import Enum
import importlib.util

from pydantic import BaseModel, ValidationError, create_model

from immuta_toolkit.config.schema import ConfigSchema
from immuta_toolkit.config.formats import ConfigFormat
from immuta_toolkit.config.versioning import ConfigVersionManager, ConfigVersion

# Import these after ConfigFormat to avoid circular imports
from immuta_toolkit.config.sources import (
    ConfigSource,
    FileConfigSource,
    EnvConfigSource,
    ArgParseConfigSource,
    SecretsConfigSource,
)
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T", bound=BaseModel)


class ConfigFormat(str, Enum):
    """Configuration file format."""

    JSON = "json"
    YAML = "yaml"
    TOML = "toml"


class ConfigManager:
    """Configuration manager for the Immuta SRE Toolkit.

    This class provides a unified interface for managing configuration
    from multiple sources, including files, environment variables, and
    secrets managers.

    Attributes:
        schema: Configuration schema.
        sources: List of configuration sources.
        config: Current configuration.
        config_model: Configuration model class.
    """

    def __init__(
        self,
        schema: Optional[ConfigSchema] = None,
        config_model: Optional[Type[BaseModel]] = None,
        load_defaults: bool = True,
        config_dir: Optional[str] = None,
        enable_versioning: bool = False,
    ):
        """Initialize the configuration manager.

        Args:
            schema: Configuration schema. If None, the default schema will be used.
            config_model: Configuration model class. If None, a model will be created
                from the schema.
            load_defaults: Whether to load default configuration values.
            config_dir: Directory containing configuration files. If None, the current
                directory will be used.
            enable_versioning: Whether to enable configuration versioning.
        """
        # Initialize schema
        self.schema = schema or ConfigSchema()

        # Initialize configuration sources
        self.sources: List[ConfigSource] = []

        # Initialize configuration model
        self.config_model = config_model or self._create_config_model()

        # Initialize configuration
        self.config: Optional[BaseModel] = None

        # Initialize versioning
        self.config_dir = config_dir or os.getcwd()
        self.enable_versioning = enable_versioning
        self.version_manager = None
        self.config_file = None  # Track the last loaded/saved config file

        if enable_versioning:
            self.version_manager = ConfigVersionManager(self.config_dir)

        # Load default configuration if requested
        if load_defaults:
            self.load_defaults()

    def add_source(self, source: ConfigSource) -> None:
        """Add a configuration source.

        Args:
            source: Configuration source to add.
        """
        self.sources.append(source)
        logger.debug(f"Added configuration source: {source}")

    def load_defaults(self) -> None:
        """Load default configuration values."""
        # Create a new configuration instance with default values
        self.config = self.config_model()
        logger.debug("Loaded default configuration values")

    def load(self) -> BaseModel:
        """Load configuration from all sources.

        Returns:
            Loaded configuration.

        Raises:
            ValidationError: If the configuration is invalid.
        """
        # Start with default configuration if not already loaded
        if self.config is None:
            self.load_defaults()

        # Load configuration from each source
        for source in self.sources:
            try:
                # Get configuration from source
                source_config = source.load()

                # Update configuration
                if source_config:
                    self._update_config(source_config)

                logger.debug(f"Loaded configuration from {source}")
            except Exception as e:
                logger.warning(f"Failed to load configuration from {source}: {e}")

        # Validate configuration
        self._validate_config()

        return self.config

    def save(
        self,
        path: Union[str, Path],
        format: ConfigFormat = ConfigFormat.JSON,
        version: Optional[str] = None,
        version_description: Optional[str] = None,
    ) -> None:
        """Save configuration to a file.

        Args:
            path: Path to save the configuration to.
            format: Configuration file format.
            version: Version string for the configuration. If None, no version
                will be created.
            version_description: Description of the version.

        Raises:
            ValueError: If the configuration is not loaded.
            IOError: If the configuration cannot be saved.
        """
        if self.config is None:
            raise ValueError("Configuration not loaded")

        # Convert configuration to dictionary
        config_dict = self.config.model_dump()

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)

        # Save configuration to file
        try:
            with open(path, "w") as f:
                if format == ConfigFormat.JSON:
                    json.dump(config_dict, f, indent=2)
                elif format == ConfigFormat.YAML:
                    yaml.dump(config_dict, f, default_flow_style=False)
                elif format == ConfigFormat.TOML:
                    toml.dump(config_dict, f)
                else:
                    raise ValueError(f"Unsupported format: {format}")

            # Store the config file path
            self.config_file = str(path)
            logger.info(f"Saved configuration to {path}")

            # Create version if requested and versioning is enabled
            if version and self.enable_versioning and self.version_manager:
                self.version_manager.create_version(
                    version=version,
                    description=version_description,
                    changes={"path": str(path), "format": format.value},
                )
                logger.info(f"Created configuration version: {version}")
        except Exception as e:
            logger.error(f"Failed to save configuration to {path}: {e}")
            raise IOError(f"Failed to save configuration: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value.

        Args:
            key: Configuration key (dot-separated path).
            default: Default value to return if the key is not found.

        Returns:
            Configuration value.
        """
        if self.config is None:
            return default

        # Split key into parts
        parts = key.split(".")

        # Get configuration value
        value = self.config
        for part in parts:
            if hasattr(value, part):
                value = getattr(value, part)
            elif isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return default

        return value

    def set(self, key: str, value: Any) -> None:
        """Set a configuration value.

        Args:
            key: Configuration key (dot-separated path).
            value: Configuration value.

        Raises:
            ValueError: If the configuration is not loaded or the key is invalid.
        """
        if self.config is None:
            raise ValueError("Configuration not loaded")

        # Split key into parts
        parts = key.split(".")

        # Set configuration value
        config = self.config
        for i, part in enumerate(parts[:-1]):
            if hasattr(config, part):
                config = getattr(config, part)
            elif isinstance(config, dict) and part in config:
                config = config[part]
            else:
                raise ValueError(f"Invalid configuration key: {key}")

        # Set the value
        last_part = parts[-1]
        if hasattr(config, last_part):
            setattr(config, last_part, value)
        elif isinstance(config, dict):
            config[last_part] = value
        else:
            raise ValueError(f"Invalid configuration key: {key}")

        # Validate configuration
        self._validate_config()

    def from_file(
        self,
        path: Union[str, Path],
        format: Optional[ConfigFormat] = None,
        required: bool = False,
    ) -> None:
        """Load configuration from a file.

        Args:
            path: Path to the configuration file.
            format: Configuration file format. If None, the format will be
                determined from the file extension.
            required: Whether the file is required to exist.

        Raises:
            FileNotFoundError: If the file does not exist and is required.
            ValueError: If the format cannot be determined.
        """
        # Determine format from file extension if not provided
        if format is None:
            ext = os.path.splitext(path)[1].lower()
            if ext == ".json":
                format = ConfigFormat.JSON
            elif ext in (".yaml", ".yml"):
                format = ConfigFormat.YAML
            elif ext == ".toml":
                format = ConfigFormat.TOML
            else:
                raise ValueError(f"Cannot determine format from file extension: {ext}")

        # Add file configuration source
        source = FileConfigSource(path, format, required)
        self.add_source(source)

        # Store the config file path
        self.config_file = str(path)

    def from_env(self, prefix: str = "IMMUTA_", lowercase: bool = True) -> None:
        """Load configuration from environment variables.

        Args:
            prefix: Prefix for environment variables.
            lowercase: Whether to convert environment variable names to lowercase.
        """
        # Add environment configuration source
        source = EnvConfigSource(prefix, lowercase)
        self.add_source(source)

    def from_secrets(self, provider: str, **kwargs) -> None:
        """Load configuration from a secrets manager.

        Args:
            provider: Secrets provider name.
            **kwargs: Additional arguments for the secrets provider.

        Raises:
            ValueError: If the provider is not supported.
        """
        # Add secrets configuration source
        source = SecretsConfigSource(provider, **kwargs)
        self.add_source(source)

    def from_args(
        self,
        parser: Optional[argparse.ArgumentParser] = None,
        args: Optional[argparse.Namespace] = None,
        prefix: str = "",
    ) -> None:
        """Load configuration from command-line arguments.

        Args:
            parser: ArgumentParser instance. If None, a new parser will be created.
            args: Parsed arguments. If None, arguments will be parsed from sys.argv.
            prefix: Prefix for configuration keys.
        """
        # Add command-line argument configuration source
        source = ArgParseConfigSource(parser, args, prefix)
        self.add_source(source)

    def get_version_history(self) -> List[Dict[str, Any]]:
        """Get the version history of the configuration.

        Returns:
            List of version dictionaries, or an empty list if versioning is not enabled.
        """
        if not self.enable_versioning or not self.version_manager:
            return []

        return [
            version.to_dict() for version in self.version_manager.get_version_history()
        ]

    def get_current_version(self) -> Optional[Dict[str, Any]]:
        """Get the current version of the configuration.

        Returns:
            Current version dictionary, or None if versioning is not enabled or no version exists.
        """
        if not self.enable_versioning or not self.version_manager:
            return None

        current_version = self.version_manager.get_current_version()
        return current_version.to_dict() if current_version else None

    def restore_version(self, version: str) -> bool:
        """Restore a previous version of the configuration.

        Args:
            version: Version string.

        Returns:
            True if the version was restored, False otherwise.
        """
        if not self.enable_versioning or not self.version_manager:
            logger.warning("Configuration versioning is not enabled")
            return False

        restored_version = self.version_manager.restore_version(version)
        if not restored_version:
            logger.warning(f"Failed to restore version: {version}")
            return False

        # Reload configuration
        self.load()

        logger.info(f"Restored configuration version: {version}")
        return True

    def _create_config_model(self) -> Type[BaseModel]:
        """Create a configuration model from the schema.

        Returns:
            Configuration model class.
        """
        # Get schema properties
        properties = self.schema.properties

        # Create model fields
        fields = {}
        for name, prop in properties.items():
            field_type = prop.get("type", Any)
            field_default = prop.get("default")
            field_description = prop.get("description", "")

            # Add field to model
            fields[name] = (field_type, field_default)

        # Create model class
        model_name = self.schema.title or "Config"
        model = create_model(model_name, **fields)

        return model

    def _update_config(self, source_config: Dict[str, Any]) -> None:
        """Update configuration with values from a source.

        Args:
            source_config: Configuration values from a source.
        """
        if self.config is None:
            return

        # Update configuration
        for key, value in source_config.items():
            if hasattr(self.config, key):
                current_value = getattr(self.config, key)

                # Recursively update nested dictionaries
                if isinstance(current_value, dict) and isinstance(value, dict):
                    current_value.update(value)
                else:
                    setattr(self.config, key, value)

    def _validate_config(self) -> None:
        """Validate the configuration.

        Raises:
            ValidationError: If the configuration is invalid.
        """
        if self.config is None:
            return

        # Validate configuration against the model
        try:
            # Create a new instance of the model with the current configuration values
            validated_config = self.config_model(**self.config.model_dump())

            # Update the configuration with the validated values
            self.config = validated_config
        except ValidationError as e:
            logger.error(f"Invalid configuration: {e}")
            raise
