"""User operations for Immuta automation."""

from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field

from immuta_toolkit.models import UserModel
from immuta_toolkit.operations.base import Operation
from immuta_toolkit.operations.registry import register_operation
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListUsersParams(BaseModel):
    """Parameters for listing users."""

    limit: int = Field(1000, description="Maximum number of users to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")


class GetUserParams(BaseModel):
    """Parameters for getting a user."""

    user_id: Union[int, str] = Field(..., description="User ID or email")


class CreateUserParams(BaseModel):
    """Parameters for creating a user."""

    user: UserModel = Field(..., description="User model")


class UpdateUserParams(BaseModel):
    """Parameters for updating a user."""

    user_id: Union[int, str] = Field(..., description="User ID or email")
    user: UserModel = Field(..., description="User model")


class DeleteUserParams(BaseModel):
    """Parameters for deleting a user."""

    user_id: Union[int, str] = Field(..., description="User ID or email")


@register_operation
class ListUsersOperation(Operation[ListUsersParams, List[Dict[str, Any]]]):
    """List users in Immuta."""

    def execute_api(self, params: ListUsersParams) -> List[Dict[str, Any]]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of user dictionaries.
        """
        return self.api_client.user_service.list_users(
            limit=params.limit,
            offset=params.offset,
            search=params.search,
        )
    
    def execute_web(self, params: ListUsersParams) -> List[Dict[str, Any]]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of user dictionaries.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # Web client doesn't support pagination or search yet
        # This is a simplified implementation
        users = self.web_client.users_page.list_users()
        
        # Apply search filter if provided
        if params.search:
            search_term = params.search.lower()
            users = [
                user for user in users
                if search_term in user.get("email", "").lower()
                or search_term in user.get("name", "").lower()
            ]
        
        # Apply pagination
        start = params.offset
        end = start + params.limit
        return users[start:end]


@register_operation
class GetUserOperation(Operation[GetUserParams, Dict[str, Any]]):
    """Get a user by ID or email."""

    def execute_api(self, params: GetUserParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            User dictionary.
        """
        return self.api_client.user_service.get_user(params.user_id)
    
    def execute_web(self, params: GetUserParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            User dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # If user_id is an email, use it directly
        if isinstance(params.user_id, str) and "@" in params.user_id:
            user = self.web_client.users_page.search_user(params.user_id)
            if not user:
                raise ValueError(f"User not found: {params.user_id}")
            return user
        
        # If user_id is an ID, we need to list all users and find by ID
        users = self.web_client.users_page.list_users()
        for user in users:
            if user.get("id") == params.user_id:
                return user
        
        raise ValueError(f"User not found: {params.user_id}")


@register_operation
class CreateUserOperation(Operation[CreateUserParams, Dict[str, Any]]):
    """Create a new user."""

    def execute_api(self, params: CreateUserParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created user dictionary.
        """
        return self.api_client.user_service.create_user(params.user)
    
    def execute_web(self, params: CreateUserParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created user dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.users_page.create_user(params.user)


@register_operation
class UpdateUserOperation(Operation[UpdateUserParams, Dict[str, Any]]):
    """Update a user."""

    def execute_api(self, params: UpdateUserParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated user dictionary.
        """
        return self.api_client.user_service.update_user(params.user_id, params.user)
    
    def execute_web(self, params: UpdateUserParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated user dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.users_page.update_user(params.user_id, params.user)


@register_operation
class DeleteUserOperation(Operation[DeleteUserParams, Dict[str, Any]]):
    """Delete a user."""

    def execute_api(self, params: DeleteUserParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted user dictionary.
        """
        return self.api_client.user_service.delete_user(params.user_id)
    
    def execute_web(self, params: DeleteUserParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted user dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.users_page.delete_user(params.user_id)
