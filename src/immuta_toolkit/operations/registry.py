"""Operation registry for Immuta automation."""

from typing import Dict, List, Optional, Type, TypeVar, Generic

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.operations.base import Operation, OperationResult
from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T", bound=Operation)


class OperationRegistry:
    """Registry for operations.
    
    This class provides a registry for operations, allowing operations to be
    registered and retrieved by name.
    
    Attributes:
        operations: Dictionary of registered operations.
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
    """

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation registry.
        
        Args:
            api_client: Immuta API client.
            web_client: Immuta web automation client.
            use_web_fallback: Whether to use web automation as a fallback.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
        """
        self.operations: Dict[str, Type[Operation]] = {}
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
    
    def register(self, operation_class: Type[T]) -> Type[T]:
        """Register an operation.
        
        Args:
            operation_class: Operation class to register.
            
        Returns:
            The registered operation class.
        """
        operation_name = operation_class.__name__
        logger.debug(f"Registering operation: {operation_name}")
        self.operations[operation_name] = operation_class
        return operation_class
    
    def get_operation(self, operation_name: str) -> Optional[Type[Operation]]:
        """Get an operation by name.
        
        Args:
            operation_name: Name of the operation to get.
            
        Returns:
            Operation class if found, None otherwise.
        """
        return self.operations.get(operation_name)
    
    def create_operation(self, operation_name: str) -> Optional[Operation]:
        """Create an instance of an operation by name.
        
        Args:
            operation_name: Name of the operation to create.
            
        Returns:
            Operation instance if found, None otherwise.
        """
        operation_class = self.get_operation(operation_name)
        if operation_class:
            return operation_class(
                api_client=self.api_client,
                web_client=self.web_client,
                use_web_fallback=self.use_web_fallback,
                max_retries=self.max_retries,
                retry_delay=self.retry_delay,
            )
        return None
    
    def list_operations(self) -> List[str]:
        """List all registered operations.
        
        Returns:
            List of operation names.
        """
        return list(self.operations.keys())
    
    def execute_operation(self, operation_name: str, params: dict) -> Optional[OperationResult]:
        """Execute an operation by name.
        
        Args:
            operation_name: Name of the operation to execute.
            params: Operation parameters.
            
        Returns:
            Operation result if operation is found, None otherwise.
        """
        operation = self.create_operation(operation_name)
        if operation:
            return operation.execute(params)
        logger.error(f"Operation not found: {operation_name}")
        return None


# Global operation registry
_registry: Optional[OperationRegistry] = None


def get_registry(
    api_client: Optional[ImmutaClient] = None,
    web_client: Optional[ImmutaWebClient] = None,
    use_web_fallback: bool = True,
    max_retries: int = 3,
    retry_delay: float = 1.0,
) -> OperationRegistry:
    """Get or create the global operation registry.
    
    Args:
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
        
    Returns:
        Global operation registry.
    """
    global _registry
    if _registry is None:
        if api_client is None:
            raise ValueError("API client is required to initialize the registry")
        _registry = OperationRegistry(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
    return _registry


def register_operation(operation_class: Type[T]) -> Type[T]:
    """Register an operation with the global registry.
    
    This function can be used as a decorator to register operations.
    
    Args:
        operation_class: Operation class to register.
        
    Returns:
        The registered operation class.
    """
    if _registry is not None:
        return _registry.register(operation_class)
    # If registry is not initialized, just return the class
    return operation_class
