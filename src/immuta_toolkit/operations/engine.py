"""Enhanced operation engine for Immuta automation."""

import time
import uuid
import asyncio
from typing import Dict, List, Optional, Union, Any, Type, Callable
from enum import Enum
from dataclasses import dataclass, field

from pydantic import BaseModel

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.operations.base import Operation, OperationResult, OperationStatus
from immuta_toolkit.operations.registry import get_registry, register_operation
from immuta_toolkit.reporting.report import Report, ReportType, ReportEntry
from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.validation.base import Validator

logger = get_logger(__name__)


class ExecutionMode(Enum):
    """Execution mode for operations."""

    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    PIPELINE = "pipeline"


@dataclass
class OperationPlan:
    """Plan for executing operations."""

    operations: List[Dict[str, Any]]
    mode: ExecutionMode = ExecutionMode.SEQUENTIAL
    max_concurrent: int = 5
    fail_fast: bool = False
    retry_failed: bool = False
    validation_enabled: bool = True
    reporting_enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExecutionContext:
    """Context for operation execution."""

    plan: OperationPlan
    report: Optional[Report] = None
    start_time: float = field(default_factory=time.time)
    results: List[OperationResult] = field(default_factory=list)
    failed_operations: List[Dict[str, Any]] = field(default_factory=list)
    completed_operations: int = 0
    total_operations: int = 0


class OperationEngine:
    """Enhanced operation engine for Immuta automation.

    This class provides a high-level interface for executing operations
    with support for different execution modes, validation, and comprehensive
    reporting.

    Attributes:
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
        validators: Dictionary of validators for different operation types.
        progress_callback: Optional callback for progress updates.
    """

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
    ):
        """Initialize the enhanced operation engine.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web automation client.
            use_web_fallback: Whether to use web automation as a fallback.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
            progress_callback: Optional callback for progress updates.
        """
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.progress_callback = progress_callback

        # Initialize validators registry
        self.validators: Dict[str, Validator] = {}

        # Initialize registry
        self.registry = get_registry(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    def execute_operation(
        self, operation_name: str, params: Dict[str, Any]
    ) -> OperationResult:
        """Execute an operation by name.

        Args:
            operation_name: Name of the operation to execute.
            params: Operation parameters.

        Returns:
            Operation result.

        Raises:
            ValueError: If the operation is not found.
        """
        operation = self.registry.create_operation(operation_name)
        if not operation:
            raise ValueError(f"Operation not found: {operation_name}")

        logger.info(f"Executing operation: {operation_name}")
        return operation.execute(params)

    def execute_batch(
        self, operations: List[Dict[str, Any]], report_title: Optional[str] = None
    ) -> Report:
        """Execute a batch of operations.

        Args:
            operations: List of operations to execute. Each operation is a dictionary
                with "operation" and "params" keys.
            report_title: Title for the batch report.

        Returns:
            Batch report.
        """
        # Create report
        report_id = str(uuid.uuid4())
        report = Report(
            id=report_id,
            type=ReportType.BATCH,
            title=report_title or f"Batch Operation {report_id}",
            description=f"Batch of {len(operations)} operations",
        )

        # Execute operations
        for i, op in enumerate(operations):
            operation_name = op.get("operation")
            params = op.get("params", {})

            if not operation_name:
                logger.warning(f"Skipping operation {i}: Missing operation name")
                continue

            try:
                result = self.execute_operation(operation_name, params)
                report.add_operation_result(operation_name, result)
            except Exception as e:
                logger.error(f"Error executing operation {operation_name}: {e}")
                # Create a failure result
                result = OperationResult(
                    status=OperationStatus.FAILURE,
                    error=str(e),
                    execution_time=0.0,
                    api_used=False,
                    web_used=False,
                    attempts=0,
                    timestamp=time.time(),
                )
                report.add_operation_result(operation_name, result)

        return report

    def get_available_operations(self) -> List[str]:
        """Get a list of available operations.

        Returns:
            List of operation names.
        """
        return self.registry.list_operations()

    def get_operation_class(self, operation_name: str) -> Optional[Type[Operation]]:
        """Get an operation class by name.

        Args:
            operation_name: Name of the operation to get.

        Returns:
            Operation class if found, None otherwise.
        """
        return self.registry.get_operation(operation_name)

    def create_operation_instance(self, operation_name: str) -> Optional[Operation]:
        """Create an instance of an operation by name.

        Args:
            operation_name: Name of the operation to create.

        Returns:
            Operation instance if found, None otherwise.
        """
        return self.registry.create_operation(operation_name)

    def register_validator(self, operation_name: str, validator: Validator) -> None:
        """Register a validator for an operation.

        Args:
            operation_name: Name of the operation.
            validator: Validator instance.
        """
        self.validators[operation_name] = validator
        logger.info(f"Registered validator for operation: {operation_name}")

    def execute_plan(self, plan: OperationPlan) -> ExecutionContext:
        """Execute an operation plan.

        Args:
            plan: Operation plan to execute.

        Returns:
            Execution context with results.
        """
        context = ExecutionContext(plan=plan)
        context.total_operations = len(plan.operations)

        # Create report if reporting is enabled
        if plan.reporting_enabled:
            report_id = str(uuid.uuid4())
            context.report = Report(
                id=report_id,
                type=ReportType.BATCH,
                title=plan.metadata.get("title", f"Operation Plan {report_id}"),
                description=plan.metadata.get("description", f"Execution of {context.total_operations} operations"),
                metadata=plan.metadata,
            )

        # Execute based on mode
        if plan.mode == ExecutionMode.SEQUENTIAL:
            self._execute_sequential(context)
        elif plan.mode == ExecutionMode.PARALLEL:
            self._execute_parallel(context)
        elif plan.mode == ExecutionMode.PIPELINE:
            self._execute_pipeline(context)

        # Retry failed operations if requested
        if plan.retry_failed and context.failed_operations:
            logger.info(f"Retrying {len(context.failed_operations)} failed operations")
            self._retry_failed_operations(context)

        return context

    def _execute_sequential(self, context: ExecutionContext) -> None:
        """Execute operations sequentially.

        Args:
            context: Execution context.
        """
        for i, operation_spec in enumerate(context.plan.operations):
            try:
                result = self._execute_single_operation(operation_spec, context)
                context.results.append(result)
                context.completed_operations += 1

                if result.status == OperationStatus.FAILURE and context.plan.fail_fast:
                    logger.warning("Stopping execution due to failure (fail_fast=True)")
                    break

            except Exception as e:
                logger.error(f"Error executing operation {i}: {e}")
                context.failed_operations.append(operation_spec)

                if context.plan.fail_fast:
                    break

            # Update progress
            if self.progress_callback:
                self.progress_callback(
                    context.completed_operations,
                    context.total_operations,
                    f"Completed operation {i + 1}/{context.total_operations}"
                )

    async def _execute_parallel(self, context: ExecutionContext) -> None:
        """Execute operations in parallel.

        Args:
            context: Execution context.
        """
        semaphore = asyncio.Semaphore(context.plan.max_concurrent)

        async def execute_with_semaphore(operation_spec: Dict[str, Any]) -> OperationResult:
            async with semaphore:
                return self._execute_single_operation(operation_spec, context)

        # Create tasks for all operations
        tasks = [
            execute_with_semaphore(op_spec)
            for op_spec in context.plan.operations
        ]

        # Execute with progress tracking
        for i, task in enumerate(asyncio.as_completed(tasks)):
            try:
                result = await task
                context.results.append(result)
                context.completed_operations += 1

                if result.status == OperationStatus.FAILURE:
                    # Find the corresponding operation spec
                    # This is a simplified approach - in practice, you'd want better tracking
                    context.failed_operations.append(context.plan.operations[i])

                    if context.plan.fail_fast:
                        # Cancel remaining tasks
                        for remaining_task in tasks[i+1:]:
                            remaining_task.cancel()
                        break

            except Exception as e:
                logger.error(f"Error in parallel execution: {e}")
                context.failed_operations.append(context.plan.operations[i])

            # Update progress
            if self.progress_callback:
                self.progress_callback(
                    context.completed_operations,
                    context.total_operations,
                    f"Completed {context.completed_operations}/{context.total_operations} operations"
                )

    def _execute_pipeline(self, context: ExecutionContext) -> None:
        """Execute operations as a pipeline (output of one feeds into next).

        Args:
            context: Execution context.
        """
        pipeline_data = {}

        for i, operation_spec in enumerate(context.plan.operations):
            try:
                # Inject pipeline data into operation parameters
                params = operation_spec.get("params", {}).copy()
                params.update(pipeline_data)

                # Create modified operation spec
                modified_spec = operation_spec.copy()
                modified_spec["params"] = params

                result = self._execute_single_operation(modified_spec, context)
                context.results.append(result)
                context.completed_operations += 1

                # Extract data for next operation
                if result.status == OperationStatus.SUCCESS and result.data:
                    pipeline_data.update(result.data)

                if result.status == OperationStatus.FAILURE and context.plan.fail_fast:
                    logger.warning("Stopping pipeline due to failure (fail_fast=True)")
                    break

            except Exception as e:
                logger.error(f"Error in pipeline operation {i}: {e}")
                context.failed_operations.append(operation_spec)

                if context.plan.fail_fast:
                    break

            # Update progress
            if self.progress_callback:
                self.progress_callback(
                    context.completed_operations,
                    context.total_operations,
                    f"Pipeline step {i + 1}/{context.total_operations}"
                )

    def _execute_single_operation(
        self, operation_spec: Dict[str, Any], context: ExecutionContext
    ) -> OperationResult:
        """Execute a single operation.

        Args:
            operation_spec: Operation specification.
            context: Execution context.

        Returns:
            Operation result.
        """
        operation_name = operation_spec.get("operation")
        params = operation_spec.get("params", {})

        if not operation_name:
            raise ValueError("Operation specification missing 'operation' field")

        # Validate parameters if validator is available
        if context.plan.validation_enabled and operation_name in self.validators:
            validator = self.validators[operation_name]
            is_valid, errors = validator.validate_with_errors(params)
            if not is_valid:
                error_msg = f"Validation failed: {'; '.join(errors)}"
                return OperationResult(
                    status=OperationStatus.FAILURE,
                    error=error_msg,
                    execution_time=0.0,
                    api_used=False,
                    web_used=False,
                    attempts=0,
                    timestamp=time.time(),
                )

        # Execute the operation
        result = self.execute_operation(operation_name, params)

        # Add to report if reporting is enabled
        if context.report:
            entry = ReportEntry(
                operation_name=operation_name,
                status=result.status.value,
                execution_time=result.execution_time,
                api_used=result.api_used,
                web_used=result.web_used,
                attempts=result.attempts,
                error=result.error,
                metadata=operation_spec.get("metadata", {}),
            )
            context.report.add_entry(entry)

        return result

    def _retry_failed_operations(self, context: ExecutionContext) -> None:
        """Retry failed operations.

        Args:
            context: Execution context.
        """
        retry_operations = context.failed_operations.copy()
        context.failed_operations.clear()

        for operation_spec in retry_operations:
            try:
                logger.info(f"Retrying operation: {operation_spec.get('operation')}")
                result = self._execute_single_operation(operation_spec, context)
                context.results.append(result)

                if result.status == OperationStatus.FAILURE:
                    context.failed_operations.append(operation_spec)

            except Exception as e:
                logger.error(f"Error retrying operation: {e}")
                context.failed_operations.append(operation_spec)
