"""Operation engine for Immuta automation."""

import time
import uuid
from typing import Dict, List, Optional, Union, Any, Type

from pydantic import BaseModel

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.operations.base import Operation, OperationResult, OperationStatus
from immuta_toolkit.operations.registry import get_registry, register_operation
from immuta_toolkit.reporting.report import Report, ReportType, ReportEntry
from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class OperationEngine:
    """Operation engine for Immuta automation.
    
    This class provides a high-level interface for executing operations
    and managing operation results.
    
    Attributes:
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
    """

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation engine.
        
        Args:
            api_client: Immuta API client.
            web_client: Immuta web automation client.
            use_web_fallback: Whether to use web automation as a fallback.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
        """
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Initialize registry
        self.registry = get_registry(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
    
    def execute_operation(
        self, operation_name: str, params: Dict[str, Any]
    ) -> OperationResult:
        """Execute an operation by name.
        
        Args:
            operation_name: Name of the operation to execute.
            params: Operation parameters.
            
        Returns:
            Operation result.
            
        Raises:
            ValueError: If the operation is not found.
        """
        operation = self.registry.create_operation(operation_name)
        if not operation:
            raise ValueError(f"Operation not found: {operation_name}")
        
        logger.info(f"Executing operation: {operation_name}")
        return operation.execute(params)
    
    def execute_batch(
        self, operations: List[Dict[str, Any]], report_title: Optional[str] = None
    ) -> Report:
        """Execute a batch of operations.
        
        Args:
            operations: List of operations to execute. Each operation is a dictionary
                with "operation" and "params" keys.
            report_title: Title for the batch report.
            
        Returns:
            Batch report.
        """
        # Create report
        report_id = str(uuid.uuid4())
        report = Report(
            id=report_id,
            type=ReportType.BATCH,
            title=report_title or f"Batch Operation {report_id}",
            description=f"Batch of {len(operations)} operations",
        )
        
        # Execute operations
        for i, op in enumerate(operations):
            operation_name = op.get("operation")
            params = op.get("params", {})
            
            if not operation_name:
                logger.warning(f"Skipping operation {i}: Missing operation name")
                continue
            
            try:
                result = self.execute_operation(operation_name, params)
                report.add_operation_result(operation_name, result)
            except Exception as e:
                logger.error(f"Error executing operation {operation_name}: {e}")
                # Create a failure result
                result = OperationResult(
                    status=OperationStatus.FAILURE,
                    error=str(e),
                    execution_time=0.0,
                    api_used=False,
                    web_used=False,
                    attempts=0,
                    timestamp=time.time(),
                )
                report.add_operation_result(operation_name, result)
        
        return report
    
    def get_available_operations(self) -> List[str]:
        """Get a list of available operations.
        
        Returns:
            List of operation names.
        """
        return self.registry.list_operations()
    
    def get_operation_class(self, operation_name: str) -> Optional[Type[Operation]]:
        """Get an operation class by name.
        
        Args:
            operation_name: Name of the operation to get.
            
        Returns:
            Operation class if found, None otherwise.
        """
        return self.registry.get_operation(operation_name)
    
    def create_operation_instance(self, operation_name: str) -> Optional[Operation]:
        """Create an instance of an operation by name.
        
        Args:
            operation_name: Name of the operation to create.
            
        Returns:
            Operation instance if found, None otherwise.
        """
        return self.registry.create_operation(operation_name)
