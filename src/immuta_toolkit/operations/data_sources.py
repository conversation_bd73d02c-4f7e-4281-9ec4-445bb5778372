"""Data source operations for Immuta automation."""

from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field

from immuta_toolkit.models import DataSourceModel
from immuta_toolkit.operations.base import Operation
from immuta_toolkit.operations.registry import register_operation
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListDataSourcesParams(BaseModel):
    """Parameters for listing data sources."""

    limit: int = Field(1000, description="Maximum number of data sources to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")


class GetDataSourceParams(BaseModel):
    """Parameters for getting a data source."""

    data_source_id: Union[int, str] = Field(..., description="Data source ID or name")


class CreateDataSourceParams(BaseModel):
    """Parameters for creating a data source."""

    data_source: DataSourceModel = Field(..., description="Data source model")


class UpdateDataSourceParams(BaseModel):
    """Parameters for updating a data source."""

    data_source_id: Union[int, str] = Field(..., description="Data source ID or name")
    data_source: DataSourceModel = Field(..., description="Data source model")


class DeleteDataSourceParams(BaseModel):
    """Parameters for deleting a data source."""

    data_source_id: Union[int, str] = Field(..., description="Data source ID or name")


class AddTagParams(BaseModel):
    """Parameters for adding a tag to a data source."""

    data_source_id: Union[int, str] = Field(..., description="Data source ID or name")
    tag_name: str = Field(..., description="Tag name")
    tag_description: Optional[str] = Field(None, description="Tag description")


class RemoveTagParams(BaseModel):
    """Parameters for removing a tag from a data source."""

    data_source_id: Union[int, str] = Field(..., description="Data source ID or name")
    tag_name: str = Field(..., description="Tag name")


@register_operation
class ListDataSourcesOperation(Operation[ListDataSourcesParams, List[Dict[str, Any]]]):
    """List data sources in Immuta."""

    def execute_api(self, params: ListDataSourcesParams) -> List[Dict[str, Any]]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of data source dictionaries.
        """
        return self.api_client.data_source_service.list_data_sources(
            limit=params.limit,
            offset=params.offset,
            search=params.search,
        )
    
    def execute_web(self, params: ListDataSourcesParams) -> List[Dict[str, Any]]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of data source dictionaries.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # Web client doesn't support pagination or search yet
        # This is a simplified implementation
        data_sources = self.web_client.data_sources_page.list_data_sources()
        
        # Apply search filter if provided
        if params.search:
            search_term = params.search.lower()
            data_sources = [
                ds for ds in data_sources
                if search_term in ds.get("name", "").lower()
                or search_term in ds.get("description", "").lower()
            ]
        
        # Apply pagination
        start = params.offset
        end = start + params.limit
        return data_sources[start:end]


@register_operation
class GetDataSourceOperation(Operation[GetDataSourceParams, Dict[str, Any]]):
    """Get a data source by ID or name."""

    def execute_api(self, params: GetDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Data source dictionary.
        """
        return self.api_client.data_source_service.get_data_source(params.data_source_id)
    
    def execute_web(self, params: GetDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Data source dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # If data_source_id is a name, use it directly
        if isinstance(params.data_source_id, str) and not params.data_source_id.isdigit():
            data_source = self.web_client.data_sources_page.search_data_source(params.data_source_id)
            if not data_source:
                raise ValueError(f"Data source not found: {params.data_source_id}")
            return data_source
        
        # If data_source_id is an ID, we need to list all data sources and find by ID
        data_sources = self.web_client.data_sources_page.list_data_sources()
        for data_source in data_sources:
            if data_source.get("id") == params.data_source_id:
                return data_source
        
        raise ValueError(f"Data source not found: {params.data_source_id}")


@register_operation
class CreateDataSourceOperation(Operation[CreateDataSourceParams, Dict[str, Any]]):
    """Create a new data source."""

    def execute_api(self, params: CreateDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created data source dictionary.
        """
        return self.api_client.data_source_service.create_data_source(params.data_source)
    
    def execute_web(self, params: CreateDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created data source dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.data_sources_page.create_data_source(params.data_source)


@register_operation
class UpdateDataSourceOperation(Operation[UpdateDataSourceParams, Dict[str, Any]]):
    """Update a data source."""

    def execute_api(self, params: UpdateDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated data source dictionary.
        """
        return self.api_client.data_source_service.update_data_source(
            params.data_source_id, params.data_source
        )
    
    def execute_web(self, params: UpdateDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated data source dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.data_sources_page.update_data_source(
            params.data_source_id, params.data_source
        )


@register_operation
class DeleteDataSourceOperation(Operation[DeleteDataSourceParams, Dict[str, Any]]):
    """Delete a data source."""

    def execute_api(self, params: DeleteDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted data source dictionary.
        """
        return self.api_client.data_source_service.delete_data_source(params.data_source_id)
    
    def execute_web(self, params: DeleteDataSourceParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted data source dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.data_sources_page.delete_data_source(params.data_source_id)


@register_operation
class AddTagOperation(Operation[AddTagParams, Dict[str, Any]]):
    """Add a tag to a data source."""

    def execute_api(self, params: AddTagParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Tag dictionary.
        """
        return self.api_client.tag_service.add_tag(
            model_type="datasource",
            model_id=params.data_source_id,
            tag_name=params.tag_name,
            tag_description=params.tag_description,
        )
    
    def execute_web(self, params: AddTagParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Tag dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.data_sources_page.add_tag(
            params.data_source_id,
            params.tag_name,
            params.tag_description,
        )


@register_operation
class RemoveTagOperation(Operation[RemoveTagParams, Dict[str, Any]]):
    """Remove a tag from a data source."""

    def execute_api(self, params: RemoveTagParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Result dictionary.
        """
        return self.api_client.tag_service.remove_tag(
            model_type="datasource",
            model_id=params.data_source_id,
            tag_name=params.tag_name,
        )
    
    def execute_web(self, params: RemoveTagParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Result dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.data_sources_page.remove_tag(
            params.data_source_id,
            params.tag_name,
        )
