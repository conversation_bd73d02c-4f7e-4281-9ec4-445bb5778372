"""Operations module for Immuta automation."""

from immuta_toolkit.operations.base import Operation, OperationResult, OperationStatus
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.operations.registry import get_registry, register_operation
from immuta_toolkit.operations.users import (
    ListUsersOperation,
    GetUserOperation,
    CreateUserOperation,
    UpdateUserOperation,
    DeleteUserOperation,
)
from immuta_toolkit.operations.data_sources import (
    ListDataSourcesOperation,
    GetDataSourceOperation,
    CreateDataSourceOperation,
    UpdateDataSourceOperation,
    DeleteDataSourceOperation,
    AddTagOperation,
    RemoveTagOperation,
)
from immuta_toolkit.operations.policies import (
    ListPoliciesOperation,
    GetPolicyOperation,
    CreatePolicyOperation,
    UpdatePolicyOperation,
    DeletePolicyOperation,
)
from immuta_toolkit.operations.projects import (
    ListProjectsOperation,
    GetProjectOperation,
    CreateProjectOperation,
    UpdateProjectOperation,
    DeleteProjectOperation,
)

__all__ = [
    # Base classes
    "Operation",
    "OperationResult",
    "OperationStatus",
    # Engine and registry
    "OperationEngine",
    "get_registry",
    "register_operation",
    # User operations
    "ListUsersOperation",
    "GetUserOperation",
    "CreateUserOperation",
    "UpdateUserOperation",
    "DeleteUserOperation",
    # Data source operations
    "ListDataSourcesOperation",
    "GetDataSourceOperation",
    "CreateDataSourceOperation",
    "UpdateDataSourceOperation",
    "DeleteDataSourceOperation",
    "AddTagOperation",
    "RemoveTagOperation",
    # Policy operations
    "ListPoliciesOperation",
    "GetPolicyOperation",
    "CreatePolicyOperation",
    "UpdatePolicyOperation",
    "DeletePolicyOperation",
    # Project operations
    "ListProjectsOperation",
    "GetProjectOperation",
    "CreateProjectOperation",
    "UpdateProjectOperation",
    "DeleteProjectOperation",
]
