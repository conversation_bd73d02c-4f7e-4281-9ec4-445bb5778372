"""Policy operations for Immuta automation."""

from typing import Dict, List, Optional, Union, Any

from pydantic import BaseModel, Field

from immuta_toolkit.models import PolicyModel
from immuta_toolkit.operations.base import Operation
from immuta_toolkit.operations.registry import register_operation
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListPoliciesParams(BaseModel):
    """Parameters for listing policies."""

    limit: int = Field(1000, description="Maximum number of policies to return")
    offset: int = Field(0, description="Offset for pagination")
    search: Optional[str] = Field(None, description="Search term")


class GetPolicyParams(BaseModel):
    """Parameters for getting a policy."""

    policy_id: Union[int, str] = Field(..., description="Policy ID or name")


class CreatePolicyParams(BaseModel):
    """Parameters for creating a policy."""

    policy: PolicyModel = Field(..., description="Policy model")


class UpdatePolicyParams(BaseModel):
    """Parameters for updating a policy."""

    policy_id: Union[int, str] = Field(..., description="Policy ID or name")
    policy: PolicyModel = Field(..., description="Policy model")


class DeletePolicyParams(BaseModel):
    """Parameters for deleting a policy."""

    policy_id: Union[int, str] = Field(..., description="Policy ID or name")


@register_operation
class ListPoliciesOperation(Operation[ListPoliciesParams, List[Dict[str, Any]]]):
    """List policies in Immuta."""

    def execute_api(self, params: ListPoliciesParams) -> List[Dict[str, Any]]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of policy dictionaries.
        """
        return self.api_client.policy_service.list_policies(
            limit=params.limit,
            offset=params.offset,
            search=params.search,
        )
    
    def execute_web(self, params: ListPoliciesParams) -> List[Dict[str, Any]]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            List of policy dictionaries.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # Web client doesn't support pagination or search yet
        # This is a simplified implementation
        policies = self.web_client.policies_page.list_policies()
        
        # Apply search filter if provided
        if params.search:
            search_term = params.search.lower()
            policies = [
                policy for policy in policies
                if search_term in policy.get("name", "").lower()
                or search_term in policy.get("description", "").lower()
            ]
        
        # Apply pagination
        start = params.offset
        end = start + params.limit
        return policies[start:end]


@register_operation
class GetPolicyOperation(Operation[GetPolicyParams, Dict[str, Any]]):
    """Get a policy by ID or name."""

    def execute_api(self, params: GetPolicyParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Policy dictionary.
        """
        return self.api_client.policy_service.get_policy(params.policy_id)
    
    def execute_web(self, params: GetPolicyParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Policy dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        # If policy_id is a name, use it directly
        if isinstance(params.policy_id, str) and not params.policy_id.isdigit():
            policy = self.web_client.policies_page.search_policy(params.policy_id)
            if not policy:
                raise ValueError(f"Policy not found: {params.policy_id}")
            return policy
        
        # If policy_id is an ID, we need to list all policies and find by ID
        policies = self.web_client.policies_page.list_policies()
        for policy in policies:
            if policy.get("id") == params.policy_id:
                return policy
        
        raise ValueError(f"Policy not found: {params.policy_id}")


@register_operation
class CreatePolicyOperation(Operation[CreatePolicyParams, Dict[str, Any]]):
    """Create a new policy."""

    def execute_api(self, params: CreatePolicyParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created policy dictionary.
        """
        return self.api_client.policy_service.create_policy(params.policy)
    
    def execute_web(self, params: CreatePolicyParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Created policy dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.policies_page.create_policy(params.policy)


@register_operation
class UpdatePolicyOperation(Operation[UpdatePolicyParams, Dict[str, Any]]):
    """Update a policy."""

    def execute_api(self, params: UpdatePolicyParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated policy dictionary.
        """
        return self.api_client.policy_service.update_policy(params.policy_id, params.policy)
    
    def execute_web(self, params: UpdatePolicyParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Updated policy dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.policies_page.update_policy(params.policy_id, params.policy)


@register_operation
class DeletePolicyOperation(Operation[DeletePolicyParams, Dict[str, Any]]):
    """Delete a policy."""

    def execute_api(self, params: DeletePolicyParams) -> Dict[str, Any]:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted policy dictionary.
        """
        return self.api_client.policy_service.delete_policy(params.policy_id)
    
    def execute_web(self, params: DeletePolicyParams) -> Dict[str, Any]:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Deleted policy dictionary.
        """
        if not self.web_client:
            raise ValueError("Web client is not available")
        
        return self.web_client.policies_page.delete_policy(params.policy_id)
