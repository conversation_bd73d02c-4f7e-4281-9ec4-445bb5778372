"""Base operation class for Immuta automation."""

import time
import traceback
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, Generic, Union

from pydantic import BaseModel, Field

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.web.client import <PERSON>mmutaWebClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")  # Operation result type
P = TypeVar("P", bound=BaseModel)  # Operation parameters type


class OperationStatus(str, Enum):
    """Operation status."""

    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL_SUCCESS = "partial_success"
    SKIPPED = "skipped"
    IN_PROGRESS = "in_progress"


class OperationResult(BaseModel, Generic[T]):
    """Operation result."""

    status: OperationStatus = Field(..., description="Operation status")
    data: Optional[T] = Field(None, description="Operation result data")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    execution_time: float = Field(..., description="Operation execution time in seconds")
    api_used: bool = Field(..., description="Whether the API was used for the operation")
    web_used: bool = Field(..., description="Whether web automation was used")
    attempts: int = Field(1, description="Number of attempts made")
    timestamp: float = Field(..., description="Operation timestamp")


class Operation(ABC, Generic[P, T]):
    """Base class for all operations.
    
    This class provides a common interface for all operations, including
    execution, validation, and fallback mechanisms.
    
    Attributes:
        name: Operation name.
        description: Operation description.
        api_client: Immuta API client.
        web_client: Immuta web automation client.
        use_web_fallback: Whether to use web automation as a fallback.
        max_retries: Maximum number of retries for API operations.
        retry_delay: Delay between retries in seconds.
    """

    def __init__(
        self,
        api_client: ImmutaClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.
        
        Args:
            api_client: Immuta API client.
            web_client: Immuta web automation client.
            use_web_fallback: Whether to use web automation as a fallback.
            max_retries: Maximum number of retries for API operations.
            retry_delay: Delay between retries in seconds.
        """
        self.api_client = api_client
        self.web_client = web_client
        self.use_web_fallback = use_web_fallback and web_client is not None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # Set operation name and description
        self.name = self.__class__.__name__
        self.description = self.__doc__ or "No description available"

    @abstractmethod
    def execute_api(self, params: P) -> T:
        """Execute the operation using the API.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Operation result.
            
        Raises:
            Exception: If the operation fails.
        """
        pass
    
    @abstractmethod
    def execute_web(self, params: P) -> T:
        """Execute the operation using web automation.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Operation result.
            
        Raises:
            Exception: If the operation fails.
        """
        pass
    
    def validate_params(self, params: P) -> Tuple[bool, Optional[str]]:
        """Validate operation parameters.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Tuple of (is_valid, error_message).
        """
        # Basic validation is handled by Pydantic
        # Subclasses can override this method to add custom validation
        return True, None
    
    def validate_result(self, result: T) -> Tuple[bool, Optional[str]]:
        """Validate operation result.
        
        Args:
            result: Operation result.
            
        Returns:
            Tuple of (is_valid, error_message).
        """
        # Subclasses should override this method to add custom validation
        return True, None
    
    def execute(self, params: P) -> OperationResult[T]:
        """Execute the operation with fallback.
        
        This method attempts to execute the operation using the API first,
        and falls back to web automation if the API operation fails and
        web fallback is enabled.
        
        Args:
            params: Operation parameters.
            
        Returns:
            Operation result.
        """
        start_time = time.time()
        api_used = False
        web_used = False
        attempts = 0
        
        # Validate parameters
        is_valid, error = self.validate_params(params)
        if not is_valid:
            return OperationResult(
                status=OperationStatus.FAILURE,
                error=f"Parameter validation failed: {error}",
                execution_time=time.time() - start_time,
                api_used=api_used,
                web_used=web_used,
                attempts=attempts,
                timestamp=start_time,
            )
        
        # Try API first
        for attempt in range(self.max_retries):
            attempts += 1
            try:
                logger.info(f"Executing operation {self.name} using API (attempt {attempt + 1})")
                result = self.execute_api(params)
                api_used = True
                
                # Validate result
                is_valid, error = self.validate_result(result)
                if not is_valid:
                    logger.warning(f"API operation result validation failed: {error}")
                    raise ValueError(f"Result validation failed: {error}")
                
                return OperationResult(
                    status=OperationStatus.SUCCESS,
                    data=result,
                    execution_time=time.time() - start_time,
                    api_used=api_used,
                    web_used=web_used,
                    attempts=attempts,
                    timestamp=start_time,
                )
            except Exception as e:
                logger.warning(f"API operation failed: {e}")
                if attempt < self.max_retries - 1:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                elif self.use_web_fallback:
                    # Fall back to web automation
                    break
                else:
                    # No fallback, return failure
                    return OperationResult(
                        status=OperationStatus.FAILURE,
                        error=f"API operation failed: {str(e)}\n{traceback.format_exc()}",
                        execution_time=time.time() - start_time,
                        api_used=api_used,
                        web_used=web_used,
                        attempts=attempts,
                        timestamp=start_time,
                    )
        
        # Try web automation if API failed and fallback is enabled
        if self.use_web_fallback:
            try:
                logger.info(f"Falling back to web automation for operation {self.name}")
                result = self.execute_web(params)
                web_used = True
                
                # Validate result
                is_valid, error = self.validate_result(result)
                if not is_valid:
                    logger.warning(f"Web operation result validation failed: {error}")
                    raise ValueError(f"Result validation failed: {error}")
                
                return OperationResult(
                    status=OperationStatus.SUCCESS,
                    data=result,
                    execution_time=time.time() - start_time,
                    api_used=api_used,
                    web_used=web_used,
                    attempts=attempts,
                    timestamp=start_time,
                )
            except Exception as e:
                logger.error(f"Web automation fallback failed: {e}")
                return OperationResult(
                    status=OperationStatus.FAILURE,
                    error=f"Both API and web automation failed: {str(e)}\n{traceback.format_exc()}",
                    execution_time=time.time() - start_time,
                    api_used=api_used,
                    web_used=web_used,
                    attempts=attempts,
                    timestamp=start_time,
                )
        
        # This should never happen, but just in case
        return OperationResult(
            status=OperationStatus.FAILURE,
            error="Operation failed with no specific error",
            execution_time=time.time() - start_time,
            api_used=api_used,
            web_used=web_used,
            attempts=attempts,
            timestamp=start_time,
        )
