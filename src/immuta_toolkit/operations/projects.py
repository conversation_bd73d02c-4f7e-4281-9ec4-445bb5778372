"""Project operations for Immuta SRE Toolkit."""

from typing import Dict, List, Optional, Union, Any, TypeVar, Generic, cast

from immuta_toolkit.api.client import ImmutaApiClient
from immuta_toolkit.models.project import ProjectModel, ProjectQueryParameters
from immuta_toolkit.operations.base import Operation, OperationResult
from immuta_toolkit.validation.project_validator import ProjectValidator
from immuta_toolkit.web.client import ImmutaWebClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

# Type variables for generic operations
P = TypeVar("P")
T = TypeVar("T")


class ListProjectsOperation(Operation[ProjectQueryParameters, List[ProjectModel]]):
    """Operation to list projects."""

    def __init__(
        self,
        api_client: ImmutaApiClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web client.
            use_web_fallback: Whether to use web automation as fallback.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.
        """
        super().__init__(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
        self.validator = ProjectValidator()

    def validate_params(self, params: ProjectQueryParameters) -> tuple[bool, Optional[str]]:
        """Validate operation parameters.

        Args:
            params: Operation parameters.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return self.validator.validate_query_parameters(params)

    def validate_result(self, result: List[ProjectModel]) -> tuple[bool, Optional[str]]:
        """Validate operation result.

        Args:
            result: Operation result.

        Returns:
            Tuple of (is_valid, error_message).
        """
        if not isinstance(result, list):
            return False, "Result is not a list"
        
        for project in result:
            is_valid, error = self.validator.validate_project(project)
            if not is_valid:
                return False, f"Invalid project: {error}"
        
        return True, None

    def execute_api(self, params: ProjectQueryParameters) -> List[ProjectModel]:
        """Execute the operation using the API.

        Args:
            params: Operation parameters.

        Returns:
            List of projects.

        Raises:
            Exception: If the API operation fails.
        """
        logger.info(f"Listing projects with parameters: {params}")
        
        # Convert parameters to dict for API client
        params_dict = params.dict(exclude_unset=True)
        
        # Call the API client
        projects = self.api_client._make_request(
            method="GET",
            endpoint="/projects",
            params=params_dict,
        )
        
        # Parse the response
        if projects.status_code != 200:
            raise Exception(f"Failed to list projects: {projects.text}")
        
        # Convert to models
        return [ProjectModel.parse_obj(project) for project in projects.json()]

    def execute_web(self, params: ProjectQueryParameters) -> List[ProjectModel]:
        """Execute the operation using web automation.

        Args:
            params: Operation parameters.

        Returns:
            List of projects.

        Raises:
            Exception: If the web operation fails.
        """
        if not self.web_client:
            raise Exception("Web client is not available")
        
        logger.info(f"Listing projects with web automation: {params}")
        
        # Use the web client to list projects
        return self.web_client.list_projects(
            limit=params.limit,
            offset=params.offset,
            search=params.search,
        )


class GetProjectOperation(Operation[str, ProjectModel]):
    """Operation to get a project by ID."""

    def __init__(
        self,
        api_client: ImmutaApiClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web client.
            use_web_fallback: Whether to use web automation as fallback.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.
        """
        super().__init__(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
        self.validator = ProjectValidator()

    def validate_params(self, params: str) -> tuple[bool, Optional[str]]:
        """Validate operation parameters.

        Args:
            params: Operation parameters.

        Returns:
            Tuple of (is_valid, error_message).
        """
        if not params:
            return False, "Project ID is required"
        
        return True, None

    def validate_result(self, result: ProjectModel) -> tuple[bool, Optional[str]]:
        """Validate operation result.

        Args:
            result: Operation result.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return self.validator.validate_project(result)

    def execute_api(self, project_id: str) -> ProjectModel:
        """Execute the operation using the API.

        Args:
            project_id: Project ID.

        Returns:
            Project.

        Raises:
            Exception: If the API operation fails.
        """
        logger.info(f"Getting project with ID: {project_id}")
        
        # Call the API client
        response = self.api_client._make_request(
            method="GET",
            endpoint=f"/projects/{project_id}",
        )
        
        # Parse the response
        if response.status_code != 200:
            raise Exception(f"Failed to get project: {response.text}")
        
        # Convert to model
        return ProjectModel.parse_obj(response.json())

    def execute_web(self, project_id: str) -> ProjectModel:
        """Execute the operation using web automation.

        Args:
            project_id: Project ID.

        Returns:
            Project.

        Raises:
            Exception: If the web operation fails.
        """
        if not self.web_client:
            raise Exception("Web client is not available")
        
        logger.info(f"Getting project with web automation: {project_id}")
        
        # Use the web client to get the project
        return self.web_client.get_project(project_id)


class CreateProjectOperation(Operation[ProjectModel, ProjectModel]):
    """Operation to create a project."""

    def __init__(
        self,
        api_client: ImmutaApiClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web client.
            use_web_fallback: Whether to use web automation as fallback.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.
        """
        super().__init__(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
        self.validator = ProjectValidator()

    def validate_params(self, params: ProjectModel) -> tuple[bool, Optional[str]]:
        """Validate operation parameters.

        Args:
            params: Operation parameters.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return self.validator.validate_project(params)

    def validate_result(self, result: ProjectModel) -> tuple[bool, Optional[str]]:
        """Validate operation result.

        Args:
            result: Operation result.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return self.validator.validate_project(result)

    def execute_api(self, project: ProjectModel) -> ProjectModel:
        """Execute the operation using the API.

        Args:
            project: Project to create.

        Returns:
            Created project.

        Raises:
            Exception: If the API operation fails.
        """
        logger.info(f"Creating project: {project.name}")
        
        # Call the API client
        response = self.api_client._make_request(
            method="POST",
            endpoint="/projects",
            json_data=project.dict(exclude_unset=True),
        )
        
        # Parse the response
        if response.status_code != 201:
            raise Exception(f"Failed to create project: {response.text}")
        
        # Convert to model
        return ProjectModel.parse_obj(response.json())

    def execute_web(self, project: ProjectModel) -> ProjectModel:
        """Execute the operation using web automation.

        Args:
            project: Project to create.

        Returns:
            Created project.

        Raises:
            Exception: If the web operation fails.
        """
        if not self.web_client:
            raise Exception("Web client is not available")
        
        logger.info(f"Creating project with web automation: {project.name}")
        
        # Use the web client to create the project
        return self.web_client.create_project(project)


class UpdateProjectOperation(Operation[Dict[str, Any], ProjectModel]):
    """Operation to update a project."""

    def __init__(
        self,
        api_client: ImmutaApiClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web client.
            use_web_fallback: Whether to use web automation as fallback.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.
        """
        super().__init__(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )
        self.validator = ProjectValidator()

    def validate_params(self, params: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """Validate operation parameters.

        Args:
            params: Operation parameters.

        Returns:
            Tuple of (is_valid, error_message).
        """
        if "project_id" not in params:
            return False, "Project ID is required"
        
        if "project" not in params:
            return False, "Project is required"
        
        return self.validator.validate_project(params["project"])

    def validate_result(self, result: ProjectModel) -> tuple[bool, Optional[str]]:
        """Validate operation result.

        Args:
            result: Operation result.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return self.validator.validate_project(result)

    def execute_api(self, params: Dict[str, Any]) -> ProjectModel:
        """Execute the operation using the API.

        Args:
            params: Operation parameters.

        Returns:
            Updated project.

        Raises:
            Exception: If the API operation fails.
        """
        project_id = params["project_id"]
        project = params["project"]
        
        logger.info(f"Updating project with ID: {project_id}")
        
        # Call the API client
        response = self.api_client._make_request(
            method="PUT",
            endpoint=f"/projects/{project_id}",
            json_data=project.dict(exclude_unset=True),
        )
        
        # Parse the response
        if response.status_code != 200:
            raise Exception(f"Failed to update project: {response.text}")
        
        # Convert to model
        return ProjectModel.parse_obj(response.json())

    def execute_web(self, params: Dict[str, Any]) -> ProjectModel:
        """Execute the operation using web automation.

        Args:
            params: Operation parameters.

        Returns:
            Updated project.

        Raises:
            Exception: If the web operation fails.
        """
        if not self.web_client:
            raise Exception("Web client is not available")
        
        project_id = params["project_id"]
        project = params["project"]
        
        logger.info(f"Updating project with web automation: {project_id}")
        
        # Use the web client to update the project
        return self.web_client.update_project(project_id, project)


class DeleteProjectOperation(Operation[str, bool]):
    """Operation to delete a project."""

    def __init__(
        self,
        api_client: ImmutaApiClient,
        web_client: Optional[ImmutaWebClient] = None,
        use_web_fallback: bool = True,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        """Initialize the operation.

        Args:
            api_client: Immuta API client.
            web_client: Immuta web client.
            use_web_fallback: Whether to use web automation as fallback.
            max_retries: Maximum number of retries.
            retry_delay: Delay between retries in seconds.
        """
        super().__init__(
            api_client=api_client,
            web_client=web_client,
            use_web_fallback=use_web_fallback,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

    def validate_params(self, params: str) -> tuple[bool, Optional[str]]:
        """Validate operation parameters.

        Args:
            params: Operation parameters.

        Returns:
            Tuple of (is_valid, error_message).
        """
        if not params:
            return False, "Project ID is required"
        
        return True, None

    def validate_result(self, result: bool) -> tuple[bool, Optional[str]]:
        """Validate operation result.

        Args:
            result: Operation result.

        Returns:
            Tuple of (is_valid, error_message).
        """
        return True, None

    def execute_api(self, project_id: str) -> bool:
        """Execute the operation using the API.

        Args:
            project_id: Project ID.

        Returns:
            True if the project was deleted, False otherwise.

        Raises:
            Exception: If the API operation fails.
        """
        logger.info(f"Deleting project with ID: {project_id}")
        
        # Call the API client
        response = self.api_client._make_request(
            method="DELETE",
            endpoint=f"/projects/{project_id}",
        )
        
        # Parse the response
        if response.status_code != 204:
            raise Exception(f"Failed to delete project: {response.text}")
        
        return True

    def execute_web(self, project_id: str) -> bool:
        """Execute the operation using web automation.

        Args:
            project_id: Project ID.

        Returns:
            True if the project was deleted, False otherwise.

        Raises:
            Exception: If the web operation fails.
        """
        if not self.web_client:
            raise Exception("Web client is not available")
        
        logger.info(f"Deleting project with web automation: {project_id}")
        
        # Use the web client to delete the project
        return self.web_client.delete_project(project_id)
