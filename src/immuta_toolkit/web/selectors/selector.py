"""Selector class for Immuta web automation.

This module provides a Selector class that represents a CSS or XPath selector
with additional metadata and helper methods.
"""

from typing import Dict, Optional, Union, Any, List
from enum import Enum


class SelectorType(Enum):
    """Enum for selector types."""

    CSS = "css"
    XPATH = "xpath"
    TEXT = "text"


class Selector:
    """Selector class for Immuta web automation.

    This class represents a CSS or XPath selector with additional metadata
    and helper methods.

    Attributes:
        name: Name of the selector.
        value: Value of the selector.
        type: Type of the selector (CSS, XPath, or Text).
        description: Description of the selector.
        metadata: Additional metadata for the selector.
    """

    def __init__(
        self,
        name: str,
        value: str,
        type: SelectorType = SelectorType.CSS,
        description: str = "",
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the selector.

        Args:
            name: Name of the selector.
            value: Value of the selector.
            type: Type of the selector (CSS, XPath, or Text).
            description: Description of the selector.
            metadata: Additional metadata for the selector.
        """
        self.name = name
        self.value = value
        self.type = type
        self.description = description
        self.metadata = metadata or {}

    def __str__(self) -> str:
        """Return the string representation of the selector.

        Returns:
            String representation of the selector.
        """
        return self.value

    def __repr__(self) -> str:
        """Return the string representation of the selector.

        Returns:
            String representation of the selector.
        """
        return f"Selector(name={self.name}, value={self.value}, type={self.type})"

    def get_playwright_selector(self) -> str:
        """Get the selector in Playwright format.

        Returns:
            Selector in Playwright format.
        """
        if self.type == SelectorType.CSS:
            return self.value
        elif self.type == SelectorType.XPATH:
            return f"xpath={self.value}"
        elif self.type == SelectorType.TEXT:
            return f"text={self.value}"
        else:
            return self.value

    def with_text(self, text: str) -> "Selector":
        """Create a new selector with text content.

        Args:
            text: Text content to match.

        Returns:
            New selector with text content.
        """
        if self.type == SelectorType.CSS:
            new_value = f"{self.value}:has-text('{text}')"
            return Selector(
                name=f"{self.name}_with_text",
                value=new_value,
                type=self.type,
                description=f"{self.description} with text '{text}'",
                metadata=self.metadata,
            )
        elif self.type == SelectorType.XPATH:
            new_value = f"{self.value}[contains(text(), '{text}')]"
            return Selector(
                name=f"{self.name}_with_text",
                value=new_value,
                type=self.type,
                description=f"{self.description} with text '{text}'",
                metadata=self.metadata,
            )
        else:
            return self

    def with_attribute(self, attribute: str, value: str) -> "Selector":
        """Create a new selector with attribute.

        Args:
            attribute: Attribute name.
            value: Attribute value.

        Returns:
            New selector with attribute.
        """
        if self.type == SelectorType.CSS:
            new_value = f"{self.value}[{attribute}='{value}']"
            return Selector(
                name=f"{self.name}_with_attr",
                value=new_value,
                type=self.type,
                description=f"{self.description} with {attribute}='{value}'",
                metadata=self.metadata,
            )
        elif self.type == SelectorType.XPATH:
            new_value = f"{self.value}[@{attribute}='{value}']"
            return Selector(
                name=f"{self.name}_with_attr",
                value=new_value,
                type=self.type,
                description=f"{self.description} with {attribute}='{value}'",
                metadata=self.metadata,
            )
        else:
            return self
