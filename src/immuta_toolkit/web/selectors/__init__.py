"""Selectors for Immuta web automation."""

from immuta_toolkit.web.selectors.registry import SelectorRegistry
from immuta_toolkit.web.selectors.elements import (
    LoginSelectors,
    UserSelectors,
    DataSourceSelectors,
    PolicySelectors,
    ProjectSelectors,
)
from immuta_toolkit.web.selectors.selector import Selector, SelectorType
from immuta_toolkit.web.selectors.selector_manager import (
    SelectorManager,
    selector_manager,
    get_selector,
    get_selector_object,
)

__all__ = [
    "SelectorRegistry",
    "LoginSelectors",
    "UserSelectors",
    "DataSourceSelectors",
    "PolicySelectors",
    "ProjectSelectors",
    "Selector",
    "SelectorType",
    "SelectorManager",
    "selector_manager",
    "get_selector",
    "get_selector_object",
]
