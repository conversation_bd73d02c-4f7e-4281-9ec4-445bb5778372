"""Selector manager for Immuta web automation.

This module provides a unified interface for accessing selectors from different sources.
"""

import os
from typing import Dict, Optional, Any, List, Union

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.selectors.selector import Selector, SelectorType
from immuta_toolkit.web.selectors.registry import SelectorRegistry
from immuta_toolkit.web.selectors.elements import (
    LoginSelectors,
    UserSelectors,
    DataSourceSelectors,
    PolicySelectors,
    ProjectSelectors,
)

logger = get_logger(__name__)


class SelectorManager:
    """Selector manager for Immuta web automation.

    This class provides a unified interface for accessing selectors from different sources.
    It combines the selector registry and the selector classes to provide a single point
    of access for selectors.

    Attributes:
        selectors: Dictionary of selectors.
        version: Immuta version.
    """

    def __init__(self, version: Optional[str] = None):
        """Initialize the selector manager.

        Args:
            version: Immuta version. If None, uses the version from the
                IMMUTA_VERSION environment variable or the default version.
        """
        self.version = version or os.getenv("IMMUTA_VERSION", "2024.02")
        self.selectors: Dict[str, Selector] = {}
        self._initialize_selectors()

    def _initialize_selectors(self) -> None:
        """Initialize selectors from different sources."""
        # Initialize selectors from selector classes
        self._initialize_from_classes()
        # Initialize selectors from compatibility layer
        self._initialize_from_compat()

    def _initialize_from_classes(self) -> None:
        """Initialize selectors from selector classes."""
        # Login selectors
        for name, value in vars(LoginSelectors).items():
            if not name.startswith("_") and isinstance(value, str):
                self.register(
                    Selector(
                        name=f"login.{name.lower()}",
                        value=value,
                        type=SelectorType.CSS,
                        description=f"Login selector: {name}",
                    )
                )

        # User selectors
        for name, value in vars(UserSelectors).items():
            if not name.startswith("_") and isinstance(value, str):
                self.register(
                    Selector(
                        name=f"users.{name.lower()}",
                        value=value,
                        type=SelectorType.CSS,
                        description=f"User selector: {name}",
                    )
                )

        # Data source selectors
        for name, value in vars(DataSourceSelectors).items():
            if not name.startswith("_") and isinstance(value, str):
                self.register(
                    Selector(
                        name=f"data_sources.{name.lower()}",
                        value=value,
                        type=SelectorType.CSS,
                        description=f"Data source selector: {name}",
                    )
                )

        # Policy selectors
        for name, value in vars(PolicySelectors).items():
            if not name.startswith("_") and isinstance(value, str):
                self.register(
                    Selector(
                        name=f"policies.{name.lower()}",
                        value=value,
                        type=SelectorType.CSS,
                        description=f"Policy selector: {name}",
                    )
                )

        # Project selectors
        for name, value in vars(ProjectSelectors).items():
            if not name.startswith("_") and isinstance(value, str):
                self.register(
                    Selector(
                        name=f"projects.{name.lower()}",
                        value=value,
                        type=SelectorType.CSS,
                        description=f"Project selector: {name}",
                    )
                )

    def _initialize_from_compat(self) -> None:
        """Initialize selectors from compatibility layer."""
        # Import here to avoid circular imports
        from immuta_toolkit.web.selectors_compat import SELECTORS

        # Get selectors for the current version
        if self.version in SELECTORS:
            compat_selectors = SELECTORS[self.version]
            for name, value in compat_selectors.items():
                if name not in self.selectors:
                    self.register(
                        Selector(
                            name=name,
                            value=value,
                            type=SelectorType.CSS,
                            description=f"Compatibility selector: {name}",
                        )
                    )

    def register(self, selector: Selector) -> None:
        """Register a selector.

        Args:
            selector: Selector to register.
        """
        logger.debug(f"Registering selector: {selector.name}")
        self.selectors[selector.name] = selector

    def get(self, name: str) -> Optional[Selector]:
        """Get a selector by name.

        Args:
            name: Name of the selector.

        Returns:
            Selector if found, None otherwise.
        """
        if name in self.selectors:
            return self.selectors[name]
        logger.warning(f"Selector not found: {name}")
        return None

    def get_value(self, name: str) -> str:
        """Get a selector value by name.

        Args:
            name: Name of the selector.

        Returns:
            Selector value if found, name otherwise.
        """
        selector = self.get(name)
        if selector:
            return selector.get_playwright_selector()
        return name

    def get_all(self) -> Dict[str, Selector]:
        """Get all selectors.

        Returns:
            Dictionary of all selectors.
        """
        return self.selectors

    def get_by_namespace(self, namespace: str) -> Dict[str, Selector]:
        """Get all selectors in a namespace.

        Args:
            namespace: Namespace to filter by.

        Returns:
            Dictionary of selectors in the namespace.
        """
        return {
            k: v for k, v in self.selectors.items() if k.startswith(f"{namespace}.")
        }


# Create a global selector manager
selector_manager = SelectorManager()


def get_selector(name: str) -> str:
    """Get a selector value by name.

    Args:
        name: Name of the selector.

    Returns:
        Selector value if found, name otherwise.
    """
    return selector_manager.get_value(name)


def get_selector_object(name: str) -> Optional[Selector]:
    """Get a selector object by name.

    Args:
        name: Name of the selector.

    Returns:
        Selector object if found, None otherwise.
    """
    return selector_manager.get(name)
