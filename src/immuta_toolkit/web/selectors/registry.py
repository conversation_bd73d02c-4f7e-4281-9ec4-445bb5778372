"""Selector registry for Immuta web automation."""

from typing import Dict, Any, Optional, Type, TypeVar, Generic

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")  # Selector class type


class SelectorRegistry(Generic[T]):
    """Registry for selectors.
    
    This class provides a registry for selectors, allowing selectors to be
    registered and retrieved by version.
    
    Attributes:
        selectors: Dictionary of selectors by version.
        default_version: Default version to use if no version is specified.
    """

    def __init__(self, default_version: str = "2024.02"):
        """Initialize the selector registry.
        
        Args:
            default_version: Default version to use if no version is specified.
        """
        self.selectors: Dict[str, Type[T]] = {}
        self.default_version = default_version
    
    def register(self, version: str, selector_class: Type[T]) -> Type[T]:
        """Register a selector class for a version.
        
        Args:
            version: Version to register the selector class for.
            selector_class: Selector class to register.
            
        Returns:
            The registered selector class.
        """
        logger.debug(f"Registering selector class {selector_class.__name__} for version {version}")
        self.selectors[version] = selector_class
        return selector_class
    
    def get(self, version: Optional[str] = None) -> Optional[Type[T]]:
        """Get a selector class for a version.
        
        Args:
            version: Version to get the selector class for. If None, the default version is used.
            
        Returns:
            Selector class if found, None otherwise.
        """
        version = version or self.default_version
        return self.selectors.get(version)
    
    def create(self, version: Optional[str] = None, **kwargs: Any) -> Optional[T]:
        """Create an instance of a selector class for a version.
        
        Args:
            version: Version to create the selector class for. If None, the default version is used.
            **kwargs: Additional arguments to pass to the selector class constructor.
            
        Returns:
            Selector instance if found, None otherwise.
        """
        selector_class = self.get(version)
        if selector_class:
            return selector_class(**kwargs)
        return None


# Decorator for registering selectors
def register_selector(registry: SelectorRegistry, version: str):
    """Decorator for registering selectors.
    
    Args:
        registry: Selector registry to register the selector class with.
        version: Version to register the selector class for.
        
    Returns:
        Decorator function.
    """
    def decorator(selector_class: Type[T]) -> Type[T]:
        """Register a selector class.
        
        Args:
            selector_class: Selector class to register.
            
        Returns:
            The registered selector class.
        """
        return registry.register(version, selector_class)
    return decorator
