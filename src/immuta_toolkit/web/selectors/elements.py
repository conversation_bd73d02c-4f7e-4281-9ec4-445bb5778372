"""Element selectors for Immuta web automation."""

from typing import Dict, Any, Optional

from immuta_toolkit.web.selectors.registry import SelectorRegistry, register_selector


# Login selectors
login_registry = SelectorRegistry[Any]()


@register_selector(login_registry, "2024.02")
class LoginSelectors2024_02:
    """Login selectors for Immuta 2024.02."""

    # Login page
    LOGIN_PAGE = "/login"
    USERNAME_INPUT = "input[name='username']"
    PASSWORD_INPUT = "input[name='password']"
    LOGIN_BUTTON = "button[type='submit']"
    ERROR_MESSAGE = ".error-message"


# Use the latest version as the default
LoginSelectors = LoginSelectors2024_02


# User selectors
user_registry = SelectorRegistry[Any]()


@register_selector(user_registry, "2024.02")
class UserSelectors2024_02:
    """User selectors for Immuta 2024.02."""

    # Users page
    USERS_PAGE = "/users"
    USER_SEARCH_INPUT = "input[placeholder='Search users']"
    USER_LIST = ".user-list"
    USER_ITEM = ".user-item"
    USER_NAME = ".user-name"
    USER_EMAIL = ".user-email"
    USER_ROLE = ".user-role"
    
    # User creation
    CREATE_USER_BUTTON = "button:has-text('Create User')"
    USER_FORM = ".user-form"
    USER_FORM_NAME_INPUT = "input[name='name']"
    USER_FORM_EMAIL_INPUT = "input[name='email']"
    USER_FORM_ROLE_SELECT = "select[name='role']"
    USER_FORM_SUBMIT_BUTTON = "button[type='submit']"
    
    # User deletion
    USER_ACTIONS_BUTTON = ".user-actions-button"
    USER_DELETE_BUTTON = "button:has-text('Delete')"
    CONFIRM_DELETE_BUTTON = "button:has-text('Confirm')"


# Use the latest version as the default
UserSelectors = UserSelectors2024_02


# Data source selectors
data_source_registry = SelectorRegistry[Any]()


@register_selector(data_source_registry, "2024.02")
class DataSourceSelectors2024_02:
    """Data source selectors for Immuta 2024.02."""

    # Data sources page
    DATA_SOURCES_PAGE = "/data-sources"
    DATA_SOURCE_SEARCH_INPUT = "input[placeholder='Search data sources']"
    DATA_SOURCE_LIST = ".data-source-list"
    DATA_SOURCE_ITEM = ".data-source-item"
    DATA_SOURCE_NAME = ".data-source-name"
    DATA_SOURCE_TYPE = ".data-source-type"
    
    # Data source creation
    CREATE_DATA_SOURCE_BUTTON = "button:has-text('Create Data Source')"
    DATA_SOURCE_FORM = ".data-source-form"
    DATA_SOURCE_FORM_NAME_INPUT = "input[name='name']"
    DATA_SOURCE_FORM_TYPE_SELECT = "select[name='type']"
    DATA_SOURCE_FORM_SUBMIT_BUTTON = "button[type='submit']"
    
    # Data source deletion
    DATA_SOURCE_ACTIONS_BUTTON = ".data-source-actions-button"
    DATA_SOURCE_DELETE_BUTTON = "button:has-text('Delete')"
    CONFIRM_DELETE_BUTTON = "button:has-text('Confirm')"


# Use the latest version as the default
DataSourceSelectors = DataSourceSelectors2024_02


# Policy selectors
policy_registry = SelectorRegistry[Any]()


@register_selector(policy_registry, "2024.02")
class PolicySelectors2024_02:
    """Policy selectors for Immuta 2024.02."""

    # Policies page
    POLICIES_PAGE = "/policies"
    POLICY_SEARCH_INPUT = "input[placeholder='Search policies']"
    POLICY_LIST = ".policy-list"
    POLICY_ITEM = ".policy-item"
    POLICY_NAME = ".policy-name"
    POLICY_TYPE = ".policy-type"
    
    # Policy creation
    CREATE_POLICY_BUTTON = "button:has-text('Create Policy')"
    POLICY_FORM = ".policy-form"
    POLICY_FORM_NAME_INPUT = "input[name='name']"
    POLICY_FORM_TYPE_SELECT = "select[name='type']"
    POLICY_FORM_SUBMIT_BUTTON = "button[type='submit']"
    
    # Policy deletion
    POLICY_ACTIONS_BUTTON = ".policy-actions-button"
    POLICY_DELETE_BUTTON = "button:has-text('Delete')"
    CONFIRM_DELETE_BUTTON = "button:has-text('Confirm')"


# Use the latest version as the default
PolicySelectors = PolicySelectors2024_02


# Project selectors
project_registry = SelectorRegistry[Any]()


@register_selector(project_registry, "2024.02")
class ProjectSelectors2024_02:
    """Project selectors for Immuta 2024.02."""

    # Projects page
    PROJECTS_PAGE = "/projects"
    PROJECT_SEARCH_INPUT = "input[placeholder='Search projects']"
    PROJECT_LIST = ".project-list"
    PROJECT_ITEM = ".project-item"
    PROJECT_NAME = ".project-name"
    
    # Project creation
    CREATE_PROJECT_BUTTON = "button:has-text('Create Project')"
    PROJECT_FORM = ".project-form"
    PROJECT_FORM_NAME_INPUT = "input[name='name']"
    PROJECT_FORM_DESCRIPTION_INPUT = "textarea[name='description']"
    PROJECT_FORM_SUBMIT_BUTTON = "button[type='submit']"
    
    # Project deletion
    PROJECT_ACTIONS_BUTTON = ".project-actions-button"
    PROJECT_DELETE_BUTTON = "button:has-text('Delete')"
    CONFIRM_DELETE_BUTTON = "button:has-text('Confirm')"
    
    # Project members
    PROJECT_MEMBERS_TAB = "button:has-text('Members')"
    ADD_MEMBER_BUTTON = "button:has-text('Add Member')"
    MEMBER_FORM = ".member-form"
    MEMBER_FORM_EMAIL_INPUT = "input[name='email']"
    MEMBER_FORM_ROLE_SELECT = "select[name='role']"
    MEMBER_FORM_SUBMIT_BUTTON = "button[type='submit']"


# Use the latest version as the default
ProjectSelectors = ProjectSelectors2024_02
