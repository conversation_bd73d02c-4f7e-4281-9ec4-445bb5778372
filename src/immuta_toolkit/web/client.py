"""Web automation client for Imm<PERSON>."""

import os
import time
from typing import Dict, List, Optional, Union, Any, Tu<PERSON>

from playwright.sync_api import sync_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>, Page
from playwright.sync_api import TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.models import (
    UserModel,
    DataSourceModel,
    PolicyModel,
    ProjectModel,
)
from immuta_toolkit.utils.retry import with_retry
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.error_handling import (
    take_screenshot,
    create_error_context,
    handle_web_error,
)
from immuta_toolkit.web.page_objects.login_page import LoginPage
from immuta_toolkit.web.page_objects.users_page import UsersPage
from immuta_toolkit.web.page_objects.data_sources_page import DataSourcesPage
from immuta_toolkit.web.page_objects.policies_page import PoliciesPage
from immuta_toolkit.web.page_objects.projects_page import ProjectsPage
from immuta_toolkit.web.components.common import NavigationComponent, DialogComponent
from immuta_toolkit.web.components.users import UserCardComponent, UserFormComponent
from immuta_toolkit.web.components.data_sources import (
    DataSourceCardComponent,
    DataSourceFormComponent,
)
from immuta_toolkit.web.components.policies import (
    PolicyCardComponent,
    PolicyFormComponent,
)
from immuta_toolkit.web.components.projects import (
    ProjectCardComponent,
    ProjectFormComponent,
)
from immuta_toolkit.web.exceptions import (
    WebAutomationError,
    ElementNotFoundError,
    ElementNotVisibleError,
    ElementNotClickableError,
    NavigationError,
    TimeoutError,
    AuthenticationError,
    FormSubmissionError,
)

logger = get_logger(__name__)


class ImmutaWebClient:
    """Web automation client for Immuta.

    This client provides a high-level interface for automating Immuta
    through the web UI using Playwright. It integrates both page objects
    and component-based architecture for more robust and maintainable
    web automation.

    Attributes:
        base_url: Base URL of the Immuta instance.
        headless: Whether to run the browser in headless mode.
        browser_type: Browser type to use (chromium, firefox, webkit).
        timeout: Default timeout for actions in milliseconds.
        retry_count: Number of times to retry actions.
        retry_delay: Delay between retries in milliseconds.
        screenshot_dir: Directory to save screenshots.
        browser: Playwright browser instance.
        context: Playwright browser context.
        page: Playwright page.
        login_page: Login page object.
        users_page: Users page object.
        data_sources_page: Data sources page object.
        policies_page: Policies page object.
        projects_page: Projects page object.
        navigation: Navigation component.
        dialog: Dialog component.
    """

    def __init__(
        self,
        base_url: Optional[str] = None,
        username: Optional[str] = None,
        password: Optional[str] = None,
        headless: bool = True,
        browser_type: str = "chromium",
        timeout: int = 30000,
        retry_count: int = 3,
        retry_delay: int = 500,
        screenshot_dir: Optional[str] = None,
        browser_args: Optional[List[str]] = None,
    ):
        """Initialize the Immuta web client.

        Args:
            base_url: Base URL of the Immuta instance. If None, will be retrieved
                from the IMMUTA_BASE_URL environment variable.
            username: Username for authentication. If None, will be retrieved
                from the IMMUTA_USERNAME environment variable.
            password: Password for authentication. If None, will be retrieved
                from the IMMUTA_PASSWORD environment variable.
            headless: Whether to run the browser in headless mode.
            browser_type: Browser type to use (chromium, firefox, webkit).
            timeout: Default timeout for actions in milliseconds.
            retry_count: Number of times to retry actions.
            retry_delay: Delay between retries in milliseconds.
            screenshot_dir: Directory to save screenshots.
            browser_args: Additional arguments to pass to the browser.
        """
        # Get base URL from environment variable if not provided
        if not base_url:
            base_url = os.getenv("IMMUTA_BASE_URL")
            if not base_url:
                raise ValueError("Base URL is required for Immuta web client")

        self.base_url = base_url.rstrip("/")
        self.headless = headless
        self.browser_type = browser_type
        self.timeout = timeout
        self.retry_count = retry_count
        self.retry_delay = retry_delay

        # Set up screenshot directory
        if screenshot_dir:
            self.screenshot_dir = screenshot_dir
        else:
            self.screenshot_dir = os.path.join(os.getcwd(), "screenshots")

        os.makedirs(self.screenshot_dir, exist_ok=True)

        # Get credentials from environment variables if not provided
        self.username = username or os.getenv("IMMUTA_USERNAME")
        self.password = password or os.getenv("IMMUTA_PASSWORD")

        if not self.username or not self.password:
            raise ValueError("Username and password are required for Immuta web client")

        # Initialize Playwright
        self.playwright = sync_playwright().start()

        # Set up browser arguments
        browser_kwargs = {"headless": headless}
        if browser_args:
            browser_kwargs["args"] = browser_args

        # Launch browser
        if browser_type == "chromium":
            self.browser = self.playwright.chromium.launch(**browser_kwargs)
        elif browser_type == "firefox":
            self.browser = self.playwright.firefox.launch(**browser_kwargs)
        elif browser_type == "webkit":
            self.browser = self.playwright.webkit.launch(**browser_kwargs)
        else:
            raise ValueError(f"Invalid browser type: {browser_type}")

        # Create browser context with viewport size
        self.context = self.browser.new_context(
            viewport={"width": 1920, "height": 1080}
        )

        # Create page
        self.page = self.context.new_page()
        self.page.set_default_timeout(timeout)

        # Initialize page objects
        self.login_page = LoginPage(self.page, self.base_url, timeout)
        self.users_page = UsersPage(self.page, self.base_url, timeout)
        self.data_sources_page = DataSourcesPage(self.page, self.base_url, timeout)
        self.policies_page = PoliciesPage(self.page, self.base_url, timeout)
        self.projects_page = ProjectsPage(self.page, self.base_url, timeout)

        # Initialize components
        self.navigation = NavigationComponent(self.page, timeout)
        self.dialog = DialogComponent(self.page, timeout)

        # Set up error handling
        self._setup_error_handling()

        # Login
        try:
            self.login()
        except Exception as e:
            logger.error(f"Failed to login: {str(e)}")
            self.take_screenshot("login_failure.png")
            raise AuthenticationError(
                f"Failed to login: {str(e)}",
                page_url=self.page.url,
                screenshot_path=os.path.join(self.screenshot_dir, "login_failure.png"),
            )

    def _setup_error_handling(self) -> None:
        """Set up error handling for the page."""
        # Set up error handling for console errors
        self.page.on(
            "console", lambda msg: logger.debug(f"Console {msg.type}: {msg.text}")
        )

        # Set up error handling for page errors
        self.page.on("pageerror", lambda err: logger.error(f"Page error: {err}"))

        # Set up error handling for request failures
        self.page.on(
            "requestfailed",
            lambda request: logger.warning(
                f"Request failed: {request.url} {request.failure}"
            ),
        )

    def take_screenshot(self, filename: Optional[str] = None) -> str:
        """Take a screenshot of the current page.

        Args:
            filename: Filename for the screenshot. If None, a timestamp-based
                filename will be generated.

        Returns:
            Path to the screenshot file.
        """
        if not filename:
            error_message = "screenshot"
        else:
            # Extract error message from filename
            error_message = filename.replace(".png", "").replace("_", " ")

        return take_screenshot(self.page, error_message, self.screenshot_dir)

    def retry(self, func, *args, **kwargs) -> Any:
        """Retry a function multiple times.

        Args:
            func: Function to retry.
            *args: Arguments to pass to the function.
            **kwargs: Keyword arguments to pass to the function.

        Returns:
            Result of the function.

        Raises:
            Exception: If all retries fail.
        """

        def on_retry(exception, attempt, max_attempts):
            logger.warning(
                f"Attempt {attempt}/{max_attempts} failed: {str(exception)}. Retrying..."
            )

        def on_failure(exception):
            # Take a screenshot on failure
            screenshot_path = self.take_screenshot(f"retry_failure.png")

            # Add screenshot path to the exception
            if hasattr(exception, "screenshot_path"):
                exception.screenshot_path = screenshot_path

            return exception

        retry_func = with_retry(
            func=func,
            max_retries=self.retry_count,
            retry_delay=self.retry_delay / 1000,  # Convert to seconds
            on_retry=on_retry,
        )

        try:
            return retry_func(*args, **kwargs)
        except Exception as e:
            raise on_failure(e)

    def login(self) -> None:
        """Log into Immuta."""
        logger.info(f"Logging into Immuta as {self.username}")
        self.login_page.login(self.username, self.password)

        # Verify login was successful
        if not self.navigation.is_logged_in():
            screenshot_path = self.take_screenshot("login_verification_failure.png")
            raise AuthenticationError(
                "Login verification failed",
                page_url=self.page.url,
                screenshot_path=screenshot_path,
            )

        logger.info("Login successful")

    def close(self) -> None:
        """Close the web client and release resources."""
        logger.info("Closing Immuta web client")

        try:
            if hasattr(self, "context") and self.context:
                self.context.close()

            if hasattr(self, "browser") and self.browser:
                self.browser.close()

            if hasattr(self, "playwright") and self.playwright:
                self.playwright.stop()

            logger.info("Closed Immuta web client")
        except Exception as e:
            logger.warning(f"Error closing Immuta web client: {str(e)}")

    def __enter__(self) -> "ImmutaWebClient":
        """Enter context manager.

        Returns:
            Self for use in with statement.
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Exit context manager.

        Args:
            exc_type: Exception type if an exception was raised in the with block.
            exc_val: Exception value if an exception was raised in the with block.
            exc_tb: Exception traceback if an exception was raised in the with block.
        """
        # Take a screenshot if there was an exception
        if exc_type is not None:
            logger.error(f"Exception in context manager: {exc_val}")
            self.take_screenshot("context_manager_exception.png")

        # Close the client
        self.close()

    def create_user(self, user: UserModel) -> Dict:
        """Create a new user.

        Args:
            user: User model.

        Returns:
            Dictionary with user details.

        Raises:
            FormSubmissionError: If the user cannot be created.
            NavigationError: If navigation to the users page fails.
            WebAutomationError: If any other error occurs.
        """
        logger.info(f"Creating user {user.email}")

        try:
            # Navigate to users page
            self.retry(self.navigation.navigate_to_users)

            # Click create user button
            self.retry(self.users_page.click, "users.create_button")

            # Initialize user form component
            user_form = UserFormComponent(self.page, self.timeout)

            # Create user
            self.retry(user_form.create_user, user)

            # Wait for navigation to complete
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)

            logger.info(f"User {user.email} created successfully")

            # Return user details
            return {
                "email": user.email,
                "name": user.name,
                "role": (
                    user.attributes.role.value
                    if hasattr(user.attributes, "role")
                    else ""
                ),
                "groups": user.groups if hasattr(user, "groups") else [],
            }
        except Exception as e:
            # Create context with user details
            context = {
                "user": (
                    user.model_dump() if hasattr(user, "model_dump") else str(user)
                )
            }

            # Handle error with common utility
            error_message = f"Failed to create user {user.email}"
            raise handle_web_error(
                error=e,
                page=self.page,
                error_message=error_message,
                error_class=WebAutomationError,
                context=context,
                screenshot_dir=self.screenshot_dir,
            )

    def get_user(self, email: str) -> Optional[Dict]:
        """Get user by email.

        Args:
            email: User email.

        Returns:
            Dictionary with user details if found, None otherwise.

        Raises:
            NavigationError: If navigation to the users page fails.
            WebAutomationError: If any other error occurs.
        """
        logger.info(f"Getting user {email}")

        try:
            # Navigate to users page
            self.retry(self.navigation.navigate_to_users)

            # Search for user
            self.retry(self.users_page.fill, "users.search_input", email)

            # Wait for search results
            time.sleep(1)

            # Initialize user card component
            user_card = UserCardComponent(self.page, email, self.timeout)

            # Check if user exists
            if not user_card.find():
                logger.info(f"User {email} not found")
                return None

            # Get user details
            user_details = self.retry(user_card.get_details)

            logger.info(f"User {email} found")
            return user_details
        except Exception as e:
            # Handle error with common utility
            error_message = f"Failed to get user {email}"
            raise handle_web_error(
                error=e,
                page=self.page,
                error_message=error_message,
                error_class=WebAutomationError,
                context={"email": email},
                screenshot_dir=self.screenshot_dir,
            )

    def delete_user(self, email: str) -> bool:
        """Delete a user by email.

        Args:
            email: User email.

        Returns:
            True if the user was deleted, False otherwise.

        Raises:
            NavigationError: If navigation to the users page fails.
            ElementNotFoundError: If the user is not found.
            WebAutomationError: If any other error occurs.
        """
        logger.info(f"Deleting user {email}")

        try:
            # Navigate to users page
            self.retry(self.navigation.navigate_to_users)

            # Search for user
            self.retry(self.users_page.fill, "users.search_input", email)

            # Wait for search results
            time.sleep(1)

            # Initialize user card component
            user_card = UserCardComponent(self.page, email, self.timeout)

            # Check if user exists
            if not user_card.find():
                logger.warning(f"User {email} not found, cannot delete")
                return False

            # Delete user
            self.retry(user_card.delete)

            # Confirm deletion
            self.retry(self.dialog.confirm)

            # Wait for navigation to complete
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)

            logger.info(f"User {email} deleted successfully")
            return True
        except Exception as e:
            # Handle error with common utility
            error_message = f"Failed to delete user {email}"
            raise handle_web_error(
                error=e,
                page=self.page,
                error_message=error_message,
                error_class=WebAutomationError,
                context={"email": email},
                screenshot_dir=self.screenshot_dir,
            )

    def create_data_source(self, data_source: Union[DataSourceModel, Dict]) -> Dict:
        """Create a new data source.

        Args:
            data_source: Data source model or dictionary.

        Returns:
            Dictionary with data source details.

        Raises:
            FormSubmissionError: If the data source cannot be created.
            NavigationError: If navigation to the data sources page fails.
            WebAutomationError: If any other error occurs.
        """
        # Convert dictionary to model if needed
        if isinstance(data_source, dict):
            data_source_dict = data_source
            data_source_name = data_source_dict.get("name", "unknown")
            data_source_obj = DataSourceModel(**data_source_dict)
        else:
            data_source_name = data_source.name
            data_source_obj = data_source

        logger.info(f"Creating data source {data_source_name}")

        try:
            # Navigate to data sources page
            self.retry(self.navigation.navigate_to_data_sources)

            # Click create data source button
            self.retry(self.data_sources_page.click, "data_sources.create_button")

            # Initialize data source form component
            data_source_form = DataSourceFormComponent(self.page, self.timeout)

            # Create data source
            self.retry(data_source_form.create_data_source, data_source_obj)

            # Wait for navigation to complete
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)

            logger.info(f"Data source {data_source_name} created successfully")

            # Return data source details
            return {
                "name": data_source_name,
                "type": getattr(data_source_obj, "handler_type", ""),
            }
        except Exception as e:
            logger.error(f"Failed to create data source {data_source_name}: {str(e)}")
            screenshot_path = self.take_screenshot(
                f"create_data_source_failure_{data_source_name}.png"
            )

            if isinstance(e, WebAutomationError):
                e.screenshot_path = screenshot_path
                raise
            else:
                raise WebAutomationError(
                    f"Failed to create data source {data_source_name}: {str(e)}",
                    page_url=self.page.url,
                    screenshot_path=screenshot_path,
                    context={
                        "data_source": (
                            data_source_obj.model_dump()
                            if hasattr(data_source_obj, "model_dump")
                            else str(data_source_obj)
                        )
                    },
                )

    def get_data_source(self, name: str) -> Optional[Dict]:
        """Get data source by name.

        Args:
            name: Data source name.

        Returns:
            Dictionary with data source details if found, None otherwise.

        Raises:
            NavigationError: If navigation to the data sources page fails.
            WebAutomationError: If any other error occurs.
        """
        logger.info(f"Getting data source {name}")

        try:
            # Navigate to data sources page
            self.retry(self.navigation.navigate_to_data_sources)

            # Search for data source
            self.retry(self.data_sources_page.fill, "data_sources.search_input", name)

            # Wait for search results
            time.sleep(1)

            # Initialize data source card component
            data_source_card = DataSourceCardComponent(self.page, name, self.timeout)

            # Check if data source exists
            if not data_source_card.find():
                logger.info(f"Data source {name} not found")
                return None

            # Get data source details
            data_source_details = self.retry(data_source_card.get_details)

            logger.info(f"Data source {name} found")
            return data_source_details
        except Exception as e:
            logger.error(f"Failed to get data source {name}: {str(e)}")
            screenshot_path = self.take_screenshot(
                f"get_data_source_failure_{name}.png"
            )

            if isinstance(e, WebAutomationError):
                e.screenshot_path = screenshot_path
                raise
            else:
                raise WebAutomationError(
                    f"Failed to get data source {name}: {str(e)}",
                    page_url=self.page.url,
                    screenshot_path=screenshot_path,
                )

    def delete_data_source(self, name: str) -> bool:
        """Delete a data source by name.

        Args:
            name: Data source name.

        Returns:
            True if the data source was deleted, False otherwise.

        Raises:
            NavigationError: If navigation to the data sources page fails.
            ElementNotFoundError: If the data source is not found.
            WebAutomationError: If any other error occurs.
        """
        logger.info(f"Deleting data source {name}")

        try:
            # Navigate to data sources page
            self.retry(self.navigation.navigate_to_data_sources)

            # Search for data source
            self.retry(self.data_sources_page.fill, "data_sources.search_input", name)

            # Wait for search results
            time.sleep(1)

            # Initialize data source card component
            data_source_card = DataSourceCardComponent(self.page, name, self.timeout)

            # Check if data source exists
            if not data_source_card.find():
                logger.warning(f"Data source {name} not found, cannot delete")
                return False

            # Delete data source
            self.retry(data_source_card.delete)

            # Confirm deletion
            self.retry(self.dialog.confirm)

            # Wait for navigation to complete
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)

            logger.info(f"Data source {name} deleted successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to delete data source {name}: {str(e)}")
            screenshot_path = self.take_screenshot(
                f"delete_data_source_failure_{name}.png"
            )

            if isinstance(e, WebAutomationError):
                e.screenshot_path = screenshot_path
                raise
            else:
                raise WebAutomationError(
                    f"Failed to delete data source {name}: {str(e)}",
                    page_url=self.page.url,
                    screenshot_path=screenshot_path,
                )

    def create_policy(self, policy: Union[PolicyModel, Dict]) -> Dict:
        """Create a new policy.

        Args:
            policy: Policy model or dictionary.

        Returns:
            Dictionary with policy details.
        """
        return self.policies_page.create_policy(policy)

    def get_policy(self, name: str) -> Optional[Dict]:
        """Get policy by name.

        Args:
            name: Policy name.

        Returns:
            Dictionary with policy details if found, None otherwise.
        """
        return self.policies_page.search_policy(name)

    def delete_policy(self, name: str) -> bool:
        """Delete a policy by name.

        Args:
            name: Policy name.

        Returns:
            True if the policy was deleted, False otherwise.
        """
        return self.policies_page.delete_policy(name)

    def create_project(self, project: Union[ProjectModel, Dict]) -> Dict:
        """Create a new project.

        Args:
            project: Project model or dictionary.

        Returns:
            Dictionary with project details.
        """
        return self.projects_page.create_project(project)

    def get_project(self, name: str) -> Optional[Dict]:
        """Get project by name.

        Args:
            name: Project name.

        Returns:
            Dictionary with project details if found, None otherwise.
        """
        return self.projects_page.search_project(name)

    def delete_project(self, name: str) -> bool:
        """Delete a project by name.

        Args:
            name: Project name.

        Returns:
            True if the project was deleted, False otherwise.
        """
        return self.projects_page.delete_project(name)

    def add_project_member(self, project_name: str, email: str, role: str) -> bool:
        """Add a member to a project.

        Args:
            project_name: Project name.
            email: Member email.
            role: Member role.

        Returns:
            True if the member was added, False otherwise.
        """
        return self.projects_page.add_member(project_name, email, role)
