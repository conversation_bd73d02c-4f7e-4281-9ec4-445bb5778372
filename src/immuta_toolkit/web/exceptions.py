"""Exceptions for Immuta web automation."""

import os
import time
from datetime import datetime
from typing import Optional, List, Dict, Any, Union

from immuta_toolkit.utils.error_handling import BaseError
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def take_screenshot(page: Any, error_message: str) -> str:
    """Take a screenshot of the current page state.

    Args:
        page: Playwright page object.
        error_message: Error message to include in the filename.

    Returns:
        Path to the screenshot file.
    """
    if page is None:
        return ""

    try:
        # Create screenshots directory if it doesn't exist
        screenshots_dir = os.path.join(os.getcwd(), "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # Create a filename with timestamp and sanitized error message
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        sanitized_error = "".join(c if c.isalnum() else "_" for c in error_message[:50])
        filename = f"{timestamp}_{sanitized_error}.png"
        filepath = os.path.join(screenshots_dir, filename)

        # Take the screenshot
        page.screenshot(path=filepath)
        logger.info(f"Screenshot saved to {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Failed to take screenshot: {str(e)}")
        return ""


def create_error_context(page: Any, selector: Optional[str], error_message: str) -> Dict[str, Any]:
    """Create context information for an error.

    Args:
        page: Playwright page object.
        selector: Selector that caused the error.
        error_message: Error message.

    Returns:
        Dictionary with error context information.
    """
    context = {
        "timestamp": datetime.now().isoformat(),
        "page_url": page.url if page else "",
        "selector": selector,
        "screenshot_path": take_screenshot(page, error_message) if page else "",
    }
    return context


class WebAutomationError(BaseError):
    """Base exception for web automation errors."""

    def __init__(
        self,
        message: str,
        selector: Optional[str] = None,
        page_url: Optional[str] = None,
        screenshot_path: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            selector: Selector that caused the error.
            page_url: URL of the page where the error occurred.
            screenshot_path: Path to the screenshot taken when the error occurred.
            context: Additional context information.
        """
        super().__init__(message)
        self.selector = selector
        self.page_url = page_url
        self.screenshot_path = screenshot_path
        self.context = context or {}


class ElementNotFoundError(WebAutomationError):
    """Exception raised when an element is not found."""

    pass


class ElementNotVisibleError(WebAutomationError):
    """Exception raised when an element is not visible."""

    pass


class ElementNotClickableError(WebAutomationError):
    """Exception raised when an element is not clickable."""

    pass


class NavigationError(WebAutomationError):
    """Exception raised when navigation fails."""

    pass


class TimeoutError(WebAutomationError):
    """Exception raised when an operation times out."""

    pass


class AuthenticationError(WebAutomationError):
    """Exception raised when authentication fails."""

    pass


class FormSubmissionError(WebAutomationError):
    """Exception raised when form submission fails."""

    pass


class ValidationError(WebAutomationError):
    """Exception raised when validation fails."""

    pass


class SelectorError(WebAutomationError):
    """Exception raised when a selector is invalid or not found."""

    pass


class VersionError(WebAutomationError):
    """Exception raised when a version is invalid or not supported."""

    def __init__(
        self,
        message: str,
        current_version: Optional[str] = None,
        supported_versions: Optional[List[str]] = None,
        **kwargs,
    ):
        """Initialize the exception.

        Args:
            message: Error message.
            current_version: Current version.
            supported_versions: Supported versions.
            **kwargs: Additional arguments to pass to the parent constructor.
        """
        super().__init__(message, **kwargs)
        self.current_version = current_version
        self.supported_versions = supported_versions

    def __str__(self) -> str:
        """Return string representation of the exception.

        Returns:
            String representation.
        """
        parts = [super().__str__()]
        if self.current_version:
            parts.append(f"Current version: {self.current_version}")
        if self.supported_versions:
            parts.append(f"Supported versions: {self.supported_versions}")
        return " | ".join(parts)


# Import error handling utilities for backward compatibility
# These are now imported directly from immuta_toolkit.utils.error_handling
