"""Base page object for Immuta web automation."""

import time
from typing import Any, Dict, List, Optional, Union, Callable

from playwright.sync_api import Page, Locator, TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class BasePage:
    """Base page object for Immuta web automation.
    
    This class provides common functionality for all page objects,
    including navigation, waiting, and element interaction.
    
    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta web UI.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 30000):
        """Initialize the page object.
        
        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta web UI.
            timeout: Default timeout for actions in milliseconds.
        """
        self.page = page
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
    
    def navigate(self, path: str) -> None:
        """Navigate to a page.
        
        Args:
            path: Path to navigate to, relative to the base URL.
        """
        url = f"{self.base_url}/{path.lstrip('/')}"
        logger.info(f"Navigating to {url}")
        self.page.goto(url)
    
    def wait_for_url(self, path: str, timeout: Optional[int] = None) -> None:
        """Wait for the URL to match a path.
        
        Args:
            path: Path to wait for, relative to the base URL.
            timeout: Timeout in milliseconds.
        """
        url = f"{self.base_url}/{path.lstrip('/')}"
        logger.debug(f"Waiting for URL to match {url}")
        self.page.wait_for_url(url, timeout=timeout or self.timeout)
    
    def wait_for_selector(self, selector: str, timeout: Optional[int] = None) -> Locator:
        """Wait for an element to be visible.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
            
        Returns:
            Locator for the element.
        """
        logger.debug(f"Waiting for selector: {selector}")
        return self.page.locator(selector).first.wait_for(
            state="visible", timeout=timeout or self.timeout
        )
    
    def wait_for_element_to_be_visible(self, selector: str, timeout: Optional[int] = None) -> Locator:
        """Wait for an element to be visible.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
            
        Returns:
            Locator for the element.
        """
        return self.wait_for_selector(selector, timeout)
    
    def wait_for_element_to_be_hidden(self, selector: str, timeout: Optional[int] = None) -> None:
        """Wait for an element to be hidden.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
        """
        logger.debug(f"Waiting for selector to be hidden: {selector}")
        self.page.locator(selector).first.wait_for(
            state="hidden", timeout=timeout or self.timeout
        )
    
    def wait_for_element_to_be_enabled(self, selector: str, timeout: Optional[int] = None) -> Locator:
        """Wait for an element to be enabled.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
            
        Returns:
            Locator for the element.
        """
        logger.debug(f"Waiting for selector to be enabled: {selector}")
        locator = self.page.locator(selector).first
        locator.wait_for(state="visible", timeout=timeout or self.timeout)
        
        # Wait for the element to be enabled
        start_time = time.time()
        timeout_ms = timeout or self.timeout
        while time.time() - start_time < timeout_ms / 1000:
            if not locator.is_disabled():
                return locator
            time.sleep(0.1)
        
        raise PlaywrightTimeoutError(f"Timeout waiting for element to be enabled: {selector}")
    
    def click(self, selector: str, timeout: Optional[int] = None) -> None:
        """Click an element.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
        """
        logger.debug(f"Clicking element: {selector}")
        self.wait_for_element_to_be_enabled(selector, timeout).click()
    
    def fill(self, selector: str, value: str, timeout: Optional[int] = None) -> None:
        """Fill a form field.
        
        Args:
            selector: CSS selector for the element.
            value: Value to fill.
            timeout: Timeout in milliseconds.
        """
        logger.debug(f"Filling element: {selector} with value: {value}")
        self.wait_for_element_to_be_enabled(selector, timeout).fill(value)
    
    def select_option(self, selector: str, value: str, timeout: Optional[int] = None) -> None:
        """Select an option from a dropdown.
        
        Args:
            selector: CSS selector for the element.
            value: Value to select.
            timeout: Timeout in milliseconds.
        """
        logger.debug(f"Selecting option: {value} from element: {selector}")
        self.wait_for_element_to_be_enabled(selector, timeout).select_option(value=value)
    
    def get_text(self, selector: str, timeout: Optional[int] = None) -> str:
        """Get the text of an element.
        
        Args:
            selector: CSS selector for the element.
            timeout: Timeout in milliseconds.
            
        Returns:
            Text of the element.
        """
        logger.debug(f"Getting text from element: {selector}")
        return self.wait_for_selector(selector, timeout).text_content() or ""
    
    def get_attribute(self, selector: str, attribute: str, timeout: Optional[int] = None) -> Optional[str]:
        """Get an attribute of an element.
        
        Args:
            selector: CSS selector for the element.
            attribute: Attribute to get.
            timeout: Timeout in milliseconds.
            
        Returns:
            Attribute value.
        """
        logger.debug(f"Getting attribute: {attribute} from element: {selector}")
        return self.wait_for_selector(selector, timeout).get_attribute(attribute)
    
    def is_visible(self, selector: str) -> bool:
        """Check if an element is visible.
        
        Args:
            selector: CSS selector for the element.
            
        Returns:
            True if the element is visible, False otherwise.
        """
        logger.debug(f"Checking if element is visible: {selector}")
        return self.page.locator(selector).first.is_visible()
    
    def is_enabled(self, selector: str) -> bool:
        """Check if an element is enabled.
        
        Args:
            selector: CSS selector for the element.
            
        Returns:
            True if the element is enabled, False otherwise.
        """
        logger.debug(f"Checking if element is enabled: {selector}")
        return not self.page.locator(selector).first.is_disabled()
    
    def wait_for_condition(
        self, condition: Callable[[], bool], timeout: Optional[int] = None, interval: float = 0.1
    ) -> None:
        """Wait for a condition to be true.
        
        Args:
            condition: Function that returns True when the condition is met.
            timeout: Timeout in milliseconds.
            interval: Interval between checks in seconds.
            
        Raises:
            TimeoutError: If the condition is not met within the timeout.
        """
        logger.debug("Waiting for condition")
        start_time = time.time()
        timeout_ms = timeout or self.timeout
        while time.time() - start_time < timeout_ms / 1000:
            if condition():
                return
            time.sleep(interval)
        
        raise TimeoutError("Timeout waiting for condition")
    
    def wait_for_navigation(self, timeout: Optional[int] = None) -> None:
        """Wait for navigation to complete.
        
        Args:
            timeout: Timeout in milliseconds.
        """
        logger.debug("Waiting for navigation")
        self.page.wait_for_load_state("networkidle", timeout=timeout or self.timeout)
    
    def wait_for_response(self, url_pattern: str, timeout: Optional[int] = None) -> None:
        """Wait for a response matching a URL pattern.
        
        Args:
            url_pattern: URL pattern to match.
            timeout: Timeout in milliseconds.
        """
        logger.debug(f"Waiting for response matching: {url_pattern}")
        self.page.wait_for_response(url_pattern, timeout=timeout or self.timeout)
    
    def take_screenshot(self, path: str) -> None:
        """Take a screenshot.
        
        Args:
            path: Path to save the screenshot to.
        """
        logger.debug(f"Taking screenshot: {path}")
        self.page.screenshot(path=path)
