"""Login page object for Immuta web automation."""

from typing import Optional

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.page_objects.base_page import BasePage

logger = get_logger(__name__)


class LoginPage(BasePage):
    """Login page object for Immuta web automation.

    This class provides functionality for logging into Immuta.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the login page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, base_url, timeout)

    def login(self, username: str, password: str) -> None:
        """Log into Immuta.

        Args:
            username: Username or email.
            password: Password.
        """
        logger.info(f"Logging in as {username}")
        
        # Navigate to login page
        self.navigate("login")
        
        # Fill login form
        self.fill("login.username", username)
        self.fill("login.password", password)
        
        # Submit form
        self.click("login.submit")
        
        # Wait for navigation to complete
        self.wait_for_navigation()
        
        logger.info("Login successful")

    def is_logged_in(self) -> bool:
        """Check if the user is logged in.

        Returns:
            True if the user is logged in, False otherwise.
        """
        # Check if any navigation element is visible
        return (
            self.is_visible("nav.users", timeout=5000)
            or self.is_visible("nav.data_sources", timeout=5000)
            or self.is_visible("nav.policies", timeout=5000)
        )
