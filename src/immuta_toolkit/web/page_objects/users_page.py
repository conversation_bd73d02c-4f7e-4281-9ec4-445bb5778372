"""Users page object for Immuta web automation."""

import time
from typing import Dict, List, Optional

from playwright.sync_api import Page

from immuta_toolkit.models import UserModel
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.page_objects.base_page import BasePage
from immuta_toolkit.web.selectors_compat import get_selector

logger = get_logger(__name__)


class UsersPage(BasePage):
    """Users page object for Immuta web automation.

    This class provides functionality for managing users in Immuta.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the users page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, base_url, timeout)

    def navigate_to_users(self) -> None:
        """Navigate to the users page."""
        logger.info("Navigating to users page")
        self.navigate("users")
        self.wait_for_selector("users.search_input")

    def create_user(self, user: UserModel) -> Dict:
        """Create a new user.

        Args:
            user: User model.

        Returns:
            Dictionary with user details.
        """
        logger.info(f"Creating user {user.email}")

        # Navigate to users page
        self.navigate_to_users()

        # Click create user button
        self.click("users.create_button")

        # Fill user form
        self.fill("user_form.email", user.email)
        self.fill("user_form.name", user.name)

        # Select role
        role_selector = get_selector("user_form.role")
        self.page.select_option(role_selector, value=user.attributes.role.value)

        # Add groups
        if user.groups:
            for group in user.groups:
                self.fill("user_form.groups", group)
                self.page.keyboard.press("Enter")

        # Submit form
        self.click("user_form.submit")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"User {user.email} created successfully")

        # Return user details
        return {
            "email": user.email,
            "name": user.name,
            "role": user.attributes.role.value,
            "groups": user.groups,
        }

    def search_user(self, email: str) -> Optional[Dict]:
        """Search for a user by email.

        Args:
            email: User email.

        Returns:
            Dictionary with user details if found, None otherwise.
        """
        logger.info(f"Searching for user {email}")

        # Navigate to users page
        self.navigate_to_users()

        # Search for user
        self.fill("users.search_input", email)

        # Wait for search results
        time.sleep(1)

        # Check if user exists
        if self.get_elements_count("users.user_row") == 0:
            logger.info(f"User {email} not found")
            return None

        # Get user details
        user_email = self.get_text("users.user_email")
        user_name = self.get_text("users.user_name")

        logger.info(f"User {email} found")

        return {
            "email": user_email,
            "name": user_name,
        }

    def delete_user(self, email: str) -> bool:
        """Delete a user by email.

        Args:
            email: User email.

        Returns:
            True if the user was deleted, False otherwise.
        """
        logger.info(f"Deleting user {email}")

        # Search for user
        user = self.search_user(email)
        if not user:
            logger.warning(f"User {email} not found, cannot delete")
            return False

        # Click actions button
        self.click("users.user_actions")

        # Click delete button
        self.click("users.delete_user")

        # Confirm deletion
        self.click("dialog.confirm")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"User {email} deleted successfully")

        return True
