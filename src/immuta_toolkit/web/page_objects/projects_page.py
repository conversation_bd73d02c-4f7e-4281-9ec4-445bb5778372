"""Projects page object for Immuta web automation."""

import time
from typing import Dict, List, Optional, Union

from playwright.sync_api import Page

from immuta_toolkit.models import ProjectModel
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.page_objects.base_page import BasePage
from immuta_toolkit.web.selectors_compat import get_selector

logger = get_logger(__name__)


class ProjectsPage(BasePage):
    """Projects page object for Immuta web automation.

    This class provides functionality for managing projects in Immuta.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the projects page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, base_url, timeout)

    def navigate_to_projects(self) -> None:
        """Navigate to the projects page."""
        logger.info("Navigating to projects page")
        self.navigate("projects")
        self.wait_for_selector("projects.search_input")

    def create_project(self, project: Union[ProjectModel, Dict]) -> Dict:
        """Create a new project.

        Args:
            project: Project model or dictionary.

        Returns:
            Dictionary with project details.
        """
        logger.info(f"Creating project {project.get('name', '')}")

        # Navigate to projects page
        self.navigate_to_projects()

        # Click create project button
        self.click("projects.create_button")

        # Fill project form
        self._fill_project_form(project)

        # Submit form
        self.click("project_form.submit")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Project {project.get('name', '')} created successfully")

        # Return project details
        return {
            "name": project.get("name", ""),
            "description": project.get("description", ""),
        }

    def _fill_project_form(self, project: Union[ProjectModel, Dict]) -> None:
        """Fill the project form.

        Args:
            project: Project model or dictionary.
        """
        # Get project details
        name = project.get("name", "")
        description = project.get("description", "")

        # Fill basic information
        self.fill("project_form.name", name)
        self.fill("project_form.description", description)

        # Add data sources
        data_sources = project.get("data_sources", [])
        for data_source in data_sources:
            self.click("project_form.add_data_source_button")
            self.fill("project_form.data_source_search", data_source)
            self.click("project_form.data_source_select")

        # Add members
        members = project.get("members", [])
        for member in members:
            self.click("project_form.add_member_button")
            self.fill("project_form.member_search", member.get("email", ""))
            self.click("project_form.member_select")

            # Select role
            role = member.get("role", "")
            if role:
                role_selector = get_selector("project_form.member_role")
                self.page.select_option(role_selector, value=role)

    def search_project(self, name: str) -> Optional[Dict]:
        """Search for a project by name.

        Args:
            name: Project name.

        Returns:
            Dictionary with project details if found, None otherwise.
        """
        logger.info(f"Searching for project {name}")

        # Navigate to projects page
        self.navigate_to_projects()

        # Search for project
        self.fill("projects.search_input", name)

        # Wait for search results
        time.sleep(1)

        # Check if project exists
        if self.get_elements_count("projects.project_row") == 0:
            logger.info(f"Project {name} not found")
            return None

        # Get project details
        project_name = self.get_text("projects.project_name")

        logger.info(f"Project {name} found")

        return {
            "name": project_name,
        }

    def delete_project(self, name: str) -> bool:
        """Delete a project by name.

        Args:
            name: Project name.

        Returns:
            True if the project was deleted, False otherwise.
        """
        logger.info(f"Deleting project {name}")

        # Search for project
        project = self.search_project(name)
        if not project:
            logger.warning(f"Project {name} not found, cannot delete")
            return False

        # Click actions button
        self.click("projects.project_actions")

        # Click delete button
        self.click("projects.delete_project")

        # Confirm deletion
        self.click("dialog.confirm")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Project {name} deleted successfully")

        return True

    def add_member(self, project_name: str, email: str, role: str) -> bool:
        """Add a member to a project.

        Args:
            project_name: Project name.
            email: Member email.
            role: Member role.

        Returns:
            True if the member was added, False otherwise.
        """
        logger.info(f"Adding member {email} to project {project_name}")

        # Search for project
        project = self.search_project(project_name)
        if not project:
            logger.warning(f"Project {project_name} not found, cannot add member")
            return False

        # Click on project to open it
        self.click("projects.project_name")

        # Wait for project page to load
        self.wait_for_selector("project.members_tab")

        # Click on members tab
        self.click("project.members_tab")

        # Click add member button
        self.click("project.add_member_button")

        # Fill member form
        self.fill("project.member_search", email)
        self.click("project.member_select")

        # Select role
        role_selector = get_selector("project.member_role")
        self.page.select_option(role_selector, value=role)

        # Submit form
        self.click("project.member_submit")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Member {email} added to project {project_name} successfully")

        return True
