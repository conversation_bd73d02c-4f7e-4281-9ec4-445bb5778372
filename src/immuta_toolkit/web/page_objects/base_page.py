"""Base page object for Immuta web automation."""

from typing import Optional, TypeVar

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.selectors import get_selector

logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar("T")


class BasePage:
    """Base page object for Immuta web automation.

    This class provides common functionality for all page objects,
    including navigation, waiting, and element interaction.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the base page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        self.page = page
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout

    def navigate(self, path: str) -> None:
        """Navigate to a specific path.

        Args:
            path: Path to navigate to (without base URL).
        """
        url = f"{self.base_url}/{path.lstrip('/')}"
        logger.info(f"Navigating to {url}")
        self.page.goto(url)

    def wait_for_selector(
        self, selector_key: str, timeout: Optional[int] = None
    ) -> None:
        """Wait for a selector to be visible.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Waiting for selector {selector_key}: {selector}")
        self.page.wait_for_selector(selector, timeout=timeout or self.timeout)

    def click(self, selector_key: str, timeout: Optional[int] = None) -> None:
        """Click an element.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Clicking element {selector_key}: {selector}")
        self.page.click(selector, timeout=timeout or self.timeout)

    def fill(
        self, selector_key: str, value: str, timeout: Optional[int] = None
    ) -> None:
        """Fill a form field.

        Args:
            selector_key: Key for the selector in the selector registry.
            value: Value to fill.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Filling element {selector_key} with value {value}")
        self.page.fill(selector, value, timeout=timeout or self.timeout)

    def get_text(self, selector_key: str, timeout: Optional[int] = None) -> str:
        """Get text from an element.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Text content of the element.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Getting text from element {selector_key}: {selector}")
        element = self.page.wait_for_selector(selector, timeout=timeout or self.timeout)
        return element.text_content() or ""

    def is_visible(self, selector_key: str, timeout: Optional[int] = None) -> bool:
        """Check if an element is visible.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            True if the element is visible, False otherwise.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Checking visibility of element {selector_key}: {selector}")
        try:
            self.page.wait_for_selector(
                selector, timeout=timeout or self.timeout, state="visible"
            )
            return True
        except Exception:
            return False

    def wait_for_navigation(self, timeout: Optional[int] = None) -> None:
        """Wait for navigation to complete.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.
        """
        logger.debug("Waiting for navigation to complete")
        self.page.wait_for_load_state("networkidle", timeout=timeout or self.timeout)

    def wait_for_url(self, url_pattern: str, timeout: Optional[int] = None) -> None:
        """Wait for URL to match a pattern.

        Args:
            url_pattern: URL pattern to match.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
        """
        logger.debug(f"Waiting for URL to match pattern: {url_pattern}")
        self.page.wait_for_url(url_pattern, timeout=timeout or self.timeout)

    def get_elements_count(
        self, selector_key: str, timeout: Optional[int] = None
    ) -> int:
        """Get the number of elements matching a selector.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Number of elements matching the selector.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Getting count of elements matching {selector_key}: {selector}")
        self.page.wait_for_selector(selector, timeout=timeout or self.timeout)
        return len(self.page.query_selector_all(selector))

    def take_screenshot(self, path: str) -> None:
        """Take a screenshot.

        Args:
            path: Path to save the screenshot.
        """
        logger.debug(f"Taking screenshot: {path}")
        self.page.screenshot(path=path)
