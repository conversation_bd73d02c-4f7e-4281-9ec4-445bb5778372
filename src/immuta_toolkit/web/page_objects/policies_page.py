"""Policies page object for Immuta web automation."""

import time
from typing import Dict, List, Optional, Union

from playwright.sync_api import Page

from immuta_toolkit.models import PolicyModel
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.page_objects.base_page import BasePage
from immuta_toolkit.web.selectors_compat import get_selector

logger = get_logger(__name__)


class PoliciesPage(BasePage):
    """Policies page object for Immuta web automation.

    This class provides functionality for managing policies in Immuta.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the policies page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, base_url, timeout)

    def navigate_to_policies(self) -> None:
        """Navigate to the policies page."""
        logger.info("Navigating to policies page")
        self.navigate("policies")
        self.wait_for_selector("policies.search_input")

    def create_policy(self, policy: Union[PolicyModel, Dict]) -> Dict:
        """Create a new policy.

        Args:
            policy: Policy model or dictionary.

        Returns:
            Dictionary with policy details.
        """
        logger.info(f"Creating policy {policy.get('name', '')}")

        # Navigate to policies page
        self.navigate_to_policies()

        # Click create policy button
        self.click("policies.create_button")

        # Fill policy form
        self._fill_policy_form(policy)

        # Submit form
        self.click("policy_form.submit")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Policy {policy.get('name', '')} created successfully")

        # Return policy details
        return {
            "name": policy.get("name", ""),
            "description": policy.get("description", ""),
            "policy_type": policy.get("policy_type", ""),
        }

    def _fill_policy_form(self, policy: Union[PolicyModel, Dict]) -> None:
        """Fill the policy form.

        Args:
            policy: Policy model or dictionary.
        """
        # Get policy details
        name = policy.get("name", "")
        description = policy.get("description", "")
        policy_type = policy.get("policy_type", "")

        # Fill basic information
        self.fill("policy_form.name", name)
        self.fill("policy_form.description", description)

        # Select policy type
        if policy_type:
            policy_type_selector = get_selector("policy_form.policy_type")
            self.page.select_option(policy_type_selector, value=policy_type)

            # Wait for policy-specific fields to appear
            time.sleep(1)

            # Fill policy-specific fields based on policy type
            if policy_type == "row_level":
                self._fill_row_level_policy_fields(policy)
            elif policy_type == "column_level":
                self._fill_column_level_policy_fields(policy)
            elif policy_type == "purpose_based":
                self._fill_purpose_based_policy_fields(policy)
            # Add more policy types as needed

    def _fill_row_level_policy_fields(self, policy: Union[PolicyModel, Dict]) -> None:
        """Fill row-level policy fields.

        Args:
            policy: Policy model or dictionary.
        """
        # Fill row-level policy fields
        condition = policy.get("condition", "")
        if condition:
            self.fill("policy_form.row_level.condition", condition)

        # Add data sources
        data_sources = policy.get("data_sources", [])
        for data_source in data_sources:
            self.click("policy_form.add_data_source_button")
            self.fill("policy_form.data_source_search", data_source)
            self.click("policy_form.data_source_select")

    def _fill_column_level_policy_fields(
        self, policy: Union[PolicyModel, Dict]
    ) -> None:
        """Fill column-level policy fields.

        Args:
            policy: Policy model or dictionary.
        """
        # Fill column-level policy fields
        columns = policy.get("columns", [])
        for column in columns:
            self.click("policy_form.add_column_button")
            self.fill("policy_form.column_name", column)
            self.click("policy_form.column_add")

        # Add data sources
        data_sources = policy.get("data_sources", [])
        for data_source in data_sources:
            self.click("policy_form.add_data_source_button")
            self.fill("policy_form.data_source_search", data_source)
            self.click("policy_form.data_source_select")

    def _fill_purpose_based_policy_fields(
        self, policy: Union[PolicyModel, Dict]
    ) -> None:
        """Fill purpose-based policy fields.

        Args:
            policy: Policy model or dictionary.
        """
        # Fill purpose-based policy fields
        purposes = policy.get("purposes", [])
        for purpose in purposes:
            self.click("policy_form.add_purpose_button")
            self.fill("policy_form.purpose_search", purpose)
            self.click("policy_form.purpose_select")

        # Add data sources
        data_sources = policy.get("data_sources", [])
        for data_source in data_sources:
            self.click("policy_form.add_data_source_button")
            self.fill("policy_form.data_source_search", data_source)
            self.click("policy_form.data_source_select")

    def search_policy(self, name: str) -> Optional[Dict]:
        """Search for a policy by name.

        Args:
            name: Policy name.

        Returns:
            Dictionary with policy details if found, None otherwise.
        """
        logger.info(f"Searching for policy {name}")

        # Navigate to policies page
        self.navigate_to_policies()

        # Search for policy
        self.fill("policies.search_input", name)

        # Wait for search results
        time.sleep(1)

        # Check if policy exists
        if self.get_elements_count("policies.policy_row") == 0:
            logger.info(f"Policy {name} not found")
            return None

        # Get policy details
        policy_name = self.get_text("policies.policy_name")

        logger.info(f"Policy {name} found")

        return {
            "name": policy_name,
        }

    def delete_policy(self, name: str) -> bool:
        """Delete a policy by name.

        Args:
            name: Policy name.

        Returns:
            True if the policy was deleted, False otherwise.
        """
        logger.info(f"Deleting policy {name}")

        # Search for policy
        policy = self.search_policy(name)
        if not policy:
            logger.warning(f"Policy {name} not found, cannot delete")
            return False

        # Click actions button
        self.click("policies.policy_actions")

        # Click delete button
        self.click("policies.delete_policy")

        # Confirm deletion
        self.click("dialog.confirm")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Policy {name} deleted successfully")

        return True
