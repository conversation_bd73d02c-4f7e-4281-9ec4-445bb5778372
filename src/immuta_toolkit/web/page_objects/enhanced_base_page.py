"""Enhanced base page object for Immuta web automation."""

import time
import os
import re
import random
from datetime import datetime
from typing import (
    Any,
    Dict,
    List,
    Optional,
    Union,
    Callable,
    TypeVar,
    cast,
    Tuple,
    Pattern,
)

from playwright.sync_api import Page, expect, TimeoutError as PlaywrightTimeoutError
from playwright.sync_api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Locator

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.selectors_compat import get_selector
from immuta_toolkit.web.exceptions import (
    WebAutomationError,
    ElementNotFoundError,
    ElementNotVisibleError,
    ElementNotClickableError,
    NavigationError,
    TimeoutError,
    AuthenticationError,
    FormSubmissionError,
    ValidationError,
    SelectorError,
    take_screenshot,
    create_error_context,
)

logger = get_logger(__name__)

# Type variables for generic functions
T = TypeVar("T")


class EnhancedBasePage:
    """Enhanced base page object for Immuta web automation.

    This class provides common functionality for all page objects,
    including navigation, waiting, and element interaction with
    improved error handling, retry mechanisms, and performance monitoring.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
        retry_count: Number of times to retry actions.
        retry_delay: Delay between retries in milliseconds.
        screenshot_on_error: Whether to take a screenshot on error.
    """

    def __init__(
        self,
        page: Page,
        base_url: str,
        timeout: int = 10000,
        retry_count: int = 3,
        retry_delay: int = 500,
        screenshot_on_error: bool = True,
    ):
        """Initialize the base page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
            retry_count: Number of times to retry actions.
            retry_delay: Delay between retries in milliseconds.
            screenshot_on_error: Whether to take a screenshot on error.
        """
        self.page = page
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.retry_count = retry_count
        self.retry_delay = retry_delay
        self.screenshot_on_error = screenshot_on_error
        self.performance_metrics: Dict[str, List[float]] = {}

    def navigate(self, path: str, wait_for_load: bool = True) -> None:
        """Navigate to a specific path.

        Args:
            path: Path to navigate to (without base URL).
            wait_for_load: Whether to wait for the page to load.

        Raises:
            NavigationError: If navigation fails.
        """
        url = f"{self.base_url}/{path.lstrip('/')}"
        logger.info(f"Navigating to {url}")

        start_time = time.time()

        try:
            self.page.goto(url)

            if wait_for_load:
                self.wait_for_load()

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("navigation", end_time - start_time)

        except Exception as e:
            error_message = f"Failed to navigate to {url}"
            error_context = create_error_context(self.page, None, error_message)

            raise NavigationError(
                error_message,
                page_url=url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e)},
            )

    def wait_for_load(self, timeout: Optional[int] = None) -> None:
        """Wait for the page to load completely.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            TimeoutError: If the page does not load within the timeout.
        """
        try:
            # Wait for DOM content to load
            self.page.wait_for_load_state(
                "domcontentloaded", timeout=timeout or self.timeout
            )

            # Wait for network to be idle
            self.page.wait_for_load_state(
                "networkidle", timeout=timeout or self.timeout
            )

        except PlaywrightTimeoutError as e:
            error_message = "Page did not load within timeout"
            error_context = create_error_context(self.page, None, error_message)

            raise TimeoutError(
                error_message,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e)},
            )

    def retry(
        self, func: Callable[[], T], error_message: str, selector: Optional[str] = None
    ) -> T:
        """Retry a function multiple times.

        Args:
            func: Function to retry.
            error_message: Error message to use if all retries fail.
            selector: Selector that the function is operating on.

        Returns:
            Result of the function.

        Raises:
            WebAutomationError: If all retries fail.
        """
        last_error = None

        for attempt in range(self.retry_count + 1):
            try:
                return func()
            except Exception as e:
                last_error = e

                if attempt < self.retry_count:
                    logger.warning(
                        f"Attempt {attempt + 1}/{self.retry_count + 1} failed: {str(e)}. Retrying..."
                    )
                    time.sleep(self.retry_delay / 1000)  # Convert to seconds
                else:
                    logger.error(
                        f"All {self.retry_count + 1} attempts failed: {str(e)}"
                    )

        # If we get here, all retries failed
        error_context = create_error_context(self.page, selector, error_message)

        raise WebAutomationError(
            error_message,
            selector=selector,
            page_url=self.page.url,
            screenshot_path=error_context["screenshot_path"],
            context={"original_error": str(last_error)},
        )

    def wait_for_selector(
        self,
        selector_key: str,
        timeout: Optional[int] = None,
        state: str = "visible",
    ) -> ElementHandle:
        """Wait for a selector to be in a specific state.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
            state: State to wait for (visible, hidden, attached, detached).

        Returns:
            Element handle.

        Raises:
            ElementNotFoundError: If the element is not found.
            ElementNotVisibleError: If the element is not visible.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Waiting for selector {selector_key}: {selector} to be {state}")

        start_time = time.time()

        try:
            element = self.page.wait_for_selector(
                selector, timeout=timeout or self.timeout, state=state
            )

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("wait_for_selector", end_time - start_time)

            if element is None:
                raise ElementNotFoundError(
                    f"Element not found: {selector_key}",
                    selector=selector,
                    page_url=self.page.url,
                )

            return element

        except PlaywrightTimeoutError as e:
            error_message = f"Timeout waiting for element: {selector_key} to be {state}"
            error_context = create_error_context(self.page, selector, error_message)

            if state == "visible":
                raise ElementNotVisibleError(
                    error_message,
                    selector=selector,
                    page_url=self.page.url,
                    screenshot_path=error_context["screenshot_path"],
                    context={"original_error": str(e)},
                )
            else:
                raise ElementNotFoundError(
                    error_message,
                    selector=selector,
                    page_url=self.page.url,
                    screenshot_path=error_context["screenshot_path"],
                    context={"original_error": str(e)},
                )

    def click(
        self,
        selector_key: str,
        timeout: Optional[int] = None,
        force: bool = False,
        retry: bool = True,
    ) -> None:
        """Click an element.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
            force: Whether to force the click.
            retry: Whether to retry if the click fails.

        Raises:
            ElementNotClickableError: If the element is not clickable.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Clicking element {selector_key}: {selector}")

        start_time = time.time()

        def click_func():
            try:
                self.page.click(selector, timeout=timeout or self.timeout, force=force)

                # Record performance metric
                end_time = time.time()
                self._record_performance_metric("click", end_time - start_time)
            except Exception as e:
                error_message = f"Failed to click element: {selector_key}"
                error_context = create_error_context(self.page, selector, error_message)

                raise ElementNotClickableError(
                    error_message,
                    selector=selector,
                    page_url=self.page.url,
                    screenshot_path=error_context["screenshot_path"],
                    context={"original_error": str(e)},
                )

        if retry:
            self.retry(click_func, f"Failed to click element: {selector_key}", selector)
        else:
            click_func()

    def fill(
        self,
        selector_key: str,
        value: str,
        timeout: Optional[int] = None,
        retry: bool = True,
    ) -> None:
        """Fill a form field.

        Args:
            selector_key: Key for the selector in the selector registry.
            value: Value to fill.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
            retry: Whether to retry if the fill fails.

        Raises:
            FormSubmissionError: If the form field cannot be filled.
        """
        selector = get_selector(selector_key)
        # Mask sensitive values in logs
        log_value = value
        if any(
            keyword in selector_key.lower()
            for keyword in ["password", "secret", "token", "key"]
        ):
            log_value = "*" * len(value)

        logger.debug(f"Filling element {selector_key} with value {log_value}")

        start_time = time.time()

        def fill_func():
            try:
                self.page.fill(selector, value, timeout=timeout or self.timeout)

                # Record performance metric
                end_time = time.time()
                self._record_performance_metric("fill", end_time - start_time)
            except Exception as e:
                error_message = f"Failed to fill element: {selector_key}"
                error_context = create_error_context(self.page, selector, error_message)

                raise FormSubmissionError(
                    error_message,
                    selector=selector,
                    page_url=self.page.url,
                    screenshot_path=error_context["screenshot_path"],
                    context={"original_error": str(e)},
                )

        if retry:
            self.retry(fill_func, f"Failed to fill element: {selector_key}", selector)
        else:
            fill_func()

    def _record_performance_metric(self, operation: str, duration: float) -> None:
        """Record a performance metric.

        Args:
            operation: Name of the operation.
            duration: Duration of the operation in seconds.
        """
        if operation not in self.performance_metrics:
            self.performance_metrics[operation] = []

        self.performance_metrics[operation].append(duration)

        # Log slow operations
        if duration > 1.0:  # More than 1 second
            logger.warning(f"Slow operation: {operation} took {duration:.2f} seconds")

    def get_performance_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get performance metrics.

        Returns:
            Dictionary with performance metrics.
        """
        metrics = {}

        for operation, durations in self.performance_metrics.items():
            if not durations:
                continue

            metrics[operation] = {
                "min": min(durations),
                "max": max(durations),
                "avg": sum(durations) / len(durations),
                "count": len(durations),
                "total": sum(durations),
            }

        return metrics

    def select_option(
        self,
        selector_key: str,
        value: Union[str, List[str]],
        timeout: Optional[int] = None,
        retry: bool = True,
    ) -> None:
        """Select an option from a dropdown.

        Args:
            selector_key: Key for the selector in the selector registry.
            value: Value or list of values to select.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
            retry: Whether to retry if the selection fails.

        Raises:
            FormSubmissionError: If the option cannot be selected.
        """
        selector = get_selector(selector_key)
        logger.debug(
            f"Selecting option {value} from element {selector_key}: {selector}"
        )

        start_time = time.time()

        def select_func():
            try:
                self.page.select_option(
                    selector, value, timeout=timeout or self.timeout
                )

                # Record performance metric
                end_time = time.time()
                self._record_performance_metric("select_option", end_time - start_time)
            except Exception as e:
                error_message = (
                    f"Failed to select option {value} from element: {selector_key}"
                )
                error_context = create_error_context(self.page, selector, error_message)

                raise FormSubmissionError(
                    error_message,
                    selector=selector,
                    page_url=self.page.url,
                    screenshot_path=error_context["screenshot_path"],
                    context={"original_error": str(e)},
                )

        if retry:
            self.retry(
                select_func,
                f"Failed to select option {value} from element: {selector_key}",
                selector,
            )
        else:
            select_func()

    def get_text(self, selector_key: str, timeout: Optional[int] = None) -> str:
        """Get text from an element.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Text content of the element.

        Raises:
            ElementNotFoundError: If the element is not found.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Getting text from element {selector_key}: {selector}")

        start_time = time.time()

        try:
            element = self.page.wait_for_selector(
                selector, timeout=timeout or self.timeout
            )

            if element is None:
                raise ElementNotFoundError(
                    f"Element not found: {selector_key}",
                    selector=selector,
                    page_url=self.page.url,
                )

            text = element.text_content() or ""

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("get_text", end_time - start_time)

            return text

        except Exception as e:
            error_message = f"Failed to get text from element: {selector_key}"
            error_context = create_error_context(self.page, selector, error_message)

            raise ElementNotFoundError(
                error_message,
                selector=selector,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e)},
            )

    def is_visible(self, selector_key: str, timeout: Optional[int] = None) -> bool:
        """Check if an element is visible.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            True if the element is visible, False otherwise.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Checking visibility of element {selector_key}: {selector}")

        try:
            self.page.wait_for_selector(
                selector, timeout=timeout or 5000, state="visible"
            )
            return True
        except Exception:
            return False

    def wait_for_navigation(self, timeout: Optional[int] = None) -> None:
        """Wait for navigation to complete.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            NavigationError: If navigation does not complete within the timeout.
        """
        logger.debug("Waiting for navigation to complete")

        start_time = time.time()

        try:
            self.page.wait_for_load_state(
                "networkidle", timeout=timeout or self.timeout
            )

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric(
                "wait_for_navigation", end_time - start_time
            )
        except PlaywrightTimeoutError as e:
            error_message = "Navigation did not complete within timeout"
            error_context = create_error_context(self.page, None, error_message)

            raise NavigationError(
                error_message,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e)},
            )

    def wait_for_url(self, url_pattern: str, timeout: Optional[int] = None) -> None:
        """Wait for URL to match a pattern.

        Args:
            url_pattern: URL pattern to match.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            NavigationError: If the URL does not match the pattern within the timeout.
        """
        logger.debug(f"Waiting for URL to match pattern: {url_pattern}")

        start_time = time.time()

        try:
            self.page.wait_for_url(url_pattern, timeout=timeout or self.timeout)

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("wait_for_url", end_time - start_time)
        except PlaywrightTimeoutError as e:
            error_message = f"URL did not match pattern {url_pattern} within timeout"
            error_context = create_error_context(self.page, None, error_message)

            raise NavigationError(
                error_message,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e)},
            )

    def get_elements_count(
        self, selector_key: str, timeout: Optional[int] = None
    ) -> int:
        """Get the number of elements matching a selector.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Number of elements matching the selector.
        """
        selector = get_selector(selector_key)
        logger.debug(f"Getting count of elements matching {selector_key}: {selector}")

        try:
            self.page.wait_for_selector(selector, timeout=timeout or self.timeout)
            return len(self.page.query_selector_all(selector))
        except Exception:
            return 0

    def take_screenshot(self, path: Optional[str] = None) -> str:
        """Take a screenshot.

        Args:
            path: Path to save the screenshot. If None, generates a path.

        Returns:
            Path to the screenshot.
        """
        if path is None:
            # Create screenshots directory if it doesn't exist
            screenshots_dir = os.path.join(os.getcwd(), "screenshots")
            os.makedirs(screenshots_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{self.__class__.__name__}.png"
            path = os.path.join(screenshots_dir, filename)

        logger.debug(f"Taking screenshot: {path}")
        self.page.screenshot(path=path)
        return path

    def upload_file(
        self, selector_key: str, file_path: str, timeout: Optional[int] = None
    ) -> None:
        """Upload a file.

        Args:
            selector_key: Key for the selector in the selector registry.
            file_path: Path to the file to upload.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            FormSubmissionError: If the file cannot be uploaded.
        """
        selector = get_selector(selector_key)
        logger.debug(
            f"Uploading file {file_path} to element {selector_key}: {selector}"
        )

        start_time = time.time()

        try:
            self.page.set_input_files(
                selector, file_path, timeout=timeout or self.timeout
            )

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("upload_file", end_time - start_time)
        except Exception as e:
            error_message = (
                f"Failed to upload file {file_path} to element: {selector_key}"
            )
            error_context = create_error_context(self.page, selector, error_message)

            raise FormSubmissionError(
                error_message,
                selector=selector,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e), "file_path": file_path},
            )

    def drag_and_drop(
        self,
        source_selector_key: str,
        target_selector_key: str,
        timeout: Optional[int] = None,
    ) -> None:
        """Drag and drop an element.

        Args:
            source_selector_key: Key for the source selector in the selector registry.
            target_selector_key: Key for the target selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            ElementNotFoundError: If the source or target element is not found.
            WebAutomationError: If the drag and drop operation fails.
        """
        source_selector = get_selector(source_selector_key)
        target_selector = get_selector(target_selector_key)
        logger.debug(
            f"Dragging element {source_selector_key}: {source_selector} "
            f"to element {target_selector_key}: {target_selector}"
        )

        start_time = time.time()

        try:
            # Wait for source and target elements
            source = self.page.wait_for_selector(
                source_selector, timeout=timeout or self.timeout
            )
            target = self.page.wait_for_selector(
                target_selector, timeout=timeout or self.timeout
            )

            if source is None:
                raise ElementNotFoundError(
                    f"Source element not found: {source_selector_key}",
                    selector=source_selector,
                    page_url=self.page.url,
                )

            if target is None:
                raise ElementNotFoundError(
                    f"Target element not found: {target_selector_key}",
                    selector=target_selector,
                    page_url=self.page.url,
                )

            # Get bounding boxes
            source_box = source.bounding_box()
            target_box = target.bounding_box()

            if source_box is None or target_box is None:
                raise WebAutomationError(
                    "Could not get bounding box for elements",
                    page_url=self.page.url,
                )

            # Perform drag and drop
            self.page.mouse.move(
                source_box["x"] + source_box["width"] / 2,
                source_box["y"] + source_box["height"] / 2,
            )
            self.page.mouse.down()
            self.page.mouse.move(
                target_box["x"] + target_box["width"] / 2,
                target_box["y"] + target_box["height"] / 2,
            )
            self.page.mouse.up()

            # Record performance metric
            end_time = time.time()
            self._record_performance_metric("drag_and_drop", end_time - start_time)

        except Exception as e:
            error_message = f"Failed to drag and drop from {source_selector_key} to {target_selector_key}"
            error_context = create_error_context(
                self.page, source_selector, error_message
            )

            raise WebAutomationError(
                error_message,
                selector=source_selector,
                page_url=self.page.url,
                screenshot_path=error_context["screenshot_path"],
                context={"original_error": str(e), "target_selector": target_selector},
            )
