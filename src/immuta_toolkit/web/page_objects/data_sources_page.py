"""Data sources page object for Immuta web automation."""

import time
from typing import Dict, List, Optional, Union

from playwright.sync_api import Page

from immuta_toolkit.models import DataSourceModel
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.page_objects.base_page import BasePage
from immuta_toolkit.web.selectors_compat import get_selector

logger = get_logger(__name__)


class DataSourcesPage(BasePage):
    """Data sources page object for Immuta web automation.

    This class provides functionality for managing data sources in Immuta.

    Attributes:
        page: Playwright page object.
        base_url: Base URL of the Immuta instance.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, base_url: str, timeout: int = 10000):
        """Initialize the data sources page object.

        Args:
            page: Playwright page object.
            base_url: Base URL of the Immuta instance.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, base_url, timeout)

    def navigate_to_data_sources(self) -> None:
        """Navigate to the data sources page."""
        logger.info("Navigating to data sources page")
        self.navigate("data-sources")
        self.wait_for_selector("data_sources.search_input")

    def create_data_source(self, data_source: Union[DataSourceModel, Dict]) -> Dict:
        """Create a new data source.

        Args:
            data_source: Data source model or dictionary.

        Returns:
            Dictionary with data source details.
        """
        logger.info(f"Creating data source {data_source.get('name', '')}")

        # Navigate to data sources page
        self.navigate_to_data_sources()

        # Click create data source button
        self.click("data_sources.create_button")

        # Fill data source form
        self._fill_data_source_form(data_source)

        # Submit form
        self.click("data_source_form.submit")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Data source {data_source.get('name', '')} created successfully")

        # Return data source details
        return {
            "name": data_source.get("name", ""),
            "description": data_source.get("description", ""),
            "handler_type": data_source.get("handler_type", ""),
        }

    def _fill_data_source_form(self, data_source: Union[DataSourceModel, Dict]) -> None:
        """Fill the data source form.

        Args:
            data_source: Data source model or dictionary.
        """
        # Get data source details
        name = data_source.get("name", "")
        description = data_source.get("description", "")
        handler_type = data_source.get("handler_type", "")

        # Fill basic information
        self.fill("data_source_form.name", name)
        self.fill("data_source_form.description", description)

        # Select handler type
        if handler_type:
            handler_selector = get_selector("data_source_form.handler_type")
            self.page.select_option(handler_selector, value=handler_type)

            # Wait for handler-specific fields to appear
            time.sleep(1)

            # Fill handler-specific fields based on handler type
            if handler_type == "snowflake":
                self._fill_snowflake_fields(data_source)
            elif handler_type == "postgres":
                self._fill_postgres_fields(data_source)
            # Add more handler types as needed

    def _fill_snowflake_fields(self, data_source: Union[DataSourceModel, Dict]) -> None:
        """Fill Snowflake-specific fields.

        Args:
            data_source: Data source model or dictionary.
        """
        # Fill Snowflake connection details
        self.fill("data_source_form.snowflake.account", data_source.get("account", ""))
        self.fill(
            "data_source_form.snowflake.warehouse", data_source.get("warehouse", "")
        )
        self.fill(
            "data_source_form.snowflake.database", data_source.get("database", "")
        )
        self.fill("data_source_form.snowflake.schema", data_source.get("schema", ""))
        self.fill(
            "data_source_form.snowflake.username", data_source.get("username", "")
        )
        self.fill(
            "data_source_form.snowflake.password", data_source.get("password", "")
        )

    def _fill_postgres_fields(self, data_source: Union[DataSourceModel, Dict]) -> None:
        """Fill PostgreSQL-specific fields.

        Args:
            data_source: Data source model or dictionary.
        """
        # Fill PostgreSQL connection details
        self.fill("data_source_form.postgres.host", data_source.get("host", ""))
        self.fill("data_source_form.postgres.port", data_source.get("port", ""))
        self.fill("data_source_form.postgres.database", data_source.get("database", ""))
        self.fill("data_source_form.postgres.schema", data_source.get("schema", ""))
        self.fill("data_source_form.postgres.username", data_source.get("username", ""))
        self.fill("data_source_form.postgres.password", data_source.get("password", ""))

    def search_data_source(self, name: str) -> Optional[Dict]:
        """Search for a data source by name.

        Args:
            name: Data source name.

        Returns:
            Dictionary with data source details if found, None otherwise.
        """
        logger.info(f"Searching for data source {name}")

        # Navigate to data sources page
        self.navigate_to_data_sources()

        # Search for data source
        self.fill("data_sources.search_input", name)

        # Wait for search results
        time.sleep(1)

        # Check if data source exists
        if self.get_elements_count("data_sources.data_source_row") == 0:
            logger.info(f"Data source {name} not found")
            return None

        # Get data source details
        data_source_name = self.get_text("data_sources.data_source_name")

        logger.info(f"Data source {name} found")

        return {
            "name": data_source_name,
        }

    def delete_data_source(self, name: str) -> bool:
        """Delete a data source by name.

        Args:
            name: Data source name.

        Returns:
            True if the data source was deleted, False otherwise.
        """
        logger.info(f"Deleting data source {name}")

        # Search for data source
        data_source = self.search_data_source(name)
        if not data_source:
            logger.warning(f"Data source {name} not found, cannot delete")
            return False

        # Click actions button
        self.click("data_sources.data_source_actions")

        # Click delete button
        self.click("data_sources.delete_data_source")

        # Confirm deletion
        self.click("dialog.confirm")

        # Wait for navigation to complete
        self.wait_for_navigation()

        logger.info(f"Data source {name} deleted successfully")

        return True
