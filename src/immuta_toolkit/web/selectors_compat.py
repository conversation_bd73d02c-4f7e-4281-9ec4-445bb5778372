"""Compatibility module for selectors.

This module provides backward compatibility for the old selectors.py module.
"""

import os
from typing import Dict, Optional

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.selectors import (
    LoginSelectors,
    UserSelectors,
    DataSourceSelectors,
    PolicySelectors,
    ProjectSelectors,
)

logger = get_logger(__name__)

# Default Immuta version
DEFAULT_VERSION = "2024.02"

# Selector registry by version
SELECTORS: Dict[str, Dict[str, str]] = {
    "2024.02": {
        # Login page
        "login.username": LoginSelectors.USERNAME_INPUT,
        "login.password": LoginSelectors.PASSWORD_INPUT,
        "login.submit": LoginSelectors.LOGIN_BUTTON,
        # Navigation
        "nav.users": f"a[href*='{UserSelectors.USERS_PAGE}']",
        "nav.data_sources": f"a[href*='{DataSourceSelectors.DATA_SOURCES_PAGE}']",
        "nav.policies": f"a[href*='{PolicySelectors.POLICIES_PAGE}']",
        "nav.projects": f"a[href*='{ProjectSelectors.PROJECTS_PAGE}']",
        # Users page
        "users.create_button": UserSelectors.CREATE_USER_BUTTON,
        "users.search_input": UserSelectors.USER_SEARCH_INPUT,
        "users.user_row": UserSelectors.USER_ITEM,
        "users.user_email": UserSelectors.USER_EMAIL,
        "users.user_name": UserSelectors.USER_NAME,
        "users.user_actions": UserSelectors.USER_ACTIONS_BUTTON,
        "users.edit_user": "button:has-text('Edit')",
        "users.delete_user": UserSelectors.USER_DELETE_BUTTON,
        # User form
        "user_form.email": UserSelectors.USER_FORM_EMAIL_INPUT,
        "user_form.name": UserSelectors.USER_FORM_NAME_INPUT,
        "user_form.role": UserSelectors.USER_FORM_ROLE_SELECT,
        "user_form.submit": UserSelectors.USER_FORM_SUBMIT_BUTTON,
        "user_form.cancel": "button:has-text('Cancel')",
        # Data sources page
        "data_sources.create_button": DataSourceSelectors.CREATE_DATA_SOURCE_BUTTON,
        "data_sources.search_input": DataSourceSelectors.DATA_SOURCE_SEARCH_INPUT,
        "data_sources.data_source_row": DataSourceSelectors.DATA_SOURCE_ITEM,
        "data_sources.data_source_name": DataSourceSelectors.DATA_SOURCE_NAME,
        "data_sources.data_source_actions": DataSourceSelectors.DATA_SOURCE_ACTIONS_BUTTON,
        "data_sources.edit_data_source": "button:has-text('Edit')",
        "data_sources.delete_data_source": DataSourceSelectors.DATA_SOURCE_DELETE_BUTTON,
        # Data source form
        "data_source_form.name": DataSourceSelectors.DATA_SOURCE_FORM_NAME_INPUT,
        "data_source_form.type": DataSourceSelectors.DATA_SOURCE_FORM_TYPE_SELECT,
        "data_source_form.submit": DataSourceSelectors.DATA_SOURCE_FORM_SUBMIT_BUTTON,
        "data_source_form.cancel": "button:has-text('Cancel')",
        # Policies page
        "policies.create_button": PolicySelectors.CREATE_POLICY_BUTTON,
        "policies.search_input": PolicySelectors.POLICY_SEARCH_INPUT,
        "policies.policy_row": PolicySelectors.POLICY_ITEM,
        "policies.policy_name": PolicySelectors.POLICY_NAME,
        "policies.policy_actions": PolicySelectors.POLICY_ACTIONS_BUTTON,
        "policies.edit_policy": "button:has-text('Edit')",
        "policies.delete_policy": PolicySelectors.POLICY_DELETE_BUTTON,
        # Policy form
        "policy_form.name": PolicySelectors.POLICY_FORM_NAME_INPUT,
        "policy_form.type": PolicySelectors.POLICY_FORM_TYPE_SELECT,
        "policy_form.submit": PolicySelectors.POLICY_FORM_SUBMIT_BUTTON,
        "policy_form.cancel": "button:has-text('Cancel')",
        # Projects page
        "projects.create_button": ProjectSelectors.CREATE_PROJECT_BUTTON,
        "projects.search_input": ProjectSelectors.PROJECT_SEARCH_INPUT,
        "projects.project_row": ProjectSelectors.PROJECT_ITEM,
        "projects.project_name": ProjectSelectors.PROJECT_NAME,
        "projects.project_actions": ProjectSelectors.PROJECT_ACTIONS_BUTTON,
        "projects.edit_project": "button:has-text('Edit')",
        "projects.delete_project": ProjectSelectors.PROJECT_DELETE_BUTTON,
        # Project form
        "project_form.name": ProjectSelectors.PROJECT_FORM_NAME_INPUT,
        "project_form.description": ProjectSelectors.PROJECT_FORM_DESCRIPTION_INPUT,
        "project_form.submit": ProjectSelectors.PROJECT_FORM_SUBMIT_BUTTON,
        "project_form.cancel": "button:has-text('Cancel')",
        # Confirmation dialogs
        "dialog.confirm": "button:has-text('Confirm')",
        "dialog.cancel": "button:has-text('Cancel')",
    },
}


def get_selector(selector_key: str, version: Optional[str] = None) -> str:
    """Get a selector by key and version.

    Args:
        selector_key: Key for the selector in the selector registry.
        version: Immuta version. If None, uses the version from the
            IMMUTA_VERSION environment variable or the default version.

    Returns:
        Selector string.

    Raises:
        ValueError: If the selector key is not found for the specified version.
    """
    # Get version from environment variable or use default
    version = version or os.getenv("IMMUTA_VERSION", DEFAULT_VERSION)

    # Check if version exists in registry
    if version not in SELECTORS:
        logger.warning(
            f"Version {version} not found in selector registry, using default"
        )
        version = DEFAULT_VERSION

    # Get selector for version
    selectors = SELECTORS[version]

    # Check if selector key exists
    if selector_key not in selectors:
        # Try to get the selector from the selector manager
        from immuta_toolkit.web.selectors.selector_manager import selector_manager

        selector_value = selector_manager.get_value(selector_key)
        if selector_value != selector_key:  # If the selector was found
            return selector_value
        raise ValueError(f"Selector key {selector_key} not found for version {version}")

    return selectors[selector_key]
