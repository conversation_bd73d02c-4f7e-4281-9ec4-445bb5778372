"""Dialog component for Immuta web automation."""

from typing import Optional

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import ElementNotFoundError, ElementNotVisibleError

logger = get_logger(__name__)


class DialogComponent(BaseComponent):
    """Dialog component for Immuta web automation.

    This component represents a dialog box in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the dialog component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "dialog.container", timeout)

    def confirm(self, timeout: Optional[int] = None) -> None:
        """Confirm the dialog.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            ElementNotFoundError: If the confirm button is not found.
            ElementNotVisibleError: If the dialog is not visible.
        """
        logger.info("Confirming dialog")
        
        if not self.is_visible(timeout=timeout or 5000):
            raise ElementNotVisibleError(
                "Dialog is not visible",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            self.click_child("dialog.confirm", timeout=timeout)
            self.wait_for_hidden(timeout=timeout)
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to confirm dialog",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def cancel(self, timeout: Optional[int] = None) -> None:
        """Cancel the dialog.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            ElementNotFoundError: If the cancel button is not found.
            ElementNotVisibleError: If the dialog is not visible.
        """
        logger.info("Canceling dialog")
        
        if not self.is_visible(timeout=timeout or 5000):
            raise ElementNotVisibleError(
                "Dialog is not visible",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            self.click_child("dialog.cancel", timeout=timeout)
            self.wait_for_hidden(timeout=timeout)
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to cancel dialog",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def get_title(self, timeout: Optional[int] = None) -> str:
        """Get the dialog title.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Dialog title.

        Raises:
            ElementNotFoundError: If the title element is not found.
            ElementNotVisibleError: If the dialog is not visible.
        """
        logger.debug("Getting dialog title")
        
        if not self.is_visible(timeout=timeout or 5000):
            raise ElementNotVisibleError(
                "Dialog is not visible",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            return self.get_child_text("dialog.title", timeout=timeout)
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to get dialog title",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def get_message(self, timeout: Optional[int] = None) -> str:
        """Get the dialog message.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Dialog message.

        Raises:
            ElementNotFoundError: If the message element is not found.
            ElementNotVisibleError: If the dialog is not visible.
        """
        logger.debug("Getting dialog message")
        
        if not self.is_visible(timeout=timeout or 5000):
            raise ElementNotVisibleError(
                "Dialog is not visible",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            return self.get_child_text("dialog.message", timeout=timeout)
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to get dialog message",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )
