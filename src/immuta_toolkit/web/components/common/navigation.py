"""Navigation component for Immuta web automation."""

from typing import Optional

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import NavigationError

logger = get_logger(__name__)


class NavigationComponent(BaseComponent):
    """Navigation component for Immuta web automation.

    This component represents the main navigation menu in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the navigation component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "nav.main", timeout)

    def navigate_to_users(self) -> None:
        """Navigate to the users page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to users page")
        try:
            self.click_child("nav.users")
            self.page.wait_for_url("**/users", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to users page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def navigate_to_data_sources(self) -> None:
        """Navigate to the data sources page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to data sources page")
        try:
            self.click_child("nav.data_sources")
            self.page.wait_for_url("**/data-sources", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to data sources page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def navigate_to_policies(self) -> None:
        """Navigate to the policies page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to policies page")
        try:
            self.click_child("nav.policies")
            self.page.wait_for_url("**/policies", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to policies page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def navigate_to_projects(self) -> None:
        """Navigate to the projects page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to projects page")
        try:
            self.click_child("nav.projects")
            self.page.wait_for_url("**/projects", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to projects page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def navigate_to_purposes(self) -> None:
        """Navigate to the purposes page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to purposes page")
        try:
            self.click_child("nav.purposes")
            self.page.wait_for_url("**/purposes", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to purposes page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def navigate_to_settings(self) -> None:
        """Navigate to the settings page.

        Raises:
            NavigationError: If navigation fails.
        """
        logger.info("Navigating to settings page")
        try:
            self.click_child("nav.settings")
            self.page.wait_for_url("**/settings", timeout=self.timeout)
        except Exception as e:
            raise NavigationError(
                "Failed to navigate to settings page",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def is_logged_in(self) -> bool:
        """Check if the user is logged in.

        Returns:
            True if the user is logged in, False otherwise.
        """
        return self.is_visible(timeout=5000)
