"""User card component for Immuta web automation."""

from typing import Dict, Optional, Any

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)

logger = get_logger(__name__)


class UserCardComponent(BaseComponent):
    """User card component for Immuta web automation.

    This component represents a user card in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, user_email: str, timeout: int = 10000):
        """Initialize the user card component.

        Args:
            page: Playwright page object.
            user_email: Email of the user.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "users.user_row", timeout)
        self.user_email = user_email

    def find(self) -> bool:
        """Find the user card by email.

        Returns:
            True if the user card is found, False otherwise.
        """
        logger.debug(f"Finding user card for {self.user_email}")
        
        try:
            # Wait for the user list to load
            self.page.wait_for_selector(self.root_selector, timeout=self.timeout)
            
            # Find all user rows
            user_rows = self.page.query_selector_all(self.root_selector)
            
            # Find the row with the matching email
            for row in user_rows:
                email_element = row.query_selector("td.user-email")
                if email_element and email_element.text_content() and self.user_email in email_element.text_content():
                    # Update the root selector to target this specific row
                    self.root_selector = f"{self.root_selector}:has-text('{self.user_email}')"
                    return True
                    
            return False
            
        except Exception:
            return False

    def get_details(self) -> Dict[str, str]:
        """Get user details from the card.

        Returns:
            Dictionary with user details.

        Raises:
            ElementNotFoundError: If the user card is not found.
        """
        logger.debug(f"Getting details for user {self.user_email}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"User card not found for {self.user_email}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the user row
            row = self.page.query_selector(self.root_selector)
            
            if not row:
                raise ElementNotFoundError(
                    f"User row not found for {self.user_email}",
                    selector=self.root_selector,
                    page_url=self.page.url,
                )
                
            # Get user details
            email = row.query_selector("td.user-email")
            name = row.query_selector("td.user-name")
            role = row.query_selector("td.user-role")
            
            return {
                "email": email.text_content().strip() if email else "",
                "name": name.text_content().strip() if name else "",
                "role": role.text_content().strip() if role else "",
            }
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get details for user {self.user_email}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def click_actions(self) -> None:
        """Click the actions button for the user.

        Raises:
            ElementNotFoundError: If the actions button is not found.
        """
        logger.debug(f"Clicking actions button for user {self.user_email}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"User card not found for {self.user_email}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the actions button
            row = self.page.query_selector(self.root_selector)
            actions_button = row.query_selector("button.user-actions")
            
            if not actions_button:
                raise ElementNotFoundError(
                    f"Actions button not found for user {self.user_email}",
                    selector=f"{self.root_selector} button.user-actions",
                    page_url=self.page.url,
                )
                
            # Click the actions button
            actions_button.click()
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to click actions button for user {self.user_email}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def edit(self) -> None:
        """Click the edit button for the user.

        Raises:
            ElementNotFoundError: If the edit button is not found.
        """
        logger.debug(f"Editing user {self.user_email}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the edit button
            self.page.click("users.edit_user")
            
            # Wait for the form to load
            self.page.wait_for_selector("user_form", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to edit user {self.user_email}",
                selector="users.edit_user",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def delete(self) -> None:
        """Click the delete button for the user.

        Raises:
            ElementNotFoundError: If the delete button is not found.
        """
        logger.debug(f"Deleting user {self.user_email}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the delete button
            self.page.click("users.delete_user")
            
            # Wait for the confirmation dialog
            self.page.wait_for_selector("dialog.confirm", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to delete user {self.user_email}",
                selector="users.delete_user",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )
