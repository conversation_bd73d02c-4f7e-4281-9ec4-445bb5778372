"""User form component for Immuta web automation."""

from typing import Dict, Optional, Any

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    FormSubmissionError,
)
from immuta_toolkit.models import UserModel

logger = get_logger(__name__)


class UserFormComponent(BaseComponent):
    """User form component for Immuta web automation.

    This component represents the user creation/edit form in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the user form component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "user_form", timeout)

    def fill_form(self, user: UserModel) -> None:
        """Fill the user form with the given user data.

        Args:
            user: User model with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled.
        """
        logger.info(f"Filling user form for {user.email}")
        
        try:
            # Wait for the form to be visible
            self.wait_for_visible()
            
            # Fill email
            if user.email:
                self.click_child("user_form.email")
                email_element = self.get_child_element("user_form.email")
                email_element.fill(user.email)
            
            # Fill name
            if user.name:
                self.click_child("user_form.name")
                name_element = self.get_child_element("user_form.name")
                name_element.fill(user.name)
            
            # Select role
            if user.role:
                role_element = self.get_child_element("user_form.role")
                role_element.select_option(user.role)
            
            # Fill groups if provided
            if hasattr(user, "groups") and user.groups:
                groups_element = self.get_child_element("user_form.groups")
                groups_element.fill(", ".join(user.groups))
                
        except Exception as e:
            raise FormSubmissionError(
                f"Failed to fill user form for {user.email}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e), "user": user.dict()},
            )

    def submit(self) -> None:
        """Submit the user form.

        Raises:
            ElementNotFoundError: If the submit button is not found.
            FormSubmissionError: If the form cannot be submitted.
        """
        logger.info("Submitting user form")
        
        try:
            # Click the submit button
            self.click_child("user_form.submit")
            
            # Wait for the form to be submitted
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)
            
        except Exception as e:
            raise FormSubmissionError(
                "Failed to submit user form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def cancel(self) -> None:
        """Cancel the user form.

        Raises:
            ElementNotFoundError: If the cancel button is not found.
        """
        logger.info("Canceling user form")
        
        try:
            # Click the cancel button
            self.click_child("user_form.cancel")
            
            # Wait for the form to be closed
            self.wait_for_hidden()
            
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to cancel user form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def create_user(self, user: UserModel) -> None:
        """Create a user by filling and submitting the form.

        Args:
            user: User model with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled or submitted.
        """
        logger.info(f"Creating user {user.email}")
        
        # Fill the form
        self.fill_form(user)
        
        # Submit the form
        self.submit()
