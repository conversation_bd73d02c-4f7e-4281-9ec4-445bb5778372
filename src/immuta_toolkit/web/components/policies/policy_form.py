"""Policy form component for Immuta web automation."""

from typing import Dict, Optional, Any, Union, List

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    FormSubmissionError,
)
from immuta_toolkit.models import PolicyModel

logger = get_logger(__name__)


class PolicyFormComponent(BaseComponent):
    """Policy form component for Immuta web automation.

    This component represents the policy creation/edit form in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the policy form component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "policy_form", timeout)

    def fill_form(self, policy: Union[PolicyModel, Dict[str, Any]]) -> None:
        """Fill the policy form with the given data.

        Args:
            policy: Policy model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled.
        """
        # Convert dictionary to model if needed
        if isinstance(policy, dict):
            policy_dict = policy
            policy = PolicyModel(**policy_dict)
        
        logger.info(f"Filling policy form for {policy.name}")
        
        try:
            # Wait for the form to be visible
            self.wait_for_visible()
            
            # Fill name
            if policy.name:
                self.click_child("policy_form.name")
                name_element = self.get_child_element("policy_form.name")
                name_element.fill(policy.name)
            
            # Fill description if provided
            if hasattr(policy, "description") and policy.description:
                self.click_child("policy_form.description")
                description_element = self.get_child_element("policy_form.description")
                description_element.fill(policy.description)
            
            # Select policy type
            if hasattr(policy, "policy_type") and policy.policy_type:
                policy_type_element = self.get_child_element("policy_form.policy_type")
                policy_type_element.select_option(policy.policy_type)
                
                # Fill policy type-specific fields
                if policy.policy_type.lower() == "row-level":
                    self._fill_row_level_fields(policy)
                elif policy.policy_type.lower() == "column-level":
                    self._fill_column_level_fields(policy)
                elif policy.policy_type.lower() == "purpose-based":
                    self._fill_purpose_based_fields(policy)
                
        except Exception as e:
            raise FormSubmissionError(
                f"Failed to fill policy form for {policy.name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e), "policy": policy.dict()},
            )

    def _fill_row_level_fields(self, policy: PolicyModel) -> None:
        """Fill row-level policy specific fields in the form.

        Args:
            policy: Policy model with row-level configuration.

        Raises:
            ElementNotFoundError: If a form field is not found.
        """
        logger.debug("Filling row-level policy fields")
        
        # Wait for row-level fields to appear
        self.page.wait_for_selector("policy_form.row_level.condition", timeout=self.timeout)
        
        # Fill condition
        if hasattr(policy, "condition") and policy.condition:
            self.click_child("policy_form.row_level.condition")
            condition_element = self.get_child_element("policy_form.row_level.condition")
            condition_element.fill(policy.condition)
            
        # Add data sources
        if hasattr(policy, "data_sources") and policy.data_sources:
            self._add_data_sources(policy.data_sources)

    def _fill_column_level_fields(self, policy: PolicyModel) -> None:
        """Fill column-level policy specific fields in the form.

        Args:
            policy: Policy model with column-level configuration.

        Raises:
            ElementNotFoundError: If a form field is not found.
        """
        logger.debug("Filling column-level policy fields")
        
        # Add columns
        if hasattr(policy, "columns") and policy.columns:
            for column in policy.columns:
                # Click add column button
                self.click_child("policy_form.add_column_button")
                
                # Wait for column name field to appear
                self.page.wait_for_selector("policy_form.column_name", timeout=self.timeout)
                
                # Fill column name
                column_name_element = self.get_child_element("policy_form.column_name")
                column_name_element.fill(column)
                
                # Click add button
                self.click_child("policy_form.column_add")
                
        # Add data sources
        if hasattr(policy, "data_sources") and policy.data_sources:
            self._add_data_sources(policy.data_sources)

    def _fill_purpose_based_fields(self, policy: PolicyModel) -> None:
        """Fill purpose-based policy specific fields in the form.

        Args:
            policy: Policy model with purpose-based configuration.

        Raises:
            ElementNotFoundError: If a form field is not found.
        """
        logger.debug("Filling purpose-based policy fields")
        
        # Add purposes
        if hasattr(policy, "purposes") and policy.purposes:
            for purpose in policy.purposes:
                # Click add purpose button
                self.click_child("policy_form.add_purpose_button")
                
                # Wait for purpose search field to appear
                self.page.wait_for_selector("policy_form.purpose_search", timeout=self.timeout)
                
                # Fill purpose search
                purpose_search_element = self.get_child_element("policy_form.purpose_search")
                purpose_search_element.fill(purpose)
                
                # Wait for purpose to appear in the list
                self.page.wait_for_selector(
                    f"policy_form.purpose_select:has-text('{purpose}')",
                    timeout=self.timeout,
                )
                
                # Click the purpose
                self.page.click(f"policy_form.purpose_select:has-text('{purpose}')")
                
        # Add data sources
        if hasattr(policy, "data_sources") and policy.data_sources:
            self._add_data_sources(policy.data_sources)

    def _add_data_sources(self, data_sources: List[str]) -> None:
        """Add data sources to the policy.

        Args:
            data_sources: List of data source names to add.

        Raises:
            ElementNotFoundError: If a data source cannot be found.
        """
        logger.debug(f"Adding data sources: {data_sources}")
        
        for data_source in data_sources:
            # Click add data source button
            self.click_child("policy_form.add_data_source_button")
            
            # Wait for data source search field to appear
            self.page.wait_for_selector("policy_form.data_source_search", timeout=self.timeout)
            
            # Fill data source search
            data_source_search_element = self.get_child_element("policy_form.data_source_search")
            data_source_search_element.fill(data_source)
            
            # Wait for data source to appear in the list
            self.page.wait_for_selector(
                f"policy_form.data_source_select:has-text('{data_source}')",
                timeout=self.timeout,
            )
            
            # Click the data source
            self.page.click(f"policy_form.data_source_select:has-text('{data_source}')")

    def submit(self) -> None:
        """Submit the policy form.

        Raises:
            ElementNotFoundError: If the submit button is not found.
            FormSubmissionError: If the form cannot be submitted.
        """
        logger.info("Submitting policy form")
        
        try:
            # Click the submit button
            self.click_child("policy_form.submit")
            
            # Wait for the form to be submitted
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)
            
        except Exception as e:
            raise FormSubmissionError(
                "Failed to submit policy form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def cancel(self) -> None:
        """Cancel the policy form.

        Raises:
            ElementNotFoundError: If the cancel button is not found.
        """
        logger.info("Canceling policy form")
        
        try:
            # Click the cancel button
            self.click_child("policy_form.cancel")
            
            # Wait for the form to be closed
            self.wait_for_hidden()
            
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to cancel policy form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def create_policy(self, policy: Union[PolicyModel, Dict[str, Any]]) -> None:
        """Create a policy by filling and submitting the form.

        Args:
            policy: Policy model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled or submitted.
        """
        # Convert dictionary to model if needed
        if isinstance(policy, dict):
            policy_name = policy.get("name", "unknown")
        else:
            policy_name = policy.name
            
        logger.info(f"Creating policy {policy_name}")
        
        # Fill the form
        self.fill_form(policy)
        
        # Submit the form
        self.submit()
