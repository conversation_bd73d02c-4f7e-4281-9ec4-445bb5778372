"""Policy card component for Immuta web automation."""

from typing import Dict, Optional, Any

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)

logger = get_logger(__name__)


class PolicyCardComponent(BaseComponent):
    """Policy card component for Immuta web automation.

    This component represents a policy card in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
        policy_name: Name of the policy.
    """

    def __init__(self, page: Page, policy_name: str, timeout: int = 10000):
        """Initialize the policy card component.

        Args:
            page: Playwright page object.
            policy_name: Name of the policy.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "policies.policy_row", timeout)
        self.policy_name = policy_name

    def find(self) -> bool:
        """Find the policy card by name.

        Returns:
            True if the policy card is found, False otherwise.
        """
        logger.debug(f"Finding policy card for {self.policy_name}")
        
        try:
            # Wait for the policy list to load
            self.page.wait_for_selector(self.root_selector, timeout=self.timeout)
            
            # Find all policy rows
            policy_rows = self.page.query_selector_all(self.root_selector)
            
            # Find the row with the matching name
            for row in policy_rows:
                name_element = row.query_selector("td.policy-name")
                if name_element and name_element.text_content() and self.policy_name in name_element.text_content():
                    # Update the root selector to target this specific row
                    self.root_selector = f"{self.root_selector}:has-text('{self.policy_name}')"
                    return True
                    
            return False
            
        except Exception:
            return False

    def get_details(self) -> Dict[str, str]:
        """Get policy details from the card.

        Returns:
            Dictionary with policy details.

        Raises:
            ElementNotFoundError: If the policy card is not found.
        """
        logger.debug(f"Getting details for policy {self.policy_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Policy card not found for {self.policy_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the policy row
            row = self.page.query_selector(self.root_selector)
            
            if not row:
                raise ElementNotFoundError(
                    f"Policy row not found for {self.policy_name}",
                    selector=self.root_selector,
                    page_url=self.page.url,
                )
                
            # Get policy details
            name = row.query_selector("td.policy-name")
            type_element = row.query_selector("td.policy-type")
            
            return {
                "name": name.text_content().strip() if name else "",
                "type": type_element.text_content().strip() if type_element else "",
            }
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get details for policy {self.policy_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def click_actions(self) -> None:
        """Click the actions button for the policy.

        Raises:
            ElementNotFoundError: If the actions button is not found.
        """
        logger.debug(f"Clicking actions button for policy {self.policy_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Policy card not found for {self.policy_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the actions button
            row = self.page.query_selector(self.root_selector)
            actions_button = row.query_selector("button.policy-actions")
            
            if not actions_button:
                raise ElementNotFoundError(
                    f"Actions button not found for policy {self.policy_name}",
                    selector=f"{self.root_selector} button.policy-actions",
                    page_url=self.page.url,
                )
                
            # Click the actions button
            actions_button.click()
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to click actions button for policy {self.policy_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def edit(self) -> None:
        """Click the edit button for the policy.

        Raises:
            ElementNotFoundError: If the edit button is not found.
        """
        logger.debug(f"Editing policy {self.policy_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the edit button
            self.page.click("policies.edit_policy")
            
            # Wait for the form to load
            self.page.wait_for_selector("policy_form", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to edit policy {self.policy_name}",
                selector="policies.edit_policy",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def delete(self) -> None:
        """Click the delete button for the policy.

        Raises:
            ElementNotFoundError: If the delete button is not found.
        """
        logger.debug(f"Deleting policy {self.policy_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the delete button
            self.page.click("policies.delete_policy")
            
            # Wait for the confirmation dialog
            self.page.wait_for_selector("dialog.confirm", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to delete policy {self.policy_name}",
                selector="policies.delete_policy",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )
