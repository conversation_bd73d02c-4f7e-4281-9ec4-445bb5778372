"""Project card component for Immuta web automation."""

from typing import Dict, Optional, Any

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)

logger = get_logger(__name__)


class ProjectCardComponent(BaseComponent):
    """Project card component for Immuta web automation.

    This component represents a project card in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
        project_name: Name of the project.
    """

    def __init__(self, page: Page, project_name: str, timeout: int = 10000):
        """Initialize the project card component.

        Args:
            page: Playwright page object.
            project_name: Name of the project.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "projects.project_row", timeout)
        self.project_name = project_name

    def find(self) -> bool:
        """Find the project card by name.

        Returns:
            True if the project card is found, False otherwise.
        """
        logger.debug(f"Finding project card for {self.project_name}")
        
        try:
            # Wait for the project list to load
            self.page.wait_for_selector(self.root_selector, timeout=self.timeout)
            
            # Find all project rows
            project_rows = self.page.query_selector_all(self.root_selector)
            
            # Find the row with the matching name
            for row in project_rows:
                name_element = row.query_selector("td.project-name")
                if name_element and name_element.text_content() and self.project_name in name_element.text_content():
                    # Update the root selector to target this specific row
                    self.root_selector = f"{self.root_selector}:has-text('{self.project_name}')"
                    return True
                    
            return False
            
        except Exception:
            return False

    def get_details(self) -> Dict[str, str]:
        """Get project details from the card.

        Returns:
            Dictionary with project details.

        Raises:
            ElementNotFoundError: If the project card is not found.
        """
        logger.debug(f"Getting details for project {self.project_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Project card not found for {self.project_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the project row
            row = self.page.query_selector(self.root_selector)
            
            if not row:
                raise ElementNotFoundError(
                    f"Project row not found for {self.project_name}",
                    selector=self.root_selector,
                    page_url=self.page.url,
                )
                
            # Get project details
            name = row.query_selector("td.project-name")
            owner = row.query_selector("td.project-owner")
            
            return {
                "name": name.text_content().strip() if name else "",
                "owner": owner.text_content().strip() if owner else "",
            }
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get details for project {self.project_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def click_actions(self) -> None:
        """Click the actions button for the project.

        Raises:
            ElementNotFoundError: If the actions button is not found.
        """
        logger.debug(f"Clicking actions button for project {self.project_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Project card not found for {self.project_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the actions button
            row = self.page.query_selector(self.root_selector)
            actions_button = row.query_selector("button.project-actions")
            
            if not actions_button:
                raise ElementNotFoundError(
                    f"Actions button not found for project {self.project_name}",
                    selector=f"{self.root_selector} button.project-actions",
                    page_url=self.page.url,
                )
                
            # Click the actions button
            actions_button.click()
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to click actions button for project {self.project_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def edit(self) -> None:
        """Click the edit button for the project.

        Raises:
            ElementNotFoundError: If the edit button is not found.
        """
        logger.debug(f"Editing project {self.project_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the edit button
            self.page.click("projects.edit_project")
            
            # Wait for the form to load
            self.page.wait_for_selector("project_form", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to edit project {self.project_name}",
                selector="projects.edit_project",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def delete(self) -> None:
        """Click the delete button for the project.

        Raises:
            ElementNotFoundError: If the delete button is not found.
        """
        logger.debug(f"Deleting project {self.project_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the delete button
            self.page.click("projects.delete_project")
            
            # Wait for the confirmation dialog
            self.page.wait_for_selector("dialog.confirm", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to delete project {self.project_name}",
                selector="projects.delete_project",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def view_members(self) -> None:
        """Click the view members button for the project.

        Raises:
            ElementNotFoundError: If the view members button is not found.
        """
        logger.debug(f"Viewing members for project {self.project_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the view members button
            self.page.click("projects.view_members")
            
            # Wait for the members list to load
            self.page.wait_for_selector("projects.members_list", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to view members for project {self.project_name}",
                selector="projects.view_members",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )
