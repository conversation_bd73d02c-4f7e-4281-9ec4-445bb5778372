"""Project form component for Immuta web automation."""

from typing import Dict, Optional, Any, Union, List

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    FormSubmissionError,
)
from immuta_toolkit.models import ProjectModel

logger = get_logger(__name__)


class ProjectFormComponent(BaseComponent):
    """Project form component for Immuta web automation.

    This component represents the project creation/edit form in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the project form component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "project_form", timeout)

    def fill_form(self, project: Union[ProjectModel, Dict[str, Any]]) -> None:
        """Fill the project form with the given data.

        Args:
            project: Project model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled.
        """
        # Convert dictionary to model if needed
        if isinstance(project, dict):
            project_dict = project
            project = ProjectModel(**project_dict)
        
        logger.info(f"Filling project form for {project.name}")
        
        try:
            # Wait for the form to be visible
            self.wait_for_visible()
            
            # Fill name
            if project.name:
                self.click_child("project_form.name")
                name_element = self.get_child_element("project_form.name")
                name_element.fill(project.name)
            
            # Fill description if provided
            if hasattr(project, "description") and project.description:
                self.click_child("project_form.description")
                description_element = self.get_child_element("project_form.description")
                description_element.fill(project.description)
            
            # Add data sources if provided
            if hasattr(project, "data_sources") and project.data_sources:
                self._add_data_sources(project.data_sources)
                
            # Add members if provided
            if hasattr(project, "members") and project.members:
                self._add_members(project.members)
                
        except Exception as e:
            raise FormSubmissionError(
                f"Failed to fill project form for {project.name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e), "project": project.dict()},
            )

    def _add_data_sources(self, data_sources: List[str]) -> None:
        """Add data sources to the project.

        Args:
            data_sources: List of data source names to add.

        Raises:
            ElementNotFoundError: If a data source cannot be found.
        """
        logger.debug(f"Adding data sources: {data_sources}")
        
        for data_source in data_sources:
            # Click add data source button
            self.click_child("project_form.add_data_source_button")
            
            # Wait for data source search field to appear
            self.page.wait_for_selector("project_form.data_source_search", timeout=self.timeout)
            
            # Fill data source search
            data_source_search_element = self.get_child_element("project_form.data_source_search")
            data_source_search_element.fill(data_source)
            
            # Wait for data source to appear in the list
            self.page.wait_for_selector(
                f"project_form.data_source_select:has-text('{data_source}')",
                timeout=self.timeout,
            )
            
            # Click the data source
            self.page.click(f"project_form.data_source_select:has-text('{data_source}')")

    def _add_members(self, members: List[Dict[str, str]]) -> None:
        """Add members to the project.

        Args:
            members: List of member dictionaries with email and role.

        Raises:
            ElementNotFoundError: If a member cannot be added.
        """
        logger.debug(f"Adding members: {members}")
        
        for member in members:
            email = member.get("email")
            role = member.get("role", "Viewer")
            
            if not email:
                continue
                
            # Click add member button
            self.click_child("project_form.add_member_button")
            
            # Wait for member search field to appear
            self.page.wait_for_selector("project_form.member_search", timeout=self.timeout)
            
            # Fill member search
            member_search_element = self.get_child_element("project_form.member_search")
            member_search_element.fill(email)
            
            # Wait for member to appear in the list
            self.page.wait_for_selector(
                f"project_form.member_select:has-text('{email}')",
                timeout=self.timeout,
            )
            
            # Click the member
            self.page.click(f"project_form.member_select:has-text('{email}')")
            
            # Select role
            role_element = self.get_child_element("project_form.member_role")
            role_element.select_option(role)
            
            # Click add button
            self.click_child("project_form.member_add")

    def submit(self) -> None:
        """Submit the project form.

        Raises:
            ElementNotFoundError: If the submit button is not found.
            FormSubmissionError: If the form cannot be submitted.
        """
        logger.info("Submitting project form")
        
        try:
            # Click the submit button
            self.click_child("project_form.submit")
            
            # Wait for the form to be submitted
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)
            
        except Exception as e:
            raise FormSubmissionError(
                "Failed to submit project form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def cancel(self) -> None:
        """Cancel the project form.

        Raises:
            ElementNotFoundError: If the cancel button is not found.
        """
        logger.info("Canceling project form")
        
        try:
            # Click the cancel button
            self.click_child("project_form.cancel")
            
            # Wait for the form to be closed
            self.wait_for_hidden()
            
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to cancel project form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def create_project(self, project: Union[ProjectModel, Dict[str, Any]]) -> None:
        """Create a project by filling and submitting the form.

        Args:
            project: Project model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled or submitted.
        """
        # Convert dictionary to model if needed
        if isinstance(project, dict):
            project_name = project.get("name", "unknown")
        else:
            project_name = project.name
            
        logger.info(f"Creating project {project_name}")
        
        # Fill the form
        self.fill_form(project)
        
        # Submit the form
        self.submit()
