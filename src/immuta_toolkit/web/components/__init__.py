"""Components for Immuta web automation."""

from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.components.common import NavigationComponent, DialogComponent
from immuta_toolkit.web.components.data_sources import (
    DataSourceCardComponent,
    DataSourceFormComponent,
)
from immuta_toolkit.web.components.policies import (
    PolicyCardComponent,
    PolicyFormComponent,
)
from immuta_toolkit.web.components.projects import (
    ProjectCardComponent,
    ProjectFormComponent,
)
from immuta_toolkit.web.components.users import UserCardComponent, UserFormComponent

__all__ = [
    "BaseComponent",
    "NavigationComponent",
    "DialogComponent",
    "DataSourceCardComponent",
    "DataSourceFormComponent",
    "PolicyCardComponent",
    "PolicyFormComponent",
    "ProjectCardComponent",
    "ProjectFormComponent",
    "UserCardComponent",
    "UserFormComponent",
]
