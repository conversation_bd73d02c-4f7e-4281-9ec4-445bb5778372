"""Data source form component for Immuta web automation."""

from typing import Dict, Optional, Any, Union

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    FormSubmissionError,
)
from immuta_toolkit.models import DataSourceModel

logger = get_logger(__name__)


class DataSourceFormComponent(BaseComponent):
    """Data source form component for Immuta web automation.

    This component represents the data source creation/edit form in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 10000):
        """Initialize the data source form component.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "data_source_form", timeout)

    def fill_form(self, data_source: Union[DataSourceModel, Dict[str, Any]]) -> None:
        """Fill the data source form with the given data.

        Args:
            data_source: Data source model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled.
        """
        # Convert dictionary to model if needed
        if isinstance(data_source, dict):
            data_source_dict = data_source
            data_source = DataSourceModel(**data_source_dict)
        
        logger.info(f"Filling data source form for {data_source.name}")
        
        try:
            # Wait for the form to be visible
            self.wait_for_visible()
            
            # Fill name
            if data_source.name:
                self.click_child("data_source_form.name")
                name_element = self.get_child_element("data_source_form.name")
                name_element.fill(data_source.name)
            
            # Fill description if provided
            if hasattr(data_source, "description") and data_source.description:
                self.click_child("data_source_form.description")
                description_element = self.get_child_element("data_source_form.description")
                description_element.fill(data_source.description)
            
            # Select handler type
            if hasattr(data_source, "handler_type") and data_source.handler_type:
                handler_type_element = self.get_child_element("data_source_form.handler_type")
                handler_type_element.select_option(data_source.handler_type)
                
                # Fill handler-specific fields
                if data_source.handler_type.lower() == "snowflake":
                    self._fill_snowflake_fields(data_source)
                elif data_source.handler_type.lower() == "postgresql":
                    self._fill_postgres_fields(data_source)
                
        except Exception as e:
            raise FormSubmissionError(
                f"Failed to fill data source form for {data_source.name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e), "data_source": data_source.dict()},
            )

    def _fill_snowflake_fields(self, data_source: DataSourceModel) -> None:
        """Fill Snowflake-specific fields in the form.

        Args:
            data_source: Data source model with Snowflake configuration.

        Raises:
            ElementNotFoundError: If a form field is not found.
        """
        logger.debug("Filling Snowflake fields")
        
        # Wait for Snowflake fields to appear
        self.page.wait_for_selector("data_source_form.snowflake.account", timeout=self.timeout)
        
        # Fill Snowflake fields
        if hasattr(data_source, "account") and data_source.account:
            self.click_child("data_source_form.snowflake.account")
            account_element = self.get_child_element("data_source_form.snowflake.account")
            account_element.fill(data_source.account)
            
        if hasattr(data_source, "warehouse") and data_source.warehouse:
            self.click_child("data_source_form.snowflake.warehouse")
            warehouse_element = self.get_child_element("data_source_form.snowflake.warehouse")
            warehouse_element.fill(data_source.warehouse)
            
        if hasattr(data_source, "database") and data_source.database:
            self.click_child("data_source_form.snowflake.database")
            database_element = self.get_child_element("data_source_form.snowflake.database")
            database_element.fill(data_source.database)
            
        if hasattr(data_source, "schema") and data_source.schema:
            self.click_child("data_source_form.snowflake.schema")
            schema_element = self.get_child_element("data_source_form.snowflake.schema")
            schema_element.fill(data_source.schema)
            
        if hasattr(data_source, "username") and data_source.username:
            self.click_child("data_source_form.snowflake.username")
            username_element = self.get_child_element("data_source_form.snowflake.username")
            username_element.fill(data_source.username)
            
        if hasattr(data_source, "password") and data_source.password:
            self.click_child("data_source_form.snowflake.password")
            password_element = self.get_child_element("data_source_form.snowflake.password")
            password_element.fill(data_source.password)

    def _fill_postgres_fields(self, data_source: DataSourceModel) -> None:
        """Fill PostgreSQL-specific fields in the form.

        Args:
            data_source: Data source model with PostgreSQL configuration.

        Raises:
            ElementNotFoundError: If a form field is not found.
        """
        logger.debug("Filling PostgreSQL fields")
        
        # Wait for PostgreSQL fields to appear
        self.page.wait_for_selector("data_source_form.postgres.host", timeout=self.timeout)
        
        # Fill PostgreSQL fields
        if hasattr(data_source, "host") and data_source.host:
            self.click_child("data_source_form.postgres.host")
            host_element = self.get_child_element("data_source_form.postgres.host")
            host_element.fill(data_source.host)
            
        if hasattr(data_source, "port") and data_source.port:
            self.click_child("data_source_form.postgres.port")
            port_element = self.get_child_element("data_source_form.postgres.port")
            port_element.fill(str(data_source.port))
            
        if hasattr(data_source, "database") and data_source.database:
            self.click_child("data_source_form.postgres.database")
            database_element = self.get_child_element("data_source_form.postgres.database")
            database_element.fill(data_source.database)
            
        if hasattr(data_source, "schema") and data_source.schema:
            self.click_child("data_source_form.postgres.schema")
            schema_element = self.get_child_element("data_source_form.postgres.schema")
            schema_element.fill(data_source.schema)
            
        if hasattr(data_source, "username") and data_source.username:
            self.click_child("data_source_form.postgres.username")
            username_element = self.get_child_element("data_source_form.postgres.username")
            username_element.fill(data_source.username)
            
        if hasattr(data_source, "password") and data_source.password:
            self.click_child("data_source_form.postgres.password")
            password_element = self.get_child_element("data_source_form.postgres.password")
            password_element.fill(data_source.password)

    def submit(self) -> None:
        """Submit the data source form.

        Raises:
            ElementNotFoundError: If the submit button is not found.
            FormSubmissionError: If the form cannot be submitted.
        """
        logger.info("Submitting data source form")
        
        try:
            # Click the submit button
            self.click_child("data_source_form.submit")
            
            # Wait for the form to be submitted
            self.page.wait_for_load_state("networkidle", timeout=self.timeout)
            
        except Exception as e:
            raise FormSubmissionError(
                "Failed to submit data source form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def cancel(self) -> None:
        """Cancel the data source form.

        Raises:
            ElementNotFoundError: If the cancel button is not found.
        """
        logger.info("Canceling data source form")
        
        try:
            # Click the cancel button
            self.click_child("data_source_form.cancel")
            
            # Wait for the form to be closed
            self.wait_for_hidden()
            
        except Exception as e:
            raise ElementNotFoundError(
                "Failed to cancel data source form",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def create_data_source(self, data_source: Union[DataSourceModel, Dict[str, Any]]) -> None:
        """Create a data source by filling and submitting the form.

        Args:
            data_source: Data source model or dictionary with data to fill.

        Raises:
            ElementNotFoundError: If a form field is not found.
            FormSubmissionError: If the form cannot be filled or submitted.
        """
        # Convert dictionary to model if needed
        if isinstance(data_source, dict):
            data_source_name = data_source.get("name", "unknown")
        else:
            data_source_name = data_source.name
            
        logger.info(f"Creating data source {data_source_name}")
        
        # Fill the form
        self.fill_form(data_source)
        
        # Submit the form
        self.submit()
