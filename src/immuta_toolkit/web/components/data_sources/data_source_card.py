"""Data source card component for Immuta web automation."""

from typing import Dict, Optional, Any

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.components.base_component import BaseComponent
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)

logger = get_logger(__name__)


class DataSourceCardComponent(BaseComponent):
    """Data source card component for Immuta web automation.

    This component represents a data source card in the Immuta UI.

    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
        data_source_name: Name of the data source.
    """

    def __init__(self, page: Page, data_source_name: str, timeout: int = 10000):
        """Initialize the data source card component.

        Args:
            page: Playwright page object.
            data_source_name: Name of the data source.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, "data_sources.data_source_row", timeout)
        self.data_source_name = data_source_name

    def find(self) -> bool:
        """Find the data source card by name.

        Returns:
            True if the data source card is found, False otherwise.
        """
        logger.debug(f"Finding data source card for {self.data_source_name}")
        
        try:
            # Wait for the data source list to load
            self.page.wait_for_selector(self.root_selector, timeout=self.timeout)
            
            # Find all data source rows
            data_source_rows = self.page.query_selector_all(self.root_selector)
            
            # Find the row with the matching name
            for row in data_source_rows:
                name_element = row.query_selector("td.data-source-name")
                if name_element and name_element.text_content() and self.data_source_name in name_element.text_content():
                    # Update the root selector to target this specific row
                    self.root_selector = f"{self.root_selector}:has-text('{self.data_source_name}')"
                    return True
                    
            return False
            
        except Exception:
            return False

    def get_details(self) -> Dict[str, str]:
        """Get data source details from the card.

        Returns:
            Dictionary with data source details.

        Raises:
            ElementNotFoundError: If the data source card is not found.
        """
        logger.debug(f"Getting details for data source {self.data_source_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Data source card not found for {self.data_source_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the data source row
            row = self.page.query_selector(self.root_selector)
            
            if not row:
                raise ElementNotFoundError(
                    f"Data source row not found for {self.data_source_name}",
                    selector=self.root_selector,
                    page_url=self.page.url,
                )
                
            # Get data source details
            name = row.query_selector("td.data-source-name")
            type_element = row.query_selector("td.data-source-type")
            
            return {
                "name": name.text_content().strip() if name else "",
                "type": type_element.text_content().strip() if type_element else "",
            }
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get details for data source {self.data_source_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def click_actions(self) -> None:
        """Click the actions button for the data source.

        Raises:
            ElementNotFoundError: If the actions button is not found.
        """
        logger.debug(f"Clicking actions button for data source {self.data_source_name}")
        
        if not self.find():
            raise ElementNotFoundError(
                f"Data source card not found for {self.data_source_name}",
                selector=self.root_selector,
                page_url=self.page.url,
            )
            
        try:
            # Get the actions button
            row = self.page.query_selector(self.root_selector)
            actions_button = row.query_selector("button.data-source-actions")
            
            if not actions_button:
                raise ElementNotFoundError(
                    f"Actions button not found for data source {self.data_source_name}",
                    selector=f"{self.root_selector} button.data-source-actions",
                    page_url=self.page.url,
                )
                
            # Click the actions button
            actions_button.click()
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to click actions button for data source {self.data_source_name}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def edit(self) -> None:
        """Click the edit button for the data source.

        Raises:
            ElementNotFoundError: If the edit button is not found.
        """
        logger.debug(f"Editing data source {self.data_source_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the edit button
            self.page.click("data_sources.edit_data_source")
            
            # Wait for the form to load
            self.page.wait_for_selector("data_source_form", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to edit data source {self.data_source_name}",
                selector="data_sources.edit_data_source",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def delete(self) -> None:
        """Click the delete button for the data source.

        Raises:
            ElementNotFoundError: If the delete button is not found.
        """
        logger.debug(f"Deleting data source {self.data_source_name}")
        
        # Click the actions button
        self.click_actions()
        
        try:
            # Click the delete button
            self.page.click("data_sources.delete_data_source")
            
            # Wait for the confirmation dialog
            self.page.wait_for_selector("dialog.confirm", timeout=self.timeout)
            
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to delete data source {self.data_source_name}",
                selector="data_sources.delete_data_source",
                page_url=self.page.url,
                context={"original_error": str(e)},
            )
