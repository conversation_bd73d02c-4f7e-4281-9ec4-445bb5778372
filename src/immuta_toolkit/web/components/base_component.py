"""Base component object for Immuta web automation."""

from typing import Optional

from playwright.sync_api import Page, Element<PERSON>andle

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.web.selectors import get_selector
from immuta_toolkit.web.exceptions import (
    ElementNotFoundError,
    ElementNotVisibleError,
)

logger = get_logger(__name__)


class BaseComponent:
    """Base component object for Immuta web automation.

    This class provides a base for reusable UI components that can be used
    across multiple pages. Components are self-contained UI elements that
    encapsulate their own behavior and selectors.

    Attributes:
        page: Playwright page object.
        root_selector_key: Key for the root selector of the component.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(
        self,
        page: Page,
        root_selector_key: str,
        timeout: int = 10000,
    ):
        """Initialize the base component object.

        Args:
            page: Playwright page object.
            root_selector_key: Key for the root selector of the component.
            timeout: Default timeout for actions in milliseconds.
        """
        self.page = page
        self.root_selector_key = root_selector_key
        self.root_selector = get_selector(root_selector_key)
        self.timeout = timeout

    def get_root_element(self, timeout: Optional[int] = None) -> ElementHandle:
        """Get the root element of the component.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Root element handle.

        Raises:
            ElementNotFoundError: If the root element is not found.
        """
        logger.debug(f"Getting root element for component: {self.root_selector_key}")

        try:
            element = self.page.wait_for_selector(
                self.root_selector, timeout=timeout or self.timeout
            )

            if element is None:
                raise ElementNotFoundError(
                    f"Root element not found: {self.root_selector_key}",
                    selector=self.root_selector,
                    page_url=self.page.url,
                )

            return element

        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get root element: {self.root_selector_key}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def is_visible(self, timeout: Optional[int] = None) -> bool:
        """Check if the component is visible.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            True if the component is visible, False otherwise.
        """
        logger.debug(f"Checking visibility of component: {self.root_selector_key}")

        try:
            self.page.wait_for_selector(
                self.root_selector, timeout=timeout or 5000, state="visible"
            )
            return True
        except Exception:
            return False

    def wait_for_visible(self, timeout: Optional[int] = None) -> None:
        """Wait for the component to be visible.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            ElementNotVisibleError: If the component is not visible within the timeout.
        """
        logger.debug(f"Waiting for component to be visible: {self.root_selector_key}")

        try:
            self.page.wait_for_selector(
                self.root_selector, timeout=timeout or self.timeout, state="visible"
            )
        except Exception as e:
            raise ElementNotVisibleError(
                f"Component not visible within timeout: {self.root_selector_key}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def wait_for_hidden(self, timeout: Optional[int] = None) -> None:
        """Wait for the component to be hidden.

        Args:
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Raises:
            ElementNotVisibleError: If the component is still visible after the timeout.
        """
        logger.debug(f"Waiting for component to be hidden: {self.root_selector_key}")

        try:
            self.page.wait_for_selector(
                self.root_selector, timeout=timeout or self.timeout, state="hidden"
            )
        except Exception as e:
            raise ElementNotVisibleError(
                f"Component still visible after timeout: {self.root_selector_key}",
                selector=self.root_selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def get_child_element(
        self, selector_key: str, timeout: Optional[int] = None
    ) -> ElementHandle:
        """Get a child element of the component.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Child element handle.

        Raises:
            ElementNotFoundError: If the child element is not found.
        """
        selector = get_selector(selector_key)
        logger.debug(
            f"Getting child element {selector_key} for component: {self.root_selector_key}"
        )

        # Get the root element first
        root = self.get_root_element(timeout)

        try:
            # Use the root element to find the child
            child = root.wait_for_selector(selector, timeout=timeout or self.timeout)

            if child is None:
                raise ElementNotFoundError(
                    f"Child element not found: {selector_key}",
                    selector=selector,
                    page_url=self.page.url,
                )

            return child

        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to get child element {selector_key} for component: {self.root_selector_key}",
                selector=selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def click_child(
        self, selector_key: str, timeout: Optional[int] = None, force: bool = False
    ) -> None:
        """Click a child element of the component.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.
            force: Whether to force the click.

        Raises:
            ElementNotFoundError: If the child element is not found.
        """
        selector = get_selector(selector_key)
        logger.debug(
            f"Clicking child element {selector_key} for component: {self.root_selector_key}"
        )

        # Get the child element
        child = self.get_child_element(selector_key, timeout)

        try:
            # Click the child element
            child.click(timeout=timeout or self.timeout, force=force)
        except Exception as e:
            raise ElementNotFoundError(
                f"Failed to click child element {selector_key} for component: {self.root_selector_key}",
                selector=selector,
                page_url=self.page.url,
                context={"original_error": str(e)},
            )

    def get_child_text(self, selector_key: str, timeout: Optional[int] = None) -> str:
        """Get text from a child element of the component.

        Args:
            selector_key: Key for the selector in the selector registry.
            timeout: Timeout in milliseconds. If None, uses the default timeout.

        Returns:
            Text content of the child element.

        Raises:
            ElementNotFoundError: If the child element is not found.
        """
        # Get the child element
        child = self.get_child_element(selector_key, timeout)

        # Get the text content
        return child.text_content() or ""
