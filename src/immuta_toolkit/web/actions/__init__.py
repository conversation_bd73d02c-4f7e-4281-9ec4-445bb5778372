"""Web actions for Immuta automation."""

from immuta_toolkit.web.actions.base import WebAction
from immuta_toolkit.web.actions.users import (
    ListUsersAction,
    GetUserAction,
    CreateUserAction,
    UpdateUserAction,
    DeleteUserAction,
)
from immuta_toolkit.web.actions.policies import (
    ListPoliciesAction,
    GetPolicyAction,
    CreatePolicyAction,
    UpdatePolicyAction,
    DeletePolicyAction,
)
from immuta_toolkit.web.actions.data_sources import (
    ListDataSourcesAction,
    GetDataSourceAction,
    CreateDataSourceAction,
    UpdateDataSourceAction,
    DeleteDataSourceAction,
)

__all__ = [
    "WebAction",
    "ListUsersAction",
    "GetUserAction",
    "CreateUserAction",
    "UpdateUserAction",
    "DeleteUserAction",
    "ListPoliciesAction",
    "GetPolicyAction",
    "CreatePolicyAction",
    "UpdatePolicyAction",
    "DeletePolicyAction",
    "ListDataSourcesAction",
    "GetDataSourceAction",
    "CreateDataSourceAction",
    "UpdateDataSourceAction",
    "DeleteDataSourceAction",
]
