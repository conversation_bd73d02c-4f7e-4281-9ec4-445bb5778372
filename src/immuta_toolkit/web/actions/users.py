"""User web actions for Immuta automation."""

from typing import Dict, List, Optional, Any, Union

from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.models import UserModel
from immuta_toolkit.web.actions.base import WebAction
from immuta_toolkit.web.selectors.elements import UserSelectors
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListUsersAction(WebAction[Dict[str, Any], List[Dict[str, Any]]]):
    """List users action.

    This action lists users in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the list users action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = UserSelectors

    def execute(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute the list users action.

        Args:
            params: Action parameters.
                - limit: Maximum number of users to return.
                - offset: Offset for pagination.
                - search: Search term.

        Returns:
            List of user dictionaries.
        """
        # Extract parameters
        limit = params.get("limit", 100)
        offset = params.get("offset", 0)
        search = params.get("search")

        # Navigate to users page
        self.page.goto(f"{self.page.url.split('#')[0]}#/users")

        # Wait for the user list to load
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Search for users if search term is provided
        if search:
            self.page.fill(self.selectors.USER_SEARCH_INPUT, search)
            self.page.wait_for_timeout(1000)  # Wait for search results

        # Get user items
        user_items = self.page.query_selector_all(self.selectors.USER_ITEM)

        # Apply pagination
        user_items = user_items[offset : offset + limit]

        # Extract user data
        users = []
        for item in user_items:
            try:
                name = (
                    item.query_selector(self.selectors.USER_NAME).text_content().strip()
                )
                email = (
                    item.query_selector(self.selectors.USER_EMAIL)
                    .text_content()
                    .strip()
                )
                role = (
                    item.query_selector(self.selectors.USER_ROLE).text_content().strip()
                )

                users.append(
                    {
                        "name": name,
                        "email": email,
                        "role": role,
                    }
                )
            except Exception as e:
                logger.warning(f"Failed to extract user data: {e}")

        return users


class GetUserAction(WebAction[str, Optional[Dict[str, Any]]]):
    """Get user action.

    This action gets a user by email in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the get user action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = UserSelectors

    def execute(self, email: str) -> Optional[Dict[str, Any]]:
        """Execute the get user action.

        Args:
            email: User email.

        Returns:
            User dictionary if found, None otherwise.
        """
        # Navigate to users page
        self.page.goto(f"{self.page.url.split('#')[0]}#/users")

        # Wait for the user list to load
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Search for the user
        self.page.fill(self.selectors.USER_SEARCH_INPUT, email)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the user exists
        try:
            user_item = self.page.wait_for_selector(
                self.selectors.USER_ITEM,
                timeout=5000,
            )

            if not user_item:
                return None

            # Extract user data
            name = (
                user_item.query_selector(self.selectors.USER_NAME)
                .text_content()
                .strip()
            )
            email = (
                user_item.query_selector(self.selectors.USER_EMAIL)
                .text_content()
                .strip()
            )
            role = (
                user_item.query_selector(self.selectors.USER_ROLE)
                .text_content()
                .strip()
            )

            return {
                "name": name,
                "email": email,
                "role": role,
            }
        except PlaywrightTimeoutError:
            return None
        except Exception as e:
            logger.warning(f"Failed to get user: {e}")
            return None


class CreateUserAction(WebAction[UserModel, Dict[str, Any]]):
    """Create user action.

    This action creates a user in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the create user action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = UserSelectors

    def execute(self, user: UserModel) -> Dict[str, Any]:
        """Execute the create user action.

        Args:
            user: User model.

        Returns:
            Created user dictionary.

        Raises:
            Exception: If the user creation fails.
        """
        # Navigate to users page
        self.page.goto(f"{self.page.url.split('#')[0]}#/users")

        # Wait for the user list to load
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Click the create user button
        self.page.click(self.selectors.CREATE_USER_BUTTON)

        # Wait for the user form to load
        self.page.wait_for_selector(self.selectors.USER_FORM, timeout=self.timeout)

        # Fill the user form
        self.page.fill(self.selectors.USER_FORM_NAME_INPUT, user.name)
        self.page.fill(self.selectors.USER_FORM_EMAIL_INPUT, user.email)
        self.page.select_option(
            self.selectors.USER_FORM_ROLE_SELECT, value=user.attributes.role
        )

        # Submit the form
        self.page.click(self.selectors.USER_FORM_SUBMIT_BUTTON)

        # Wait for the user to be created
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Return the created user
        return {
            "name": user.name,
            "email": user.email,
            "role": user.attributes.role,
        }


class UpdateUserAction(WebAction[Dict[str, Any], Dict[str, Any]]):
    """Update user action.

    This action updates a user in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the update user action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = UserSelectors

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the update user action.

        Args:
            params: Action parameters.
                - email: User email.
                - user: Updated user model.

        Returns:
            Updated user dictionary.

        Raises:
            Exception: If the user update fails.
        """
        # Extract parameters
        email = params.get("email")
        user = params.get("user")

        if not email or not user:
            raise ValueError("Email and user are required")

        # Navigate to users page
        self.page.goto(f"{self.page.url.split('#')[0]}#/users")

        # Wait for the user list to load
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Search for the user
        self.page.fill(self.selectors.USER_SEARCH_INPUT, email)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Click on the user to edit
        self.page.click(self.selectors.USER_ITEM)

        # Wait for the user form to load
        self.page.wait_for_selector(self.selectors.USER_FORM, timeout=self.timeout)

        # Fill the user form
        self.page.fill(self.selectors.USER_FORM_NAME_INPUT, user.name)
        self.page.select_option(
            self.selectors.USER_FORM_ROLE_SELECT, value=user.attributes.role
        )

        # Submit the form
        self.page.click(self.selectors.USER_FORM_SUBMIT_BUTTON)

        # Wait for the user to be updated
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Return the updated user
        return {
            "name": user.name,
            "email": email,
            "role": user.attributes.role,
        }


class DeleteUserAction(WebAction[str, bool]):
    """Delete user action.

    This action deletes a user in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the delete user action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = UserSelectors

    def execute(self, email: str) -> bool:
        """Execute the delete user action.

        Args:
            email: User email.

        Returns:
            True if the user was deleted, False otherwise.
        """
        # Navigate to users page
        self.page.goto(f"{self.page.url.split('#')[0]}#/users")

        # Wait for the user list to load
        self.page.wait_for_selector(self.selectors.USER_LIST, timeout=self.timeout)

        # Search for the user
        self.page.fill(self.selectors.USER_SEARCH_INPUT, email)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the user exists
        try:
            user_item = self.page.wait_for_selector(
                self.selectors.USER_ITEM,
                timeout=5000,
            )

            if not user_item:
                return False

            # Click the actions button
            user_item.query_selector(self.selectors.USER_ACTIONS_BUTTON).click()

            # Click the delete button
            self.page.click(self.selectors.USER_DELETE_BUTTON)

            # Confirm deletion
            self.page.click(self.selectors.CONFIRM_DELETE_BUTTON)

            # Wait for the user to be deleted
            self.page.wait_for_timeout(1000)

            return True
        except PlaywrightTimeoutError:
            return False
        except Exception as e:
            logger.warning(f"Failed to delete user: {e}")
            return False
