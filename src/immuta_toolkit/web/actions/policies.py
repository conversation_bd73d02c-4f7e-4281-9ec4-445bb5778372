"""Policy web actions for Immuta automation."""

from typing import Dict, List, Optional, Any, Union

from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.models import PolicyModel
from immuta_toolkit.web.actions.base import WebAction
from immuta_toolkit.web.selectors.elements import PolicySelectors
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListPoliciesAction(WebAction[Dict[str, Any], List[Dict[str, Any]]]):
    """List policies action.

    This action lists policies in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the list policies action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = PolicySelectors

    def execute(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute the list policies action.

        Args:
            params: Action parameters.
                - limit: Maximum number of policies to return.
                - offset: Offset for pagination.
                - search: Search term.
                - type: Policy type.

        Returns:
            List of policy dictionaries.
        """
        # Extract parameters
        limit = params.get("limit", 100)
        offset = params.get("offset", 0)
        search = params.get("search")
        policy_type = params.get("type")

        # Navigate to policies page
        self.page.goto(f"{self.page.url.split('#')[0]}#/policies")

        # Wait for the policy list to load
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Search for policies if search term is provided
        if search:
            self.page.fill(self.selectors.POLICY_SEARCH_INPUT, search)
            self.page.wait_for_timeout(1000)  # Wait for search results

        # Get policy items
        policy_items = self.page.query_selector_all(self.selectors.POLICY_ITEM)

        # Filter by type if provided
        if policy_type:
            policy_items = [
                item
                for item in policy_items
                if item.query_selector(self.selectors.POLICY_TYPE)
                .text_content()
                .strip()
                .lower()
                == policy_type.lower()
            ]

        # Apply pagination
        policy_items = policy_items[offset : offset + limit]

        # Extract policy data
        policies = []
        for item in policy_items:
            try:
                name = (
                    item.query_selector(self.selectors.POLICY_NAME)
                    .text_content()
                    .strip()
                )
                policy_type = (
                    item.query_selector(self.selectors.POLICY_TYPE)
                    .text_content()
                    .strip()
                )

                policies.append(
                    {
                        "name": name,
                        "type": policy_type,
                    }
                )
            except Exception as e:
                logger.warning(f"Failed to extract policy data: {e}")

        return policies


class GetPolicyAction(WebAction[str, Optional[Dict[str, Any]]]):
    """Get policy action.

    This action gets a policy by name in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the get policy action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = PolicySelectors

    def execute(self, name: str) -> Optional[Dict[str, Any]]:
        """Execute the get policy action.

        Args:
            name: Policy name.

        Returns:
            Policy dictionary if found, None otherwise.
        """
        # Navigate to policies page
        self.page.goto(f"{self.page.url.split('#')[0]}#/policies")

        # Wait for the policy list to load
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Search for the policy
        self.page.fill(self.selectors.POLICY_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the policy exists
        try:
            policy_item = self.page.wait_for_selector(
                self.selectors.POLICY_ITEM,
                timeout=5000,
            )

            if not policy_item:
                return None

            # Extract policy data
            name = (
                policy_item.query_selector(self.selectors.POLICY_NAME)
                .text_content()
                .strip()
            )
            policy_type = (
                policy_item.query_selector(self.selectors.POLICY_TYPE)
                .text_content()
                .strip()
            )

            return {
                "name": name,
                "type": policy_type,
            }
        except PlaywrightTimeoutError:
            return None
        except Exception as e:
            logger.warning(f"Failed to get policy: {e}")
            return None


class CreatePolicyAction(WebAction[PolicyModel, Dict[str, Any]]):
    """Create policy action.

    This action creates a policy in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the create policy action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = PolicySelectors

    def execute(self, policy: PolicyModel) -> Dict[str, Any]:
        """Execute the create policy action.

        Args:
            policy: Policy model.

        Returns:
            Created policy dictionary.

        Raises:
            Exception: If the policy creation fails.
        """
        # Navigate to policies page
        self.page.goto(f"{self.page.url.split('#')[0]}#/policies")

        # Wait for the policy list to load
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Click the create policy button
        self.page.click(self.selectors.CREATE_POLICY_BUTTON)

        # Wait for the policy form to load
        self.page.wait_for_selector(self.selectors.POLICY_FORM, timeout=self.timeout)

        # Fill the policy form
        self.page.fill(self.selectors.POLICY_FORM_NAME_INPUT, policy.name)
        self.page.select_option(
            self.selectors.POLICY_FORM_TYPE_SELECT, value=policy.type
        )

        # TODO: Implement policy-specific form fields based on policy type

        # Submit the form
        self.page.click(self.selectors.POLICY_FORM_SUBMIT_BUTTON)

        # Wait for the policy to be created
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Return the created policy
        return {
            "name": policy.name,
            "type": policy.type,
        }


class UpdatePolicyAction(WebAction[Dict[str, Any], Dict[str, Any]]):
    """Update policy action.

    This action updates a policy in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the update policy action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = PolicySelectors

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the update policy action.

        Args:
            params: Action parameters.
                - name: Policy name.
                - policy: Updated policy model.

        Returns:
            Updated policy dictionary.

        Raises:
            Exception: If the policy update fails.
        """
        # Extract parameters
        name = params.get("name")
        policy = params.get("policy")

        if not name or not policy:
            raise ValueError("Name and policy are required")

        # Navigate to policies page
        self.page.goto(f"{self.page.url.split('#')[0]}#/policies")

        # Wait for the policy list to load
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Search for the policy
        self.page.fill(self.selectors.POLICY_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Click on the policy to edit
        self.page.click(self.selectors.POLICY_ITEM)

        # Wait for the policy form to load
        self.page.wait_for_selector(self.selectors.POLICY_FORM, timeout=self.timeout)

        # Fill the policy form
        self.page.fill(self.selectors.POLICY_FORM_NAME_INPUT, policy.name)

        # TODO: Implement policy-specific form fields based on policy type

        # Submit the form
        self.page.click(self.selectors.POLICY_FORM_SUBMIT_BUTTON)

        # Wait for the policy to be updated
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Return the updated policy
        return {
            "name": policy.name,
            "type": policy.type,
        }


class DeletePolicyAction(WebAction[str, bool]):
    """Delete policy action.

    This action deletes a policy in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the delete policy action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = PolicySelectors

    def execute(self, name: str) -> bool:
        """Execute the delete policy action.

        Args:
            name: Policy name.

        Returns:
            True if the policy was deleted, False otherwise.
        """
        # Navigate to policies page
        self.page.goto(f"{self.page.url.split('#')[0]}#/policies")

        # Wait for the policy list to load
        self.page.wait_for_selector(self.selectors.POLICY_LIST, timeout=self.timeout)

        # Search for the policy
        self.page.fill(self.selectors.POLICY_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the policy exists
        try:
            policy_item = self.page.wait_for_selector(
                self.selectors.POLICY_ITEM,
                timeout=5000,
            )

            if not policy_item:
                return False

            # Click the actions button
            policy_item.query_selector(self.selectors.POLICY_ACTIONS_BUTTON).click()

            # Click the delete button
            self.page.click(self.selectors.POLICY_DELETE_BUTTON)

            # Confirm deletion
            self.page.click(self.selectors.CONFIRM_DELETE_BUTTON)

            # Wait for the policy to be deleted
            self.page.wait_for_timeout(1000)

            return True
        except PlaywrightTimeoutError:
            return False
        except Exception as e:
            logger.warning(f"Failed to delete policy: {e}")
            return False
