"""Base web action for Immuta automation."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic

from playwright.sync_api import Page

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T")  # Input type
R = TypeVar("R")  # Result type


class WebAction(ABC, Generic[T, R]):
    """Base class for web actions.
    
    This class provides a common interface for all web actions,
    including execution and error handling.
    
    Attributes:
        page: Playwright page object.
        timeout: Default timeout for actions in milliseconds.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the web action.
        
        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        self.page = page
        self.timeout = timeout
        self.name = self.__class__.__name__
    
    @abstractmethod
    def execute(self, params: T) -> R:
        """Execute the web action.
        
        Args:
            params: Action parameters.
            
        Returns:
            Action result.
            
        Raises:
            Exception: If the action fails.
        """
        pass
    
    def handle_error(self, error: Exception) -> None:
        """Handle an error.
        
        Args:
            error: Error to handle.
            
        Raises:
            Exception: The original error or a wrapped error.
        """
        logger.error(f"Web action {self.name} failed: {error}")
        
        # Take a screenshot if possible
        try:
            screenshot_path = f"error_{self.name}_{id(error)}.png"
            self.page.screenshot(path=screenshot_path)
            logger.info(f"Screenshot saved to {screenshot_path}")
        except Exception as e:
            logger.warning(f"Failed to take screenshot: {e}")
        
        # Re-raise the error
        raise error
    
    def __call__(self, params: T) -> R:
        """Execute the web action.
        
        Args:
            params: Action parameters.
            
        Returns:
            Action result.
            
        Raises:
            Exception: If the action fails.
        """
        try:
            logger.info(f"Executing web action: {self.name}")
            return self.execute(params)
        except Exception as e:
            self.handle_error(e)
            raise
