"""Data source web actions for Immuta automation."""

from typing import Dict, List, Optional, Any, Union

from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError

from immuta_toolkit.models import DataSourceModel
from immuta_toolkit.web.actions.base import WebAction
from immuta_toolkit.web.selectors.elements import DataSourceSelectors
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ListDataSourcesAction(WebAction[Dict[str, Any], List[Dict[str, Any]]]):
    """List data sources action.

    This action lists data sources in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the list data sources action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = DataSourceSelectors

    def execute(self, params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Execute the list data sources action.

        Args:
            params: Action parameters.
                - limit: Maximum number of data sources to return.
                - offset: Offset for pagination.
                - search: Search term.
                - type: Data source type.

        Returns:
            List of data source dictionaries.
        """
        # Extract parameters
        limit = params.get("limit", 100)
        offset = params.get("offset", 0)
        search = params.get("search")
        data_source_type = params.get("type")

        # Navigate to data sources page
        self.page.goto(f"{self.page.url.split('#')[0]}#/data-sources")

        # Wait for the data source list to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Search for data sources if search term is provided
        if search:
            self.page.fill(self.selectors.DATA_SOURCE_SEARCH_INPUT, search)
            self.page.wait_for_timeout(1000)  # Wait for search results

        # Get data source items
        data_source_items = self.page.query_selector_all(
            self.selectors.DATA_SOURCE_ITEM
        )

        # Filter by type if provided
        if data_source_type:
            data_source_items = [
                item
                for item in data_source_items
                if item.query_selector(self.selectors.DATA_SOURCE_TYPE)
                .text_content()
                .strip()
                .lower()
                == data_source_type.lower()
            ]

        # Apply pagination
        data_source_items = data_source_items[offset : offset + limit]

        # Extract data source data
        data_sources = []
        for item in data_source_items:
            try:
                name = (
                    item.query_selector(self.selectors.DATA_SOURCE_NAME)
                    .text_content()
                    .strip()
                )
                data_source_type = (
                    item.query_selector(self.selectors.DATA_SOURCE_TYPE)
                    .text_content()
                    .strip()
                )

                data_sources.append(
                    {
                        "name": name,
                        "type": data_source_type,
                    }
                )
            except Exception as e:
                logger.warning(f"Failed to extract data source data: {e}")

        return data_sources


class GetDataSourceAction(WebAction[str, Optional[Dict[str, Any]]]):
    """Get data source action.

    This action gets a data source by name in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the get data source action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = DataSourceSelectors

    def execute(self, name: str) -> Optional[Dict[str, Any]]:
        """Execute the get data source action.

        Args:
            name: Data source name.

        Returns:
            Data source dictionary if found, None otherwise.
        """
        # Navigate to data sources page
        self.page.goto(f"{self.page.url.split('#')[0]}#/data-sources")

        # Wait for the data source list to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Search for the data source
        self.page.fill(self.selectors.DATA_SOURCE_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the data source exists
        try:
            data_source_item = self.page.wait_for_selector(
                self.selectors.DATA_SOURCE_ITEM,
                timeout=5000,
            )

            if not data_source_item:
                return None

            # Extract data source data
            name = (
                data_source_item.query_selector(self.selectors.DATA_SOURCE_NAME)
                .text_content()
                .strip()
            )
            data_source_type = (
                data_source_item.query_selector(self.selectors.DATA_SOURCE_TYPE)
                .text_content()
                .strip()
            )

            return {
                "name": name,
                "type": data_source_type,
            }
        except PlaywrightTimeoutError:
            return None
        except Exception as e:
            logger.warning(f"Failed to get data source: {e}")
            return None


class CreateDataSourceAction(WebAction[DataSourceModel, Dict[str, Any]]):
    """Create data source action.

    This action creates a data source in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the create data source action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = DataSourceSelectors

    def execute(self, data_source: DataSourceModel) -> Dict[str, Any]:
        """Execute the create data source action.

        Args:
            data_source: Data source model.

        Returns:
            Created data source dictionary.

        Raises:
            Exception: If the data source creation fails.
        """
        # Navigate to data sources page
        self.page.goto(f"{self.page.url.split('#')[0]}#/data-sources")

        # Wait for the data source list to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Click the create data source button
        self.page.click(self.selectors.CREATE_DATA_SOURCE_BUTTON)

        # Wait for the data source form to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_FORM, timeout=self.timeout
        )

        # Fill the data source form
        self.page.fill(self.selectors.DATA_SOURCE_FORM_NAME_INPUT, data_source.name)
        self.page.select_option(
            self.selectors.DATA_SOURCE_FORM_TYPE_SELECT, value=data_source.type
        )

        # TODO: Implement data source-specific form fields based on data source type

        # Submit the form
        self.page.click(self.selectors.DATA_SOURCE_FORM_SUBMIT_BUTTON)

        # Wait for the data source to be created
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Return the created data source
        return {
            "name": data_source.name,
            "type": data_source.type,
        }


class UpdateDataSourceAction(WebAction[Dict[str, Any], Dict[str, Any]]):
    """Update data source action.

    This action updates a data source in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the update data source action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = DataSourceSelectors

    def execute(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the update data source action.

        Args:
            params: Action parameters.
                - name: Data source name.
                - data_source: Updated data source model.

        Returns:
            Updated data source dictionary.

        Raises:
            Exception: If the data source update fails.
        """
        # Extract parameters
        name = params.get("name")
        data_source = params.get("data_source")

        if not name or not data_source:
            raise ValueError("Name and data_source are required")

        # Navigate to data sources page
        self.page.goto(f"{self.page.url.split('#')[0]}#/data-sources")

        # Wait for the data source list to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Search for the data source
        self.page.fill(self.selectors.DATA_SOURCE_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Click on the data source to edit
        self.page.click(self.selectors.DATA_SOURCE_ITEM)

        # Wait for the data source form to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_FORM, timeout=self.timeout
        )

        # Fill the data source form
        self.page.fill(self.selectors.DATA_SOURCE_FORM_NAME_INPUT, data_source.name)

        # TODO: Implement data source-specific form fields based on data source type

        # Submit the form
        self.page.click(self.selectors.DATA_SOURCE_FORM_SUBMIT_BUTTON)

        # Wait for the data source to be updated
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Return the updated data source
        return {
            "name": data_source.name,
            "type": data_source.type,
        }


class DeleteDataSourceAction(WebAction[str, bool]):
    """Delete data source action.

    This action deletes a data source in the Immuta web UI.
    """

    def __init__(self, page: Page, timeout: int = 30000):
        """Initialize the delete data source action.

        Args:
            page: Playwright page object.
            timeout: Default timeout for actions in milliseconds.
        """
        super().__init__(page, timeout)
        self.selectors = DataSourceSelectors

    def execute(self, name: str) -> bool:
        """Execute the delete data source action.

        Args:
            name: Data source name.

        Returns:
            True if the data source was deleted, False otherwise.
        """
        # Navigate to data sources page
        self.page.goto(f"{self.page.url.split('#')[0]}#/data-sources")

        # Wait for the data source list to load
        self.page.wait_for_selector(
            self.selectors.DATA_SOURCE_LIST, timeout=self.timeout
        )

        # Search for the data source
        self.page.fill(self.selectors.DATA_SOURCE_SEARCH_INPUT, name)
        self.page.wait_for_timeout(1000)  # Wait for search results

        # Check if the data source exists
        try:
            data_source_item = self.page.wait_for_selector(
                self.selectors.DATA_SOURCE_ITEM,
                timeout=5000,
            )

            if not data_source_item:
                return False

            # Click the actions button
            data_source_item.query_selector(
                self.selectors.DATA_SOURCE_ACTIONS_BUTTON
            ).click()

            # Click the delete button
            self.page.click(self.selectors.DATA_SOURCE_DELETE_BUTTON)

            # Confirm deletion
            self.page.click(self.selectors.CONFIRM_DELETE_BUTTON)

            # Wait for the data source to be deleted
            self.page.wait_for_timeout(1000)

            return True
        except PlaywrightTimeoutError:
            return False
        except Exception as e:
            logger.warning(f"Failed to delete data source: {e}")
            return False
