"""Configuration service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List, Optional, Union, Any

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ConfigService:
    """Service for managing configuration in Immuta.
    
    This service provides functionality for managing configuration settings,
    including schema evolution settings.
    
    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the configuration service.
        
        Args:
            client: Immuta client instance.
        """
        self.client = client

    def get_schema_evolution_config(self) -> Dict:
        """Get schema evolution configuration.
        
        Returns:
            Schema evolution configuration dictionary.
        """
        if self.client.is_local:
            return {
                "datasource_name_format": "{table_name}_v{version}",
                "query_engine_table_name_format": "{table_name}_v{version}",
                "enabled": True,
            }
            
        response = self.client.make_request("GET", "config/schema-evolution")
        return response.json()

    def configure_schema_evolution(
        self, config: Dict, backup: bool = True, dry_run: bool = False, validate: bool = True
    ) -> Dict:
        """Configure schema evolution settings.
        
        Args:
            config: Schema evolution configuration dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the configuration before applying it.
            
        Returns:
            Updated schema evolution configuration dictionary.
            
        Raises:
            ValueError: If validation fails.
        """
        if validate:
            self._validate_schema_evolution_config(config)
            
        if dry_run:
            logger.info("Dry run: Would configure schema evolution")
            self.client.notifier.send_notification(
                "schema_evolution_configure_dry_run",
                {
                    "datasource_name_format": config.get("datasource_name_format"),
                    "query_engine_table_name_format": config.get("query_engine_table_name_format"),
                    "enabled": config.get("enabled", True),
                },
                "Success",
            )
            return config
            
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"schema_evolution_{int(time.time())}.json"
            try:
                current_config = self.get_schema_evolution_config()
                self.client.storage.upload_file(
                    current_config,
                    blob_name,
                    metadata={"type": "schema_evolution", "operation": "configure"},
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")
                
        if self.client.is_local:
            logger.info("Mock: Configured schema evolution")
            self.client.notifier.send_notification(
                "schema_evolution_configure",
                {
                    "datasource_name_format": config.get("datasource_name_format"),
                    "query_engine_table_name_format": config.get("query_engine_table_name_format"),
                    "enabled": config.get("enabled", True),
                    "backup": blob_name,
                },
                "Success",
            )
            return config
            
        try:
            # Get current configuration to merge with new configuration
            current_config = self.get_schema_evolution_config()
            
            # Merge configurations
            merged_config = {**current_config, **config}
            
            # Update configuration
            response = self.client.make_request(
                "PUT", "config/schema-evolution", data=merged_config
            )
            updated_config = response.json()
            
            logger.info("Configured schema evolution")
            self.client.notifier.send_notification(
                "schema_evolution_configure",
                {
                    "datasource_name_format": config.get("datasource_name_format"),
                    "query_engine_table_name_format": config.get("query_engine_table_name_format"),
                    "enabled": config.get("enabled", True),
                    "backup": blob_name,
                },
                "Success",
            )
            
            return updated_config
        except Exception as e:
            logger.error(f"Failed to configure schema evolution: {e}")
            self.client.notifier.send_notification(
                "schema_evolution_configure", {}, "Failed", str(e)
            )
            
            # Restore backup if available
            if blob_name and self.client.storage:
                logger.info("Restoring backup...")
                try:
                    backup_data = self.client.storage.download_file(blob_name)
                    self.client.make_request(
                        "PUT", "config/schema-evolution", data=backup_data
                    )
                    logger.info("Restored schema evolution configuration from backup")
                except Exception as restore_error:
                    logger.error(f"Failed to restore backup: {restore_error}")
                    
            raise

    def _validate_schema_evolution_config(self, config: Dict) -> None:
        """Validate schema evolution configuration.
        
        Args:
            config: Schema evolution configuration dictionary.
            
        Raises:
            ValueError: If validation fails.
        """
        # Check required fields
        if "datasource_name_format" not in config and "query_engine_table_name_format" not in config:
            raise ValueError(
                "At least one of datasource_name_format or query_engine_table_name_format must be provided"
            )
            
        # Validate datasource_name_format if present
        if "datasource_name_format" in config:
            if not isinstance(config["datasource_name_format"], str):
                raise ValueError("datasource_name_format must be a string")
                
            if "{table_name}" not in config["datasource_name_format"]:
                raise ValueError("datasource_name_format must contain {table_name}")
                
            if "{version}" not in config["datasource_name_format"]:
                raise ValueError("datasource_name_format must contain {version}")
                
        # Validate query_engine_table_name_format if present
        if "query_engine_table_name_format" in config:
            if not isinstance(config["query_engine_table_name_format"], str):
                raise ValueError("query_engine_table_name_format must be a string")
                
            if "{table_name}" not in config["query_engine_table_name_format"]:
                raise ValueError("query_engine_table_name_format must contain {table_name}")
                
            if "{version}" not in config["query_engine_table_name_format"]:
                raise ValueError("query_engine_table_name_format must contain {version}")
                
        # Validate enabled if present
        if "enabled" in config:
            if not isinstance(config["enabled"], bool):
                raise ValueError("enabled must be a boolean")


class GlobalConfigService:
    """Service for managing global configuration in Immuta.
    
    This service provides functionality for managing global configuration settings,
    including system-wide settings and defaults.
    
    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the global configuration service.
        
        Args:
            client: Immuta client instance.
        """
        self.client = client

    def get_global_config(self) -> Dict:
        """Get global configuration.
        
        Returns:
            Global configuration dictionary.
        """
        if self.client.is_local:
            return {
                "default_user_role": "DataScientist",
                "default_data_source_handler": "snowflake",
                "default_policy_exceptions": {"groups": ["Administrators"]},
            }
            
        response = self.client.make_request("GET", "config/global")
        return response.json()

    def update_global_config(
        self, config: Dict, backup: bool = True, dry_run: bool = False, validate: bool = True
    ) -> Dict:
        """Update global configuration.
        
        Args:
            config: Global configuration dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the configuration before applying it.
            
        Returns:
            Updated global configuration dictionary.
            
        Raises:
            ValueError: If validation fails.
        """
        if validate:
            self._validate_global_config(config)
            
        if dry_run:
            logger.info("Dry run: Would update global configuration")
            self.client.notifier.send_notification(
                "global_config_update_dry_run",
                config,
                "Success",
            )
            return config
            
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"global_config_{int(time.time())}.json"
            try:
                current_config = self.get_global_config()
                self.client.storage.upload_file(
                    current_config,
                    blob_name,
                    metadata={"type": "global_config", "operation": "update"},
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")
                
        if self.client.is_local:
            logger.info("Mock: Updated global configuration")
            self.client.notifier.send_notification(
                "global_config_update",
                {**config, "backup": blob_name},
                "Success",
            )
            return config
            
        try:
            # Get current configuration to merge with new configuration
            current_config = self.get_global_config()
            
            # Merge configurations
            merged_config = {**current_config, **config}
            
            # Update configuration
            response = self.client.make_request(
                "PUT", "config/global", data=merged_config
            )
            updated_config = response.json()
            
            logger.info("Updated global configuration")
            self.client.notifier.send_notification(
                "global_config_update",
                {**config, "backup": blob_name},
                "Success",
            )
            
            return updated_config
        except Exception as e:
            logger.error(f"Failed to update global configuration: {e}")
            self.client.notifier.send_notification(
                "global_config_update", {}, "Failed", str(e)
            )
            
            # Restore backup if available
            if blob_name and self.client.storage:
                logger.info("Restoring backup...")
                try:
                    backup_data = self.client.storage.download_file(blob_name)
                    self.client.make_request(
                        "PUT", "config/global", data=backup_data
                    )
                    logger.info("Restored global configuration from backup")
                except Exception as restore_error:
                    logger.error(f"Failed to restore backup: {restore_error}")
                    
            raise

    def _validate_global_config(self, config: Dict) -> None:
        """Validate global configuration.
        
        Args:
            config: Global configuration dictionary.
            
        Raises:
            ValueError: If validation fails.
        """
        # Validate default_user_role if present
        if "default_user_role" in config:
            valid_roles = ["Admin", "DataOwner", "DataScientist", "ComplianceOfficer"]
            if config["default_user_role"] not in valid_roles:
                raise ValueError(
                    f"Invalid default_user_role: {config['default_user_role']}. "
                    f"Valid roles: {valid_roles}"
                )
                
        # Validate default_data_source_handler if present
        if "default_data_source_handler" in config:
            valid_handlers = ["snowflake", "postgres", "mysql", "redshift", "s3", "azure"]
            if config["default_data_source_handler"] not in valid_handlers:
                raise ValueError(
                    f"Invalid default_data_source_handler: {config['default_data_source_handler']}. "
                    f"Valid handlers: {valid_handlers}"
                )
                
        # Validate default_policy_exceptions if present
        if "default_policy_exceptions" in config:
            if not isinstance(config["default_policy_exceptions"], dict):
                raise ValueError("default_policy_exceptions must be a dictionary")
                
            # Validate exception groups
            if "groups" in config["default_policy_exceptions"]:
                if not isinstance(config["default_policy_exceptions"]["groups"], list):
                    raise ValueError("default_policy_exceptions.groups must be a list")
                    
            # Validate exception users
            if "users" in config["default_policy_exceptions"]:
                if not isinstance(config["default_policy_exceptions"]["users"], list):
                    raise ValueError("default_policy_exceptions.users must be a list")
