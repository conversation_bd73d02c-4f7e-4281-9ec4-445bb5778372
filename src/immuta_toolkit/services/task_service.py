"""Task service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List, Optional, Any

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class TaskService:
    """Service for managing tasks in Immuta.

    This service provides functionality for managing tasks, including
    getting pending tasks, completing tasks, and deleting tasks.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the task service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def get_pending_tasks(self, task_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get pending tasks for the current user.

        Args:
            task_types: Optional list of task types to filter by.
                Valid values include: "unmask", "dataSourceCreated", "columnAdded",
                "columnDeleted", "columnTypeChanged".

        Returns:
            List of task dictionaries.
        """
        if self.client.is_local:
            tasks = [
                {
                    "id": 1,
                    "state": "pending",
                    "type": "columnAdded",
                    "reason": "New column detected",
                    "dataSource": {"id": 1, "name": "customer_data"},
                },
                {
                    "id": 2,
                    "state": "pending",
                    "type": "unmask",
                    "reason": "Unmask request for sensitive data",
                    "dataSource": {"id": 2, "name": "transaction_data"},
                },
            ]
            
            if task_types:
                return [task for task in tasks if task["type"] in task_types]
            return tasks

        # Build query parameters
        params = {}
        if task_types:
            params["type"] = task_types

        # According to Immuta API V1, the endpoint for getting pending tasks is /dataSource/tasks/pending
        response = self.client.make_request("GET", "dataSource/tasks/pending", params=params)
        return response.json()

    def get_data_source_tasks(self, data_source_id: int, task_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Get tasks for a specific data source.

        Args:
            data_source_id: Data source ID.
            task_types: Optional list of task types to filter by.
                Valid values include: "unmask", "dataSourceCreated", "columnAdded",
                "columnDeleted", "columnTypeChanged".

        Returns:
            List of task dictionaries.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            tasks = [
                {
                    "id": 1,
                    "state": "pending",
                    "type": "columnAdded",
                    "reason": "New column detected",
                    "dataSource": {"id": data_source_id, "name": "customer_data"},
                },
                {
                    "id": 2,
                    "state": "pending",
                    "type": "unmask",
                    "reason": "Unmask request for sensitive data",
                    "dataSource": {"id": data_source_id, "name": "transaction_data"},
                },
            ]
            
            if task_types:
                return [task for task in tasks if task["type"] in task_types]
            return tasks

        # Build query parameters
        params = {}
        if task_types:
            params["type"] = task_types

        # According to Immuta API V1, the endpoint for getting data source tasks is /dataSource/{id}/tasks
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/tasks", params=params
        )
        return response.json()

    def complete_task(self, task_id: int, backup: bool = True, dry_run: bool = False) -> Dict[str, Any]:
        """Mark a task as complete.

        Args:
            task_id: Task ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If task is not found.
        """
        if dry_run:
            logger.info(f"Dry run: Would complete task {task_id}")
            self.client.notifier.send_notification(
                "complete_task_dry_run",
                {"task_id": task_id},
                "Success",
            )
            return {"success": True, "id": task_id, "message": "Dry run: Would complete task"}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"task_{task_id}_{int(time.time())}.json"
            try:
                # Get task details before completing
                response = self.client.make_request("GET", f"dataSource/tasks/{task_id}")
                task = response.json()
                self.client.storage.upload_file(
                    task,
                    blob_name,
                    metadata={
                        "type": "task",
                        "id": str(task_id),
                        "operation": "complete_task",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Completed task {task_id}")
            self.client.notifier.send_notification(
                "complete_task",
                {"task_id": task_id, "backup": blob_name},
                "Success",
            )
            return {"success": True, "id": task_id, "message": "Task completed"}

        try:
            # According to Immuta API V1, the endpoint for completing a task is /dataSource/tasks/{id}
            response = self.client.make_request("PUT", f"dataSource/tasks/{task_id}")
            result = response.json()
            logger.info(f"Completed task {task_id}")
            self.client.notifier.send_notification(
                "complete_task",
                {"task_id": task_id, "backup": blob_name},
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to complete task: {e}")
            self.client.notifier.send_notification(
                "complete_task", {"task_id": task_id}, "Failed", str(e)
            )
            raise

    def delete_task(self, task_id: int, backup: bool = True, dry_run: bool = False) -> Dict[str, Any]:
        """Delete a task.

        Args:
            task_id: Task ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If task is not found.
        """
        if dry_run:
            logger.info(f"Dry run: Would delete task {task_id}")
            self.client.notifier.send_notification(
                "delete_task_dry_run",
                {"task_id": task_id},
                "Success",
            )
            return {"success": True, "id": task_id, "message": "Dry run: Would delete task"}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"task_{task_id}_{int(time.time())}.json"
            try:
                # Get task details before deleting
                response = self.client.make_request("GET", f"dataSource/tasks/{task_id}")
                task = response.json()
                self.client.storage.upload_file(
                    task,
                    blob_name,
                    metadata={
                        "type": "task",
                        "id": str(task_id),
                        "operation": "delete_task",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Deleted task {task_id}")
            self.client.notifier.send_notification(
                "delete_task",
                {"task_id": task_id, "backup": blob_name},
                "Success",
            )
            return {"success": True, "id": task_id, "message": "Task deleted"}

        try:
            # According to Immuta API V1, the endpoint for deleting a task is /dataSource/tasks/{id}
            response = self.client.make_request("DELETE", f"dataSource/tasks/{task_id}")
            result = response.json()
            logger.info(f"Deleted task {task_id}")
            self.client.notifier.send_notification(
                "delete_task",
                {"task_id": task_id, "backup": blob_name},
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to delete task: {e}")
            self.client.notifier.send_notification(
                "delete_task", {"task_id": task_id}, "Failed", str(e)
            )
            raise
