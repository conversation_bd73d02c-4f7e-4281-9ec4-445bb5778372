"""Batch operations service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List, Optional, Union, Callable, Any, Tuple

from rich.progress import Progress

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.parallel import ParallelProcessor
from immuta_toolkit.utils.cache import cached

logger = get_logger(__name__)


class BatchService:
    """Service for batch operations in Immuta.

    This service provides functionality for batch operations, including
    batch updating users and tagging data sources.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the batch service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def batch_update_users(
        self,
        users: List[Dict],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
        batch_size: int = 50,
        max_workers: int = 5,
    ) -> Dict:
        """Batch update users via API, including group memberships.

        Args:
            users: List of user dictionaries with user_id, role, and groups.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate users before updating them.
            batch_size: Number of users to update in each batch.

        Returns:
            Dictionary with update results.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            # Validate all users first
            for user in users:
                if "user_id" not in user:
                    raise ValueError(f"Missing user_id in user: {user}")
                if "role" not in user:
                    raise ValueError(f"Missing role in user: {user}")

                # Validate role
                valid_roles = [
                    "Admin",
                    "DataOwner",
                    "DataScientist",
                    "ComplianceOfficer",
                ]
                if user["role"] not in valid_roles:
                    raise ValueError(
                        f"Invalid role for user {user['user_id']}: {user['role']}. "
                        f"Valid roles: {valid_roles}"
                    )

                # Validate groups if present
                if "groups" in user:
                    if not isinstance(user["groups"], list):
                        raise ValueError(
                            f"Groups must be a list for user {user['user_id']}"
                        )

            logger.info(f"Validation passed for {len(users)} users")

        if dry_run:
            # Simulate batch update in dry-run mode
            logger.info(f"Dry run: Would update {len(users)} users")
            self.client.notifier.send_notification(
                "batch_user_update_dry_run",
                {
                    "user_count": len(users),
                    "roles_updated": [u["role"] for u in users],
                    "groups_updated": [u.get("groups", []) for u in users],
                    "details": f"Would update {len(users)} users with roles and group memberships",
                },
                "Success",
            )
            return {
                "status": "success",
                "updated": len(users),
                "failed": 0,
                "failed_users": [],
            }

        # Create backup before making changes
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"users_batch_{int(time.time())}.json"
            try:
                all_users = self.client.user_service.list_users()
                self.client.storage.upload_file(
                    all_users,
                    blob_name,
                    metadata={"type": "users", "operation": "batch_user_update"},
                )
                logger.info(f"Created backup: {blob_name}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Batch updated {len(users)} users")
            self.client.notifier.send_notification(
                "batch_user_update",
                {"user_count": len(users), "backup": blob_name},
                "Success",
            )
            return {
                "status": "success",
                "updated": len(users),
                "failed": 0,
                "failed_users": [],
            }

        # Process in batches
        results = {"status": "success", "updated": 0, "failed": 0, "failed_users": []}

        try:
            # Optimize for large datasets by using batch API with chunking
            with Progress() as progress:
                task = progress.add_task("[cyan]Updating users...", total=len(users))

                # Process users in batches using parallel processing
                # We'll still use the batch API endpoint, but process batches in parallel

                # Create batches
                batches = [
                    users[i : i + batch_size] for i in range(0, len(users), batch_size)
                ]

                # Define batch update function
                def update_user_batch(batch: List[Dict]) -> Dict:
                    """Update a batch of users.

                    Args:
                        batch: List of user dictionaries.

                    Returns:
                        Dictionary with operation results.
                    """
                    try:
                        # Create payload
                        payload = {
                            "users": [
                                {
                                    "user_id": u["user_id"],
                                    "attributes": {"role": u["role"]},
                                    "groups": u.get("groups", []),
                                }
                                for u in batch
                            ]
                        }

                        # Make request
                        response = self.client.make_request(
                            "PUT",
                            "user/batch",
                            data=payload,
                            timeout=self.client.timeout * 3,
                        )

                        # Process response
                        response_data = response.json()
                        batch_results = response_data.get("results", [])

                        # Return results
                        return {
                            "status": "success",
                            "results": batch_results,
                        }
                    except Exception as e:
                        logger.error(f"Failed to update batch: {e}")
                        return {
                            "status": "error",
                            "error": str(e),
                            "batch": batch,
                        }

                # Define progress callback
                def update_progress(processed: int, total: int) -> None:
                    """Update progress bar.

                    Args:
                        processed: Number of processed items.
                        total: Total number of items.
                    """
                    # Update progress bar
                    progress.update(task, completed=processed, total=total)

                # Create a parallel processor with specified max_workers
                parallel_processor = ParallelProcessor(max_workers=max_workers)

                # Process batches in parallel with retry
                successful_batches, failed_batches = (
                    parallel_processor.process_with_retry(
                        items=batches,
                        operation=update_user_batch,
                        max_retries=3,
                        retry_delay=1.0,
                        show_progress=True,
                        progress_callback=update_progress,
                    )
                )

                # Process successful batches
                for batch_result in successful_batches:
                    if batch_result["status"] == "success":
                        # Process individual user results
                        for result in batch_result.get("results", []):
                            if result.get("status") == "success":
                                results["updated"] += 1
                            else:
                                results["failed"] += 1
                                results["failed_users"].append(
                                    {
                                        "user_id": result.get("user_id"),
                                        "error": result.get("error", "Unknown error"),
                                    }
                                )
                    else:
                        # Handle batch-level errors
                        for user in batch_result.get("batch", []):
                            results["failed"] += 1
                            results["failed_users"].append(
                                {
                                    "user_id": user["user_id"],
                                    "error": batch_result.get("error", "Unknown error"),
                                }
                            )

                # Process failed batches
                for batch, error in failed_batches:
                    for user in batch:
                        results["failed"] += 1
                        results["failed_users"].append(
                            {
                                "user_id": user["user_id"],
                                "error": str(error),
                            }
                        )

            # Set status based on results
            if results["failed"] > 0:
                if results["updated"] > 0:
                    results["status"] = "partial_success"
                else:
                    results["status"] = "failure"

            # Report results
            logger.info(
                f"Batch update completed: {results['updated']} succeeded, "
                f"{results['failed']} failed"
            )

            self.client.notifier.send_notification(
                "batch_user_update",
                {
                    "user_count": len(users),
                    "successful_count": results["updated"],
                    "failed_count": results["failed"],
                    "backup": blob_name,
                },
                "Success" if results["failed"] == 0 else "Partial Success",
            )

            return results
        except Exception as e:
            logger.error(f"Failed to batch update users: {e}")
            self.client.notifier.send_notification(
                "batch_user_update", {}, "Failed", str(e)
            )

            # Restore backup if available
            if blob_name and self.client.storage:
                logger.info("Restoring backup...")
                try:
                    # Implement restore logic here
                    pass
                except Exception as restore_error:
                    logger.error(f"Failed to restore backup: {restore_error}")

            raise

    def batch_tag_data_sources(
        self,
        data_sources: List[Dict],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
        max_workers: int = 5,
    ) -> Dict:
        """Batch tag data sources.

        Args:
            data_sources: List of data source dictionaries with data_source_id and tags.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate data sources before tagging them.
            batch_size: Number of data sources to tag in each batch.

        Returns:
            Dictionary with tagging results.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            # Validate all data sources first
            for ds in data_sources:
                if "data_source_id" not in ds:
                    raise ValueError(f"Missing data_source_id in data source: {ds}")
                if "tags" not in ds:
                    raise ValueError(f"Missing tags in data source: {ds}")
                if not isinstance(ds["tags"], list):
                    raise ValueError(
                        f"Tags must be a list for data source {ds['data_source_id']}"
                    )

            logger.info(f"Validation passed for {len(data_sources)} data sources")

        if dry_run:
            # Simulate batch tagging in dry-run mode
            logger.info(f"Dry run: Would tag {len(data_sources)} data sources")
            self.client.notifier.send_notification(
                "batch_tag_data_sources_dry_run",
                {
                    "data_source_count": len(data_sources),
                    "tags": [ds["tags"] for ds in data_sources],
                    "details": f"Would tag {len(data_sources)} data sources",
                },
                "Success",
            )
            return {
                "status": "success",
                "tagged": len(data_sources),
                "failed": 0,
                "failed_data_sources": [],
            }

        # Create backup before making changes
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"data_sources_batch_{int(time.time())}.json"
            try:
                all_data_sources = self.client.data_source_service.list_data_sources()
                self.client.storage.upload_file(
                    all_data_sources,
                    blob_name,
                    metadata={
                        "type": "data_sources",
                        "operation": "batch_tag_data_sources",
                    },
                )
                logger.info(f"Created backup: {blob_name}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Batch tagged {len(data_sources)} data sources")
            self.client.notifier.send_notification(
                "batch_tag_data_sources",
                {"data_source_count": len(data_sources), "backup": blob_name},
                "Success",
            )
            return {
                "status": "success",
                "tagged": len(data_sources),
                "failed": 0,
                "failed_data_sources": [],
            }

        # Process in batches
        results = {
            "status": "success",
            "tagged": 0,
            "failed": 0,
            "failed_data_sources": [],
        }

        try:
            # Process data sources in batches
            with Progress() as progress:
                task = progress.add_task(
                    "[cyan]Tagging data sources...", total=len(data_sources)
                )

                # Define tag operation function
                def tag_data_source(ds: Dict) -> Dict:
                    """Tag a data source with the specified tags.

                    Args:
                        ds: Data source dictionary with data_source_id and tags.

                    Returns:
                        Dictionary with operation result.
                    """
                    try:
                        # Add tags to data source
                        self.client.tag_service.add_tags_to_data_source(
                            data_source_id=ds["data_source_id"], tags=ds["tags"]
                        )
                        return {
                            "status": "success",
                            "data_source_id": ds["data_source_id"],
                            "tags": ds["tags"],
                        }
                    except Exception as e:
                        logger.error(
                            f"Failed to tag data source {ds['data_source_id']}: {e}"
                        )
                        return {
                            "status": "error",
                            "data_source_id": ds["data_source_id"],
                            "error": str(e),
                        }

                # Define progress callback
                def update_progress(processed: int, total: int) -> None:
                    """Update progress bar.

                    Args:
                        processed: Number of processed items.
                        total: Total number of items.
                    """
                    # Update progress bar
                    progress.update(task, completed=processed, total=total)

                # Create a parallel processor with specified max_workers
                parallel_processor = ParallelProcessor(max_workers=max_workers)

                # Process data sources in parallel with retry
                successful_results, failed_results = (
                    parallel_processor.process_with_retry(
                        items=data_sources,
                        operation=tag_data_source,
                        max_retries=3,
                        retry_delay=1.0,
                        show_progress=True,
                        progress_callback=update_progress,
                    )
                )

                # Count successes and failures
                for result in successful_results:
                    if result["status"] == "success":
                        results["tagged"] += 1
                    else:
                        results["failed"] += 1
                        results["failed_data_sources"].append(
                            {
                                "data_source_id": result["data_source_id"],
                                "error": result.get("error", "Unknown error"),
                            }
                        )

                # Add failures from parallel processing
                for ds, error in failed_results:
                    results["failed"] += 1
                    results["failed_data_sources"].append(
                        {
                            "data_source_id": ds["data_source_id"],
                            "error": str(error),
                        }
                    )

            # Set status based on results
            if results["failed"] > 0:
                if results["tagged"] > 0:
                    results["status"] = "partial_success"
                else:
                    results["status"] = "failure"

            # Report results
            logger.info(
                f"Batch tagging completed: {results['tagged']} succeeded, "
                f"{results['failed']} failed"
            )

            self.client.notifier.send_notification(
                "batch_tag_data_sources",
                {
                    "data_source_count": len(data_sources),
                    "successful_count": results["tagged"],
                    "failed_count": results["failed"],
                    "backup": blob_name,
                },
                "Success" if results["failed"] == 0 else "Partial Success",
            )

            return results
        except Exception as e:
            logger.error(f"Failed to batch tag data sources: {e}")
            self.client.notifier.send_notification(
                "batch_tag_data_sources", {}, "Failed", str(e)
            )
            raise

    def batch_create_policies(
        self,
        policies: List[Dict],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
        max_workers: int = 5,
    ) -> Dict:
        """Batch create policies.

        Args:
            policies: List of policy dictionaries.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate policies before creating them.
            batch_size: Number of policies to create in each batch.

        Returns:
            Dictionary with creation results.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            # Validate all policies first
            for policy in policies:
                if "name" not in policy:
                    raise ValueError(f"Missing name in policy: {policy}")
                if "policy_type" not in policy:
                    raise ValueError(f"Missing policy_type in policy: {policy}")

                # Validate policy type
                valid_policy_types = ["subscription", "masking", "row_redaction"]
                if policy["policy_type"] not in valid_policy_types:
                    raise ValueError(
                        f"Invalid policy_type: {policy['policy_type']}. "
                        f"Valid types: {valid_policy_types}"
                    )

            logger.info(f"Validation passed for {len(policies)} policies")

        if dry_run:
            # Simulate batch creation in dry-run mode
            logger.info(f"Dry run: Would create {len(policies)} policies")
            self.client.notifier.send_notification(
                "batch_create_policies_dry_run",
                {
                    "policy_count": len(policies),
                    "policy_types": [p["policy_type"] for p in policies],
                    "details": f"Would create {len(policies)} policies",
                },
                "Success",
            )
            return {
                "status": "success",
                "created": len(policies),
                "failed": 0,
                "failed_policies": [],
                "created_policies": [],
            }

        # Create backup before making changes
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"policies_batch_{int(time.time())}.json"
            try:
                all_policies = self.client.policy_service.list_policies()
                self.client.storage.upload_file(
                    all_policies,
                    blob_name,
                    metadata={"type": "policies", "operation": "batch_create_policies"},
                )
                logger.info(f"Created backup: {blob_name}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Batch created {len(policies)} policies")
            self.client.notifier.send_notification(
                "batch_create_policies",
                {"policy_count": len(policies), "backup": blob_name},
                "Success",
            )
            return {
                "status": "success",
                "created": len(policies),
                "failed": 0,
                "failed_policies": [],
                "created_policies": [
                    {"id": i + 1, **p} for i, p in enumerate(policies)
                ],
            }

        # Process in batches
        results = {
            "status": "success",
            "created": 0,
            "failed": 0,
            "failed_policies": [],
            "created_policies": [],
        }

        try:
            # Process policies in batches
            with Progress() as progress:
                task = progress.add_task(
                    "[cyan]Creating policies...", total=len(policies)
                )

                # Define policy creation function
                def create_policy(policy: Dict) -> Dict:
                    """Create a policy.

                    Args:
                        policy: Policy dictionary.

                    Returns:
                        Dictionary with operation result.
                    """
                    try:
                        # Create policy
                        created_policy = self.client.policy_service.create_policy(
                            policy=policy,
                            backup=False,  # We already created a backup
                        )
                        return {
                            "status": "success",
                            "policy": created_policy,
                        }
                    except Exception as e:
                        logger.error(
                            f"Failed to create policy {policy.get('name')}: {e}"
                        )
                        return {
                            "status": "error",
                            "policy": policy,
                            "error": str(e),
                        }

                # Define progress callback
                def update_progress(processed: int, total: int) -> None:
                    """Update progress bar.

                    Args:
                        processed: Number of processed items.
                        total: Total number of items.
                    """
                    # Update progress bar
                    progress.update(task, completed=processed, total=total)

                # Create a parallel processor with specified max_workers
                parallel_processor = ParallelProcessor(max_workers=max_workers)

                # Process policies in parallel with retry
                successful_results, failed_results = (
                    parallel_processor.process_with_retry(
                        items=policies,
                        operation=create_policy,
                        max_retries=3,
                        retry_delay=1.0,
                        show_progress=True,
                        progress_callback=update_progress,
                    )
                )

                # Count successes and failures
                for result in successful_results:
                    if result["status"] == "success":
                        results["created"] += 1
                        results["created_policies"].append(result["policy"])
                    else:
                        results["failed"] += 1
                        results["failed_policies"].append(
                            {
                                "policy": result["policy"],
                                "error": result.get("error", "Unknown error"),
                            }
                        )

                # Add failures from parallel processing
                for policy, error in failed_results:
                    results["failed"] += 1
                    results["failed_policies"].append(
                        {
                            "policy": policy,
                            "error": str(error),
                        }
                    )

            # Set status based on results
            if results["failed"] > 0:
                if results["created"] > 0:
                    results["status"] = "partial_success"
                else:
                    results["status"] = "failure"

            # Report results
            logger.info(
                f"Batch policy creation completed: {results['created']} succeeded, "
                f"{results['failed']} failed"
            )

            self.client.notifier.send_notification(
                "batch_create_policies",
                {
                    "policy_count": len(policies),
                    "successful_count": results["created"],
                    "failed_count": results["failed"],
                    "backup": blob_name,
                },
                "Success" if results["failed"] == 0 else "Partial Success",
            )

            return results
        except Exception as e:
            logger.error(f"Failed to batch create policies: {e}")
            self.client.notifier.send_notification(
                "batch_create_policies", {}, "Failed", str(e)
            )
            raise

    def batch_update_data_source_metadata(
        self,
        data_sources: List[Dict],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
        max_workers: int = 5,
    ) -> Dict:
        """Batch update data source metadata.

        Args:
            data_sources: List of data source dictionaries with data_source_id and metadata.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate data sources before updating them.
            batch_size: Number of data sources to update in each batch.

        Returns:
            Dictionary with update results.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            # Validate all data sources first
            for ds in data_sources:
                if "data_source_id" not in ds:
                    raise ValueError(f"Missing data_source_id in data source: {ds}")
                if "metadata" not in ds:
                    raise ValueError(f"Missing metadata in data source: {ds}")
                if not isinstance(ds["metadata"], dict):
                    raise ValueError(
                        f"Metadata must be a dictionary for data source {ds['data_source_id']}"
                    )

            logger.info(f"Validation passed for {len(data_sources)} data sources")

        if dry_run:
            # Simulate batch update in dry-run mode
            logger.info(
                f"Dry run: Would update metadata for {len(data_sources)} data sources"
            )
            self.client.notifier.send_notification(
                "batch_update_data_source_metadata_dry_run",
                {
                    "data_source_count": len(data_sources),
                    "details": f"Would update metadata for {len(data_sources)} data sources",
                },
                "Success",
            )
            return {
                "status": "success",
                "updated": len(data_sources),
                "failed": 0,
                "failed_data_sources": [],
            }

        # Create backup before making changes
        blob_name = None
        if backup and self.client.storage:
            blob_name = f"data_sources_metadata_batch_{int(time.time())}.json"
            try:
                all_data_sources = self.client.data_source_service.list_data_sources()
                self.client.storage.upload_file(
                    all_data_sources,
                    blob_name,
                    metadata={
                        "type": "data_sources",
                        "operation": "batch_update_data_source_metadata",
                    },
                )
                logger.info(f"Created backup: {blob_name}")
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(
                f"Mock: Batch updated metadata for {len(data_sources)} data sources"
            )
            self.client.notifier.send_notification(
                "batch_update_data_source_metadata",
                {"data_source_count": len(data_sources), "backup": blob_name},
                "Success",
            )
            return {
                "status": "success",
                "updated": len(data_sources),
                "failed": 0,
                "failed_data_sources": [],
            }

        # Process in batches
        results = {
            "status": "success",
            "updated": 0,
            "failed": 0,
            "failed_data_sources": [],
        }

        try:
            # Process data sources in batches
            with Progress() as progress:
                task = progress.add_task(
                    "[cyan]Updating data source metadata...", total=len(data_sources)
                )

                # Define update metadata function
                def update_metadata(ds: Dict) -> Dict:
                    """Update metadata for a data source.

                    Args:
                        ds: Data source dictionary with data_source_id and metadata.

                    Returns:
                        Dictionary with operation result.
                    """
                    try:
                        # Update data source metadata
                        self.client.data_source_service.update_data_source(
                            data_source_id=ds["data_source_id"],
                            metadata=ds["metadata"],
                            backup=False,  # We already created a backup
                        )
                        return {
                            "status": "success",
                            "data_source_id": ds["data_source_id"],
                        }
                    except Exception as e:
                        logger.error(
                            f"Failed to update metadata for data source {ds['data_source_id']}: {e}"
                        )
                        return {
                            "status": "error",
                            "data_source_id": ds["data_source_id"],
                            "error": str(e),
                        }

                # Define progress callback
                def update_progress(processed: int, total: int) -> None:
                    """Update progress bar.

                    Args:
                        processed: Number of processed items.
                        total: Total number of items.
                    """
                    # Update progress bar
                    progress.update(task, completed=processed, total=total)

                # Create a parallel processor with specified max_workers
                parallel_processor = ParallelProcessor(max_workers=max_workers)

                # Process data sources in parallel with retry
                successful_results, failed_results = (
                    parallel_processor.process_with_retry(
                        items=data_sources,
                        operation=update_metadata,
                        max_retries=3,
                        retry_delay=1.0,
                        show_progress=True,
                        progress_callback=update_progress,
                    )
                )

                # Count successes and failures
                for result in successful_results:
                    if result["status"] == "success":
                        results["updated"] += 1
                    else:
                        results["failed"] += 1
                        results["failed_data_sources"].append(
                            {
                                "data_source_id": result["data_source_id"],
                                "error": result.get("error", "Unknown error"),
                            }
                        )

                # Add failures from parallel processing
                for ds, error in failed_results:
                    results["failed"] += 1
                    results["failed_data_sources"].append(
                        {
                            "data_source_id": ds["data_source_id"],
                            "error": str(error),
                        }
                    )

            # Set status based on results
            if results["failed"] > 0:
                if results["updated"] > 0:
                    results["status"] = "partial_success"
                else:
                    results["status"] = "failure"

            # Report results
            logger.info(
                f"Batch metadata update completed: {results['updated']} succeeded, "
                f"{results['failed']} failed"
            )

            self.client.notifier.send_notification(
                "batch_update_data_source_metadata",
                {
                    "data_source_count": len(data_sources),
                    "successful_count": results["updated"],
                    "failed_count": results["failed"],
                    "backup": blob_name,
                },
                "Success" if results["failed"] == 0 else "Partial Success",
            )

            return results
        except Exception as e:
            logger.error(f"Failed to batch update data source metadata: {e}")
            self.client.notifier.send_notification(
                "batch_update_data_source_metadata", {}, "Failed", str(e)
            )
            raise
