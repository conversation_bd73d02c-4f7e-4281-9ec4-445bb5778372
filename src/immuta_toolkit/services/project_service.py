"""Project service for the Immuta SRE Toolkit."""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Optional, Any, Union, Callable, Awaitable

from immuta_toolkit.utils.cache import get_cache
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ProjectService:
    """Service for managing projects in Immuta.

    This service provides functionality for managing projects, including
    creating, updating, and deleting projects, as well as managing project
    members and data sources.

    Attributes:
        client: Immuta client instance.
        cache: Cache for project data.
    """

    def __init__(self, client):
        """Initialize the project service.

        Args:
            client: Immuta client instance.
        """
        self.client = client
        self.cache = get_cache("projects", default_ttl_seconds=300)  # 5 minutes TTL

    def list_projects(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """List projects in Immuta.

        Args:
            limit: Maximum number of projects to return.
            offset: Offset for pagination.

        Returns:
            List of project dictionaries.
        """
        if self.client.is_local:
            # Start with the default projects
            projects = [
                {
                    "id": 1,
                    "name": "Customer Analytics",
                    "description": "Project for customer analytics",
                    "owner": {"id": 1, "email": "<EMAIL>"},
                    "members": [
                        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                        {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
                    ],
                    "dataSources": [1, 2],
                    "purposes": [1],
                    "tags": ["analytics", "customer"],
                },
                {
                    "id": 2,
                    "name": "Fraud Detection",
                    "description": "Project for fraud detection",
                    "owner": {"id": 1, "email": "<EMAIL>"},
                    "members": [
                        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                        {"id": 3, "email": "<EMAIL>", "role": "MEMBER"},
                    ],
                    "dataSources": [3],
                    "purposes": [2],
                    "tags": ["fraud", "security"],
                },
            ]

            # Add any projects that were created during tests
            deleted_projects = getattr(self, "_deleted_projects", set())

            # Check all cache keys for project_* patterns
            for key in self.cache.keys():
                if key.startswith("project_") and key != "projects_list":
                    try:
                        project_id = int(key.split("_")[1])
                        # Skip if the project has been deleted
                        if project_id in deleted_projects:
                            continue
                        # Skip if it's one of the default projects
                        if project_id in [1, 2]:
                            continue
                        # Get the project from cache
                        project = self.cache.get(key)
                        if project:
                            projects.append(project)
                    except (ValueError, IndexError):
                        # Skip invalid keys
                        pass

            return projects

        # Build query parameters
        params = {"limit": limit, "offset": offset}

        # According to Immuta API V1, the endpoint for listing projects is /projects
        response = self.client.make_request("GET", "projects", params=params)
        return response.json()

    def get_project(self, project_id: int, use_cache: bool = True) -> Dict[str, Any]:
        """Get project by ID.

        Args:
            project_id: Project ID.
            use_cache: Whether to use cached data if available.

        Returns:
            Project dictionary.

        Raises:
            ValueError: If project is not found.
        """
        # Check cache first if enabled
        cache_key = f"project_{project_id}"
        if use_cache:
            cached_project = self.cache.get(cache_key)
            if cached_project:
                logger.debug(f"Using cached project {project_id}")
                return cached_project

        if self.client.is_local:
            # Check if the project has been deleted
            deleted_projects = getattr(self, "_deleted_projects", set())
            if project_id in deleted_projects:
                raise ValueError(f"Project not found: {project_id}")

            # First check if we have a cached project from create_project
            cached_project = self.cache.get(f"project_{project_id}")
            if cached_project:
                return cached_project

            # Otherwise, return predefined projects or generate a mock project
            if project_id == 1:
                project = {
                    "id": 1,
                    "name": "Customer Analytics",
                    "description": "Project for customer analytics",
                    "owner": {"id": 1, "email": "<EMAIL>"},
                    "members": [
                        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                        {"id": 2, "email": "<EMAIL>", "role": "MEMBER"},
                    ],
                    "dataSources": [1, 2],
                    "purposes": [1],
                    "tags": ["analytics", "customer"],
                }
            elif project_id == 2:
                project = {
                    "id": 2,
                    "name": "Fraud Detection",
                    "description": "Project for fraud detection",
                    "owner": {"id": 1, "email": "<EMAIL>"},
                    "members": [
                        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                        {"id": 3, "email": "<EMAIL>", "role": "MEMBER"},
                    ],
                    "dataSources": [3],
                    "purposes": [2],
                    "tags": ["fraud", "security"],
                }
            else:
                # For integration tests, generate a mock project for any ID
                project = {
                    "id": project_id,
                    "name": f"Test Project {project_id}",
                    "description": f"Test project {project_id} for integration testing",
                    "owner": {"id": 1, "email": "<EMAIL>"},
                    "members": [
                        {"id": 1, "email": "<EMAIL>", "role": "OWNER"},
                    ],
                    "dataSources": [],
                    "purposes": [],
                    "tags": ["test", "integration"],
                }
        else:
            # According to Immuta API V1, the endpoint for getting a project is /projects/{id}
            response = self.client.make_request("GET", f"projects/{project_id}")
            project = response.json()

        # Cache the result
        self.cache.set(cache_key, project)
        return project

    def create_project(
        self,
        project: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Create a new project.

        Args:
            project: Project dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the project before creating it.

        Returns:
            Created project dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate project
        if validate:
            if not project.get("name"):
                raise ValueError("Project name is required")
            if not project.get("description"):
                raise ValueError("Project description is required")

        if dry_run:
            logger.info(f"Dry run: Would create project {project.get('name')}")
            self.client.notifier.send_notification(
                "project_create_dry_run",
                {
                    "name": project.get("name"),
                    "description": project.get("description"),
                },
                "Success",
            )
            return {"id": 0, **project}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"projects_{int(time.time())}.json"
            try:
                projects = self.list_projects()
                self.client.storage.upload_file(
                    projects,
                    blob_name,
                    metadata={
                        "type": "projects",
                        "operation": "project_create",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            # Generate a new project ID
            project_id = len(self.list_projects()) + 1

            # Create a new project with the given data
            created_project = {"id": project_id, **project}

            # Store the project in the cache
            self.cache.set(f"project_{project_id}", created_project)

            logger.info(f"Mock: Created project {project.get('name')}")
            self.client.notifier.send_notification(
                "project_create",
                {
                    "name": project.get("name"),
                    "description": project.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )
            return created_project

        try:
            # According to Immuta API V1, the endpoint for creating a project is /projects
            response = self.client.make_request("POST", "projects", data=project)
            created_project = response.json()
            logger.info(
                f"Created project {project.get('name')} with ID {created_project['id']}"
            )

            self.client.notifier.send_notification(
                "project_create",
                {
                    "id": created_project["id"],
                    "name": project.get("name"),
                    "description": project.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )

            # Invalidate list cache
            self.cache.delete("projects_list")

            return created_project
        except Exception as e:
            logger.error(f"Failed to create project: {e}")
            self.client.notifier.send_notification(
                "project_create",
                {"name": project.get("name")},
                "Failed",
                str(e),
            )
            raise

    def update_project(
        self,
        project_id: int,
        project: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Update a project.

        Args:
            project_id: Project ID.
            project: Project dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the project before updating it.

        Returns:
            Updated project dictionary.

        Raises:
            ValueError: If validation fails or project is not found.
        """
        # Get existing project to ensure it exists
        existing_project = self.get_project(project_id)

        # Validate project
        if validate:
            if not project.get("name"):
                raise ValueError("Project name is required")
            if not project.get("description"):
                raise ValueError("Project description is required")

        if dry_run:
            logger.info(f"Dry run: Would update project {project_id}")
            self.client.notifier.send_notification(
                "project_update_dry_run",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                    "description": project.get("description"),
                },
                "Success",
            )
            return {"id": project_id, **project}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"project_{project_id}_{int(time.time())}.json"
            try:
                self.client.storage.upload_file(
                    existing_project,
                    blob_name,
                    metadata={
                        "type": "project",
                        "id": project_id,
                        "operation": "project_update",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Updated project {project_id}")
            self.client.notifier.send_notification(
                "project_update",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                    "description": project.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": project_id, **project}

        try:
            # According to Immuta API V1, the endpoint for updating a project is /projects/{id}
            response = self.client.make_request(
                "PUT", f"projects/{project_id}", data=project
            )
            updated_project = response.json()
            logger.info(f"Updated project {project_id}")

            self.client.notifier.send_notification(
                "project_update",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                    "description": project.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )

            # Invalidate caches
            self.cache.delete(f"project_{project_id}")
            self.cache.delete("projects_list")

            return updated_project
        except Exception as e:
            logger.error(f"Failed to update project {project_id}: {e}")
            self.client.notifier.send_notification(
                "project_update",
                {"project_id": project_id},
                "Failed",
                str(e),
            )
            raise

    def delete_project(
        self, project_id: int, backup: bool = True, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Delete a project.

        Args:
            project_id: Project ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        # Get project details for backup and notification
        project = self.get_project(project_id)

        if dry_run:
            logger.info(
                f"Dry run: Would delete project {project_id} ({project.get('name')})"
            )
            self.client.notifier.send_notification(
                "project_delete_dry_run",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                },
                "Success",
            )
            return {"status": "dry_run_success", "project_id": project_id}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"project_{project_id}_{int(time.time())}.json"
            try:
                self.client.storage.upload_file(
                    project,
                    blob_name,
                    metadata={
                        "type": "project",
                        "id": project_id,
                        "operation": "project_delete",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            # Delete the project from the cache
            self.cache.delete(f"project_{project_id}")

            # Also delete related caches
            self.cache.delete(f"project_{project_id}_members")
            self.cache.delete(f"project_{project_id}_data_sources")
            self.cache.delete(f"project_{project_id}_purposes")

            # Invalidate the projects list cache
            self.cache.delete("projects_list")

            logger.info(f"Mock: Deleted project {project_id}")
            self.client.notifier.send_notification(
                "project_delete",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )

            # For integration tests, we need to make sure get_project raises ValueError
            # after deletion
            self._deleted_projects = getattr(self, "_deleted_projects", set())
            self._deleted_projects.add(project_id)

            return {"status": "success", "project_id": project_id}

        try:
            # According to Immuta API V1, the endpoint for deleting a project is /projects/{id}
            self.client.make_request("DELETE", f"projects/{project_id}")
            logger.info(f"Deleted project {project_id}")

            self.client.notifier.send_notification(
                "project_delete",
                {
                    "project_id": project_id,
                    "name": project.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )

            # Invalidate caches
            self.cache.delete(f"project_{project_id}")
            self.cache.delete("projects_list")
            self.cache.delete(f"project_{project_id}_members")
            self.cache.delete(f"project_{project_id}_data_sources")
            self.cache.delete(f"project_{project_id}_purposes")

            return {"status": "success", "project_id": project_id}
        except Exception as e:
            logger.error(f"Failed to delete project {project_id}: {e}")
            self.client.notifier.send_notification(
                "project_delete",
                {"project_id": project_id},
                "Failed",
                str(e),
            )
            raise

    def add_project_member(
        self, project_id: int, user_id: int, role: str = "MEMBER", dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add a member to a project.

        Args:
            project_id: Project ID.
            user_id: User ID.
            role: Member role. Valid values are "OWNER", "MEMBER".
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or user is not found, or if role is invalid.
        """
        # Validate role
        valid_roles = ["OWNER", "MEMBER"]
        if role not in valid_roles:
            raise ValueError(f"Invalid role: {role}. Valid roles: {valid_roles}")

        if dry_run:
            logger.info(
                f"Dry run: Would add user {user_id} to project {project_id} with role {role}"
            )
            self.client.notifier.send_notification(
                "add_project_member_dry_run",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                    "role": role,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would add project member",
                "project_id": project_id,
                "user_id": user_id,
                "role": role,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Added user {user_id} to project {project_id} with role {role}"
            )
            self.client.notifier.send_notification(
                "add_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                    "role": role,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "user_id": user_id,
                "role": role,
            }

        try:
            # According to Immuta API V1, the endpoint for adding a member to a project is /projects/{id}/members
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/members",
                data={"userId": user_id, "role": role},
            )
            result = response.json()
            logger.info(
                f"Added user {user_id} to project {project_id} with role {role}"
            )
            self.client.notifier.send_notification(
                "add_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                    "role": role,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to add user {user_id} to project {project_id}: {e}")
            self.client.notifier.send_notification(
                "add_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                },
                "Failed",
                str(e),
            )
            raise

    def remove_project_member(
        self, project_id: int, user_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove a member from a project.

        Args:
            project_id: Project ID.
            user_id: User ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or user is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would remove user {user_id} from project {project_id}"
            )
            self.client.notifier.send_notification(
                "remove_project_member_dry_run",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would remove project member",
                "project_id": project_id,
                "user_id": user_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Removed user {user_id} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "user_id": user_id,
            }

        try:
            # According to Immuta API V1, the endpoint for removing a member from a project is /projects/{id}/members/{userId}
            self.client.make_request(
                "DELETE", f"projects/{project_id}/members/{user_id}"
            )
            logger.info(f"Removed user {user_id} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "user_id": user_id,
            }
        except Exception as e:
            logger.error(
                f"Failed to remove user {user_id} from project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "remove_project_member",
                {
                    "project_id": project_id,
                    "user_id": user_id,
                },
                "Failed",
                str(e),
            )
            raise

    def add_data_source_to_project(
        self, project_id: int, data_source_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add a data source to a project.

        Args:
            project_id: Project ID.
            data_source_id: Data source ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or data source is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would add data source {data_source_id} to project {project_id}"
            )
            self.client.notifier.send_notification(
                "add_data_source_to_project_dry_run",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would add data source to project",
                "project_id": project_id,
                "data_source_id": data_source_id,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Added data source {data_source_id} to project {project_id}"
            )
            self.client.notifier.send_notification(
                "add_data_source_to_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "data_source_id": data_source_id,
            }

        try:
            # According to Immuta API V1, the endpoint for adding a data source to a project is /projects/{id}/dataSources
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/dataSources",
                data={"dataSourceId": data_source_id},
            )
            result = response.json()
            logger.info(f"Added data source {data_source_id} to project {project_id}")
            self.client.notifier.send_notification(
                "add_data_source_to_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(
                f"Failed to add data source {data_source_id} to project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "add_data_source_to_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Failed",
                str(e),
            )
            raise

    def remove_data_source_from_project(
        self, project_id: int, data_source_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove a data source from a project.

        Args:
            project_id: Project ID.
            data_source_id: Data source ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or data source is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would remove data source {data_source_id} from project {project_id}"
            )
            self.client.notifier.send_notification(
                "remove_data_source_from_project_dry_run",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would remove data source from project",
                "project_id": project_id,
                "data_source_id": data_source_id,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Removed data source {data_source_id} from project {project_id}"
            )
            self.client.notifier.send_notification(
                "remove_data_source_from_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "data_source_id": data_source_id,
            }

        try:
            # According to Immuta API V1, the endpoint for removing a data source from a project is /projects/{id}/dataSources/{dataSourceId}
            self.client.make_request(
                "DELETE", f"projects/{project_id}/dataSources/{data_source_id}"
            )
            logger.info(
                f"Removed data source {data_source_id} from project {project_id}"
            )
            self.client.notifier.send_notification(
                "remove_data_source_from_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "data_source_id": data_source_id,
            }
        except Exception as e:
            logger.error(
                f"Failed to remove data source {data_source_id} from project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "remove_data_source_from_project",
                {
                    "project_id": project_id,
                    "data_source_id": data_source_id,
                },
                "Failed",
                str(e),
            )
            raise

    def add_purpose_to_project(
        self, project_id: int, purpose_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add a purpose to a project.

        Args:
            project_id: Project ID.
            purpose_id: Purpose ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or purpose is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would add purpose {purpose_id} to project {project_id}"
            )
            self.client.notifier.send_notification(
                "add_purpose_to_project_dry_run",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would add purpose to project",
                "project_id": project_id,
                "purpose_id": purpose_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Added purpose {purpose_id} to project {project_id}")
            self.client.notifier.send_notification(
                "add_purpose_to_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "purpose_id": purpose_id,
            }

        try:
            # According to Immuta API V1, the endpoint for adding a purpose to a project is /projects/{id}/purposes
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/purposes",
                data={"purposeId": purpose_id},
            )
            result = response.json()
            logger.info(f"Added purpose {purpose_id} to project {project_id}")
            self.client.notifier.send_notification(
                "add_purpose_to_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(
                f"Failed to add purpose {purpose_id} to project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "add_purpose_to_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Failed",
                str(e),
            )
            raise

    def remove_purpose_from_project(
        self, project_id: int, purpose_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove a purpose from a project.

        Args:
            project_id: Project ID.
            purpose_id: Purpose ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project or purpose is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would remove purpose {purpose_id} from project {project_id}"
            )
            self.client.notifier.send_notification(
                "remove_purpose_from_project_dry_run",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would remove purpose from project",
                "project_id": project_id,
                "purpose_id": purpose_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Removed purpose {purpose_id} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_purpose_from_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "purpose_id": purpose_id,
            }

        try:
            # According to Immuta API V1, the endpoint for removing a purpose from a project is /projects/{id}/purposes/{purposeId}
            self.client.make_request(
                "DELETE", f"projects/{project_id}/purposes/{purpose_id}"
            )
            logger.info(f"Removed purpose {purpose_id} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_purpose_from_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Success",
            )
            return {
                "success": True,
                "project_id": project_id,
                "purpose_id": purpose_id,
            }
        except Exception as e:
            logger.error(
                f"Failed to remove purpose {purpose_id} from project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "remove_purpose_from_project",
                {
                    "project_id": project_id,
                    "purpose_id": purpose_id,
                },
                "Failed",
                str(e),
            )
            raise

    def get_project_members(self, project_id: int) -> List[Dict[str, Any]]:
        """Get members of a project.

        Args:
            project_id: Project ID.

        Returns:
            List of project member dictionaries.

        Raises:
            ValueError: If project is not found.
        """
        # Get project details to ensure it exists and to get members in local mode
        project = self.get_project(project_id)

        if self.client.is_local:
            return project.get("members", [])

        # According to Immuta API V1, the endpoint for getting project members is /projects/{id}/members
        response = self.client.make_request("GET", f"projects/{project_id}/members")
        return response.json()

    def get_project_data_sources(self, project_id: int) -> List[Dict[str, Any]]:
        """Get data sources of a project.

        Args:
            project_id: Project ID.

        Returns:
            List of project data source dictionaries.

        Raises:
            ValueError: If project is not found.
        """
        # Get project details to ensure it exists
        project = self.get_project(project_id)

        if self.client.is_local:
            data_source_ids = project.get("dataSources", [])
            return [
                {"id": ds_id, "name": f"Data Source {ds_id}"}
                for ds_id in data_source_ids
            ]

        # According to Immuta API V1, the endpoint for getting project data sources is /projects/{id}/dataSources
        response = self.client.make_request("GET", f"projects/{project_id}/dataSources")
        return response.json()

    def get_project_purposes(
        self, project_id: int, use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """Get purposes of a project.

        Args:
            project_id: Project ID.
            use_cache: Whether to use cached data if available.

        Returns:
            List of project purpose dictionaries.

        Raises:
            ValueError: If project is not found.
        """
        # Check cache first if enabled
        cache_key = f"project_{project_id}_purposes"
        if use_cache:
            cached_purposes = self.cache.get(cache_key)
            if cached_purposes:
                logger.debug(f"Using cached purposes for project {project_id}")
                return cached_purposes

        # Get project details to ensure it exists
        project = self.get_project(project_id)

        if self.client.is_local:
            purpose_ids = project.get(
                "purposes", [1, 2]
            )  # Default purposes if none specified
            purposes = [
                {"id": 1, "name": "Marketing", "description": "Marketing purpose"},
                {"id": 2, "name": "Analytics", "description": "Analytics purpose"},
            ]
        else:
            # According to Immuta API V1, the endpoint for getting project purposes is /projects/{id}/purposes
            response = self.client.make_request(
                "GET", f"projects/{project_id}/purposes"
            )
            purposes = response.json()

        # Cache the result
        self.cache.set(cache_key, purposes)
        return purposes

    def list_project_tags(
        self, project_id: int, use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """List tags of a project.

        Args:
            project_id: Project ID.
            use_cache: Whether to use cached data if available.

        Returns:
            List of project tag dictionaries.

        Raises:
            ValueError: If project is not found.
        """
        # Check cache first if enabled
        cache_key = f"project_{project_id}_tags"
        if use_cache:
            cached_tags = self.cache.get(cache_key)
            if cached_tags:
                logger.debug(f"Using cached tags for project {project_id}")
                return cached_tags

        if self.client.is_local:
            # For local mode, we don't need to call get_project
            # Just return the tags from the default projects
            if project_id == 1:
                tags = ["analytics", "customer"]
            elif project_id == 2:
                tags = ["fraud", "security"]
            else:
                tags = ["test", "integration"]
            return tags
        else:
            # According to Immuta API V1, the endpoint for getting project tags is /projects/{id}/tags
            response = self.client.make_request("GET", f"projects/{project_id}/tags")
            tags = response.json()

        # Cache the result
        self.cache.set(cache_key, tags)
        return tags

    def add_tag_to_project(
        self, project_id: int, tag: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add a tag to a project.

        Args:
            project_id: Project ID.
            tag: Tag to add.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if dry_run:
            logger.info(f"Dry run: Would add tag {tag} to project {project_id}")
            self.client.notifier.send_notification(
                "add_tag_to_project_dry_run",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would add tag {tag} to project {project_id}",
                "project_id": project_id,
                "tag": tag,
            }

        if self.client.is_local:
            logger.info(f"Mock: Added tag {tag} to project {project_id}")
            self.client.notifier.send_notification(
                "add_tag_to_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )
            return {
                "status": "success",
                "project_id": project_id,
                "tag": tag,
            }

        try:
            # According to Immuta API V1, the endpoint for adding a tag to a project is /projects/{id}/tags
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/tags",
                data={"name": tag},
            )
            result = response.json()
            logger.info(f"Added tag {tag} to project {project_id}")
            self.client.notifier.send_notification(
                "add_tag_to_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )

            # Invalidate tags cache
            self.cache.delete(f"project_{project_id}_tags")

            return result
        except Exception as e:
            logger.error(f"Failed to add tag {tag} to project {project_id}: {e}")
            self.client.notifier.send_notification(
                "add_tag_to_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Failed",
                str(e),
            )
            raise

    def remove_tag_from_project(
        self, project_id: int, tag: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove a tag from a project.

        Args:
            project_id: Project ID.
            tag: Tag to remove.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if dry_run:
            logger.info(f"Dry run: Would remove tag {tag} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_tag_from_project_dry_run",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would remove tag {tag} from project {project_id}",
                "project_id": project_id,
                "tag": tag,
            }

        if self.client.is_local:
            logger.info(f"Mock: Removed tag {tag} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_tag_from_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )
            return {
                "status": "success",
                "project_id": project_id,
                "tag": tag,
            }

        try:
            # According to Immuta API V1, the endpoint for removing a tag from a project is /projects/{id}/tags/{tag}
            self.client.make_request("DELETE", f"projects/{project_id}/tags/{tag}")
            logger.info(f"Removed tag {tag} from project {project_id}")
            self.client.notifier.send_notification(
                "remove_tag_from_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Success",
            )

            # Invalidate tags cache
            self.cache.delete(f"project_{project_id}_tags")

            return {
                "success": True,
                "project_id": project_id,
                "tag": tag,
            }
        except Exception as e:
            logger.error(f"Failed to remove tag {tag} from project {project_id}: {e}")
            self.client.notifier.send_notification(
                "remove_tag_from_project",
                {
                    "project_id": project_id,
                    "tag": tag,
                },
                "Failed",
                str(e),
            )
            raise

    def batch_add_members_to_project(
        self,
        project_id: int,
        members: List[Dict[str, Any]],
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Add multiple members to a project in a single operation.

        Args:
            project_id: Project ID.
            members: List of member dictionaries, each with userId and role.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found, or if role is invalid.
        """
        if not members:
            return {"success": True, "message": "No members to add", "added": 0}

        # Validate members
        for member in members:
            if "userId" not in member:
                raise ValueError(f"Missing userId in member: {member}")
            if "role" not in member:
                raise ValueError(f"Missing role in member: {member}")

            # Validate role
            valid_roles = ["OWNER", "MEMBER"]
            if member["role"] not in valid_roles:
                raise ValueError(
                    f"Invalid role: {member['role']}. Valid roles: {valid_roles}"
                )

        if dry_run:
            logger.info(
                f"Dry run: Would add {len(members)} members to project {project_id}"
            )
            self.client.notifier.send_notification(
                "batch_add_project_members_dry_run",
                {
                    "project_id": project_id,
                    "member_count": len(members),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would add {len(members)} members to project",
                "project_id": project_id,
                "members": members,
            }

        if self.client.is_local:
            logger.info(f"Mock: Added {len(members)} members to project {project_id}")
            self.client.notifier.send_notification(
                "batch_add_project_members",
                {
                    "project_id": project_id,
                    "member_count": len(members),
                },
                "Success",
            )
            return {
                "status": "success",
                "added": len(members),
                "project_id": project_id,
                "members": members,
            }

        try:
            # According to Immuta API V1, the endpoint for batch adding members to a project is /projects/{id}/members/bulk
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/members/bulk",
                data={"members": members},
            )
            result = response.json()
            logger.info(f"Added {len(members)} members to project {project_id}")

            # Invalidate members cache
            self.cache.delete(f"project_{project_id}_members")

            self.client.notifier.send_notification(
                "batch_add_project_members",
                {
                    "project_id": project_id,
                    "member_count": len(members),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to add members to project {project_id}: {e}")
            self.client.notifier.send_notification(
                "batch_add_project_members",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def bulk_remove_members_from_project(
        self,
        project_id: int,
        user_ids: List[int],
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Remove multiple members from a project in a single operation.

        Args:
            project_id: Project ID.
            user_ids: List of user IDs to remove.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not user_ids:
            return {"success": True, "message": "No members to remove", "removed": 0}

        if dry_run:
            logger.info(
                f"Dry run: Would remove {len(user_ids)} members from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_project_members_dry_run",
                {
                    "project_id": project_id,
                    "user_count": len(user_ids),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would remove {len(user_ids)} members from project",
                "project_id": project_id,
                "user_ids": user_ids,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Removed {len(user_ids)} members from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_project_members",
                {
                    "project_id": project_id,
                    "user_count": len(user_ids),
                },
                "Success",
            )
            return {
                "status": "success",
                "removed": len(user_ids),
                "project_id": project_id,
                "user_ids": user_ids,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk removing members from a project is /projects/{id}/members/bulk
            response = self.client.make_request(
                "DELETE",
                f"projects/{project_id}/members/bulk",
                data={"userIds": user_ids},
            )
            result = response.json()
            logger.info(f"Removed {len(user_ids)} members from project {project_id}")

            # Invalidate members cache
            self.cache.delete(f"project_{project_id}_members")

            self.client.notifier.send_notification(
                "bulk_remove_project_members",
                {
                    "project_id": project_id,
                    "user_count": len(user_ids),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to remove members from project {project_id}: {e}")
            self.client.notifier.send_notification(
                "bulk_remove_project_members",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def batch_add_data_sources_to_project(
        self, project_id: int, data_source_ids: List[int], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add multiple data sources to a project in a single operation.

        Args:
            project_id: Project ID.
            data_source_ids: List of data source IDs to add.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not data_source_ids:
            return {"success": True, "message": "No data sources to add", "added": 0}

        if dry_run:
            logger.info(
                f"Dry run: Would add {len(data_source_ids)} data sources to project {project_id}"
            )
            self.client.notifier.send_notification(
                "batch_add_data_sources_to_project_dry_run",
                {
                    "project_id": project_id,
                    "data_source_count": len(data_source_ids),
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Dry run: Would add {len(data_source_ids)} data sources to project",
                "project_id": project_id,
                "data_source_ids": data_source_ids,
            }

        results = {"success": True, "added": 0, "failed": 0, "errors": []}

        for data_source_id in data_source_ids:
            try:
                self.add_data_source_to_project(
                    project_id, data_source_id, dry_run=False
                )
                results["added"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(
                    {"data_source_id": data_source_id, "error": str(e)}
                )
                logger.error(
                    f"Failed to add data source {data_source_id} to project {project_id}: {e}"
                )

        # Invalidate data sources cache
        self.cache.delete(f"project_{project_id}_data_sources")

        logger.info(
            f"Added {results['added']} data sources to project {project_id}, {results['failed']} failed"
        )

        self.client.notifier.send_notification(
            "batch_add_data_sources_to_project",
            {
                "project_id": project_id,
                "added": results["added"],
                "failed": results["failed"],
            },
            "Success" if results["failed"] == 0 else "Partial",
        )

        return results

    def bulk_remove_data_sources_from_project(
        self, project_id: int, data_source_ids: List[int], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove multiple data sources from a project in a single operation.

        Args:
            project_id: Project ID.
            data_source_ids: List of data source IDs to remove.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not data_source_ids:
            return {
                "success": True,
                "message": "No data sources to remove",
                "removed": 0,
            }

        if dry_run:
            logger.info(
                f"Dry run: Would remove {len(data_source_ids)} data sources from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_data_sources_from_project_dry_run",
                {
                    "project_id": project_id,
                    "data_source_count": len(data_source_ids),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would remove {len(data_source_ids)} data sources from project",
                "project_id": project_id,
                "data_source_ids": data_source_ids,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Removed {len(data_source_ids)} data sources from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_data_sources_from_project",
                {
                    "project_id": project_id,
                    "data_source_count": len(data_source_ids),
                },
                "Success",
            )
            return {
                "status": "success",
                "removed": len(data_source_ids),
                "project_id": project_id,
                "data_source_ids": data_source_ids,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk removing data sources from a project is /projects/{id}/dataSources/bulk
            response = self.client.make_request(
                "DELETE",
                f"projects/{project_id}/dataSources/bulk",
                data={"dataSourceIds": data_source_ids},
            )
            result = response.json()
            logger.info(
                f"Removed {len(data_source_ids)} data sources from project {project_id}"
            )

            # Invalidate data sources cache
            self.cache.delete(f"project_{project_id}_data_sources")

            self.client.notifier.send_notification(
                "bulk_remove_data_sources_from_project",
                {
                    "project_id": project_id,
                    "data_source_count": len(data_source_ids),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(
                f"Failed to remove data sources from project {project_id}: {e}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_data_sources_from_project",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def bulk_add_purposes_to_project(
        self, project_id: int, purpose_ids: List[int], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add multiple purposes to a project in a single operation.

        Args:
            project_id: Project ID.
            purpose_ids: List of purpose IDs to add.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not purpose_ids:
            return {"success": True, "message": "No purposes to add", "added": 0}

        if dry_run:
            logger.info(
                f"Dry run: Would add {len(purpose_ids)} purposes to project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_add_purposes_to_project_dry_run",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would add {len(purpose_ids)} purposes to project",
                "project_id": project_id,
                "purpose_ids": purpose_ids,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Added {len(purpose_ids)} purposes to project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_add_purposes_to_project",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return {
                "status": "success",
                "added": len(purpose_ids),
                "project_id": project_id,
                "purpose_ids": purpose_ids,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk adding purposes to a project is /projects/{id}/purposes/bulk
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/purposes/bulk",
                data={"purposeIds": purpose_ids},
            )
            result = response.json()
            logger.info(f"Added {len(purpose_ids)} purposes to project {project_id}")

            # Invalidate purposes cache
            self.cache.delete(f"project_{project_id}_purposes")

            self.client.notifier.send_notification(
                "bulk_add_purposes_to_project",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to add purposes to project {project_id}: {e}")
            self.client.notifier.send_notification(
                "bulk_add_purposes_to_project",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def bulk_remove_purposes_from_project(
        self, project_id: int, purpose_ids: List[int], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove multiple purposes from a project in a single operation.

        Args:
            project_id: Project ID.
            purpose_ids: List of purpose IDs to remove.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not purpose_ids:
            return {"success": True, "message": "No purposes to remove", "removed": 0}

        if dry_run:
            logger.info(
                f"Dry run: Would remove {len(purpose_ids)} purposes from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_purposes_from_project_dry_run",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would remove {len(purpose_ids)} purposes from project",
                "project_id": project_id,
                "purpose_ids": purpose_ids,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Removed {len(purpose_ids)} purposes from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_purposes_from_project",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return {
                "status": "success",
                "removed": len(purpose_ids),
                "project_id": project_id,
                "purpose_ids": purpose_ids,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk removing purposes from a project is /projects/{id}/purposes/bulk
            response = self.client.make_request(
                "DELETE",
                f"projects/{project_id}/purposes/bulk",
                data={"purposeIds": purpose_ids},
            )
            result = response.json()
            logger.info(
                f"Removed {len(purpose_ids)} purposes from project {project_id}"
            )

            # Invalidate purposes cache
            self.cache.delete(f"project_{project_id}_purposes")

            self.client.notifier.send_notification(
                "bulk_remove_purposes_from_project",
                {
                    "project_id": project_id,
                    "purpose_count": len(purpose_ids),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to remove purposes from project {project_id}: {e}")
            self.client.notifier.send_notification(
                "bulk_remove_purposes_from_project",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def bulk_add_tags_to_project(
        self, project_id: int, tags: List[str], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Add multiple tags to a project in a single operation.

        Args:
            project_id: Project ID.
            tags: List of tags to add.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not tags:
            return {"success": True, "message": "No tags to add", "added": 0}

        if dry_run:
            logger.info(f"Dry run: Would add {len(tags)} tags to project {project_id}")
            self.client.notifier.send_notification(
                "bulk_add_tags_to_project_dry_run",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would add {len(tags)} tags to project",
                "project_id": project_id,
                "tags": tags,
            }

        if self.client.is_local:
            logger.info(f"Mock: Added {len(tags)} tags to project {project_id}")
            self.client.notifier.send_notification(
                "bulk_add_tags_to_project",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return {
                "status": "success",
                "added": len(tags),
                "project_id": project_id,
                "tags": tags,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk adding tags to a project is /projects/{id}/tags/bulk
            response = self.client.make_request(
                "POST",
                f"projects/{project_id}/tags/bulk",
                data={"tags": tags},
            )
            result = response.json()
            logger.info(f"Added {len(tags)} tags to project {project_id}")

            # Invalidate tags cache
            self.cache.delete(f"project_{project_id}_tags")

            self.client.notifier.send_notification(
                "bulk_add_tags_to_project",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to add tags to project {project_id}: {e}")
            self.client.notifier.send_notification(
                "bulk_add_tags_to_project",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    def bulk_remove_tags_from_project(
        self, project_id: int, tags: List[str], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove multiple tags from a project in a single operation.

        Args:
            project_id: Project ID.
            tags: List of tags to remove.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found.
        """
        if not tags:
            return {"success": True, "message": "No tags to remove", "removed": 0}

        if dry_run:
            logger.info(
                f"Dry run: Would remove {len(tags)} tags from project {project_id}"
            )
            self.client.notifier.send_notification(
                "bulk_remove_tags_from_project_dry_run",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return {
                "status": "dry_run_success",
                "message": f"Dry run: Would remove {len(tags)} tags from project",
                "project_id": project_id,
                "tags": tags,
            }

        if self.client.is_local:
            logger.info(f"Mock: Removed {len(tags)} tags from project {project_id}")
            self.client.notifier.send_notification(
                "bulk_remove_tags_from_project",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return {
                "status": "success",
                "removed": len(tags),
                "project_id": project_id,
                "tags": tags,
            }

        try:
            # According to Immuta API V1, the endpoint for bulk removing tags from a project is /projects/{id}/tags/bulk
            response = self.client.make_request(
                "DELETE",
                f"projects/{project_id}/tags/bulk",
                data={"tags": tags},
            )
            result = response.json()
            logger.info(f"Removed {len(tags)} tags from project {project_id}")

            # Invalidate tags cache
            self.cache.delete(f"project_{project_id}_tags")

            self.client.notifier.send_notification(
                "bulk_remove_tags_from_project",
                {
                    "project_id": project_id,
                    "tag_count": len(tags),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to remove tags from project {project_id}: {e}")
            self.client.notifier.send_notification(
                "bulk_remove_tags_from_project",
                {
                    "project_id": project_id,
                },
                "Failed",
                str(e),
            )
            raise

    async def list_projects_async(
        self, limit: int = 1000, offset: int = 0
    ) -> List[Dict[str, Any]]:
        """List projects in Immuta asynchronously.

        Args:
            limit: Maximum number of projects to return.
            offset: Offset for pagination.

        Returns:
            List of project dictionaries.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, lambda: self.list_projects(limit=limit, offset=offset)
        )

    async def get_project_async(
        self, project_id: int, use_cache: bool = True
    ) -> Dict[str, Any]:
        """Get project by ID asynchronously.

        Args:
            project_id: Project ID.
            use_cache: Whether to use cached data if available.

        Returns:
            Project dictionary.

        Raises:
            ValueError: If project is not found.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, lambda: self.get_project(project_id, use_cache=use_cache)
        )

    async def create_project_async(
        self,
        project: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Create a new project asynchronously.

        Args:
            project: Project dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the project before creating it.

        Returns:
            Created project dictionary.

        Raises:
            ValueError: If validation fails.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.create_project(
                project=project, backup=backup, dry_run=dry_run, validate=validate
            ),
        )

    async def batch_add_members_to_project_async(
        self,
        project_id: int,
        members: List[Dict[str, Any]],
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Add multiple members to a project in a single operation asynchronously.

        Args:
            project_id: Project ID.
            members: List of member dictionaries, each with userId and role.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If project is not found, or if role is invalid.
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            lambda: self.batch_add_members_to_project(
                project_id=project_id, members=members, dry_run=dry_run
            ),
        )

    async def batch_operation_async(
        self, operations: List[Dict[str, Any]], max_workers: int = 5
    ) -> List[Dict[str, Any]]:
        """Execute multiple operations asynchronously.

        Args:
            operations: List of operations to execute. Each operation is a dictionary with:
                - "type": Operation type (e.g., "create_project", "add_member").
                - "params": Parameters for the operation.
            max_workers: Maximum number of concurrent workers.

        Returns:
            List of operation results.
        """
        results = []

        # Create a thread pool executor
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create tasks for each operation
            tasks = []
            for op in operations:
                op_type = op.get("type")
                params = op.get("params", {})

                if op_type == "create_project":
                    task = self.create_project_async(**params)
                elif op_type == "update_project":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.update_project(**params)
                    )
                elif op_type == "delete_project":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.delete_project(**params)
                    )
                elif op_type == "add_member":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.add_project_member(**params)
                    )
                elif op_type == "remove_member":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.remove_project_member(**params)
                    )
                elif op_type == "add_data_source":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.add_data_source_to_project(**params)
                    )
                elif op_type == "remove_data_source":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.remove_data_source_from_project(**params)
                    )
                elif op_type == "add_purpose":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.add_purpose_to_project(**params)
                    )
                elif op_type == "remove_purpose":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor, lambda: self.remove_purpose_from_project(**params)
                    )
                elif op_type == "batch_add_members":
                    task = self.batch_add_members_to_project_async(**params)
                elif op_type == "batch_add_data_sources":
                    task = asyncio.get_event_loop().run_in_executor(
                        executor,
                        lambda: self.batch_add_data_sources_to_project(**params),
                    )
                else:
                    logger.warning(f"Unknown operation type: {op_type}")
                    continue

                tasks.append((op_type, task))

            # Wait for all tasks to complete
            for op_type, task in tasks:
                try:
                    result = await task
                    results.append({"type": op_type, "success": True, "result": result})
                except Exception as e:
                    logger.error(f"Failed to execute operation {op_type}: {e}")
                    results.append({"type": op_type, "success": False, "error": str(e)})

        return results
