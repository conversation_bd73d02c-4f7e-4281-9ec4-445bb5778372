"""Purpose service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List, Optional, Any

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class PurposeService:
    """Service for managing purposes in Immuta.

    This service provides functionality for managing purposes, including
    creating, updating, and deleting purposes, as well as managing purpose
    approvals and subscriptions.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the purpose service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def list_purposes(self, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """List purposes in Immuta.

        Args:
            limit: Maximum number of purposes to return.
            offset: Offset for pagination.

        Returns:
            List of purpose dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "name": "Marketing Analytics",
                    "description": "For marketing analytics purposes",
                    "approvalRequired": True,
                    "autoApproval": False,
                    "approvalWorkflow": "manual",
                    "approvers": ["<EMAIL>"],
                    "tags": ["marketing", "analytics"],
                },
                {
                    "id": 2,
                    "name": "Fraud Detection",
                    "description": "For fraud detection purposes",
                    "approvalRequired": True,
                    "autoApproval": True,
                    "approvalWorkflow": "auto",
                    "approvers": [],
                    "tags": ["fraud", "security"],
                },
            ]

        # According to Immuta API V1, the endpoint for listing purposes is /purposes
        response = self.client.make_request(
            "GET", "purposes", params={"limit": limit, "offset": offset}
        )
        return response.json()

    def get_purpose(self, purpose_id: int) -> Dict[str, Any]:
        """Get purpose by ID.

        Args:
            purpose_id: Purpose ID.

        Returns:
            Purpose dictionary.

        Raises:
            ValueError: If purpose is not found.
        """
        if self.client.is_local:
            if purpose_id == 1:
                return {
                    "id": 1,
                    "name": "Marketing Analytics",
                    "description": "For marketing analytics purposes",
                    "approvalRequired": True,
                    "autoApproval": False,
                    "approvalWorkflow": "manual",
                    "approvers": ["<EMAIL>"],
                    "tags": ["marketing", "analytics"],
                }
            elif purpose_id == 2:
                return {
                    "id": 2,
                    "name": "Fraud Detection",
                    "description": "For fraud detection purposes",
                    "approvalRequired": True,
                    "autoApproval": True,
                    "approvalWorkflow": "auto",
                    "approvers": [],
                    "tags": ["fraud", "security"],
                }
            else:
                raise ValueError(f"Purpose not found: {purpose_id}")

        # According to Immuta API V1, the endpoint for getting a purpose by ID is /purposes/{id}
        response = self.client.make_request("GET", f"purposes/{purpose_id}")
        return response.json()

    def create_purpose(
        self,
        purpose: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Create a new purpose.

        Args:
            purpose: Purpose dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the purpose before creating it.

        Returns:
            Created purpose dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate purpose
        if validate:
            if not purpose.get("name"):
                raise ValueError("Purpose name is required")
            if not purpose.get("description"):
                raise ValueError("Purpose description is required")

        if dry_run:
            logger.info(f"Dry run: Would create purpose {purpose.get('name')}")
            self.client.notifier.send_notification(
                "purpose_create_dry_run",
                {
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                    "approval_required": purpose.get("approvalRequired", False),
                },
                "Success",
            )
            return {"id": 0, **purpose}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"purposes_{int(time.time())}.json"
            try:
                purposes = self.list_purposes()
                self.client.storage.upload_file(
                    purposes,
                    blob_name,
                    metadata={
                        "type": "purposes",
                        "operation": "purpose_create",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Created purpose {purpose.get('name')}")
            self.client.notifier.send_notification(
                "purpose_create",
                {
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                    "approval_required": purpose.get("approvalRequired", False),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": 3, **purpose}

        try:
            # According to Immuta API V1, the endpoint for creating a purpose is /purposes
            response = self.client.make_request("POST", "purposes", data=purpose)
            created_purpose = response.json()
            logger.info(
                f"Created purpose {purpose.get('name')} with ID {created_purpose['id']}"
            )

            self.client.notifier.send_notification(
                "purpose_create",
                {
                    "id": created_purpose["id"],
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                    "approval_required": purpose.get("approvalRequired", False),
                    "backup": blob_name,
                },
                "Success",
            )
            return created_purpose
        except Exception as e:
            logger.error(f"Failed to create purpose: {e}")
            self.client.notifier.send_notification(
                "purpose_create",
                {"name": purpose.get("name")},
                "Failed",
                str(e),
            )
            raise

    def update_purpose(
        self,
        purpose_id: int,
        purpose: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Update an existing purpose.

        Args:
            purpose_id: Purpose ID.
            purpose: Purpose dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the purpose before updating it.

        Returns:
            Updated purpose dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate purpose
        if validate:
            if not purpose.get("name"):
                raise ValueError("Purpose name is required")
            if not purpose.get("description"):
                raise ValueError("Purpose description is required")

        if dry_run:
            logger.info(f"Dry run: Would update purpose {purpose_id}")
            self.client.notifier.send_notification(
                "purpose_update_dry_run",
                {
                    "id": purpose_id,
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                },
                "Success",
            )
            return {"id": purpose_id, **purpose}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"purpose_{purpose_id}_{int(time.time())}.json"
            try:
                current_purpose = self.get_purpose(purpose_id)
                self.client.storage.upload_file(
                    current_purpose,
                    blob_name,
                    metadata={
                        "type": "purpose",
                        "id": str(purpose_id),
                        "operation": "purpose_update",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Updated purpose {purpose_id}")
            self.client.notifier.send_notification(
                "purpose_update",
                {
                    "id": purpose_id,
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": purpose_id, **purpose}

        try:
            # According to Immuta API V1, the endpoint for updating a purpose is /purposes/{id}
            response = self.client.make_request(
                "PUT", f"purposes/{purpose_id}", data=purpose
            )
            updated_purpose = response.json()
            logger.info(f"Updated purpose {purpose_id}")

            self.client.notifier.send_notification(
                "purpose_update",
                {
                    "id": purpose_id,
                    "name": purpose.get("name"),
                    "description": purpose.get("description"),
                    "backup": blob_name,
                },
                "Success",
            )
            return updated_purpose
        except Exception as e:
            logger.error(f"Failed to update purpose: {e}")
            self.client.notifier.send_notification(
                "purpose_update", {"id": purpose_id}, "Failed", str(e)
            )
            raise

    def get_purpose_approvals(
        self, purpose_id: int, status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get approvals for a purpose.

        Args:
            purpose_id: Purpose ID.
            status: Optional status filter. Valid values are "pending", "approved", "denied".

        Returns:
            List of approval dictionaries.

        Raises:
            ValueError: If purpose is not found.
        """
        if self.client.is_local:
            approvals = [
                {
                    "id": 1,
                    "purposeId": purpose_id,
                    "userId": 1,
                    "userName": "John Doe",
                    "userEmail": "<EMAIL>",
                    "status": "pending",
                    "requestedAt": "2023-01-01T00:00:00Z",
                    "reason": "Need access for marketing analysis",
                },
                {
                    "id": 2,
                    "purposeId": purpose_id,
                    "userId": 2,
                    "userName": "Jane Smith",
                    "userEmail": "<EMAIL>",
                    "status": "approved",
                    "requestedAt": "2023-01-02T00:00:00Z",
                    "approvedAt": "2023-01-03T00:00:00Z",
                    "reason": "Need access for customer segmentation",
                },
                {
                    "id": 3,
                    "purposeId": purpose_id,
                    "userId": 3,
                    "userName": "Bob Johnson",
                    "userEmail": "<EMAIL>",
                    "status": "denied",
                    "requestedAt": "2023-01-04T00:00:00Z",
                    "deniedAt": "2023-01-05T00:00:00Z",
                    "reason": "Need access for campaign analysis",
                    "denialReason": "Insufficient justification",
                },
            ]

            if status:
                return [
                    approval for approval in approvals if approval["status"] == status
                ]
            return approvals

        # Build query parameters
        params = {}
        if status:
            params["status"] = status

        # According to Immuta API V1, the endpoint for getting purpose approvals is /purposes/{id}/approvals
        response = self.client.make_request(
            "GET", f"purposes/{purpose_id}/approvals", params=params
        )
        return response.json()

    def approve_purpose_request(
        self, purpose_id: int, approval_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Approve a purpose request.

        Args:
            purpose_id: Purpose ID.
            approval_id: Approval ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If purpose or approval is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would approve purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "approve_purpose_request_dry_run",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would approve purpose request",
                "purpose_id": purpose_id,
                "approval_id": approval_id,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Approved purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "approve_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                },
                "Success",
            )
            return {
                "success": True,
                "purpose_id": purpose_id,
                "approval_id": approval_id,
            }

        try:
            # According to Immuta API V1, the endpoint for approving a purpose request is /purposes/{id}/approvals/{approvalId}/approve
            response = self.client.make_request(
                "POST", f"purposes/{purpose_id}/approvals/{approval_id}/approve"
            )
            result = response.json()
            logger.info(
                f"Approved purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "approve_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to approve purpose request: {e}")
            self.client.notifier.send_notification(
                "approve_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                },
                "Failed",
                str(e),
            )
            raise

    def deny_purpose_request(
        self, purpose_id: int, approval_id: int, reason: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Deny a purpose request.

        Args:
            purpose_id: Purpose ID.
            approval_id: Approval ID.
            reason: Reason for denial.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If purpose or approval is not found.
        """
        if not reason:
            raise ValueError("Denial reason is required")

        if dry_run:
            logger.info(
                f"Dry run: Would deny purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "deny_purpose_request_dry_run",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                    "reason": reason,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would deny purpose request",
                "purpose_id": purpose_id,
                "approval_id": approval_id,
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Denied purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "deny_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                    "reason": reason,
                },
                "Success",
            )
            return {
                "success": True,
                "purpose_id": purpose_id,
                "approval_id": approval_id,
            }

        try:
            # According to Immuta API V1, the endpoint for denying a purpose request is /purposes/{id}/approvals/{approvalId}/deny
            response = self.client.make_request(
                "POST",
                f"purposes/{purpose_id}/approvals/{approval_id}/deny",
                data={"reason": reason},
            )
            result = response.json()
            logger.info(
                f"Denied purpose request {approval_id} for purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "deny_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                    "reason": reason,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to deny purpose request: {e}")
            self.client.notifier.send_notification(
                "deny_purpose_request",
                {
                    "purpose_id": purpose_id,
                    "approval_id": approval_id,
                },
                "Failed",
                str(e),
            )
            raise

    def request_purpose_subscription(
        self, purpose_id: int, reason: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Request a subscription to a purpose.

        Args:
            purpose_id: Purpose ID.
            reason: Reason for subscription request.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If purpose is not found.
        """
        if not reason:
            raise ValueError("Subscription reason is required")

        if dry_run:
            logger.info(f"Dry run: Would request subscription to purpose {purpose_id}")
            self.client.notifier.send_notification(
                "request_purpose_subscription_dry_run",
                {
                    "purpose_id": purpose_id,
                    "reason": reason,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would request purpose subscription",
                "purpose_id": purpose_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Requested subscription to purpose {purpose_id}")
            self.client.notifier.send_notification(
                "request_purpose_subscription",
                {
                    "purpose_id": purpose_id,
                    "reason": reason,
                },
                "Success",
            )
            return {
                "success": True,
                "purpose_id": purpose_id,
                "approval_id": 4,
                "status": "pending",
            }

        try:
            # According to Immuta API V1, the endpoint for requesting a purpose subscription is /purposes/{id}/subscribe
            response = self.client.make_request(
                "POST",
                f"purposes/{purpose_id}/subscribe",
                data={"reason": reason},
            )
            result = response.json()
            logger.info(f"Requested subscription to purpose {purpose_id}")
            self.client.notifier.send_notification(
                "request_purpose_subscription",
                {
                    "purpose_id": purpose_id,
                    "reason": reason,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to request purpose subscription: {e}")
            self.client.notifier.send_notification(
                "request_purpose_subscription",
                {
                    "purpose_id": purpose_id,
                },
                "Failed",
                str(e),
            )
            raise

    def get_purpose_subscribers(self, purpose_id: int) -> List[Dict[str, Any]]:
        """Get subscribers for a purpose.

        Args:
            purpose_id: Purpose ID.

        Returns:
            List of subscriber dictionaries.

        Raises:
            ValueError: If purpose is not found.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "subscriptionDate": "2023-01-03T00:00:00Z",
                },
                {
                    "id": 2,
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "subscriptionDate": "2023-01-05T00:00:00Z",
                },
            ]

        # According to Immuta API V1, the endpoint for getting purpose subscribers is /purposes/{id}/subscribers
        response = self.client.make_request("GET", f"purposes/{purpose_id}/subscribers")
        return response.json()

    def remove_purpose_subscriber(
        self, purpose_id: int, user_id: int, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Remove a subscriber from a purpose.

        Args:
            purpose_id: Purpose ID.
            user_id: User ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If purpose or user is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would remove user {user_id} from purpose {purpose_id}"
            )
            self.client.notifier.send_notification(
                "remove_purpose_subscriber_dry_run",
                {
                    "purpose_id": purpose_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would remove purpose subscriber",
                "purpose_id": purpose_id,
                "user_id": user_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Removed user {user_id} from purpose {purpose_id}")
            self.client.notifier.send_notification(
                "remove_purpose_subscriber",
                {
                    "purpose_id": purpose_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return {
                "success": True,
                "purpose_id": purpose_id,
                "user_id": user_id,
            }

        try:
            # According to Immuta API V1, the endpoint for removing a purpose subscriber is /purposes/{id}/subscribers/{userId}
            response = self.client.make_request(
                "DELETE", f"purposes/{purpose_id}/subscribers/{user_id}"
            )
            result = response.json()
            logger.info(f"Removed user {user_id} from purpose {purpose_id}")
            self.client.notifier.send_notification(
                "remove_purpose_subscriber",
                {
                    "purpose_id": purpose_id,
                    "user_id": user_id,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to remove purpose subscriber: {e}")
            self.client.notifier.send_notification(
                "remove_purpose_subscriber",
                {
                    "purpose_id": purpose_id,
                    "user_id": user_id,
                },
                "Failed",
                str(e),
            )
            raise

    def delete_purpose(
        self, purpose_id: int, backup: bool = True, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Delete a purpose.

        Args:
            purpose_id: Purpose ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If purpose is not found.
        """
        # Get purpose details for backup and notification
        purpose = self.get_purpose(purpose_id)

        if dry_run:
            logger.info(
                f"Dry run: Would delete purpose {purpose_id} ({purpose.get('name')})"
            )
            self.client.notifier.send_notification(
                "purpose_delete_dry_run",
                {
                    "purpose_id": purpose_id,
                    "name": purpose.get("name"),
                },
                "Success",
            )
            return {"status": "dry_run_success", "purpose_id": purpose_id}

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"purpose_{purpose_id}_{int(time.time())}.json"
            try:
                self.client.storage.upload_file(
                    purpose,
                    blob_name,
                    metadata={
                        "type": "purpose",
                        "id": str(purpose_id),
                        "operation": "purpose_delete",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Deleted purpose {purpose_id} ({purpose.get('name')})")
            self.client.notifier.send_notification(
                "purpose_delete",
                {
                    "purpose_id": purpose_id,
                    "name": purpose.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "purpose_id": purpose_id}

        try:
            # According to Immuta API V1, the endpoint for deleting a purpose is /purposes/{id}
            response = self.client.make_request("DELETE", f"purposes/{purpose_id}")
            logger.info(f"Deleted purpose {purpose_id} ({purpose.get('name')})")
            self.client.notifier.send_notification(
                "purpose_delete",
                {
                    "purpose_id": purpose_id,
                    "name": purpose.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "purpose_id": purpose_id}
        except Exception as e:
            logger.error(f"Failed to delete purpose: {e}")
            self.client.notifier.send_notification(
                "purpose_delete", {"purpose_id": purpose_id}, "Failed", str(e)
            )
            raise
