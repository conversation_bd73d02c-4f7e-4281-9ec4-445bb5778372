"""Tag service for the Immuta SRE Toolkit."""

import os
import re
import time
from typing import Dict, List, Optional, Union, Any

import yaml
from rich.progress import Progress

from immuta_toolkit.utils.cache import get_cache
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class TagService:
    """Service for managing tags in Immuta.

    This service provides functionality for managing tags, including
    validating tags, adding tags to data sources, and bulk tagging.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the tag service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def _validate_tags(self, tags: List[str]) -> List[str]:
        """Validate tag format and check for conflicts using cache with TTL.

        Args:
            tags: List of tags to validate.

        Returns:
            List of valid tags.

        Raises:
            ValueError: If tag format is invalid.
        """
        if not tags:
            return []

        # Get existing tags from cache or fetch them
        existing_tags = self.client.tag_cache.get_or_set(
            "all_tags",
            lambda: self._fetch_all_tags(),
        )

        # Validate tag format (alphanumeric, underscore, hyphen)
        invalid_tags = [t for t in tags if not re.match(r"^[a-zA-Z0-9_-]+$", t)]
        if invalid_tags:
            raise ValueError(f"Invalid tag format: {invalid_tags}")

        # Return new tags that don't exist in the cache
        return [t for t in tags if t not in existing_tags]

    def _fetch_all_tags(self) -> List[str]:
        """Fetch all tags from the API.

        Returns:
            List of tag names.
        """
        try:
            if self.client.is_local:
                existing_tags = [
                    "sensitive",
                    "pci",
                    "pii",
                    "restricted",
                    "public",
                    "financial",
                    "health",
                ]
            else:
                # According to Immuta API V1, the endpoint for getting all tags is /tag
                response = self.client.make_request("GET", "tag")
                existing_tags = [t["name"] for t in response.json()]

            logger.info(f"Fetched {len(existing_tags)} tags from API")
            return existing_tags
        except Exception as e:
            logger.error(f"Failed to fetch tags: {e}")
            raise

    def add_tags(
        self,
        data_source_id: int,
        tags: List[str],
        column_tags: Optional[Dict[str, List[str]]] = None,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> None:
        """Add tags to a data source and its columns.

        Args:
            data_source_id: Data source ID.
            tags: List of tags to add to the data source.
            column_tags: Dictionary mapping column names to lists of tags.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate tags before adding them.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            self._validate_tags(tags)
            if column_tags:
                for col, col_tags in column_tags.items():
                    self._validate_tags(col_tags)
            # According to Immuta API V1, the endpoint for validating tags is /dataSource/{id}/validate
            validation = self.client.make_request(
                "GET",
                f"dataSource/{data_source_id}/validate",
                params={"tags": tags, "columnTags": column_tags or {}},
            )
            validation_data = validation.json()
            if validation_data.get("status") != "valid":
                raise ValueError(f"Validation failed: {validation_data.get('error')}")
            logger.info(f"Validation passed: {validation_data.get('details')}")

        if dry_run:
            logger.info(
                f"Dry run: Would add tags {tags} to data source {data_source_id}"
            )
            if column_tags:
                logger.info(f"Dry run: Would add column tags {column_tags}")
            self.client.notifier.send_notification(
                "data_source_tag_dry_run",
                {
                    "data_source_id": data_source_id,
                    "tags": ", ".join(tags),
                    "column_tags": str(column_tags or {}),
                },
                "Success",
            )
            return

        blob_name = None
        if backup and self.client.storage:
            blob_name = f"data_source_{data_source_id}_tags_{int(time.time())}.json"
            # Get current tags
            try:
                # According to Immuta API V1, the endpoint for getting data source tags is /dataSource/{id}/tags
                response = self.client.make_request(
                    "GET", f"dataSource/{data_source_id}/tags"
                )
                current_tags = response.json()
                self.client.storage.upload_file(
                    current_tags,
                    blob_name,
                    metadata={
                        "type": "data_source",
                        "id": str(data_source_id),
                        "operation": "tag_add",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Added tags {tags} to data source {data_source_id}")
            if column_tags:
                logger.info(f"Mock: Added column tags {column_tags}")
            self.client.notifier.send_notification(
                "data_source_tag",
                {
                    "data_source_id": data_source_id,
                    "tags": ", ".join(tags),
                    "column_tags": str(column_tags or {}),
                    "backup": blob_name,
                },
                "Success",
            )
            return

        try:
            # According to Immuta API V1, the endpoint for adding tags to a data source is /dataSource/{id}/tags
            self.client.make_request(
                "POST",
                f"dataSource/{data_source_id}/tags",
                data={"tags": tags, "columnTags": column_tags or {}},
            )
            logger.info(f"Added tags {tags} to data source {data_source_id}")
            if column_tags:
                logger.info(f"Added column tags {column_tags}")

            # Invalidate cache
            self.client.tag_cache.delete("all_tags")
            self.client.tag_cache.delete("all_tag_objects")
            self.client.tag_cache.delete(f"data_source_{data_source_id}_tags")

            self.client.notifier.send_notification(
                "data_source_tag",
                {
                    "data_source_id": data_source_id,
                    "tags": ", ".join(tags),
                    "column_tags": str(column_tags or {}),
                    "backup": blob_name,
                },
                "Success",
            )
        except Exception as e:
            logger.error(f"Failed to add tags: {e}")
            self.client.notifier.send_notification(
                "data_source_tag",
                {"data_source_id": data_source_id},
                "Failed",
                str(e),
            )
            # Restore backup if available
            if blob_name and self.client.storage:
                logger.info("Restoring backup...")
                try:
                    # Implement restore logic here
                    pass
                except Exception as restore_error:
                    logger.error(f"Failed to restore backup: {restore_error}")
            raise

    def add_tag(self, tag_data: Dict) -> Dict:
        """Add a single tag.

        Args:
            tag_data: Tag data dictionary with name, modelType, and modelId.

        Returns:
            Added tag dictionary.

        Raises:
            ValueError: If tag data is invalid.
        """
        if not tag_data.get("name"):
            raise ValueError("Tag name is required")
        if not tag_data.get("modelType"):
            raise ValueError("Model type is required")
        if not tag_data.get("modelId"):
            raise ValueError("Model ID is required")

        if self.client.is_local:
            return {
                "name": tag_data["name"],
                "source": "curated",
                "modelType": tag_data["modelType"],
                "modelId": tag_data["modelId"],
                "addedBy": 1,
                "deleted": False,
            }

        # According to Immuta API V1, the endpoint for adding a tag is /tag
        response = self.client.make_request("POST", "tag", data=tag_data)
        result = response.json()

        # Invalidate cache
        self.client.tag_cache.delete("all_tags")
        self.client.tag_cache.delete("all_tag_objects")

        # If it's a data source tag, invalidate that specific cache too
        if tag_data.get("modelType") == "datasource":
            self.client.tag_cache.delete(f"data_source_{tag_data.get('modelId')}_tags")

        return result

    def get_tags(self) -> List[Dict[str, Any]]:
        """Get all tags in the system.

        Returns:
            List of tag dictionaries.
        """
        # Use cache to store full tag objects
        return self.client.tag_cache.get_or_set(
            "all_tag_objects",
            lambda: self._fetch_all_tag_objects(),
        )

    def _fetch_all_tag_objects(self) -> List[Dict[str, Any]]:
        """Fetch all tag objects from the API.

        Returns:
            List of tag dictionaries.
        """
        if self.client.is_local:
            return [
                {"name": "sensitive", "source": "curated"},
                {"name": "pci", "source": "curated"},
                {"name": "pii", "source": "curated"},
                {"name": "restricted", "source": "curated"},
                {"name": "public", "source": "curated"},
                {"name": "financial", "source": "curated"},
                {"name": "health", "source": "curated"},
            ]

        # According to Immuta API V1, the endpoint for getting all tags is /tag
        response = self.client.make_request("GET", "tag")
        tags = response.json()
        logger.info(f"Fetched {len(tags)} tag objects from API")
        return tags

    def get_data_source_tags(
        self,
        data_source_id: int,
        blob_id: str = None,
        blob_tags_only: bool = False,
        use_cache: bool = True,
    ) -> Dict[str, Any]:
        """Get tags for a specific data source.

        Args:
            data_source_id: Data source ID.
            blob_id: Optional blob ID to get tags for a specific blob.
            blob_tags_only: When True, will only display blob tags associated with a data source.
            use_cache: Whether to use cached data if available.

        Returns:
            Dictionary containing tags for the data source.
        """
        # Skip cache if blob_id or blob_tags_only is specified
        if not use_cache or blob_id or blob_tags_only:
            return self._fetch_data_source_tags(data_source_id, blob_id, blob_tags_only)

        # Use cache for standard data source tags
        cache_key = f"data_source_{data_source_id}_tags"
        return self.client.tag_cache.get_or_set(
            cache_key,
            lambda: self._fetch_data_source_tags(data_source_id, None, False),
        )

    def _fetch_data_source_tags(
        self, data_source_id: int, blob_id: str = None, blob_tags_only: bool = False
    ) -> Dict[str, Any]:
        """Fetch tags for a specific data source from the API.

        Args:
            data_source_id: Data source ID.
            blob_id: Optional blob ID to get tags for a specific blob.
            blob_tags_only: When True, will only display blob tags associated with a data source.

        Returns:
            Dictionary containing tags for the data source.
        """
        if self.client.is_local:
            return {
                "tags": [
                    {
                        "name": "sensitive",
                        "source": "curated",
                        "modelType": "datasource",
                        "modelId": str(data_source_id),
                        "addedBy": 1,
                        "deleted": False,
                    }
                ]
            }

        # Build query parameters
        params = {}
        if blob_id:
            params["blobId"] = blob_id
        if blob_tags_only:
            params["blobTagsOnly"] = blob_tags_only

        # According to Immuta API V1, the endpoint for getting data source tags is /dataSource/{id}/tags
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/tags", params=params
        )
        tags = response.json()
        logger.info(f"Fetched tags for data source {data_source_id}")
        return tags

    def add_data_source_tag(self, data_source_id: int, tag_name: str) -> Dict[str, Any]:
        """Add a tag to a data source.

        Args:
            data_source_id: Data source ID.
            tag_name: Name of the tag to add.

        Returns:
            Added tag dictionary.
        """
        tag_data = {
            "name": tag_name,
            "modelType": "datasource",
            "modelId": str(data_source_id),
        }
        result = self.add_tag(tag_data)

        # Invalidate cache
        self.client.tag_cache.delete(f"data_source_{data_source_id}_tags")

        return result

    def load_tag_config(self, config_path: str) -> Dict:
        """Load tag configuration from YAML file.

        Args:
            config_path: Path to YAML configuration file.

        Returns:
            Tag configuration dictionary.

        Raises:
            FileNotFoundError: If configuration file is not found.
            ValueError: If configuration is invalid.
        """
        try:
            if not os.path.exists(config_path):
                raise FileNotFoundError(
                    f"Tag configuration file not found: {config_path}"
                )

            with open(config_path, "r") as f:
                # Use secure YAML parsing
                config = yaml.safe_load(f)

            # Validate configuration structure
            if not isinstance(config, dict):
                raise ValueError("Tag configuration must be a dictionary")

            # Validate data source tags
            if "data_source_tags" in config and not isinstance(
                config["data_source_tags"], dict
            ):
                raise ValueError("data_source_tags must be a dictionary")

            # Validate column tags
            if "column_tags" in config and not isinstance(config["column_tags"], dict):
                raise ValueError("column_tags must be a dictionary")

            # Validate tag formats
            for tag_type, tag_mapping in config.items():
                for pattern, tags in tag_mapping.items():
                    if not isinstance(tags, list) or not tags:
                        raise ValueError(f"Tags for {pattern} must be a non-empty list")
                    self._validate_tags(tags)

            logger.info(f"Loaded tag configuration from {config_path}")
            return config
        except Exception as e:
            logger.error(f"Failed to load tag configuration: {e}")
            raise
