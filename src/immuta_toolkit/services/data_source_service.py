"""Data source service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List

from rich.progress import Progress

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.backup import create_backup

logger = get_logger(__name__)


class DataSourceService:
    """Service for managing data sources in Immuta.

    This service provides functionality for managing data sources, including
    creating, updating, and deleting data sources, as well as managing data
    source tags and policies.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the data source service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def list_data_sources(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List data sources in Immuta.

        Args:
            limit: Maximum number of data sources to return.
            offset: Offset for pagination.

        Returns:
            List of data source dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "name": "customer_data",
                    "description": "Customer data",
                    "columns": {"customer_id": {}, "email": {}, "address": {}},
                },
                {
                    "id": 2,
                    "name": "transaction_data",
                    "description": "Transaction data",
                    "columns": {"transaction_id": {}, "amount": {}, "date": {}},
                },
            ]

        # According to Immuta API V1, the endpoint for listing data sources is /dataSource
        response = self.client.make_request(
            "GET", "dataSource", params={"limit": limit, "offset": offset}
        )
        return response.json()

    def get_data_source(self, data_source_id: int) -> Dict:
        """Get data source by ID.

        Args:
            data_source_id: Data source ID.

        Returns:
            Data source dictionary.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            if data_source_id == 1:
                return {
                    "id": 1,
                    "name": "customer_data",
                    "description": "Customer data",
                    "columns": {"customer_id": {}, "email": {}, "address": {}},
                }
            elif data_source_id == 2:
                return {
                    "id": 2,
                    "name": "transaction_data",
                    "description": "Transaction data",
                    "columns": {"transaction_id": {}, "amount": {}, "date": {}},
                }
            else:
                raise ValueError(f"Data source not found: {data_source_id}")

        # According to Immuta API V1, the endpoint for getting a data source by ID is /dataSource/{id}
        response = self.client.make_request("GET", f"dataSource/{data_source_id}")
        return response.json()

    def create_data_source(
        self,
        data_source: Dict,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict:
        """Create a new data source.

        Args:
            data_source: Data source dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the data source before creating it.

        Returns:
            Created data source dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate data source
        if validate:
            if not data_source.get("name"):
                raise ValueError("Data source name is required")
            if not data_source.get("handler"):
                raise ValueError("Data source handler is required")

        if dry_run:
            logger.info(f"Dry run: Would create data source {data_source.get('name')}")
            self.client.notifier.send_notification(
                "data_source_create_dry_run",
                {
                    "name": data_source.get("name"),
                    "handler_type": data_source.get("handler", {}).get("type"),
                    "tags": ", ".join(data_source.get("tags", [])),
                },
                "Success",
            )
            return {"id": 0, **data_source}

        blob_name = None
        if backup and self.client.storage:
            try:
                data_sources = self.list_data_sources()
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=data_sources,
                    resource_type="data_source",
                    operation="create",
                    metadata={"count": len(data_sources)},
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Created data source {data_source.get('name')}")
            self.client.notifier.send_notification(
                "data_source_create",
                {
                    "name": data_source.get("name"),
                    "handler_type": data_source.get("handler", {}).get("type"),
                    "tags": ", ".join(data_source.get("tags", [])),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": 3, **data_source}

        try:
            # According to Immuta API V1, the endpoint for creating a data source is /dataSource
            response = self.client.make_request("POST", "dataSource", data=data_source)
            created_ds = response.json()
            logger.info(
                f"Created data source {data_source.get('name')} with ID {created_ds['id']}"
            )

            # Apply tags if specified
            if data_source.get("tags") or data_source.get("columnTags"):
                try:
                    self.client.tag_service.add_tags(
                        data_source_id=created_ds["id"],
                        tags=data_source.get("tags", []),
                        column_tags=data_source.get("columnTags", {}),
                        backup=False,  # Already backed up the data sources
                        dry_run=False,
                        validate=validate,
                    )
                except Exception as e:
                    logger.warning(f"Failed to add tags to data source: {e}")

            self.client.notifier.send_notification(
                "data_source_create",
                {
                    "id": created_ds["id"],
                    "name": data_source.get("name"),
                    "handler_type": data_source.get("handler", {}).get("type"),
                    "tags": ", ".join(data_source.get("tags", [])),
                    "backup": blob_name,
                },
                "Success",
            )
            return created_ds
        except Exception as e:
            logger.error(f"Failed to create data source: {e}")
            self.client.notifier.send_notification(
                "data_source_create",
                {"name": data_source.get("name")},
                "Failed",
                str(e),
            )
            raise

    def update_data_source(
        self,
        data_source_id: int,
        data_source: Dict,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict:
        """Update an existing data source.

        Args:
            data_source_id: Data source ID.
            data_source: Data source dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the data source before updating it.

        Returns:
            Updated data source dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate data source
        if validate:
            if not data_source.get("name"):
                raise ValueError("Data source name is required")

        if dry_run:
            logger.info(f"Dry run: Would update data source {data_source_id}")
            self.client.notifier.send_notification(
                "data_source_update_dry_run",
                {
                    "id": data_source_id,
                    "name": data_source.get("name"),
                    "tags": ", ".join(data_source.get("tags", [])),
                },
                "Success",
            )
            return {"id": data_source_id, **data_source}

        blob_name = None
        if backup and self.client.storage:
            try:
                current_ds = self.get_data_source(data_source_id)
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=current_ds,
                    resource_type="data_source",
                    resource_id=data_source_id,
                    operation="update",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Updated data source {data_source_id}")
            self.client.notifier.send_notification(
                "data_source_update",
                {
                    "id": data_source_id,
                    "name": data_source.get("name"),
                    "tags": ", ".join(data_source.get("tags", [])),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": data_source_id, **data_source}

        try:
            # According to Immuta API V1, the endpoint for updating a data source is /dataSource/{id}
            response = self.client.make_request(
                "PUT", f"dataSource/{data_source_id}", data=data_source
            )
            updated_ds = response.json()
            logger.info(f"Updated data source {data_source_id}")

            # Apply tags if specified
            if data_source.get("tags") or data_source.get("columnTags"):
                try:
                    self.client.tag_service.add_tags(
                        data_source_id=data_source_id,
                        tags=data_source.get("tags", []),
                        column_tags=data_source.get("columnTags", {}),
                        backup=False,  # Already backed up the data source
                        dry_run=False,
                        validate=validate,
                    )
                except Exception as e:
                    logger.warning(f"Failed to add tags to data source: {e}")

            self.client.notifier.send_notification(
                "data_source_update",
                {
                    "id": data_source_id,
                    "name": data_source.get("name"),
                    "tags": ", ".join(data_source.get("tags", [])),
                    "backup": blob_name,
                },
                "Success",
            )
            return updated_ds
        except Exception as e:
            logger.error(f"Failed to update data source: {e}")
            self.client.notifier.send_notification(
                "data_source_update", {"id": data_source_id}, "Failed", str(e)
            )
            raise

    def tag_existing_data_sources(
        self,
        config_file: str,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict:
        """Bulk tag data sources and columns based on YAML configuration.

        Args:
            config_file: Path to YAML configuration file.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate tags before adding them.

        Returns:
            Dictionary with tagging results.

        Raises:
            FileNotFoundError: If configuration file is not found.
            ValueError: If configuration is invalid.
        """
        # Load and validate tag configuration
        tag_config = self.client.tag_service.load_tag_config(config_file)

        # Get all data sources
        if self.client.is_local:
            data_sources = [
                {
                    "id": 1,
                    "name": "customer_data",
                    "columns": {"customer_id": {}, "email": {}, "address": {}},
                },
                {
                    "id": 2,
                    "name": "transaction_data",
                    "columns": {"transaction_id": {}, "amount": {}, "date": {}},
                },
            ]
        else:
            try:
                response = self.client.make_request("GET", "dataSource")
                data_sources = response.json()
            except Exception as e:
                logger.error(f"Failed to list data sources: {e}")
                raise

        # Track results
        results = {
            "total": len(data_sources),
            "tagged": 0,
            "skipped": 0,
            "failed": 0,
            "failed_ds": [],
        }

        if dry_run:
            logger.info(f"Dry run: Would process {len(data_sources)} data sources")
            self.client.notifier.send_notification(
                "tag_existing_data_sources_dry_run",
                {
                    "data_source_count": len(data_sources),
                    "config_file": config_file,
                    "details": f"Would apply tags to data sources and columns based on patterns in {config_file}",
                },
                "Success",
            )
            return results

        # Process each data source
        with Progress() as progress:
            task = progress.add_task(
                "[cyan]Tagging data sources...", total=len(data_sources)
            )

            for ds in data_sources:
                ds_id = ds["id"]
                ds_name = ds.get("name", f"DS-{ds_id}")

                try:
                    # Initialize tags for this data source
                    ds_tags = []
                    column_tags = {}

                    # Apply data source tags based on name patterns
                    for pattern, tag_list in tag_config.get(
                        "data_source_tags", {}
                    ).items():
                        if pattern.lower() in ds_name.lower():
                            ds_tags.extend(tag_list)

                    # Apply column tags based on column name patterns
                    for col_name in ds.get("columns", {}).keys():
                        for col_key, tag_list in tag_config.get(
                            "column_tags", {}
                        ).items():
                            if col_key.lower() in col_name.lower():
                                column_tags[col_name] = tag_list

                    # Skip if no tags to apply
                    if not ds_tags and not column_tags:
                        results["skipped"] += 1
                        progress.update(task, advance=1)
                        continue

                    # Apply tags
                    if ds_tags or column_tags:
                        try:
                            self.client.tag_service.add_tags(
                                data_source_id=ds_id,
                                tags=ds_tags,
                                column_tags=column_tags,
                                backup=backup,
                                dry_run=False,
                                validate=validate,
                            )
                            results["tagged"] += 1
                            logger.info(
                                f"Tagged data source {ds_id} ({ds_name}) with {len(ds_tags)} tags and {len(column_tags)} column tags"
                            )
                        except Exception as e:
                            logger.error(
                                f"Failed to tag data source {ds_id} ({ds_name}): {e}"
                            )
                            results["failed"] += 1
                            results["failed_ds"].append(
                                {"ds_id": ds_id, "name": ds_name, "error": str(e)}
                            )
                except Exception as e:
                    logger.error(
                        f"Error processing data source {ds_id} ({ds_name}): {e}"
                    )
                    results["failed"] += 1
                    results["failed_ds"].append(
                        {"ds_id": ds_id, "name": ds_name, "error": str(e)}
                    )

                # Update progress and add a small delay to avoid rate limits
                progress.update(task, advance=1)
                time.sleep(0.1)  # Small delay between operations

        # Log and notify results
        logger.info(
            f"Tagged {results['tagged']} out of {results['total']} data sources"
        )
        if results["skipped"] > 0:
            logger.info(
                f"Skipped {results['skipped']} data sources (no matching patterns)"
            )
        if results["failed"] > 0:
            logger.warning(f"Failed to tag {results['failed']} data sources")

        self.client.notifier.send_notification(
            "tag_existing_data_sources",
            {
                "total": results["total"],
                "tagged": results["tagged"],
                "skipped": results["skipped"],
                "failed": results["failed"],
                "config_file": config_file,
            },
            "Success" if results["failed"] == 0 else "Partial Success",
        )

        return results

    def delete_data_source(
        self,
        data_source_id: int,
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a data source.

        Args:
            data_source_id: Data source ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.

        Raises:
            ValueError: If data source is not found.
        """
        # Get data source details for notification and backup
        try:
            data_source = self.get_data_source(data_source_id)
        except Exception as e:
            logger.error(
                f"Failed to get data source {data_source_id} for deletion: {e}"
            )
            raise ValueError(f"Data source not found: {data_source_id}")

        if dry_run:
            logger.info(
                f"Dry run: Would delete data source {data_source_id} ({data_source.get('name')})"
            )
            self.client.notifier.send_notification(
                "data_source_delete_dry_run",
                {
                    "data_source_id": data_source_id,
                    "name": data_source.get("name"),
                },
                "Success",
            )
            return {"status": "dry_run_success", "data_source_id": data_source_id}

        blob_name = None
        if backup and self.client.storage:
            try:
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=data_source,
                    resource_type="data_source",
                    resource_id=data_source_id,
                    operation="delete",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(
                f"Mock: Deleted data source {data_source_id} ({data_source.get('name')})"
            )
            self.client.notifier.send_notification(
                "data_source_delete",
                {
                    "data_source_id": data_source_id,
                    "name": data_source.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "data_source_id": data_source_id}

        try:
            # According to Immuta API V1, the endpoint for deleting a data source is /dataSource/{id}
            self.client.make_request("DELETE", f"dataSource/{data_source_id}")
            logger.info(
                f"Deleted data source {data_source_id} ({data_source.get('name')})"
            )
            self.client.notifier.send_notification(
                "data_source_delete",
                {
                    "data_source_id": data_source_id,
                    "name": data_source.get("name"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "data_source_id": data_source_id}
        except Exception as e:
            logger.error(f"Failed to delete data source: {e}")
            self.client.notifier.send_notification(
                "data_source_delete",
                {"data_source_id": data_source_id},
                "Failed",
                str(e),
            )
            raise

    def get_data_source_tags(self, data_source_id: int) -> Dict:
        """Get tags for a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            Dictionary with tags information.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return {
                "tags": [
                    {
                        "name": "PII",
                        "source": "curated",
                        "modelType": "datasource",
                        "modelId": str(data_source_id),
                        "addedBy": 1,
                        "deleted": False,
                    }
                ]
            }

        # According to Immuta API V1, the endpoint for getting data source tags is /dataSource/{id}/tags
        response = self.client.make_request("GET", f"dataSource/{data_source_id}/tags")
        return response.json()

    def add_data_source_tag(self, data_source_id: int, tag_name: str) -> Dict:
        """Add a tag to a data source.

        Args:
            data_source_id: Data source ID.
            tag_name: Tag name to add.

        Returns:
            Dictionary with tag information.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return {
                "name": tag_name,
                "source": "curated",
                "modelType": "datasource",
                "modelId": str(data_source_id),
                "addedBy": 1,
                "deleted": False,
            }

        # According to Immuta API V1, tags are managed through the tag service
        # This is a simplified implementation
        tag_data = {
            "name": tag_name,
            "modelType": "datasource",
            "modelId": str(data_source_id),
        }

        # Use the tag service to add the tag
        return self.client.tag_service.add_tag(tag_data)

    def get_data_source_by_name(self, name: str) -> Dict:
        """Get data source by name.

        Args:
            name: Data source name.

        Returns:
            Data source dictionary.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            if name == "customer_data":
                return {
                    "id": 1,
                    "name": "customer_data",
                    "description": "Customer data",
                    "columns": {"customer_id": {}, "email": {}, "address": {}},
                }
            elif name == "transaction_data":
                return {
                    "id": 2,
                    "name": "transaction_data",
                    "description": "Transaction data",
                    "columns": {"transaction_id": {}, "amount": {}, "date": {}},
                }
            else:
                raise ValueError(f"Data source not found: {name}")

        # According to Immuta API V1, the endpoint for getting a data source by name is /dataSource/name/{name}
        response = self.client.make_request("GET", f"dataSource/name/{name}")
        return response.json()

    def subscribe_to_data_source(self, data_source_id: int) -> Dict:
        """Subscribe to a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            Dictionary with subscription status.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return {"success": True}

        # According to Immuta API V1, the endpoint for subscribing to a data source is /dataSource/subscribe
        data = {"dataSourceId": data_source_id}
        response = self.client.make_request("POST", "dataSource/subscribe", data=data)
        return response.json()

    def unsubscribe_from_data_source(self, data_source_id: int) -> Dict:
        """Unsubscribe from a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            Dictionary with unsubscription status.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return {"success": True}

        # According to Immuta API V1, the endpoint for unsubscribing from a data source is /dataSource/{id}/unsubscribe
        response = self.client.make_request(
            "DELETE", f"dataSource/{data_source_id}/unsubscribe"
        )
        return response.json()

    def get_data_source_users(self, data_source_id: int) -> List[Dict]:
        """Get users with access to a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            List of user dictionaries.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "email": "<EMAIL>",
                    "name": "Admin User",
                    "accessLevel": "owner",
                },
                {
                    "id": 2,
                    "email": "<EMAIL>",
                    "name": "Regular User",
                    "accessLevel": "subscriber",
                },
            ]

        # According to Immuta API V1, the endpoint for getting data source users is /dataSource/{id}/access
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/access"
        )
        return response.json()

    def add_user_to_data_source(
        self, data_source_id: int, user_id: int, access_level: str = "subscriber"
    ) -> Dict:
        """Add a user to a data source with specified access level.

        Args:
            data_source_id: Data source ID.
            user_id: User ID to add.
            access_level: Access level to grant (subscriber, owner, expert).

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source or user is not found.
        """
        if self.client.is_local:
            return {"success": True}

        # According to Immuta API V1, the endpoint for adding a user to a data source is /dataSource/{id}/access
        data = {"id": user_id, "accessLevel": access_level}
        response = self.client.make_request(
            "POST", f"dataSource/{data_source_id}/access", data=data
        )
        return response.json()

    def change_user_data_source_access(
        self, data_source_id: int, user_id: int, access_level: str
    ) -> Dict:
        """Change a user's access level to a data source.

        Args:
            data_source_id: Data source ID.
            user_id: User ID to modify.
            access_level: New access level (subscriber, owner, expert).

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source or user is not found.
        """
        if self.client.is_local:
            return {"success": True}

        # According to Immuta API V1, the endpoint for changing user access is /dataSource/{id}/access/{user_id}
        data = {"accessLevel": access_level}
        response = self.client.make_request(
            "PUT", f"dataSource/{data_source_id}/access/{user_id}", data=data
        )
        return response.json()

    def remove_user_from_data_source(self, data_source_id: int, user_id: int) -> Dict:
        """Remove a user from a data source.

        Args:
            data_source_id: Data source ID.
            user_id: User ID to remove.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source or user is not found.
        """
        if self.client.is_local:
            return {"success": True}

        # According to Immuta API V1, the endpoint for removing a user is /dataSource/{id}/access/{user_id}
        response = self.client.make_request(
            "DELETE", f"dataSource/{data_source_id}/access/{user_id}"
        )
        return response.json()

    def get_pending_tasks(self) -> List[Dict]:
        """Get pending tasks for the current user.

        Returns:
            List of task dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "state": "pending",
                    "type": "columnAdded",
                    "reason": "New column detected",
                    "dataSource": {"id": 1, "name": "customer_data"},
                }
            ]

        # According to Immuta API V1, the endpoint for getting pending tasks is /dataSource/tasks/pending
        response = self.client.make_request("GET", "dataSource/tasks/pending")
        return response.json()

    def get_data_source_tasks(self, data_source_id: int) -> List[Dict]:
        """Get tasks for a specific data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            List of task dictionaries.

        Raises:
            ValueError: If data source is not found.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "state": "pending",
                    "type": "columnAdded",
                    "reason": "New column detected",
                    "dataSource": {"id": data_source_id, "name": "customer_data"},
                }
            ]

        # According to Immuta API V1, the endpoint for getting data source tasks is /dataSource/{id}/tasks
        response = self.client.make_request("GET", f"dataSource/{data_source_id}/tasks")
        return response.json()

    def complete_task(self, task_id: int) -> Dict:
        """Mark a task as complete.

        Args:
            task_id: Task ID.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If task is not found.
        """
        if self.client.is_local:
            return {"success": True, "id": task_id}

        # According to Immuta API V1, the endpoint for completing a task is /dataSource/tasks/{id}
        response = self.client.make_request("PUT", f"dataSource/tasks/{task_id}")
        return response.json()

    def delete_task(self, task_id: int) -> Dict:
        """Delete a task.

        Args:
            task_id: Task ID.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If task is not found.
        """
        if self.client.is_local:
            return {"success": True, "id": task_id}

        # According to Immuta API V1, the endpoint for deleting a task is /dataSource/tasks/{id}
        response = self.client.make_request("DELETE", f"dataSource/tasks/{task_id}")
        return response.json()

    def trigger_schema_monitoring(
        self,
        data_source_ids: List[int] = None,
        schema_id: int = None,
        hostname: str = None,
        database: str = None,
        port: int = None,
        table: str = None,
        ephemeral_path: str = None,
        dry_run: bool = False,
    ) -> Dict:
        """Trigger schema monitoring jobs.

        Args:
            data_source_ids: List of data source IDs to run schema monitoring on.
            schema_id: ID of the schema to run schema monitoring on.
            hostname: Hostname of the database to run schema monitoring on.
            database: Database name to run schema monitoring on.
            port: Port of the database to run schema monitoring on.
            table: Table name to run schema monitoring on.
            ephemeral_path: Alternative HTTP path for ephemeral clusters.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with schema monitoring result.

        Raises:
            ValueError: If invalid parameters are provided.
        """
        # Validate parameters
        if schema_id and (data_source_ids or hostname or database or port or table):
            raise ValueError(
                "Cannot specify both schema_id and data_source_ids/hostname/database/port/table"
            )

        if not data_source_ids and not schema_id and not (hostname and database):
            raise ValueError(
                "Either data_source_ids, schema_id, or hostname and database must be provided"
            )

        if dry_run:
            if data_source_ids:
                logger.info(
                    f"Dry run: Would trigger schema monitoring for data sources: {data_source_ids}"
                )
            elif schema_id:
                logger.info(
                    f"Dry run: Would trigger schema monitoring for schema: {schema_id}"
                )
            else:
                logger.info(
                    f"Dry run: Would trigger schema monitoring for {hostname}:{port}/{database}/{table or '*'}"
                )

            self.client.notifier.send_notification(
                "schema_monitoring_dry_run",
                {
                    "data_source_ids": data_source_ids,
                    "schema_id": schema_id,
                    "hostname": hostname,
                    "database": database,
                    "port": port,
                    "table": table,
                },
                "Success",
            )
            return {"success": True, "message": "Dry run completed"}

        if self.client.is_local:
            logger.info("Mock: Triggered schema monitoring")
            self.client.notifier.send_notification(
                "schema_monitoring",
                {
                    "data_source_ids": data_source_ids,
                    "schema_id": schema_id,
                    "hostname": hostname,
                    "database": database,
                    "port": port,
                    "table": table,
                },
                "Success",
            )
            return {"success": True, "message": "Schema monitoring triggered"}

        # Build request payload
        payload = {}
        if data_source_ids:
            payload["dataSourceIds"] = data_source_ids
        if schema_id:
            payload["schemaId"] = schema_id
        if hostname:
            payload["hostname"] = hostname
        if database:
            payload["database"] = database
        if port:
            payload["port"] = port
        if table:
            payload["table"] = table
        if ephemeral_path:
            payload["ephemeralPath"] = ephemeral_path

        try:
            # According to Immuta API V1, the endpoint for triggering schema monitoring is /dataSource/detectRemoteChanges
            response = self.client.make_request(
                "POST", "dataSource/detectRemoteChanges", data=payload
            )
            result = response.json()
            logger.info("Triggered schema monitoring")
            self.client.notifier.send_notification(
                "schema_monitoring",
                {
                    "data_source_ids": data_source_ids,
                    "schema_id": schema_id,
                    "hostname": hostname,
                    "database": database,
                    "port": port,
                    "table": table,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to trigger schema monitoring: {e}")
            self.client.notifier.send_notification(
                "schema_monitoring", {}, "Failed", str(e)
            )
            raise

    def refresh_views(self, data_source_ids: List[int], dry_run: bool = False) -> Dict:
        """Refresh views for the specified data sources.

        Args:
            data_source_ids: List of data source IDs to refresh views for.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with refresh result.

        Raises:
            ValueError: If no data source IDs are provided.
        """
        if not data_source_ids:
            raise ValueError("At least one data source ID must be provided")

        if dry_run:
            logger.info(
                f"Dry run: Would refresh views for data sources: {data_source_ids}"
            )
            self.client.notifier.send_notification(
                "refresh_views_dry_run",
                {"data_source_ids": data_source_ids},
                "Success",
            )
            return {"success": True, "message": "Dry run completed"}

        if self.client.is_local:
            logger.info(f"Mock: Refreshed views for data sources: {data_source_ids}")
            self.client.notifier.send_notification(
                "refresh_views",
                {"data_source_ids": data_source_ids},
                "Success",
            )
            return {"success": True, "message": "Views refreshed"}

        try:
            # According to Immuta API V1, the endpoint for refreshing views is /dataSource/bulkRefreshViews
            response = self.client.make_request(
                "POST",
                "dataSource/bulkRefreshViews",
                data={"dataSourceIds": data_source_ids},
            )
            result = response.json()
            logger.info(f"Refreshed views for data sources: {data_source_ids}")
            self.client.notifier.send_notification(
                "refresh_views",
                {"data_source_ids": data_source_ids},
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to refresh views: {e}")
            self.client.notifier.send_notification(
                "refresh_views", {"data_source_ids": data_source_ids}, "Failed", str(e)
            )
            raise
