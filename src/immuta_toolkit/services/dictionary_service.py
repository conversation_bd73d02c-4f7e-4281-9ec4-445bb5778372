"""Data dictionary management service for Immuta."""

import json
import logging
import os
from typing import Dict, List, Optional, Any

import pandas as pd

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class DictionaryService:
    """Service for managing data dictionaries in Immuta.

    This service provides functionality for managing data dictionaries,
    including importing, exporting, and updating column descriptions
    and business definitions.

    Attributes:
        client: Immuta client.
    """

    def __init__(self, client):
        """Initialize the dictionary service.

        Args:
            client: Immuta client.
        """
        self.client = client

    def get_data_dictionary(self, data_source_id: int) -> Dict[str, Any]:
        """Get data dictionary for a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            Data dictionary.
        """
        if self.client.is_local:
            return {
                "columns": [
                    {
                        "name": "customer_id",
                        "description": "Unique identifier for the customer",
                        "business_definition": "Primary key for customer records",
                        "data_type": "INTEGER",
                        "tags": ["identifier"],
                    },
                    {
                        "name": "email",
                        "description": "Customer email address",
                        "business_definition": "Primary contact email for the customer",
                        "data_type": "VARCHAR",
                        "tags": ["pii", "contact"],
                    },
                ]
            }

        # Get data source details
        data_source = self.client.data_source_service.get_data_source(data_source_id)

        # Extract column metadata
        columns = []
        for column in data_source.get("columns", []):
            column_info = {
                "name": column["name"],
                "description": column.get("description", ""),
                "business_definition": column.get("businessDefinition", ""),
                "data_type": column.get("dataType", ""),
                "tags": column.get("tags", []),
            }
            columns.append(column_info)

        return {"columns": columns}

    def update_data_dictionary(
        self,
        data_source_id: int,
        dictionary: Dict[str, Any],
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Update data dictionary for a data source.

        Args:
            data_source_id: Data source ID.
            dictionary: Data dictionary.
            backup: Whether to backup before updating.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the update.

        Returns:
            Update result.
        """
        if backup and not dry_run:
            self._backup_data_source(data_source_id)

        if validate:
            self._validate_dictionary(dictionary)

        if dry_run:
            logger.info(
                f"Dry run: Would update data dictionary for data source {data_source_id}"
            )
            self.client.notifier.send_notification(
                "dictionary_update",
                {"data_source_id": data_source_id, "dry_run": True},
                "Success",
            )
            return {"status": "dry_run_success"}

        if self.client.is_local:
            logger.info(
                f"Local mode: Would update data dictionary for data source {data_source_id}"
            )
            self.client.notifier.send_notification(
                "dictionary_update",
                {"data_source_id": data_source_id},
                "Success",
            )
            return {"status": "success"}

        # Get current data source
        data_source = self.client.data_source_service.get_data_source(data_source_id)

        # Update column descriptions and business definitions
        columns = data_source.get("columns", [])
        column_dict = {col["name"]: col for col in columns}

        for column_info in dictionary.get("columns", []):
            column_name = column_info["name"]
            if column_name in column_dict:
                if "description" in column_info:
                    column_dict[column_name]["description"] = column_info["description"]
                if "business_definition" in column_info:
                    column_dict[column_name]["businessDefinition"] = column_info[
                        "business_definition"
                    ]

        # Update data source
        update_payload = {
            "columns": list(column_dict.values()),
        }

        # According to Immuta API V1, the endpoint for updating a data source is /dataSource/{id}
        # Using PATCH to update only the specified fields
        result = self.client.make_request(
            "PATCH",
            f"dataSource/{data_source_id}",
            json=update_payload,
        )

        logger.info(f"Updated data dictionary for data source {data_source_id}")
        self.client.notifier.send_notification(
            "dictionary_update",
            {"data_source_id": data_source_id},
            "Success",
        )

        return {"status": "success", "data_source_id": data_source_id}

    def import_data_dictionary(
        self,
        data_source_id: int,
        file_path: str,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict[str, Any]:
        """Import data dictionary from a file.

        Args:
            data_source_id: Data source ID.
            file_path: Path to file containing data dictionary.
            backup: Whether to backup before importing.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the import.

        Returns:
            Import result.
        """
        # Determine file type
        file_ext = os.path.splitext(file_path)[1].lower()

        # Load dictionary from file
        if file_ext == ".json":
            with open(file_path, "r") as f:
                dictionary = json.load(f)
        elif file_ext in [".xlsx", ".xls"]:
            df = pd.read_excel(file_path)
            dictionary = self._dataframe_to_dictionary(df)
        elif file_ext == ".csv":
            df = pd.read_csv(file_path)
            dictionary = self._dataframe_to_dictionary(df)
        else:
            raise ValueError(f"Unsupported file type: {file_ext}")

        # Update data dictionary
        return self.update_data_dictionary(
            data_source_id=data_source_id,
            dictionary=dictionary,
            backup=backup,
            dry_run=dry_run,
            validate=validate,
        )

    def export_data_dictionary(
        self,
        data_source_id: int,
        file_path: str,
        format: str = "json",
    ) -> Dict[str, Any]:
        """Export data dictionary to a file.

        Args:
            data_source_id: Data source ID.
            file_path: Path to export file.
            format: Export format (json, csv, excel).

        Returns:
            Export result.
        """
        # Get data dictionary
        dictionary = self.get_data_dictionary(data_source_id)

        # Export dictionary to file
        if format == "json":
            with open(file_path, "w") as f:
                json.dump(dictionary, f, indent=2)
        elif format in ["csv", "excel"]:
            df = self._dictionary_to_dataframe(dictionary)
            if format == "csv":
                df.to_csv(file_path, index=False)
            else:
                df.to_excel(file_path, index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")

        logger.info(
            f"Exported data dictionary for data source {data_source_id} to {file_path}"
        )
        return {"status": "success", "file_path": file_path}

    def _backup_data_source(self, data_source_id: int) -> None:
        """Backup data source before updating.

        Args:
            data_source_id: Data source ID.
        """
        if self.client.is_local or not self.client.storage:
            return

        # Get data source
        data_source = self.client.data_source_service.get_data_source(data_source_id)

        # Save backup to a secure temporary directory
        import os
        import tempfile

        # Create a secure temporary file
        temp_dir = tempfile.gettempdir()
        backup_file = os.path.join(
            temp_dir, f"data_source_{data_source_id}_backup.json"
        )
        with open(backup_file, "w") as f:
            json.dump(data_source, f)

        # Upload backup
        metadata = {
            "type": "data_source_backup",
            "data_source_id": data_source_id,
            "operation": "dictionary_update",
        }
        self.client.storage.upload_file(
            backup_file,
            f"data_source_{data_source_id}_dictionary_backup.json",
            metadata,
        )

        logger.info(f"Backed up data source {data_source_id}")

    def _validate_dictionary(self, dictionary: Dict[str, Any]) -> None:
        """Validate data dictionary.

        Args:
            dictionary: Data dictionary.

        Raises:
            ValueError: If dictionary is invalid.
        """
        if "columns" not in dictionary:
            raise ValueError("Dictionary must contain 'columns' key")

        for column in dictionary.get("columns", []):
            if "name" not in column:
                raise ValueError("Each column must have a 'name'")

    def _dataframe_to_dictionary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Convert DataFrame to dictionary.

        Args:
            df: DataFrame.

        Returns:
            Dictionary.
        """
        columns = []
        for _, row in df.iterrows():
            column = {"name": row["name"]}
            if "description" in df.columns and not pd.isna(row["description"]):
                column["description"] = row["description"]
            if "business_definition" in df.columns and not pd.isna(
                row["business_definition"]
            ):
                column["business_definition"] = row["business_definition"]
            if "data_type" in df.columns and not pd.isna(row["data_type"]):
                column["data_type"] = row["data_type"]
            if "tags" in df.columns and not pd.isna(row["tags"]):
                column["tags"] = row["tags"].split(",")
            columns.append(column)

        return {"columns": columns}

    def _dictionary_to_dataframe(self, dictionary: Dict[str, Any]) -> pd.DataFrame:
        """Convert dictionary to DataFrame.

        Args:
            dictionary: Dictionary.

        Returns:
            DataFrame.
        """
        rows = []
        for column in dictionary.get("columns", []):
            row = {
                "name": column["name"],
                "description": column.get("description", ""),
                "business_definition": column.get("business_definition", ""),
                "data_type": column.get("data_type", ""),
                "tags": ",".join(column.get("tags", [])),
            }
            rows.append(row)

        return pd.DataFrame(rows)
