"""User service for the Immuta SRE Toolkit."""

from typing import Dict, List, Optional, Union, Any

from immuta_toolkit.models import UserModel
from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.backup import create_backup

logger = get_logger(__name__)


class UserService:
    """Service for managing users in Immuta.

    This service provides functionality for managing users, including
    creating, updating, and deleting users, as well as managing user groups.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the user service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def list_users(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List users in Immuta.

        Args:
            limit: Maximum number of users to return.
            offset: Offset for pagination.

        Returns:
            List of user dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "email": "<EMAIL>",
                    "name": "Admin User",
                    "attributes": {"role": "Admin"},
                    "groups": ["Administrators"],
                },
                {
                    "id": 2,
                    "email": "<EMAIL>",
                    "name": "Regular User",
                    "attributes": {"role": "DataScientist"},
                    "groups": ["Data Scientists"],
                },
            ]

        # According to Immuta API V1, the endpoint for listing all users is /bim/users
        response = self.client.make_request(
            "GET", "bim/users", params={"limit": limit, "offset": offset}
        )
        return response.json()

    def get_user(self, user_id: Union[int, str]) -> Dict:
        """Get user by ID.

        Args:
            user_id: User ID or email.

        Returns:
            User dictionary.

        Raises:
            ValueError: If user is not found.
        """
        if self.client.is_local:
            if user_id == 1 or user_id == "<EMAIL>":
                return {
                    "id": 1,
                    "email": "<EMAIL>",
                    "name": "Admin User",
                    "attributes": {"role": "Admin"},
                    "groups": ["Administrators"],
                }
            elif user_id == 2 or user_id == "<EMAIL>":
                return {
                    "id": 2,
                    "email": "<EMAIL>",
                    "name": "Regular User",
                    "attributes": {"role": "DataScientist"},
                    "groups": ["Data Scientists"],
                }
            else:
                raise ValueError(f"User not found: {user_id}")

        # Check if user_id is an email
        if isinstance(user_id, str) and "@" in user_id:
            # Search for user by email
            users = self.list_users()
            for user in users:
                if user.get("email") == user_id:
                    return user
            raise ValueError(f"User not found: {user_id}")

        # According to Immuta API V1, the endpoint for getting a user by ID is /bim/user/{id}
        response = self.client.make_request("GET", f"bim/user/{user_id}")
        return response.json()

    def create_user(
        self, user: UserModel, backup: bool = True, dry_run: bool = False
    ) -> Dict:
        """Create a new user.

        Args:
            user: User model.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Created user dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate user model
        user_dict = user.model_dump()

        if dry_run:
            logger.info(f"Dry run: Would create user {user.email}")
            self.client.notifier.send_notification(
                "user_create_dry_run",
                {
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                },
                "Success",
            )
            return {"id": 0, **user_dict}

        blob_name = None
        if backup and self.client.storage:
            try:
                users = self.list_users()
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=users,
                    resource_type="user",
                    operation="create",
                    metadata={"count": len(users)},
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Created user {user.email}")
            self.client.notifier.send_notification(
                "user_create",
                {
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": 3, **user_dict}

        try:
            # According to Immuta API V1, the endpoint for creating a user is /bim/user
            response = self.client.make_request("POST", "bim/user", data=user_dict)
            created_user = response.json()
            logger.info(f"Created user {user.email} with ID {created_user['id']}")
            self.client.notifier.send_notification(
                "user_create",
                {
                    "id": created_user["id"],
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                    "backup": blob_name,
                },
                "Success",
            )
            return created_user
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            self.client.notifier.send_notification(
                "user_create", {"email": user.email}, "Failed", str(e)
            )
            raise

    def update_user(
        self,
        user_id: int,
        user: UserModel,
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Update an existing user.

        Args:
            user_id: User ID.
            user: User model.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Updated user dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Validate user model
        user_dict = user.model_dump()

        if dry_run:
            logger.info(f"Dry run: Would update user {user_id}")
            self.client.notifier.send_notification(
                "user_update_dry_run",
                {
                    "id": user_id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                },
                "Success",
            )
            return {"id": user_id, **user_dict}

        blob_name = None
        if backup and self.client.storage:
            try:
                current_user = self.get_user(user_id)
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=current_user,
                    resource_type="user",
                    resource_id=user_id,
                    operation="update",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Updated user {user_id}")
            self.client.notifier.send_notification(
                "user_update",
                {
                    "id": user_id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": user_id, **user_dict}

        try:
            # According to Immuta API V1, the endpoint for updating a user is /bim/user/{id}
            response = self.client.make_request(
                "PUT", f"bim/user/{user_id}", data=user_dict
            )
            updated_user = response.json()
            logger.info(f"Updated user {user_id}")
            self.client.notifier.send_notification(
                "user_update",
                {
                    "id": user_id,
                    "email": user.email,
                    "name": user.name,
                    "role": user.attributes.role.value,
                    "groups": ", ".join(user.groups or []),
                    "backup": blob_name,
                },
                "Success",
            )
            return updated_user
        except Exception as e:
            logger.error(f"Failed to update user: {e}")
            self.client.notifier.send_notification(
                "user_update", {"id": user_id}, "Failed", str(e)
            )
            raise

    def delete_user(
        self,
        user_id: int,
        backup: bool = True,
        dry_run: bool = False,
    ) -> Dict:
        """Delete a user.

        Args:
            user_id: User ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with deletion status.

        Raises:
            ValueError: If user is not found.
        """
        # Get user details for notification and backup
        try:
            user = self.get_user(user_id)
        except Exception as e:
            logger.error(f"Failed to get user {user_id} for deletion: {e}")
            raise ValueError(f"User not found: {user_id}")

        if dry_run:
            logger.info(f"Dry run: Would delete user {user_id} ({user.get('email')})")
            self.client.notifier.send_notification(
                "user_delete_dry_run",
                {
                    "user_id": user_id,
                    "email": user.get("email"),
                    "name": user.get("name"),
                },
                "Success",
            )
            return {"status": "dry_run_success", "user_id": user_id}

        blob_name = None
        if backup and self.client.storage:
            try:
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=user,
                    resource_type="user",
                    resource_id=user_id,
                    operation="delete",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Deleted user {user_id} ({user.get('email')})")
            self.client.notifier.send_notification(
                "user_delete",
                {
                    "user_id": user_id,
                    "email": user.get("email"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "user_id": user_id}

        try:
            # According to Immuta API V1, the endpoint for deleting a user is /bim/user/{id}
            self.client.make_request("DELETE", f"bim/user/{user_id}")
            logger.info(f"Deleted user {user_id} ({user.get('email')})")
            self.client.notifier.send_notification(
                "user_delete",
                {
                    "user_id": user_id,
                    "email": user.get("email"),
                    "backup": blob_name,
                },
                "Success",
            )
            return {"status": "success", "user_id": user_id}
        except Exception as e:
            logger.error(f"Failed to delete user: {e}")
            self.client.notifier.send_notification(
                "user_delete", {"user_id": user_id}, "Failed", str(e)
            )
            raise

    def get_user_access_info(self, data_source_id: int, user_id: int) -> Dict[str, Any]:
        """Get user access information for a data source.

        Args:
            data_source_id: Data source ID.
            user_id: User ID.

        Returns:
            Dictionary containing user access information.
        """
        if self.client.is_local:
            return {
                "user": {
                    "id": user_id,
                    "name": "Test User",
                    "email": "<EMAIL>",
                },
                "dataSource": {
                    "id": data_source_id,
                    "name": "Test Data Source",
                },
                "policies": [
                    {
                        "id": 1,
                        "name": "Test Policy",
                        "type": "subscription",
                        "effect": "allow",
                    }
                ],
                "access": "ALLOWED",
            }

        # According to Immuta API V1, the endpoint for getting user access info is /dataSource/{id}/users/{profileId}/policyInfo
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/users/{user_id}/policyInfo"
        )
        return response.json()

    def get_user_visibility_info(
        self, data_source_id: int, user_id: int
    ) -> Dict[str, Any]:
        """Get user visibility information for a data source.

        Args:
            data_source_id: Data source ID.
            user_id: User ID.

        Returns:
            Dictionary containing user visibility information.
        """
        if self.client.is_local:
            return {
                "user": {
                    "id": user_id,
                    "name": "Test User",
                    "email": "<EMAIL>",
                },
                "dataSource": {
                    "id": data_source_id,
                    "name": "Test Data Source",
                },
                "columns": {
                    "customer_id": {"visible": True, "masked": False},
                    "email": {"visible": True, "masked": True},
                    "address": {"visible": False, "masked": False},
                },
                "rowFilter": "region = 'US'",
            }

        # According to Immuta API V1, the endpoint for getting user visibility info is /dataSource/{id}/users/{profileId}/visibilityReport
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/users/{user_id}/visibilityReport"
        )
        return response.json()

    def get_current_user_visibility_info(self, data_source_id: int) -> Dict[str, Any]:
        """Get current user visibility information for a data source.

        Args:
            data_source_id: Data source ID.

        Returns:
            Dictionary containing current user visibility information.
        """
        if self.client.is_local:
            return {
                "dataSource": {
                    "id": data_source_id,
                    "name": "Test Data Source",
                },
                "columns": {
                    "customer_id": {"visible": True, "masked": False},
                    "email": {"visible": True, "masked": True},
                    "address": {"visible": False, "masked": False},
                },
                "rowFilter": "region = 'US'",
            }

        # According to Immuta API V1, the endpoint for getting current user visibility info is /dataSource/{id}/visibilityReport
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/visibilityReport"
        )
        return response.json()

    def get_users_by_access_level(
        self, data_source_id: int, access_level: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get users by access level for a data source.

        Args:
            data_source_id: Data source ID.
            access_level: Access level to filter by. If None, returns all users.
                Valid values are "OWNER", "EXPERT", "ALLOWED", "DENIED".

        Returns:
            List of user dictionaries.
        """
        if self.client.is_local:
            users = [
                {
                    "id": 1,
                    "name": "Admin User",
                    "email": "<EMAIL>",
                    "accessLevel": "OWNER",
                },
                {
                    "id": 2,
                    "name": "Expert User",
                    "email": "<EMAIL>",
                    "accessLevel": "EXPERT",
                },
                {
                    "id": 3,
                    "name": "Regular User",
                    "email": "<EMAIL>",
                    "accessLevel": "ALLOWED",
                },
                {
                    "id": 4,
                    "name": "Denied User",
                    "email": "<EMAIL>",
                    "accessLevel": "DENIED",
                },
            ]

            if access_level:
                return [user for user in users if user["accessLevel"] == access_level]
            return users

        # Build query parameters
        params = {}
        if access_level:
            params["accessLevel"] = access_level

        # According to Immuta API V1, the endpoint for getting users by access level is /dataSource/{id}/access
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/access", params=params
        )
        return response.json()

    def add_user_to_data_source(
        self,
        data_source_id: int,
        user_id: int,
        access_level: str = "ALLOWED",
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Add a user to a data source with the specified access level.

        Args:
            data_source_id: Data source ID.
            user_id: User ID.
            access_level: Access level to assign. Valid values are "OWNER", "EXPERT", "ALLOWED".
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary containing the result of the operation.

        Raises:
            ValueError: If the access level is invalid.
        """
        # Validate access level
        valid_access_levels = ["OWNER", "EXPERT", "ALLOWED"]
        if access_level not in valid_access_levels:
            raise ValueError(
                f"Invalid access level: {access_level}. Valid values are: {', '.join(valid_access_levels)}"
            )

        if dry_run:
            logger.info(
                f"Dry run: Would add user {user_id} to data source {data_source_id} with access level {access_level}"
            )
            self.client.notifier.send_notification(
                "add_user_to_data_source_dry_run",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Dry run: Would add user {user_id} to data source {data_source_id} with access level {access_level}",
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Added user {user_id} to data source {data_source_id} with access level {access_level}"
            )
            self.client.notifier.send_notification(
                "add_user_to_data_source",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Added user {user_id} to data source {data_source_id} with access level {access_level}",
            }

        try:
            # According to Immuta API V1, the endpoint for adding a user to a data source is /dataSource/{id}/access
            payload = {
                "userId": user_id,
                "accessLevel": access_level,
            }
            response = self.client.make_request(
                "POST", f"dataSource/{data_source_id}/access", data=payload
            )
            result = response.json()
            logger.info(
                f"Added user {user_id} to data source {data_source_id} with access level {access_level}"
            )
            self.client.notifier.send_notification(
                "add_user_to_data_source",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to add user to data source: {e}")
            self.client.notifier.send_notification(
                "add_user_to_data_source",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                },
                "Failed",
                str(e),
            )
            raise

    def change_user_access_level(
        self,
        data_source_id: int,
        user_id: int,
        access_level: str,
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Change a user's access level for a data source.

        Args:
            data_source_id: Data source ID.
            user_id: User ID.
            access_level: New access level. Valid values are "OWNER", "EXPERT", "ALLOWED", "DENIED".
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary containing the result of the operation.

        Raises:
            ValueError: If the access level is invalid.
        """
        # Validate access level
        valid_access_levels = ["OWNER", "EXPERT", "ALLOWED", "DENIED"]
        if access_level not in valid_access_levels:
            raise ValueError(
                f"Invalid access level: {access_level}. Valid values are: {', '.join(valid_access_levels)}"
            )

        if dry_run:
            logger.info(
                f"Dry run: Would change access level for user {user_id} on data source {data_source_id} to {access_level}"
            )
            self.client.notifier.send_notification(
                "change_user_access_level_dry_run",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Dry run: Would change access level for user {user_id} on data source {data_source_id} to {access_level}",
            }

        if self.client.is_local:
            logger.info(
                f"Mock: Changed access level for user {user_id} on data source {data_source_id} to {access_level}"
            )
            self.client.notifier.send_notification(
                "change_user_access_level",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Changed access level for user {user_id} on data source {data_source_id} to {access_level}",
            }

        try:
            # According to Immuta API V1, the endpoint for changing a user's access level is /dataSource/{id}/access/{userId}
            payload = {
                "accessLevel": access_level,
            }
            response = self.client.make_request(
                "PUT", f"dataSource/{data_source_id}/access/{user_id}", data=payload
            )
            result = response.json()
            logger.info(
                f"Changed access level for user {user_id} on data source {data_source_id} to {access_level}"
            )
            self.client.notifier.send_notification(
                "change_user_access_level",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                    "access_level": access_level,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to change user access level: {e}")
            self.client.notifier.send_notification(
                "change_user_access_level",
                {
                    "data_source_id": data_source_id,
                    "user_id": user_id,
                },
                "Failed",
                str(e),
            )
            raise

    def get_users_who_can_unmask(
        self, data_source_id: int, column_name: str
    ) -> List[Dict[str, Any]]:
        """Get users who can unmask a specific column in a data source.

        Args:
            data_source_id: Data source ID.
            column_name: Column name.

        Returns:
            List of user dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "name": "Admin User",
                    "profileId": 1,
                    "iamid": "bim",
                },
                {
                    "name": "Expert User",
                    "profileId": 2,
                    "iamid": "bim",
                },
            ]

        # According to Immuta API V1, the endpoint for getting users who can unmask a column is /dataSource/{id}/{columnName}/unmaskUsers
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/{column_name}/unmaskUsers"
        )
        return response.json()
