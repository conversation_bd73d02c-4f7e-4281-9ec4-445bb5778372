"""Policy service for the Immuta SRE Toolkit."""

from typing import Dict, List

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.backup import create_backup

logger = get_logger(__name__)


class PolicyService:
    """Service for managing policies in Immuta.

    This service provides functionality for managing policies, including
    creating, updating, and deleting policies, as well as validating
    policy structures.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the policy service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def list_policies(self, limit: int = 1000, offset: int = 0) -> List[Dict]:
        """List policies in Immuta.

        Args:
            limit: Maximum number of policies to return.
            offset: Offset for pagination.

        Returns:
            List of policy dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "name": "PII Masking Policy",
                    "description": "Masks PII data",
                    "actions": [{"type": "mask", "fields": ["email", "phone"]}],
                    "rules": [{"type": "tag", "value": "pii"}],
                },
                {
                    "id": 2,
                    "name": "Financial Data Policy",
                    "description": "Restricts access to financial data",
                    "actions": [{"type": "restrict"}],
                    "rules": [{"type": "tag", "value": "financial"}],
                },
            ]

        # According to Immuta API V1, the endpoint for listing policies is /policy
        response = self.client.make_request(
            "GET", "policy", params={"limit": limit, "offset": offset}
        )
        return response.json()

    def get_policy(self, policy_id: int) -> Dict:
        """Get policy by ID.

        Args:
            policy_id: Policy ID.

        Returns:
            Policy dictionary.

        Raises:
            ValueError: If policy is not found.
        """
        if self.client.is_local:
            if policy_id == 1:
                return {
                    "id": 1,
                    "name": "PII Masking Policy",
                    "description": "Masks PII data",
                    "actions": [{"type": "mask", "fields": ["email", "phone"]}],
                    "rules": [{"type": "tag", "value": "pii"}],
                }
            elif policy_id == 2:
                return {
                    "id": 2,
                    "name": "Financial Data Policy",
                    "description": "Restricts access to financial data",
                    "actions": [{"type": "restrict"}],
                    "rules": [{"type": "tag", "value": "financial"}],
                }
            else:
                raise ValueError(f"Policy not found: {policy_id}")

        # According to Immuta API V1, the endpoint for getting a policy by ID is /policy/{id}
        response = self.client.make_request("GET", f"policy/{policy_id}")
        return response.json()

    def create_policy(
        self,
        policy: Dict,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict:
        """Create a new policy.

        Args:
            policy: Policy dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the policy before creating it.

        Returns:
            Created policy dictionary.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            self._validate_policy(policy)

        if dry_run:
            logger.info(f"Dry run: Would create policy {policy['name']}")
            self.client.notifier.send_notification(
                "policy_create_dry_run",
                {
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                },
                "Success",
            )
            return {"id": 0, **policy}

        blob_name = None
        if backup and self.client.storage:
            try:
                policies = self.list_policies()
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=policies,
                    resource_type="policy",
                    operation="create",
                    metadata={"count": len(policies)},
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Created policy {policy['name']}")
            self.client.notifier.send_notification(
                "policy_create",
                {
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": 3, **policy}

        try:
            # According to Immuta API V1, the endpoint for creating a policy is /policy
            response = self.client.make_request("POST", "policy", data=policy)
            created_policy = response.json()
            logger.info(
                f"Created policy {policy['name']} with ID {created_policy['id']}"
            )
            self.client.notifier.send_notification(
                "policy_create",
                {
                    "id": created_policy["id"],
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                    "backup": blob_name,
                },
                "Success",
            )
            return created_policy
        except Exception as e:
            logger.error(f"Failed to create policy: {e}")
            self.client.notifier.send_notification(
                "policy_create", {"name": policy["name"]}, "Failed", str(e)
            )
            raise

    def update_policy(
        self,
        policy_id: int,
        policy: Dict,
        backup: bool = True,
        dry_run: bool = False,
        validate: bool = True,
    ) -> Dict:
        """Update an existing policy.

        Args:
            policy_id: Policy ID.
            policy: Policy dictionary.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.
            validate: Whether to validate the policy before updating it.

        Returns:
            Updated policy dictionary.

        Raises:
            ValueError: If validation fails.
        """
        if validate:
            self._validate_policy(policy)

        if dry_run:
            logger.info(f"Dry run: Would update policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_update_dry_run",
                {
                    "id": policy_id,
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                },
                "Success",
            )
            return {"id": policy_id, **policy}

        blob_name = None
        if backup and self.client.storage:
            try:
                current_policy = self.get_policy(policy_id)
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=current_policy,
                    resource_type="policy",
                    resource_id=policy_id,
                    operation="update",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Updated policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_update",
                {
                    "id": policy_id,
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                    "backup": blob_name,
                },
                "Success",
            )
            return {"id": policy_id, **policy}

        try:
            # According to Immuta API V1, the endpoint for updating a policy is /policy/{id}
            response = self.client.make_request(
                "PUT", f"policy/{policy_id}", data=policy
            )
            updated_policy = response.json()
            logger.info(f"Updated policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_update",
                {
                    "id": policy_id,
                    "name": policy["name"],
                    "description": policy.get("description", ""),
                    "actions": policy["actions"],
                    "rules": policy["rules"],
                    "backup": blob_name,
                },
                "Success",
            )
            return updated_policy
        except Exception as e:
            logger.error(f"Failed to update policy: {e}")
            self.client.notifier.send_notification(
                "policy_update", {"id": policy_id}, "Failed", str(e)
            )
            raise

    def delete_policy(
        self, policy_id: int, backup: bool = True, dry_run: bool = False
    ) -> None:
        """Delete a policy.

        Args:
            policy_id: Policy ID.
            backup: Whether to create a backup before making changes.
            dry_run: Whether to perform a dry run.

        Raises:
            ValueError: If policy is not found.
        """
        if dry_run:
            logger.info(f"Dry run: Would delete policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_delete_dry_run",
                {"id": policy_id},
                "Success",
            )
            return

        blob_name = None
        if backup and self.client.storage:
            try:
                current_policy = self.get_policy(policy_id)
                blob_name = create_backup(
                    storage_client=self.client.storage,
                    data=current_policy,
                    resource_type="policy",
                    resource_id=policy_id,
                    operation="delete",
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Deleted policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_delete",
                {"id": policy_id, "backup": blob_name},
                "Success",
            )
            return

        try:
            # According to Immuta API V1, the endpoint for deleting a policy is /policy/{id}
            self.client.make_request("DELETE", f"policy/{policy_id}")
            logger.info(f"Deleted policy {policy_id}")
            self.client.notifier.send_notification(
                "policy_delete",
                {"id": policy_id, "backup": blob_name},
                "Success",
            )
        except Exception as e:
            logger.error(f"Failed to delete policy: {e}")
            self.client.notifier.send_notification(
                "policy_delete", {"id": policy_id}, "Failed", str(e)
            )
            raise

    def _validate_policy(self, policy: Dict) -> None:
        """Validate a policy.

        Args:
            policy: Policy dictionary.

        Raises:
            ValueError: If validation fails.
        """
        # Check required fields
        required_fields = ["name", "actions", "rules"]
        missing_fields = [field for field in required_fields if field not in policy]
        if missing_fields:
            raise ValueError(f"Missing required fields: {missing_fields}")

        # Validate actions
        if not isinstance(policy["actions"], list) or not policy["actions"]:
            raise ValueError("Actions must be a non-empty list")

        for action in policy["actions"]:
            if not isinstance(action, dict):
                raise ValueError("Each action must be a dictionary")

            if "type" not in action:
                raise ValueError("Each action must have a type")

            # Validate action type
            valid_action_types = ["mask", "restrict", "redact", "hash"]
            if action["type"] not in valid_action_types:
                raise ValueError(
                    f"Invalid action type: {action['type']}. "
                    f"Valid types: {valid_action_types}"
                )

            # Validate masking action
            if action["type"] == "mask" and "fields" not in action:
                raise ValueError("Mask action must have fields")

        # Validate rules
        if not isinstance(policy["rules"], list) or not policy["rules"]:
            raise ValueError("Rules must be a non-empty list")

        for rule in policy["rules"]:
            if not isinstance(rule, dict):
                raise ValueError("Each rule must be a dictionary")

            if "type" not in rule:
                raise ValueError("Each rule must have a type")

            # Validate rule type
            valid_rule_types = ["tag", "group", "user", "column"]
            if rule["type"] not in valid_rule_types:
                raise ValueError(
                    f"Invalid rule type: {rule['type']}. "
                    f"Valid types: {valid_rule_types}"
                )

            # Validate rule value
            if "value" not in rule:
                raise ValueError("Each rule must have a value")

        # Validate exceptions if present
        if "exceptions" in policy:
            if not isinstance(policy["exceptions"], dict):
                raise ValueError("Exceptions must be a dictionary")

            # Validate exception groups
            if "groups" in policy["exceptions"]:
                if not isinstance(policy["exceptions"]["groups"], list):
                    raise ValueError("Exception groups must be a list")

            # Validate exception users
            if "users" in policy["exceptions"]:
                if not isinstance(policy["exceptions"]["users"], list):
                    raise ValueError("Exception users must be a list")

        # Validate circumstances if present
        if "circumstances" in policy:
            if not isinstance(policy["circumstances"], list):
                raise ValueError("Circumstances must be a list")

            for circumstance in policy["circumstances"]:
                if not isinstance(circumstance, dict):
                    raise ValueError("Each circumstance must be a dictionary")

                if "type" not in circumstance:
                    raise ValueError("Each circumstance must have a type")

                # Validate circumstance type
                valid_circumstance_types = ["purpose", "location", "time"]
                if circumstance["type"] not in valid_circumstance_types:
                    raise ValueError(
                        f"Invalid circumstance type: {circumstance['type']}. "
                        f"Valid types: {valid_circumstance_types}"
                    )

                # Validate circumstance value
                if "value" not in circumstance:
                    raise ValueError("Each circumstance must have a value")
