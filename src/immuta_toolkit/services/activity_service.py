"""Activity service for the Immuta SRE Toolkit."""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ActivityService:
    """Service for monitoring activity in Immuta.

    This service provides functionality for monitoring activity, including
    policy activity tracking, user activity tracking, and data source activity tracking.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the activity service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def get_policy_activity(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        policy_id: Optional[int] = None,
        data_source_id: Optional[int] = None,
        user_id: Optional[int] = None,
        limit: int = 1000,
        offset: int = 0,
    ) -> List[Dict[str, Any]]:
        """Get policy activity.

        Args:
            start_time: Start time for filtering activity.
            end_time: End time for filtering activity.
            policy_id: Policy ID for filtering activity.
            data_source_id: Data source ID for filtering activity.
            user_id: User ID for filtering activity.
            limit: Maximum number of activities to return.
            offset: Offset for pagination.

        Returns:
            List of policy activity dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "timestamp": "2023-01-01T00:00:00Z",
                    "policyId": 1,
                    "policyName": "Test Policy",
                    "dataSourceId": 1,
                    "dataSourceName": "Test Data Source",
                    "userId": 1,
                    "userName": "Test User",
                    "action": "QUERY",
                    "effect": "ALLOW",
                    "reason": "Policy allowed access",
                },
                {
                    "id": 2,
                    "timestamp": "2023-01-02T00:00:00Z",
                    "policyId": 2,
                    "policyName": "Restricted Policy",
                    "dataSourceId": 2,
                    "dataSourceName": "Sensitive Data Source",
                    "userId": 2,
                    "userName": "Another User",
                    "action": "QUERY",
                    "effect": "DENY",
                    "reason": "Policy denied access",
                },
            ]

        # Build query parameters
        params = {"limit": limit, "offset": offset}
        if start_time:
            params["startTime"] = start_time.isoformat()
        if end_time:
            params["endTime"] = end_time.isoformat()
        if policy_id:
            params["policyId"] = policy_id
        if data_source_id:
            params["dataSourceId"] = data_source_id
        if user_id:
            params["userId"] = user_id

        # According to Immuta API V1, the endpoint for getting policy activity is /activity/policy
        response = self.client.make_request("GET", "activity/policy", params=params)
        return response.json()

    def get_user_activity(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        user_id: Optional[int] = None,
        action_type: Optional[str] = None,
        limit: int = 1000,
        offset: int = 0,
    ) -> List[Dict[str, Any]]:
        """Get user activity.

        Args:
            start_time: Start time for filtering activity.
            end_time: End time for filtering activity.
            user_id: User ID for filtering activity.
            action_type: Action type for filtering activity.
                Valid values include: "LOGIN", "LOGOUT", "CREATE", "UPDATE", "DELETE".
            limit: Maximum number of activities to return.
            offset: Offset for pagination.

        Returns:
            List of user activity dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "timestamp": "2023-01-01T00:00:00Z",
                    "userId": 1,
                    "userName": "Test User",
                    "actionType": "LOGIN",
                    "details": "User logged in",
                },
                {
                    "id": 2,
                    "timestamp": "2023-01-02T00:00:00Z",
                    "userId": 2,
                    "userName": "Another User",
                    "actionType": "CREATE",
                    "details": "User created a data source",
                    "resourceId": 1,
                    "resourceType": "DATA_SOURCE",
                },
            ]

        # Build query parameters
        params = {"limit": limit, "offset": offset}
        if start_time:
            params["startTime"] = start_time.isoformat()
        if end_time:
            params["endTime"] = end_time.isoformat()
        if user_id:
            params["userId"] = user_id
        if action_type:
            params["actionType"] = action_type

        # According to Immuta API V1, the endpoint for getting user activity is /activity/user
        response = self.client.make_request("GET", "activity/user", params=params)
        return response.json()

    def get_data_source_activity(
        self,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        data_source_id: Optional[int] = None,
        user_id: Optional[int] = None,
        action_type: Optional[str] = None,
        limit: int = 1000,
        offset: int = 0,
    ) -> List[Dict[str, Any]]:
        """Get data source activity.

        Args:
            start_time: Start time for filtering activity.
            end_time: End time for filtering activity.
            data_source_id: Data source ID for filtering activity.
            user_id: User ID for filtering activity.
            action_type: Action type for filtering activity.
                Valid values include: "QUERY", "EXPORT", "SUBSCRIBE", "UNSUBSCRIBE".
            limit: Maximum number of activities to return.
            offset: Offset for pagination.

        Returns:
            List of data source activity dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": 1,
                    "timestamp": "2023-01-01T00:00:00Z",
                    "dataSourceId": 1,
                    "dataSourceName": "Test Data Source",
                    "userId": 1,
                    "userName": "Test User",
                    "actionType": "QUERY",
                    "details": "User queried the data source",
                },
                {
                    "id": 2,
                    "timestamp": "2023-01-02T00:00:00Z",
                    "dataSourceId": 2,
                    "dataSourceName": "Sensitive Data Source",
                    "userId": 2,
                    "userName": "Another User",
                    "actionType": "EXPORT",
                    "details": "User exported data from the data source",
                },
            ]

        # Build query parameters
        params = {"limit": limit, "offset": offset}
        if start_time:
            params["startTime"] = start_time.isoformat()
        if end_time:
            params["endTime"] = end_time.isoformat()
        if data_source_id:
            params["dataSourceId"] = data_source_id
        if user_id:
            params["userId"] = user_id
        if action_type:
            params["actionType"] = action_type

        # According to Immuta API V1, the endpoint for getting data source activity is /activity/dataSource
        response = self.client.make_request("GET", "activity/dataSource", params=params)
        return response.json()

    def export_activity_report(
        self,
        report_type: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        format: str = "csv",
        dry_run: bool = False,
    ) -> Dict[str, Any]:
        """Export activity report.

        Args:
            report_type: Type of report to export.
                Valid values are "policy", "user", "dataSource".
            start_time: Start time for filtering activity.
            end_time: End time for filtering activity.
            format: Format of the report. Valid values are "csv", "json".
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status and report URL.

        Raises:
            ValueError: If report type or format is invalid.
        """
        # Validate report type
        valid_report_types = ["policy", "user", "dataSource"]
        if report_type not in valid_report_types:
            raise ValueError(
                f"Invalid report type: {report_type}. Valid values are: {', '.join(valid_report_types)}"
            )

        # Validate format
        valid_formats = ["csv", "json"]
        if format not in valid_formats:
            raise ValueError(
                f"Invalid format: {format}. Valid values are: {', '.join(valid_formats)}"
            )

        if dry_run:
            logger.info(f"Dry run: Would export {report_type} activity report")
            self.client.notifier.send_notification(
                "export_activity_report_dry_run",
                {
                    "report_type": report_type,
                    "format": format,
                },
                "Success",
            )
            return {
                "success": True,
                "message": f"Dry run: Would export {report_type} activity report",
                "report_type": report_type,
                "format": format,
            }

        if self.client.is_local:
            logger.info(f"Mock: Exported {report_type} activity report")
            self.client.notifier.send_notification(
                "export_activity_report",
                {
                    "report_type": report_type,
                    "format": format,
                },
                "Success",
            )
            return {
                "success": True,
                "report_type": report_type,
                "format": format,
                "url": f"https://example.com/reports/{report_type}_activity_report.{format}",
            }

        try:
            # Build request data
            data = {"format": format}
            if start_time:
                data["startTime"] = start_time.isoformat()
            if end_time:
                data["endTime"] = end_time.isoformat()

            # According to Immuta API V1, the endpoint for exporting activity report is /activity/{reportType}/export
            response = self.client.make_request(
                "POST", f"activity/{report_type}/export", data=data
            )
            result = response.json()
            logger.info(f"Exported {report_type} activity report")
            self.client.notifier.send_notification(
                "export_activity_report",
                {
                    "report_type": report_type,
                    "format": format,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to export activity report: {e}")
            self.client.notifier.send_notification(
                "export_activity_report",
                {
                    "report_type": report_type,
                    "format": format,
                },
                "Failed",
                str(e),
            )
            raise
