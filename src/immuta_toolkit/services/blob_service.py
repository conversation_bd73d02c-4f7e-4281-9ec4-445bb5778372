"""Blob service for the Immuta SRE Toolkit."""

import time
from typing import Dict, List, Optional, Any

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class BlobService:
    """Service for managing blobs in Immuta.

    This service provides functionality for managing blobs, including
    saving blob metadata, retrieving blobs, and deleting blobs.

    Attributes:
        client: Immuta client instance.
    """

    def __init__(self, client):
        """Initialize the blob service.

        Args:
            client: Immuta client instance.
        """
        self.client = client

    def get_blob_handler_types(self) -> List[Dict[str, Any]]:
        """Get available blob handler types.

        Returns:
            List of blob handler type dictionaries.
        """
        if self.client.is_local:
            return [
                {
                    "id": "s3",
                    "name": "Amazon S3",
                    "description": "Amazon S3 blob handler",
                },
                {
                    "id": "azure",
                    "name": "Azure Blob Storage",
                    "description": "Azure Blob Storage blob handler",
                },
                {
                    "id": "gcs",
                    "name": "Google Cloud Storage",
                    "description": "Google Cloud Storage blob handler",
                },
            ]

        # According to Immuta API V1, the endpoint for getting blob handler types is /dataSource/blobHandlerTypes
        response = self.client.make_request("GET", "dataSource/blobHandlerTypes")
        return response.json()

    def save_blob_metadata(
        self, data_source_id: int, blob_metadata: Dict[str, Any], dry_run: bool = False
    ) -> Dict[str, Any]:
        """Save blob metadata to Immuta.

        Args:
            data_source_id: Data source ID.
            blob_metadata: Blob metadata dictionary.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source is not found or blob metadata is invalid.
        """
        # Validate blob metadata
        required_fields = ["name", "size", "contentType", "handler"]
        for field in required_fields:
            if field not in blob_metadata:
                raise ValueError(f"Blob metadata missing required field: {field}")

        if dry_run:
            logger.info(
                f"Dry run: Would save blob metadata for data source {data_source_id}"
            )
            self.client.notifier.send_notification(
                "save_blob_metadata_dry_run",
                {
                    "data_source_id": data_source_id,
                    "blob_name": blob_metadata.get("name"),
                },
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would save blob metadata",
                "data_source_id": data_source_id,
                "blob_name": blob_metadata.get("name"),
            }

        if self.client.is_local:
            logger.info(f"Mock: Saved blob metadata for data source {data_source_id}")
            self.client.notifier.send_notification(
                "save_blob_metadata",
                {
                    "data_source_id": data_source_id,
                    "blob_name": blob_metadata.get("name"),
                },
                "Success",
            )
            return {
                "success": True,
                "id": 1,
                "data_source_id": data_source_id,
                "name": blob_metadata.get("name"),
            }

        try:
            # According to Immuta API V1, the endpoint for saving blob metadata is /dataSource/{id}/blobs
            response = self.client.make_request(
                "POST", f"dataSource/{data_source_id}/blobs", data=blob_metadata
            )
            result = response.json()
            logger.info(f"Saved blob metadata for data source {data_source_id}")
            self.client.notifier.send_notification(
                "save_blob_metadata",
                {
                    "data_source_id": data_source_id,
                    "blob_name": blob_metadata.get("name"),
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to save blob metadata: {e}")
            self.client.notifier.send_notification(
                "save_blob_metadata",
                {
                    "data_source_id": data_source_id,
                    "blob_name": blob_metadata.get("name"),
                },
                "Failed",
                str(e),
            )
            raise

    def persist_blob(
        self, data_source_id: int, blob_id: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Persist blob metadata locally.

        Args:
            data_source_id: Data source ID.
            blob_id: Blob ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source or blob is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would persist blob {blob_id} for data source {data_source_id}"
            )
            self.client.notifier.send_notification(
                "persist_blob_dry_run",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would persist blob",
                "data_source_id": data_source_id,
                "blob_id": blob_id,
            }

        if self.client.is_local:
            logger.info(f"Mock: Persisted blob {blob_id} for data source {data_source_id}")
            self.client.notifier.send_notification(
                "persist_blob",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Success",
            )
            return {
                "success": True,
                "data_source_id": data_source_id,
                "blob_id": blob_id,
            }

        try:
            # According to Immuta API V1, the endpoint for persisting a blob is /dataSource/{id}/persistBlob
            response = self.client.make_request(
                "POST",
                f"dataSource/{data_source_id}/persistBlob",
                data={"blobId": blob_id},
            )
            result = response.json()
            logger.info(f"Persisted blob {blob_id} for data source {data_source_id}")
            self.client.notifier.send_notification(
                "persist_blob",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to persist blob: {e}")
            self.client.notifier.send_notification(
                "persist_blob",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Failed",
                str(e),
            )
            raise

    def get_blob(self, data_source_id: int, blob_id: str) -> Dict[str, Any]:
        """Get blob metadata.

        Args:
            data_source_id: Data source ID.
            blob_id: Blob ID.

        Returns:
            Blob metadata dictionary.

        Raises:
            ValueError: If data source or blob is not found.
        """
        if self.client.is_local:
            return {
                "id": blob_id,
                "name": f"blob_{blob_id}",
                "size": 1024,
                "contentType": "application/octet-stream",
                "handler": {"type": "s3", "bucket": "test-bucket", "key": "test-key"},
                "dataSourceId": data_source_id,
            }

        # According to Immuta API V1, the endpoint for getting a blob is /dataSource/{id}/blob/{blobId}
        response = self.client.make_request(
            "GET", f"dataSource/{data_source_id}/blob/{blob_id}"
        )
        return response.json()

    def delete_blob(
        self, data_source_id: int, blob_id: str, dry_run: bool = False
    ) -> Dict[str, Any]:
        """Delete a blob.

        Args:
            data_source_id: Data source ID.
            blob_id: Blob ID.
            dry_run: Whether to perform a dry run.

        Returns:
            Dictionary with operation status.

        Raises:
            ValueError: If data source or blob is not found.
        """
        if dry_run:
            logger.info(
                f"Dry run: Would delete blob {blob_id} from data source {data_source_id}"
            )
            self.client.notifier.send_notification(
                "delete_blob_dry_run",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Success",
            )
            return {
                "success": True,
                "message": "Dry run: Would delete blob",
                "data_source_id": data_source_id,
                "blob_id": blob_id,
            }

        blob_backup = None
        if self.client.storage:
            try:
                # Get blob details before deleting
                blob = self.get_blob(data_source_id, blob_id)
                blob_backup = f"blob_{data_source_id}_{blob_id}_{int(time.time())}.json"
                self.client.storage.upload_file(
                    blob,
                    blob_backup,
                    metadata={
                        "type": "blob",
                        "data_source_id": str(data_source_id),
                        "blob_id": blob_id,
                        "operation": "delete_blob",
                    },
                )
            except Exception as e:
                logger.warning(f"Failed to create backup: {e}")

        if self.client.is_local:
            logger.info(f"Mock: Deleted blob {blob_id} from data source {data_source_id}")
            self.client.notifier.send_notification(
                "delete_blob",
                {
                    "data_source_id": data_source_id,
                    "blob_id": blob_id,
                    "backup": blob_backup,
                },
                "Success",
            )
            return {
                "success": True,
                "data_source_id": data_source_id,
                "blob_id": blob_id,
            }

        try:
            # According to Immuta API V1, the endpoint for deleting a blob is /dataSource/{id}/blob/{blobId}
            response = self.client.make_request(
                "DELETE", f"dataSource/{data_source_id}/blob/{blob_id}"
            )
            result = response.json()
            logger.info(f"Deleted blob {blob_id} from data source {data_source_id}")
            self.client.notifier.send_notification(
                "delete_blob",
                {
                    "data_source_id": data_source_id,
                    "blob_id": blob_id,
                    "backup": blob_backup,
                },
                "Success",
            )
            return result
        except Exception as e:
            logger.error(f"Failed to delete blob: {e}")
            self.client.notifier.send_notification(
                "delete_blob",
                {"data_source_id": data_source_id, "blob_id": blob_id},
                "Failed",
                str(e),
            )
            raise
