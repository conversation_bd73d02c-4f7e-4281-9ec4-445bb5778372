"""API server commands for the Immuta SRE Toolkit."""

import os
import sys
import uuid
import secrets
import uvicorn
import click
from rich.console import Console
from rich.table import Table
from datetime import datetime, timedelta
from jose import jwt

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.api.auth import SECRET_KEY, ALGORITHM

logger = get_logger(__name__)
console = Console()


@click.group(name="api")
def api_group() -> click.Group:
    """API server commands."""
    return click.Group


@api_group.command(name="generate-key")
@click.option(
    "--username",
    required=True,
    help="Username for the API key",
)
@click.option(
    "--expires-days",
    default=30,
    type=int,
    help="Number of days until the key expires",
)
def generate_api_key(username: str, expires_days: int) -> None:
    """Generate an API key for authentication.

    Args:
        username: Username for the API key.
        expires_days: Number of days until the key expires.
    """
    try:
        # Generate expiration date
        expire = datetime.utcnow() + timedelta(days=expires_days)

        # Create token data
        token_data = {
            "sub": username,
            "exp": expire,
            "jti": str(uuid.uuid4()),
        }

        # Create token
        token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)

        # Create table for display
        table = Table(title="API Key Generated")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("Username", username)
        table.add_row("Expires", expire.strftime("%Y-%m-%d %H:%M:%S UTC"))
        table.add_row("API Key", token)

        console.print(table)
        console.print(
            "\n[yellow]Keep this key secure! It will not be shown again.[/yellow]"
        )

        logger.info(f"API key generated for user {username}")
    except Exception as e:
        console.print(f"[red]Error generating API key: {e}[/red]")
        logger.error(f"Failed to generate API key: {e}", exc_info=True)
        sys.exit(1)


@api_group.command(name="start")
@click.option(
    "--host",
    default="127.0.0.1",
    help="Host to bind the server to",
)
@click.option(
    "--port",
    default=8000,
    type=int,
    help="Port to bind the server to",
)
@click.option(
    "--reload",
    is_flag=True,
    help="Enable auto-reload for development",
)
@click.option(
    "--workers",
    default=1,
    type=int,
    help="Number of worker processes",
)
def start_api(host: str, port: int, reload: bool, workers: int) -> None:
    """Start the API server.

    Args:
        host: Host to bind the server to.
        port: Port to bind the server to.
        reload: Enable auto-reload for development.
        workers: Number of worker processes.
    """
    try:
        console.print(f"[green]Starting API server at http://{host}:{port}[/green]")
        console.print(
            f"[green]API documentation available at http://{host}:{port}/docs[/green]"
        )

        # Start the API server
        uvicorn.run(
            "immuta_toolkit.api.app:app",
            host=host,
            port=port,
            reload=reload,
            workers=workers,
        )
    except Exception as e:
        console.print(f"[red]Error starting API server: {e}[/red]")
        logger.error(f"Failed to start API server: {e}", exc_info=True)
        sys.exit(1)


@api_group.command(name="user-add")
@click.option(
    "--username",
    required=True,
    help="Username for the API user",
)
@click.option(
    "--password",
    required=True,
    help="Password for the API user",
)
def add_api_user(username: str, password: str) -> None:
    """Add a new API user.

    Args:
        username: Username for the API user.
        password: Password for the API user.
    """
    try:
        from immuta_toolkit.api.auth import get_password_hash, USERS

        # Check if user already exists
        if username in USERS:
            console.print(
                f"[yellow]User {username} already exists. Updating password.[/yellow]"
            )

        # Hash password
        hashed_password = get_password_hash(password)

        # Add user to database
        USERS[username] = {
            "username": username,
            "hashed_password": hashed_password,
            "disabled": False,
        }

        console.print(f"[green]User {username} added successfully.[/green]")
        logger.info(f"API user {username} added")
    except Exception as e:
        console.print(f"[red]Error adding API user: {e}[/red]")
        logger.error(f"Failed to add API user: {e}", exc_info=True)
        sys.exit(1)


@api_group.command(name="user-list")
def list_api_users() -> None:
    """List all API users."""
    try:
        from immuta_toolkit.api.auth import USERS

        # Create table for display
        table = Table(title="API Users")
        table.add_column("Username", style="cyan")
        table.add_column("Status", style="green")

        # Add rows for each user
        for username, user in USERS.items():
            status = "Disabled" if user.get("disabled") else "Active"
            table.add_row(username, status)

        console.print(table)
        logger.info("API users listed")
    except Exception as e:
        console.print(f"[red]Error listing API users: {e}[/red]")
        logger.error(f"Failed to list API users: {e}", exc_info=True)
        sys.exit(1)


@api_group.command(name="user-disable")
@click.option(
    "--username",
    required=True,
    help="Username of the API user to disable",
)
def disable_api_user(username: str) -> None:
    """Disable an API user.

    Args:
        username: Username of the API user to disable.
    """
    try:
        from immuta_toolkit.api.auth import USERS

        # Check if user exists
        if username not in USERS:
            console.print(f"[red]User {username} does not exist.[/red]")
            sys.exit(1)

        # Disable user
        USERS[username]["disabled"] = True

        console.print(f"[green]User {username} disabled successfully.[/green]")
        logger.info(f"API user {username} disabled")
    except Exception as e:
        console.print(f"[red]Error disabling API user: {e}[/red]")
        logger.error(f"Failed to disable API user: {e}", exc_info=True)
        sys.exit(1)
