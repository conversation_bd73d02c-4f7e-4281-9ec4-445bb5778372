"""Batch operation commands for the Immuta SRE Toolkit CLI."""

import os
import json
import csv
from typing import Dict

import click
from rich.console import Console
from rich.table import Table
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    BarColumn,
    TimeElapsedColumn,
)

from immuta_toolkit.models import (
    UserModel,
    DataSourceModel,
    PolicyModel,
)
from immuta_toolkit.operations.engine import OperationEngine
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(name="batch")
def batch_group():
    """Batch operations commands."""
    pass


@batch_group.command(name="import")
@click.option(
    "--file",
    "-f",
    required=True,
    help="Path to the import file (JSON or CSV)",
)
@click.option(
    "--type",
    "-t",
    required=True,
    type=click.Choice(["users", "data-sources", "policies", "projects"]),
    help="Type of entities to import",
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Perform a dry run without making changes",
)
@click.option(
    "--backup",
    is_flag=True,
    help="Create a backup before making changes",
)
@click.option(
    "--batch-size",
    type=int,
    default=10,
    help="Number of entities to process in each batch",
)
@click.pass_context
def import_command(ctx, file, type, dry_run, backup, batch_size):
    """Import entities from a file."""
    client = ctx.obj["client"]

    # Check if file exists
    if not os.path.exists(file):
        console.print(f"[red]Error: File not found: {file}[/red]")
        return

    # Load data from file
    try:
        if file.endswith(".json"):
            with open(file, "r") as f:
                data = json.load(f)
        elif file.endswith(".csv"):
            with open(file, "r") as f:
                reader = csv.DictReader(f)
                data = list(reader)
        else:
            console.print(f"[red]Error: Unsupported file format: {file}[/red]")
            return
    except Exception as e:
        console.print(f"[red]Error loading file: {e}[/red]")
        return

    # Create operation engine
    engine = OperationEngine(client)

    # Process data in batches
    total = len(data)
    success_count = 0
    error_count = 0
    skipped_count = 0
    errors = []

    # Validate data before processing
    console.print("[bold blue]Validating data before import...[/bold blue]")
    validation_errors = []

    for i, item in enumerate(data):
        try:
            if type == "users":
                UserModel(**item)
            elif type == "data-sources":
                DataSourceModel(**item)
            elif type == "policies":
                PolicyModel(**item)
            elif type == "projects":
                # TODO: Add project model validation
                pass
        except Exception as e:
            validation_errors.append((i, str(e)))

    if validation_errors:
        console.print(
            f"[bold red]Found {len(validation_errors)} validation errors:[/bold red]"
        )
        for i, (index, error) in enumerate(validation_errors[:5]):
            console.print(f"  [red]{index+1}. Item {index}: {error}[/red]")

        if len(validation_errors) > 5:
            console.print(
                f"  [red]...and {len(validation_errors) - 5} more errors[/red]"
            )

        if not click.confirm(
            "Do you want to continue with valid items only?", default=False
        ):
            console.print("[yellow]Import cancelled by user[/yellow]")
            return

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
        TimeElapsedColumn(),
        TextColumn(
            "[bold green]{task.fields[success]}[/bold green] succeeded, [bold red]{task.fields[errors]}[/bold red] failed, [bold yellow]{task.fields[skipped]}[/bold yellow] skipped"
        ),
    ) as progress:
        task = progress.add_task(
            f"Importing {type}", total=total, success=0, errors=0, skipped=0
        )

        for i in range(0, total, batch_size):
            batch = data[i : i + batch_size]

            for item_index, item in enumerate(batch):
                # Skip items with validation errors
                global_index = i + item_index
                if any(index == global_index for index, _ in validation_errors):
                    skipped_count += 1
                    progress.update(task, advance=1, skipped=skipped_count)
                    continue

                try:
                    item_id = item.get(
                        "email", item.get("name", f"item_{global_index}")
                    )

                    if type == "users":
                        if dry_run:
                            console.print(
                                f"[yellow]Would create user: {item['email']}[/yellow]"
                            )
                        else:
                            user = UserModel(**item)
                            engine.create_user(user, backup=backup)
                    elif type == "data-sources":
                        if dry_run:
                            console.print(
                                f"[yellow]Would create data source: {item['name']}[/yellow]"
                            )
                        else:
                            data_source = DataSourceModel(**item)
                            engine.create_data_source(data_source, backup=backup)
                    elif type == "policies":
                        if dry_run:
                            console.print(
                                f"[yellow]Would create policy: {item['name']}[/yellow]"
                            )
                        else:
                            policy = PolicyModel(**item)
                            engine.create_policy(policy, backup=backup)
                    elif type == "projects":
                        if dry_run:
                            console.print(
                                f"[yellow]Would create project: {item['name']}[/yellow]"
                            )
                        else:
                            engine.create_project(item, backup=backup)

                    success_count += 1
                    progress.update(task, advance=1, success=success_count)
                except Exception as e:
                    error_message = str(e)
                    errors.append((item_id, error_message))
                    logger.error(
                        f"Error processing item {item_id}: {error_message}",
                        exc_info=True,
                    )
                    error_count += 1
                    progress.update(task, advance=1, errors=error_count)

    # Print summary
    console.print(f"\n[bold green]Import completed:[/bold green]")

    # Create a summary table
    table = Table(show_header=True, header_style="bold")
    table.add_column("Metric", style="dim")
    table.add_column("Count", justify="right")
    table.add_column("Percentage", justify="right")

    table.add_row("Total", str(total), "100%")
    table.add_row(
        "[green]Success[/green]",
        f"[green]{success_count}[/green]",
        f"[green]{success_count/total*100:.1f}%[/green]" if total > 0 else "0%",
    )
    table.add_row(
        "[red]Errors[/red]",
        f"[red]{error_count}[/red]",
        f"[red]{error_count/total*100:.1f}%[/red]" if total > 0 else "0%",
    )
    table.add_row(
        "[yellow]Skipped[/yellow]",
        f"[yellow]{skipped_count}[/yellow]",
        f"[yellow]{skipped_count/total*100:.1f}%[/yellow]" if total > 0 else "0%",
    )

    console.print(table)

    # Show error details if there were errors
    if errors:
        console.print("\n[bold red]Error details:[/bold red]")
        error_table = Table(show_header=True, header_style="bold red")
        error_table.add_column("Item", style="dim")
        error_table.add_column("Error")

        for item_id, error in errors[:10]:  # Show only first 10 errors
            error_table.add_row(str(item_id), error)

        if len(errors) > 10:
            error_table.add_row(
                "...", f"[dim]...and {len(errors) - 10} more errors[/dim]"
            )

        console.print(error_table)

    if dry_run:
        console.print(
            "\n[bold yellow]This was a dry run. No changes were made.[/bold yellow]"
        )


@batch_group.command(name="export")
@click.option(
    "--file",
    "-f",
    required=True,
    help="Path to the export file (JSON or CSV)",
)
@click.option(
    "--type",
    "-t",
    required=True,
    type=click.Choice(["users", "data-sources", "policies", "projects"]),
    help="Type of entities to export",
)
@click.option(
    "--limit",
    type=int,
    default=1000,
    help="Maximum number of entities to export",
)
@click.option(
    "--filter",
    help="Filter expression (e.g., 'role=admin' for users)",
)
@click.pass_context
def export_command(ctx, file, type, limit, filter):
    """Export entities to a file."""
    client = ctx.obj["client"]

    # Create operation engine
    engine = OperationEngine(client)

    # Parse filter
    filter_dict = {}
    if filter:
        for item in filter.split(","):
            if "=" in item:
                key, value = item.split("=", 1)
                filter_dict[key.strip()] = value.strip()

    # Get data
    try:
        with console.status(f"Exporting {type}..."):
            if type == "users":
                data = engine.list_users(limit=limit, **filter_dict)
            elif type == "data-sources":
                data = engine.list_data_sources(limit=limit, **filter_dict)
            elif type == "policies":
                data = engine.list_policies(limit=limit, **filter_dict)
            elif type == "projects":
                data = engine.list_projects(limit=limit, **filter_dict)
    except Exception as e:
        console.print(f"[red]Error exporting data: {e}[/red]")
        return

    # Save data to file
    try:
        if file.endswith(".json"):
            with open(file, "w") as f:
                json.dump(data, f, indent=2)
        elif file.endswith(".csv"):
            if not data:
                console.print("[yellow]No data to export[/yellow]")
                return

            with open(file, "w", newline="") as f:
                writer = csv.DictWriter(f, fieldnames=data[0].keys())
                writer.writeheader()
                writer.writerows(data)
        else:
            console.print(f"[red]Error: Unsupported file format: {file}[/red]")
            return
    except Exception as e:
        console.print(f"[red]Error saving file: {e}[/red]")
        return

    console.print(f"[green]Successfully exported {len(data)} {type} to {file}[/green]")


@batch_group.command(name="validate")
@click.option(
    "--file",
    "-f",
    required=True,
    help="Path to the file to validate (JSON or CSV)",
)
@click.option(
    "--type",
    "-t",
    required=True,
    type=click.Choice(["users", "data-sources", "policies", "projects"]),
    help="Type of entities to validate",
)
def validate_command(file, type):
    """Validate entities in a file."""
    # Check if file exists
    if not os.path.exists(file):
        console.print(f"[red]Error: File not found: {file}[/red]")
        return

    # Load data from file
    try:
        if file.endswith(".json"):
            with open(file, "r") as f:
                data = json.load(f)
        elif file.endswith(".csv"):
            with open(file, "r") as f:
                reader = csv.DictReader(f)
                data = list(reader)
        else:
            console.print(f"[red]Error: Unsupported file format: {file}[/red]")
            return
    except Exception as e:
        console.print(f"[red]Error loading file: {e}[/red]")
        return

    # Validate data
    valid_count = 0
    invalid_count = 0
    errors = []

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
    ) as progress:
        task = progress.add_task(f"Validating {type}", total=len(data))

        for i, item in enumerate(data):
            try:
                if type == "users":
                    UserModel(**item)
                elif type == "data-sources":
                    DataSourceModel(**item)
                elif type == "policies":
                    PolicyModel(**item)
                elif type == "projects":
                    # TODO: Add project model validation
                    pass

                valid_count += 1
            except Exception as e:
                invalid_count += 1
                errors.append((i, str(e)))

            progress.update(task, advance=1)

    # Print summary
    console.print(f"\n[green]Validation completed:[/green]")
    console.print(f"  Total: {len(data)}")
    console.print(f"  Valid: {valid_count}")
    console.print(f"  Invalid: {invalid_count}")

    if errors:
        console.print("\n[red]Validation errors:[/red]")
        table = Table(show_header=True, header_style="bold")
        table.add_column("Index")
        table.add_column("Error")

        for index, error in errors[:10]:  # Show only first 10 errors
            table.add_row(str(index), error)

        console.print(table)

        if len(errors) > 10:
            console.print(f"[yellow]...and {len(errors) - 10} more errors[/yellow]")
