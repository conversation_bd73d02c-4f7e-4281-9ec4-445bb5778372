"""CLI commands for project management."""

import sys
from typing import List, Optional, Dict, Any

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from immuta_toolkit.cli.commands import common_options, format_output, with_progress, handle_error
from immuta_toolkit.cli.help_formatter import <PERSON><PERSON>ommand, add_command_examples
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(name="project")
def project_group():
    """Project management operations."""
    pass


@project_group.command(name="list", cls=RichCommand)
@common_options
@click.option(
    "--limit", type=int, default=100, help="Maximum number of projects to return"
)
@click.option("--offset", type=int, default=0, help="Offset for pagination")
@click.option(
    "--filter", help="Filter projects by name (case-insensitive substring match)"
)
@click.pass_context
@handle_error
def list_projects(ctx, output, no_color, verbose, limit, offset, filter):
    """List projects.
    
    Retrieves a list of projects from the Immuta instance.
    Results can be filtered by name and paginated using limit and offset.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("[cyan]Fetching projects...[/cyan]", total=None)
        
        try:
            # Get projects
            projects = client.project_service.list_projects(
                limit=limit,
                offset=offset,
                name_filter=filter,
            )
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                table = Table(title=f"Projects ({len(projects)})")
                table.add_column("ID", justify="right", style="cyan")
                table.add_column("Name", style="green")
                table.add_column("Description", style="blue")
                table.add_column("Owner", style="yellow")
                table.add_column("Data Sources", justify="right")
                
                for project in projects:
                    table.add_row(
                        str(project.get("id", "")),
                        project.get("name", ""),
                        project.get("description", "")[:50] + ("..." if len(project.get("description", "")) > 50 else ""),
                        project.get("owner", {}).get("name", ""),
                        str(len(project.get("dataSources", []))),
                    )
                
                console.print(table)
            else:
                format_output(projects, output, "Projects")
            
            return projects
        except Exception as e:
            progress.update(task, completed=True)
            raise e


@project_group.command(name="get", cls=RichCommand)
@common_options
@click.option("--id", required=True, help="Project ID")
@click.pass_context
@handle_error
def get_project(ctx, output, no_color, verbose, id):
    """Get project details.
    
    Retrieves detailed information about a specific project.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"[cyan]Fetching project {id}...[/cyan]", total=None)
        
        try:
            # Get project
            project = client.project_service.get_project(id)
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                # Project details table
                details_table = Table(title=f"Project: {project.get('name', '')}")
                details_table.add_column("Property", style="cyan")
                details_table.add_column("Value", style="green")
                
                details_table.add_row("ID", str(project.get("id", "")))
                details_table.add_row("Name", project.get("name", ""))
                details_table.add_row("Description", project.get("description", ""))
                details_table.add_row("Owner", project.get("owner", {}).get("name", ""))
                details_table.add_row("Created", project.get("created", ""))
                details_table.add_row("Updated", project.get("updated", ""))
                
                console.print(details_table)
                
                # Data sources table
                if project.get("dataSources"):
                    ds_table = Table(title="Data Sources")
                    ds_table.add_column("ID", style="cyan")
                    ds_table.add_column("Name", style="green")
                    ds_table.add_column("Type", style="blue")
                    
                    for ds in project.get("dataSources", []):
                        ds_table.add_row(
                            str(ds.get("id", "")),
                            ds.get("name", ""),
                            ds.get("type", ""),
                        )
                    
                    console.print(ds_table)
                
                # Members table
                if project.get("members"):
                    members_table = Table(title="Members")
                    members_table.add_column("ID", style="cyan")
                    members_table.add_column("Name", style="green")
                    members_table.add_column("Role", style="blue")
                    
                    for member in project.get("members", []):
                        members_table.add_row(
                            str(member.get("id", "")),
                            member.get("name", ""),
                            member.get("role", ""),
                        )
                    
                    console.print(members_table)
            else:
                format_output(project, output, f"Project {id}")
            
            return project
        except Exception as e:
            progress.update(task, completed=True)
            raise e


@project_group.command(name="create", cls=RichCommand)
@common_options
@click.option("--name", required=True, help="Project name")
@click.option("--description", help="Project description")
@click.option("--data-source", multiple=True, help="Data source ID to add to the project")
@click.option("--no-backup", is_flag=True, help="Skip backup before making changes")
@click.option("--dry-run", is_flag=True, help="Perform a dry run without making changes")
@click.pass_context
@handle_error
def create_project(ctx, output, no_color, verbose, name, description, data_source, no_backup, dry_run):
    """Create a new project.
    
    Creates a new project with the specified name, description, and data sources.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("[cyan]Creating project...[/cyan]", total=None)
        
        try:
            # Create project
            project = {
                "name": name,
                "description": description or "",
                "dataSources": [{"id": ds_id} for ds_id in data_source],
            }
            
            if dry_run:
                # Simulate project creation
                progress.update(task, completed=True)
                console.print(Panel("[yellow]Dry run: Would create project[/yellow]"))
                console.print(f"Name: {name}")
                console.print(f"Description: {description or ''}")
                console.print(f"Data Sources: {', '.join(data_source) if data_source else 'None'}")
                return project
            
            # Create project
            result = client.project_service.create_project(
                project=project,
                backup=not no_backup,
            )
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                console.print(f"[green]Project created successfully[/green]")
                console.print(f"ID: {result.get('id', '')}")
                console.print(f"Name: {result.get('name', '')}")
            else:
                format_output(result, output, "Created Project")
            
            return result
        except Exception as e:
            progress.update(task, completed=True)
            raise e


# Add examples to commands
add_command_examples(list_projects, [
    {
        "command": "immuta-toolkit project list",
        "description": "List all projects",
    },
    {
        "command": "immuta-toolkit project list --filter marketing",
        "description": "List projects with 'marketing' in the name",
    },
    {
        "command": "immuta-toolkit project list --limit 10 --offset 20",
        "description": "List 10 projects starting from the 20th project",
    },
    {
        "command": "immuta-toolkit project list --output json",
        "description": "List projects and output in JSON format",
    },
])

add_command_examples(get_project, [
    {
        "command": "immuta-toolkit project get --id 123",
        "description": "Get details for project with ID 123",
    },
    {
        "command": "immuta-toolkit project get --id 123 --output yaml",
        "description": "Get project details in YAML format",
    },
])

add_command_examples(create_project, [
    {
        "command": "immuta-toolkit project create --name 'Marketing Analytics' --description 'Project for marketing analytics'",
        "description": "Create a new project",
    },
    {
        "command": "immuta-toolkit project create --name 'Sales Data' --data-source 123 --data-source 456",
        "description": "Create a project with data sources",
    },
    {
        "command": "immuta-toolkit project create --name 'Test Project' --dry-run",
        "description": "Simulate creating a project without actually creating it",
    },
])
