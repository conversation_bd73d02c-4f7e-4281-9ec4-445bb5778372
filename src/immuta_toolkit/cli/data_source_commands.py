"""CLI commands for data source management."""

import json
import sys
from typing import List, Optional

import click
from rich.console import Console
from rich.table import Table

from immuta_toolkit.client import ImmutaClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(name="data-source")
def data_source_group():
    """Data source management operations."""
    pass


@data_source_group.command(name="list")
@click.option(
    "--limit", type=int, default=1000, help="Maximum number of data sources to return"
)
@click.option("--offset", type=int, default=0, help="Offset for pagination")
@click.pass_context
def list_data_sources(ctx, limit, offset):
    """List data sources."""
    client = ctx.obj["client"]
    data_sources = client.data_source_service.list_data_sources(
        limit=limit, offset=offset
    )

    table = Table(title=f"Data Sources ({len(data_sources)})")
    table.add_column("ID", justify="right")
    table.add_column("Name")
    table.add_column("Description")
    table.add_column("Type")
    table.add_column("Tags")

    for ds in data_sources:
        table.add_row(
            str(ds["id"]),
            ds["name"],
            ds.get("description", ""),
            ds.get("handler", {}).get("type", ""),
            ", ".join(ds.get("tags", [])),
        )

    console.print(table)


@data_source_group.command(name="get")
@click.option("--id", required=True, help="Data source ID")
@click.pass_context
def get_data_source(ctx, id):
    """Get data source by ID."""
    client = ctx.obj["client"]

    try:
        # Try to convert to int if it's a numeric ID
        if id.isdigit():
            id = int(id)

        data_source = client.data_source_service.get_data_source(id)

        table = Table(title=f"Data Source {id}")
        table.add_column("Field", style="cyan")
        table.add_column("Value")

        table.add_row("ID", str(data_source["id"]))
        table.add_row("Name", data_source["name"])
        table.add_row("Description", data_source.get("description", ""))
        table.add_row("Type", data_source.get("handler", {}).get("type", ""))
        table.add_row("Tags", ", ".join(data_source.get("tags", [])))

        console.print(table)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)


@data_source_group.command(name="create")
@click.argument("file_path", type=click.Path(exists=True))
@click.option("--no-backup", is_flag=True, help="Skip backup before making changes")
@click.option(
    "--dry-run", is_flag=True, help="Perform a dry run without making changes"
)
@click.pass_context
def create_data_source(ctx, file_path, no_backup, dry_run):
    """Create a new data source from a JSON file."""
    client = ctx.obj["client"]

    try:
        with open(file_path, "r") as f:
            data_source = json.load(f)

        result = client.data_source_service.create_data_source(
            data_source=data_source,
            backup=not no_backup,
            dry_run=dry_run,
        )

        if dry_run:
            console.print("[yellow]Dry run: Would create data source[/yellow]")
        else:
            console.print(f"[green]Data source created successfully[/green]")
            console.print(f"ID: {result['id']}")
            console.print(f"Name: {result['name']}")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)
