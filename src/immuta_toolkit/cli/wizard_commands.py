"""Wizard commands for the Immuta SRE Toolkit CLI."""

import os
import sys
import json
import time
from typing import Dict, Any, List, Optional, Callable, Union, Tuple

import click
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.markdown import Markdown
from rich.syntax import Syntax
from rich.table import Table

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.utils.wizard import (
    Wizard,
    WizardStep,
    create_input_step,
    create_confirmation_step,
    create_summary_step,
)

logger = get_logger(__name__)
console = Console()


@click.group(name="wizard")
@click.pass_context
def wizard_group(ctx):
    """Wizard commands for guided operations."""
    pass


@wizard_group.command(name="user")
@click.pass_context
def user_wizard(ctx):
    """User management wizard."""
    client = ctx.obj["client"]
    
    # Create wizard steps
    steps = [
        create_input_step(
            name="operation",
            title="User Operation",
            description="Select the user operation you want to perform.",
            prompts=[
                {
                    "key": "operation",
                    "message": "Select operation",
                    "choices": ["create", "update", "delete"],
                    "required": True,
                },
            ],
            next_step="user_details",
        ),
        create_input_step(
            name="user_details",
            title="User Details",
            description="Enter user details.",
            prompts=[
                {
                    "key": "username",
                    "message": "Username",
                    "required": True,
                },
                {
                    "key": "email",
                    "message": "Email",
                    "required": True,
                },
                {
                    "key": "role",
                    "message": "Role",
                    "choices": ["user", "admin", "data_owner"],
                    "default": "user",
                    "required": True,
                },
            ],
            next_step="groups",
            skip_condition=lambda ctx: ctx.get("operation") == "delete",
        ),
        create_input_step(
            name="groups",
            title="User Groups",
            description="Enter user groups (comma-separated).",
            prompts=[
                {
                    "key": "groups",
                    "message": "Groups (comma-separated)",
                    "required": False,
                },
            ],
            next_step="summary",
            skip_condition=lambda ctx: ctx.get("operation") == "delete",
        ),
        create_summary_step(
            name="summary",
            title="Summary",
            description="Review the information before proceeding.",
            fields=[
                {"key": "operation", "label": "Operation"},
                {"key": "username", "label": "Username"},
                {"key": "email", "label": "Email"},
                {"key": "role", "label": "Role"},
                {"key": "groups", "label": "Groups"},
            ],
            next_step="confirm",
        ),
        create_confirmation_step(
            name="confirm",
            title="Confirmation",
            description="Confirm the operation.",
            message="Do you want to proceed with this operation?",
            next_step="execute",
        ),
        WizardStep(
            name="execute",
            title="Executing Operation",
            description="Executing the requested operation...",
            action=lambda ctx: execute_user_operation(client, ctx),
            next_step=None,
        ),
    ]
    
    # Create and run wizard
    wizard = Wizard(
        name="user_wizard",
        title="User Management Wizard",
        description="This wizard will guide you through user management operations.",
        steps=steps,
        start_step="operation",
    )
    
    result = wizard.run()
    
    # Display result
    if result.get("success"):
        console.print("[green]Operation completed successfully![/green]")
    else:
        console.print("[red]Operation failed.[/red]")


@wizard_group.command(name="data-source")
@click.pass_context
def data_source_wizard(ctx):
    """Data source management wizard."""
    client = ctx.obj["client"]
    
    # Create wizard steps
    steps = [
        create_input_step(
            name="operation",
            title="Data Source Operation",
            description="Select the data source operation you want to perform.",
            prompts=[
                {
                    "key": "operation",
                    "message": "Select operation",
                    "choices": ["tag", "untag", "update_metadata"],
                    "required": True,
                },
            ],
            next_step="data_source_id",
        ),
        create_input_step(
            name="data_source_id",
            title="Data Source ID",
            description="Enter the data source ID.",
            prompts=[
                {
                    "key": "data_source_id",
                    "message": "Data Source ID",
                    "required": True,
                },
            ],
            next_step="tags",
            skip_condition=lambda ctx: ctx.get("operation") == "update_metadata",
        ),
        create_input_step(
            name="tags",
            title="Tags",
            description="Enter tags (comma-separated).",
            prompts=[
                {
                    "key": "tags",
                    "message": "Tags (comma-separated)",
                    "required": True,
                },
            ],
            next_step="summary",
            skip_condition=lambda ctx: ctx.get("operation") == "update_metadata",
        ),
        create_input_step(
            name="metadata",
            title="Metadata",
            description="Enter metadata as JSON.",
            prompts=[
                {
                    "key": "metadata",
                    "message": "Metadata (JSON)",
                    "required": True,
                },
            ],
            next_step="summary",
            skip_condition=lambda ctx: ctx.get("operation") != "update_metadata",
        ),
        create_summary_step(
            name="summary",
            title="Summary",
            description="Review the information before proceeding.",
            fields=[
                {"key": "operation", "label": "Operation"},
                {"key": "data_source_id", "label": "Data Source ID"},
                {"key": "tags", "label": "Tags"},
                {"key": "metadata", "label": "Metadata"},
            ],
            next_step="confirm",
        ),
        create_confirmation_step(
            name="confirm",
            title="Confirmation",
            description="Confirm the operation.",
            message="Do you want to proceed with this operation?",
            next_step="execute",
        ),
        WizardStep(
            name="execute",
            title="Executing Operation",
            description="Executing the requested operation...",
            action=lambda ctx: execute_data_source_operation(client, ctx),
            next_step=None,
        ),
    ]
    
    # Create and run wizard
    wizard = Wizard(
        name="data_source_wizard",
        title="Data Source Management Wizard",
        description="This wizard will guide you through data source management operations.",
        steps=steps,
        start_step="operation",
    )
    
    result = wizard.run()
    
    # Display result
    if result.get("success"):
        console.print("[green]Operation completed successfully![/green]")
    else:
        console.print("[red]Operation failed.[/red]")


@wizard_group.command(name="policy")
@click.pass_context
def policy_wizard(ctx):
    """Policy management wizard."""
    client = ctx.obj["client"]
    
    # Create wizard steps
    steps = [
        create_input_step(
            name="operation",
            title="Policy Operation",
            description="Select the policy operation you want to perform.",
            prompts=[
                {
                    "key": "operation",
                    "message": "Select operation",
                    "choices": ["create", "update", "delete"],
                    "required": True,
                },
            ],
            next_step="policy_type",
            skip_condition=lambda ctx: ctx.get("operation") == "delete",
        ),
        create_input_step(
            name="policy_id",
            title="Policy ID",
            description="Enter the policy ID.",
            prompts=[
                {
                    "key": "policy_id",
                    "message": "Policy ID",
                    "required": True,
                },
            ],
            next_step="summary",
            skip_condition=lambda ctx: ctx.get("operation") == "create",
        ),
        create_input_step(
            name="policy_type",
            title="Policy Type",
            description="Select the policy type.",
            prompts=[
                {
                    "key": "policy_type",
                    "message": "Policy Type",
                    "choices": ["subscription", "masking", "row_redaction"],
                    "required": True,
                },
            ],
            next_step="policy_details",
            skip_condition=lambda ctx: ctx.get("operation") != "create",
        ),
        create_input_step(
            name="policy_details",
            title="Policy Details",
            description="Enter policy details.",
            prompts=[
                {
                    "key": "name",
                    "message": "Policy Name",
                    "required": True,
                },
                {
                    "key": "description",
                    "message": "Policy Description",
                    "required": False,
                },
            ],
            next_step="summary",
            skip_condition=lambda ctx: ctx.get("operation") == "delete",
        ),
        create_summary_step(
            name="summary",
            title="Summary",
            description="Review the information before proceeding.",
            fields=[
                {"key": "operation", "label": "Operation"},
                {"key": "policy_id", "label": "Policy ID"},
                {"key": "policy_type", "label": "Policy Type"},
                {"key": "name", "label": "Policy Name"},
                {"key": "description", "label": "Policy Description"},
            ],
            next_step="confirm",
        ),
        create_confirmation_step(
            name="confirm",
            title="Confirmation",
            description="Confirm the operation.",
            message="Do you want to proceed with this operation?",
            next_step="execute",
        ),
        WizardStep(
            name="execute",
            title="Executing Operation",
            description="Executing the requested operation...",
            action=lambda ctx: execute_policy_operation(client, ctx),
            next_step=None,
        ),
    ]
    
    # Create and run wizard
    wizard = Wizard(
        name="policy_wizard",
        title="Policy Management Wizard",
        description="This wizard will guide you through policy management operations.",
        steps=steps,
        start_step="operation",
    )
    
    result = wizard.run()
    
    # Display result
    if result.get("success"):
        console.print("[green]Operation completed successfully![/green]")
    else:
        console.print("[red]Operation failed.[/red]")


def execute_user_operation(client, context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
    """Execute user operation.
    
    Args:
        client: Immuta client instance.
        context: Wizard context.
        
    Returns:
        Tuple of (success, next_step).
    """
    operation = context.get("operation")
    
    try:
        if operation == "create":
            # Parse groups
            groups = []
            if context.get("groups"):
                groups = [g.strip() for g in context["groups"].split(",")]
            
            # Create user
            user = client.user_service.create_user(
                username=context["username"],
                email=context["email"],
                role=context["role"],
                groups=groups,
            )
            
            # Store result
            context["result"] = user
            context["success"] = True
            
            # Display result
            console.print(f"[green]User created successfully: {user['username']}[/green]")
        
        elif operation == "update":
            # Parse groups
            groups = []
            if context.get("groups"):
                groups = [g.strip() for g in context["groups"].split(",")]
            
            # Update user
            user = client.user_service.update_user(
                user_id=context["username"],
                email=context["email"],
                role=context["role"],
                groups=groups,
            )
            
            # Store result
            context["result"] = user
            context["success"] = True
            
            # Display result
            console.print(f"[green]User updated successfully: {user['username']}[/green]")
        
        elif operation == "delete":
            # Delete user
            result = client.user_service.delete_user(
                user_id=context["username"],
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]User deleted successfully: {context['username']}[/green]")
        
        else:
            console.print(f"[red]Unknown operation: {operation}[/red]")
            context["success"] = False
            return False, None
        
        return True, None
    
    except Exception as e:
        logger.error(f"Error executing user operation: {e}", exc_info=True)
        console.print(f"[red]Error: {e}[/red]")
        context["success"] = False
        return False, None


def execute_data_source_operation(client, context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
    """Execute data source operation.
    
    Args:
        client: Immuta client instance.
        context: Wizard context.
        
    Returns:
        Tuple of (success, next_step).
    """
    operation = context.get("operation")
    
    try:
        if operation == "tag":
            # Parse tags
            tags = []
            if context.get("tags"):
                tags = [t.strip() for t in context["tags"].split(",")]
            
            # Add tags
            result = client.tag_service.add_tags_to_data_source(
                data_source_id=context["data_source_id"],
                tags=tags,
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Tags added successfully to data source: {context['data_source_id']}[/green]")
        
        elif operation == "untag":
            # Parse tags
            tags = []
            if context.get("tags"):
                tags = [t.strip() for t in context["tags"].split(",")]
            
            # Remove tags
            result = client.tag_service.remove_tags_from_data_source(
                data_source_id=context["data_source_id"],
                tags=tags,
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Tags removed successfully from data source: {context['data_source_id']}[/green]")
        
        elif operation == "update_metadata":
            # Parse metadata
            metadata = {}
            if context.get("metadata"):
                metadata = json.loads(context["metadata"])
            
            # Update metadata
            result = client.data_source_service.update_data_source(
                data_source_id=context["data_source_id"],
                metadata=metadata,
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Metadata updated successfully for data source: {context['data_source_id']}[/green]")
        
        else:
            console.print(f"[red]Unknown operation: {operation}[/red]")
            context["success"] = False
            return False, None
        
        return True, None
    
    except Exception as e:
        logger.error(f"Error executing data source operation: {e}", exc_info=True)
        console.print(f"[red]Error: {e}[/red]")
        context["success"] = False
        return False, None


def execute_policy_operation(client, context: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
    """Execute policy operation.
    
    Args:
        client: Immuta client instance.
        context: Wizard context.
        
    Returns:
        Tuple of (success, next_step).
    """
    operation = context.get("operation")
    
    try:
        if operation == "create":
            # Create policy
            policy = {
                "name": context["name"],
                "description": context.get("description", ""),
                "policy_type": context["policy_type"],
            }
            
            result = client.policy_service.create_policy(
                policy=policy,
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Policy created successfully: {result['name']}[/green]")
        
        elif operation == "update":
            # Update policy
            policy = {
                "name": context["name"],
                "description": context.get("description", ""),
            }
            
            result = client.policy_service.update_policy(
                policy_id=context["policy_id"],
                policy=policy,
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Policy updated successfully: {result['name']}[/green]")
        
        elif operation == "delete":
            # Delete policy
            result = client.policy_service.delete_policy(
                policy_id=context["policy_id"],
            )
            
            # Store result
            context["result"] = result
            context["success"] = True
            
            # Display result
            console.print(f"[green]Policy deleted successfully: {context['policy_id']}[/green]")
        
        else:
            console.print(f"[red]Unknown operation: {operation}[/red]")
            context["success"] = False
            return False, None
        
        return True, None
    
    except Exception as e:
        logger.error(f"Error executing policy operation: {e}", exc_info=True)
        console.print(f"[red]Error: {e}[/red]")
        context["success"] = False
        return False, None
