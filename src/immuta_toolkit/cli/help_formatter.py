"""Custom help formatter for the Immuta SRE Toolkit CLI."""

import click
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.markdown import Markdown
from rich.syntax import Syntax

console = Console()


class RichHelpFormatter(click.HelpFormatter):
    """Custom help formatter using Rich for better formatting."""

    def __init__(self, indent_increment=2, width=None, max_width=None):
        """Initialize the formatter.

        Args:
            indent_increment: Number of spaces to indent.
            width: Width of the help text.
            max_width: Maximum width of the help text.
        """
        super().__init__(indent_increment, width, max_width)
        self.current_indent = 0

    def write_usage(self, prog, args='', prefix='Usage: '):
        """Write the usage line.

        Args:
            prog: Program name.
            args: Arguments.
            prefix: Prefix for the usage line.
        """
        usage = f"{prefix}{prog} {args}"
        console.print(Panel(usage, title="Usage", border_style="blue"))

    def write_heading(self, heading):
        """Write a heading.

        Args:
            heading: Heading text.
        """
        console.print(f"\n[bold cyan]{heading}[/bold cyan]")

    def write_paragraph(self):
        """Write a paragraph."""
        console.print()

    def write_text(self, text):
        """Write text.

        Args:
            text: Text to write.
        """
        if text:
            console.print(text)

    def write_dl(self, rows, col_max=30, col_spacing=2):
        """Write a definition list.

        Args:
            rows: List of (term, definition) tuples.
            col_max: Maximum width of the first column.
            col_spacing: Spacing between columns.
        """
        table = Table(show_header=False, box=None, padding=(0, 1, 0, 0))
        table.add_column("Option", style="green", no_wrap=True)
        table.add_column("Description")

        for row in rows:
            term, definition = row
            table.add_row(term, definition)

        console.print(table)


class RichCommand(click.Command):
    """Custom command class with rich help formatting."""

    def format_help(self, ctx, formatter):
        """Format the help text.

        Args:
            ctx: Click context.
            formatter: Help formatter.
        """
        # Use our custom formatter
        console.print(Panel(f"[bold]{self.help}[/bold]", title=self.name.upper(), border_style="green"))

        # Usage
        usage = self.collect_usage_pieces(ctx)
        console.print(Panel(f"[bold]Usage:[/bold] {ctx.command_path} {' '.join(usage)}", border_style="blue"))

        # Options
        if self.params:
            console.print("\n[bold cyan]Options:[/bold cyan]")
            table = Table(show_header=False, box=None, padding=(0, 1, 0, 0))
            table.add_column("Option", style="green", no_wrap=True)
            table.add_column("Description")

            for param in self.params:
                # Get option strings
                opts = []
                for opt in param.opts:
                    opts.append(opt)
                for opt in param.secondary_opts:
                    opts.append(opt)
                
                # Get help text
                help_text = param.help or ""
                if param.default is not None and param.default != "" and not param.is_flag:
                    help_text += f" [default: {param.default}]"
                
                # Get required indicator
                if param.required:
                    help_text = f"[bold red](required)[/bold red] {help_text}"
                
                # Add row
                table.add_row(", ".join(opts), help_text)
            
            console.print(table)

        # Examples
        if hasattr(self, 'examples') and self.examples:
            console.print("\n[bold cyan]Examples:[/bold cyan]")
            for example in self.examples:
                console.print(f"  [bold]{example['command']}[/bold]")
                console.print(f"    {example['description']}")
                console.print()


class RichGroup(click.Group):
    """Custom group class with rich help formatting."""

    def format_help(self, ctx, formatter):
        """Format the help text.

        Args:
            ctx: Click context.
            formatter: Help formatter.
        """
        # Use our custom formatter
        console.print(Panel(f"[bold]{self.help}[/bold]", title=self.name.upper(), border_style="green"))

        # Usage
        usage = self.collect_usage_pieces(ctx)
        console.print(Panel(f"[bold]Usage:[/bold] {ctx.command_path} {' '.join(usage)}", border_style="blue"))

        # Options
        if self.params:
            console.print("\n[bold cyan]Options:[/bold cyan]")
            table = Table(show_header=False, box=None, padding=(0, 1, 0, 0))
            table.add_column("Option", style="green", no_wrap=True)
            table.add_column("Description")

            for param in self.params:
                # Get option strings
                opts = []
                for opt in param.opts:
                    opts.append(opt)
                for opt in param.secondary_opts:
                    opts.append(opt)
                
                # Get help text
                help_text = param.help or ""
                if param.default is not None and param.default != "" and not param.is_flag:
                    help_text += f" [default: {param.default}]"
                
                # Get required indicator
                if param.required:
                    help_text = f"[bold red](required)[/bold red] {help_text}"
                
                # Add row
                table.add_row(", ".join(opts), help_text)
            
            console.print(table)

        # Commands
        console.print("\n[bold cyan]Commands:[/bold cyan]")
        table = Table(show_header=False, box=None, padding=(0, 1, 0, 0))
        table.add_column("Command", style="green", no_wrap=True)
        table.add_column("Description")

        commands = []
        for subcommand in self.list_commands(ctx):
            cmd = self.get_command(ctx, subcommand)
            if cmd is None or cmd.hidden:
                continue
            
            commands.append((subcommand, cmd))
        
        commands.sort()
        
        for subcommand, cmd in commands:
            help_text = cmd.get_short_help_str() or ""
            table.add_row(subcommand, help_text)
        
        console.print(table)

        # Examples
        if hasattr(self, 'examples') and self.examples:
            console.print("\n[bold cyan]Examples:[/bold cyan]")
            for example in self.examples:
                console.print(f"  [bold]{example['command']}[/bold]")
                console.print(f"    {example['description']}")
                console.print()


def add_command_examples(command, examples):
    """Add examples to a command.

    Args:
        command: Command to add examples to.
        examples: List of example dictionaries with 'command' and 'description' keys.
    """
    command.examples = examples
    return command
