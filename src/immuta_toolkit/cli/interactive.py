"""Interactive CLI mode for the Immuta SRE Toolkit."""

import os
import sys
import time
from typing import Dict, Any, List, Optional, Tuple, Callable

import click
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.markdown import Markdown
from rich.syntax import Syntax

from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


class InteractiveShell:
    """Interactive shell for the Immuta SRE Toolkit.
    
    This class provides an interactive shell for the Immuta SRE Toolkit,
    allowing users to run commands in a more user-friendly way.
    
    Attributes:
        client: Immuta client instance.
        context: Context dictionary.
        running: Whether the shell is running.
    """
    
    def __init__(self, client: ImmutaHybridClient, context: Dict[str, Any]):
        """Initialize the interactive shell.
        
        Args:
            client: Immuta client instance.
            context: Context dictionary.
        """
        self.client = client
        self.context = context
        self.running = False
        self.commands = self._get_commands()
    
    def _get_commands(self) -> Dict[str, Dict[str, Any]]:
        """Get available commands.
        
        Returns:
            Dictionary of commands.
        """
        return {
            "help": {
                "func": self.cmd_help,
                "help": "Show help",
                "args": [],
            },
            "exit": {
                "func": self.cmd_exit,
                "help": "Exit the shell",
                "args": [],
            },
            "users": {
                "func": self.cmd_users,
                "help": "User management commands",
                "args": ["list", "get", "create", "update", "delete"],
            },
            "datasources": {
                "func": self.cmd_datasources,
                "help": "Data source management commands",
                "args": ["list", "get", "tag", "untag"],
            },
            "policies": {
                "func": self.cmd_policies,
                "help": "Policy management commands",
                "args": ["list", "get", "create", "update", "delete"],
            },
            "projects": {
                "func": self.cmd_projects,
                "help": "Project management commands",
                "args": ["list", "get", "create", "update", "delete"],
            },
            "backup": {
                "func": self.cmd_backup,
                "help": "Backup commands",
                "args": ["create", "list", "restore"],
            },
            "config": {
                "func": self.cmd_config,
                "help": "Configuration commands",
                "args": ["show", "set", "profiles"],
            },
        }
    
    def start(self):
        """Start the interactive shell."""
        self.running = True
        
        # Show welcome message
        console.print(
            Panel.fit(
                "[bold green]Welcome to the Immuta SRE Toolkit Interactive Shell[/bold green]\n\n"
                "Type [bold]help[/bold] to see available commands, or [bold]exit[/bold] to quit.",
                title="Immuta SRE Toolkit",
                subtitle="Interactive Mode",
            )
        )
        
        # Show connection info
        self._show_connection_info()
        
        # Main loop
        while self.running:
            try:
                # Get command
                command = Prompt.ask("\n[bold blue]immuta-toolkit[/bold blue]")
                
                # Skip empty commands
                if not command:
                    continue
                
                # Parse command
                parts = command.strip().split()
                cmd = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []
                
                # Execute command
                if cmd in self.commands:
                    self.commands[cmd]["func"](args)
                else:
                    console.print(f"[red]Unknown command: {cmd}[/red]")
                    console.print("Type [bold]help[/bold] to see available commands.")
            except KeyboardInterrupt:
                console.print("\n[yellow]Interrupted[/yellow]")
            except Exception as e:
                console.print(f"[red]Error: {e}[/red]")
                logger.error(f"Error in interactive shell: {e}", exc_info=True)
    
    def _show_connection_info(self):
        """Show connection information."""
        table = Table(title="Connection Information")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        # Add connection info
        table.add_row("Base URL", self.client.base_url)
        table.add_row("API Key", "***" + self.client.api_key[-4:] if self.client.api_key else "Not set")
        table.add_row("Web Fallback", "Enabled" if self.client.use_web_fallback else "Disabled")
        table.add_row("Headless Mode", "Enabled" if self.client.headless else "Disabled")
        table.add_row("Local Mode", "Enabled" if self.client.is_local else "Disabled")
        
        console.print(table)
    
    def cmd_help(self, args: List[str]):
        """Show help.
        
        Args:
            args: Command arguments.
        """
        if not args:
            # Show general help
            table = Table(title="Available Commands")
            table.add_column("Command", style="cyan")
            table.add_column("Description", style="green")
            table.add_column("Arguments", style="yellow")
            
            for cmd, info in self.commands.items():
                args_str = ", ".join(info["args"]) if info["args"] else "None"
                table.add_row(cmd, info["help"], args_str)
            
            console.print(table)
        else:
            # Show help for specific command
            cmd = args[0].lower()
            if cmd in self.commands:
                info = self.commands[cmd]
                
                console.print(f"[bold cyan]{cmd}[/bold cyan]: {info['help']}")
                
                if info["args"]:
                    console.print("\n[bold]Arguments:[/bold]")
                    for arg in info["args"]:
                        console.print(f"  [yellow]{arg}[/yellow]")
                else:
                    console.print("\n[bold]Arguments:[/bold] None")
            else:
                console.print(f"[red]Unknown command: {cmd}[/red]")
    
    def cmd_exit(self, args: List[str]):
        """Exit the shell.
        
        Args:
            args: Command arguments.
        """
        self.running = False
        console.print("[green]Goodbye![/green]")
    
    def cmd_users(self, args: List[str]):
        """User management commands.
        
        Args:
            args: Command arguments.
        """
        if not args:
            console.print("[yellow]Please specify a subcommand: list, get, create, update, delete[/yellow]")
            return
        
        subcommand = args[0].lower()
        
        if subcommand == "list":
            # List users
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
            ) as progress:
                task = progress.add_task("[cyan]Fetching users...", total=None)
                
                try:
                    users = self.client.user_service.list_users()
                    progress.update(task, completed=True)
                    
                    # Display users
                    table = Table(title=f"Users ({len(users)})")
                    table.add_column("ID", style="cyan")
                    table.add_column("Username", style="green")
                    table.add_column("Email", style="blue")
                    table.add_column("Role", style="yellow")
                    
                    for user in users:
                        table.add_row(
                            str(user.get("id", "")),
                            user.get("username", ""),
                            user.get("email", ""),
                            user.get("role", ""),
                        )
                    
                    console.print(table)
                except Exception as e:
                    progress.update(task, completed=True)
                    console.print(f"[red]Error: {e}[/red]")
        elif subcommand == "get":
            # Get user
            if len(args) < 2:
                console.print("[yellow]Please specify a user ID or username[/yellow]")
                return
            
            user_id = args[1]
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
            ) as progress:
                task = progress.add_task(f"[cyan]Fetching user {user_id}...", total=None)
                
                try:
                    user = self.client.user_service.get_user(user_id)
                    progress.update(task, completed=True)
                    
                    # Display user
                    table = Table(title=f"User: {user.get('username', '')}")
                    table.add_column("Property", style="cyan")
                    table.add_column("Value", style="green")
                    
                    for key, value in user.items():
                        table.add_row(key, str(value))
                    
                    console.print(table)
                except Exception as e:
                    progress.update(task, completed=True)
                    console.print(f"[red]Error: {e}[/red]")
        else:
            console.print(f"[yellow]Subcommand not implemented: {subcommand}[/yellow]")
    
    def cmd_datasources(self, args: List[str]):
        """Data source management commands.
        
        Args:
            args: Command arguments.
        """
        if not args:
            console.print("[yellow]Please specify a subcommand: list, get, tag, untag[/yellow]")
            return
        
        subcommand = args[0].lower()
        
        if subcommand == "list":
            # List data sources
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
            ) as progress:
                task = progress.add_task("[cyan]Fetching data sources...", total=None)
                
                try:
                    data_sources = self.client.data_source_service.list_data_sources()
                    progress.update(task, completed=True)
                    
                    # Display data sources
                    table = Table(title=f"Data Sources ({len(data_sources)})")
                    table.add_column("ID", style="cyan")
                    table.add_column("Name", style="green")
                    table.add_column("Type", style="blue")
                    table.add_column("Tags", style="yellow")
                    
                    for ds in data_sources:
                        table.add_row(
                            str(ds.get("id", "")),
                            ds.get("name", ""),
                            ds.get("type", ""),
                            ", ".join(ds.get("tags", [])),
                        )
                    
                    console.print(table)
                except Exception as e:
                    progress.update(task, completed=True)
                    console.print(f"[red]Error: {e}[/red]")
        else:
            console.print(f"[yellow]Subcommand not implemented: {subcommand}[/yellow]")
    
    def cmd_policies(self, args: List[str]):
        """Policy management commands.
        
        Args:
            args: Command arguments.
        """
        console.print("[yellow]Not implemented yet[/yellow]")
    
    def cmd_projects(self, args: List[str]):
        """Project management commands.
        
        Args:
            args: Command arguments.
        """
        console.print("[yellow]Not implemented yet[/yellow]")
    
    def cmd_backup(self, args: List[str]):
        """Backup commands.
        
        Args:
            args: Command arguments.
        """
        console.print("[yellow]Not implemented yet[/yellow]")
    
    def cmd_config(self, args: List[str]):
        """Configuration commands.
        
        Args:
            args: Command arguments.
        """
        console.print("[yellow]Not implemented yet[/yellow]")


@click.command()
@click.pass_context
def interactive(ctx):
    """Start interactive mode."""
    client = ctx.obj["client"]
    shell = InteractiveShell(client, ctx.obj)
    shell.start()


if __name__ == "__main__":
    interactive()
