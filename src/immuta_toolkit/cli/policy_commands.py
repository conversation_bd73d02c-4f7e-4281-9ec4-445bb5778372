"""CLI commands for policy management."""

import sys
import json
from typing import List, Optional, Dict, Any

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from immuta_toolkit.cli.commands import common_options, format_output, with_progress, handle_error
from immuta_toolkit.cli.help_formatter import RichCommand, add_command_examples
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(name="policy")
def policy_group():
    """Policy management operations."""
    pass


@policy_group.command(name="list", cls=RichCommand)
@common_options
@click.option(
    "--limit", type=int, default=100, help="Maximum number of policies to return"
)
@click.option("--offset", type=int, default=0, help="Offset for pagination")
@click.option(
    "--type",
    type=click.Choice(["subscription", "masking", "row_redaction", "all"]),
    default="all",
    help="Filter policies by type",
)
@click.option(
    "--filter", help="Filter policies by name (case-insensitive substring match)"
)
@click.pass_context
@handle_error
def list_policies(ctx, output, no_color, verbose, limit, offset, type, filter):
    """List policies.
    
    Retrieves a list of policies from the Immuta instance.
    Results can be filtered by type and name, and paginated using limit and offset.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TimeElapsedColumn(),
        console=console,
    ) as progress:
        task = progress.add_task("[cyan]Fetching policies...[/cyan]", total=None)
        
        try:
            # Get policies
            policies = client.policy_service.list_policies(
                limit=limit,
                offset=offset,
                policy_type=None if type == "all" else type,
                name_filter=filter,
            )
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                table = Table(title=f"Policies ({len(policies)})")
                table.add_column("ID", justify="right", style="cyan")
                table.add_column("Name", style="green")
                table.add_column("Type", style="blue")
                table.add_column("Status", style="yellow")
                table.add_column("Data Sources", justify="right")
                
                for policy in policies:
                    table.add_row(
                        str(policy.get("id", "")),
                        policy.get("name", ""),
                        policy.get("type", ""),
                        policy.get("status", ""),
                        str(len(policy.get("dataSources", []))),
                    )
                
                console.print(table)
            else:
                format_output(policies, output, "Policies")
            
            return policies
        except Exception as e:
            progress.update(task, completed=True)
            raise e


@policy_group.command(name="get", cls=RichCommand)
@common_options
@click.option("--id", required=True, help="Policy ID")
@click.pass_context
@handle_error
def get_policy(ctx, output, no_color, verbose, id):
    """Get policy details.
    
    Retrieves detailed information about a specific policy.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"[cyan]Fetching policy {id}...[/cyan]", total=None)
        
        try:
            # Get policy
            policy = client.policy_service.get_policy(id)
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                # Policy details table
                details_table = Table(title=f"Policy: {policy.get('name', '')}")
                details_table.add_column("Property", style="cyan")
                details_table.add_column("Value", style="green")
                
                details_table.add_row("ID", str(policy.get("id", "")))
                details_table.add_row("Name", policy.get("name", ""))
                details_table.add_row("Type", policy.get("type", ""))
                details_table.add_row("Status", policy.get("status", ""))
                details_table.add_row("Created", policy.get("created", ""))
                details_table.add_row("Updated", policy.get("updated", ""))
                
                # Add policy-specific details
                if policy.get("config"):
                    config_str = json.dumps(policy.get("config"), indent=2)
                    details_table.add_row("Configuration", config_str)
                
                console.print(details_table)
                
                # Data sources table
                if policy.get("dataSources"):
                    ds_table = Table(title="Data Sources")
                    ds_table.add_column("ID", style="cyan")
                    ds_table.add_column("Name", style="green")
                    ds_table.add_column("Type", style="blue")
                    
                    for ds in policy.get("dataSources", []):
                        ds_table.add_row(
                            str(ds.get("id", "")),
                            ds.get("name", ""),
                            ds.get("type", ""),
                        )
                    
                    console.print(ds_table)
            else:
                format_output(policy, output, f"Policy {id}")
            
            return policy
        except Exception as e:
            progress.update(task, completed=True)
            raise e


@policy_group.command(name="create", cls=RichCommand)
@common_options
@click.option("--name", required=True, help="Policy name")
@click.option("--type", 
    type=click.Choice(["subscription", "masking", "row_redaction"]),
    required=True,
    help="Policy type")
@click.option("--description", help="Policy description")
@click.option("--config", help="Policy configuration as JSON string")
@click.option("--config-file", type=click.Path(exists=True), help="Path to policy configuration JSON file")
@click.option("--data-source", multiple=True, help="Data source ID to apply the policy to")
@click.option("--no-backup", is_flag=True, help="Skip backup before making changes")
@click.option("--dry-run", is_flag=True, help="Perform a dry run without making changes")
@click.pass_context
@handle_error
def create_policy(ctx, output, no_color, verbose, name, type, description, config, config_file, data_source, no_backup, dry_run):
    """Create a new policy.
    
    Creates a new policy with the specified name, type, description, and configuration.
    The policy can be applied to one or more data sources.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Parse configuration
    policy_config = None
    if config:
        try:
            policy_config = json.loads(config)
        except json.JSONDecodeError as e:
            raise click.BadParameter(f"Invalid JSON in config: {e}")
    elif config_file:
        try:
            with open(config_file, "r") as f:
                policy_config = json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            raise click.BadParameter(f"Failed to load config file: {e}")
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task("[cyan]Creating policy...[/cyan]", total=None)
        
        try:
            # Create policy
            policy = {
                "name": name,
                "type": type,
                "description": description or "",
                "config": policy_config or {},
                "dataSources": [{"id": ds_id} for ds_id in data_source],
            }
            
            if dry_run:
                # Simulate policy creation
                progress.update(task, completed=True)
                console.print(Panel("[yellow]Dry run: Would create policy[/yellow]"))
                console.print(f"Name: {name}")
                console.print(f"Type: {type}")
                console.print(f"Description: {description or ''}")
                if policy_config:
                    console.print("Configuration:")
                    console.print(Syntax(json.dumps(policy_config, indent=2), "json"))
                console.print(f"Data Sources: {', '.join(data_source) if data_source else 'None'}")
                return policy
            
            # Create policy
            result = client.policy_service.create_policy(
                policy=policy,
                backup=not no_backup,
            )
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            if output == "table":
                console.print(f"[green]Policy created successfully[/green]")
                console.print(f"ID: {result.get('id', '')}")
                console.print(f"Name: {result.get('name', '')}")
                console.print(f"Type: {result.get('type', '')}")
            else:
                format_output(result, output, "Created Policy")
            
            return result
        except Exception as e:
            progress.update(task, completed=True)
            raise e


@policy_group.command(name="delete", cls=RichCommand)
@common_options
@click.option("--id", required=True, help="Policy ID")
@click.option("--force", is_flag=True, help="Force deletion without confirmation")
@click.option("--no-backup", is_flag=True, help="Skip backup before making changes")
@click.pass_context
@handle_error
def delete_policy(ctx, output, no_color, verbose, id, force, no_backup):
    """Delete a policy.
    
    Deletes a policy with the specified ID.
    """
    client = ctx.obj["client"]
    
    # Apply console settings
    console.no_color = no_color
    
    # Confirm deletion
    if not force and not click.confirm(f"Are you sure you want to delete policy {id}?"):
        console.print("[yellow]Operation cancelled[/yellow]")
        return
    
    # Show progress indicator
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        task = progress.add_task(f"[cyan]Deleting policy {id}...[/cyan]", total=None)
        
        try:
            # Delete policy
            result = client.policy_service.delete_policy(
                policy_id=id,
                backup=not no_backup,
            )
            
            # Update progress
            progress.update(task, completed=True)
            
            # Format and display output
            console.print(f"[green]Policy {id} deleted successfully[/green]")
            
            return result
        except Exception as e:
            progress.update(task, completed=True)
            raise e


# Add examples to commands
add_command_examples(list_policies, [
    {
        "command": "immuta-toolkit policy list",
        "description": "List all policies",
    },
    {
        "command": "immuta-toolkit policy list --type masking",
        "description": "List only masking policies",
    },
    {
        "command": "immuta-toolkit policy list --filter pii",
        "description": "List policies with 'pii' in the name",
    },
    {
        "command": "immuta-toolkit policy list --output json",
        "description": "List policies and output in JSON format",
    },
])

add_command_examples(get_policy, [
    {
        "command": "immuta-toolkit policy get --id 123",
        "description": "Get details for policy with ID 123",
    },
    {
        "command": "immuta-toolkit policy get --id 123 --output yaml",
        "description": "Get policy details in YAML format",
    },
])

add_command_examples(create_policy, [
    {
        "command": "immuta-toolkit policy create --name 'PII Masking' --type masking --description 'Masks PII data'",
        "description": "Create a new masking policy",
    },
    {
        "command": "immuta-toolkit policy create --name 'Row Redaction' --type row_redaction --config '{\"rules\": [{\"column\": \"status\", \"operator\": \"equals\", \"value\": \"inactive\"}]}'",
        "description": "Create a policy with inline JSON configuration",
    },
    {
        "command": "immuta-toolkit policy create --name 'Subscription Policy' --type subscription --config-file ./policy-config.json",
        "description": "Create a policy using a configuration file",
    },
])

add_command_examples(delete_policy, [
    {
        "command": "immuta-toolkit policy delete --id 123",
        "description": "Delete policy with ID 123 (with confirmation)",
    },
    {
        "command": "immuta-toolkit policy delete --id 123 --force",
        "description": "Delete policy without confirmation",
    },
])
