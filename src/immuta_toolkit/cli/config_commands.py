"""Configuration management commands for the Immuta SRE Toolkit CLI."""

import os
import sys
import json
import yaml
import toml
from typing import Optional, Dict, Any, List

import click
from rich.console import Console
from rich.table import Table
from rich.syntax import Syntax
from rich.panel import Panel

from immuta_toolkit.config import (
    ConfigManager,
    ConfigFormat,
    ConfigProfile,
    ProfileType,
    ProfileManager,
    ConfigValidator,
    ValidationLevel,
    get_config,
    get_config_value,
    set_config_value,
    save_config,
    validate_config,
    load_config,
    get_config_manager,
)
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(name="config")
def config_group():
    """Configuration management commands."""
    pass


@config_group.command(name="view")
@click.option(
    "--format",
    "-f",
    type=click.Choice(["json", "yaml", "toml"]),
    default="yaml",
    help="Output format",
)
@click.option(
    "--key",
    "-k",
    help="Specific configuration key to view (e.g., 'api.base_url')",
)
def view_config(format: str, key: Optional[str]):
    """View the current configuration."""
    try:
        # Get configuration
        if key:
            value = get_config_value(key)
            if value is None:
                console.print(
                    f"[yellow]Key '{key}' not found in configuration[/yellow]"
                )
                return

            # Display the value
            if isinstance(value, (dict, list)):
                if format == "json":
                    formatted = json.dumps(value, indent=2)
                    syntax = Syntax(
                        formatted, "json", theme="monokai", line_numbers=True
                    )
                elif format == "yaml":
                    formatted = yaml.dump(value, default_flow_style=False)
                    syntax = Syntax(
                        formatted, "yaml", theme="monokai", line_numbers=True
                    )
                elif format == "toml":
                    formatted = toml.dumps(value)
                    syntax = Syntax(
                        formatted, "toml", theme="monokai", line_numbers=True
                    )

                console.print(Panel(syntax, title=f"Configuration: {key}"))
            else:
                console.print(f"[cyan]{key}[/cyan] = [green]{value}[/green]")
        else:
            # Get full configuration
            config = get_config()

            # Convert to dictionary if it's a model
            if hasattr(config, "dict"):
                config = config.dict()

            # Format the configuration
            if format == "json":
                formatted = json.dumps(config, indent=2)
                syntax = Syntax(formatted, "json", theme="monokai", line_numbers=True)
            elif format == "yaml":
                formatted = yaml.dump(config, default_flow_style=False)
                syntax = Syntax(formatted, "yaml", theme="monokai", line_numbers=True)
            elif format == "toml":
                formatted = toml.dumps(config)
                syntax = Syntax(formatted, "toml", theme="monokai", line_numbers=True)

            console.print(Panel(syntax, title="Current Configuration"))
    except Exception as e:
        console.print(f"[red]Error viewing configuration: {e}[/red]")
        logger.error(f"Failed to view configuration: {e}", exc_info=True)
        sys.exit(1)


@config_group.command(name="set")
@click.argument("key")
@click.argument("value")
@click.option(
    "--save",
    "-s",
    is_flag=True,
    help="Save the configuration to the default configuration file",
)
@click.option(
    "--file",
    "-f",
    help="Save the configuration to a specific file",
)
def set_config_value_command(key: str, value: str, save: bool, file: Optional[str]):
    """Set a configuration value.

    KEY is the configuration key (e.g., 'api.base_url')
    VALUE is the value to set
    """
    try:
        # Convert value to appropriate type
        if value.lower() in ("true", "yes", "1"):
            value = True
        elif value.lower() in ("false", "no", "0"):
            value = False
        elif value.isdigit():
            value = int(value)
        elif value.replace(".", "", 1).isdigit() and value.count(".") == 1:
            value = float(value)

        # Set the value
        set_config_value(key, value)
        console.print(f"[green]Set {key} = {value}[/green]")

        # Save if requested
        if save or file:
            if file:
                save_config(file)
                console.print(f"[green]Configuration saved to {file}[/green]")
            else:
                # Get the default config file path from the manager
                config_file = get_config_manager().config_file or "config.yaml"
                save_config(config_file)
                console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error setting configuration value: {e}[/red]")
        logger.error(f"Failed to set configuration value: {e}", exc_info=True)
        sys.exit(1)


@config_group.command(name="validate")
@click.option(
    "--level",
    type=click.Choice(["info", "warning", "error"]),
    default="warning",
    help="Minimum validation level to display",
)
def validate_config_command(level: str):
    """Validate the current configuration."""
    try:
        # Validate configuration
        results = validate_config()

        # Filter results by level
        level_map = {
            "info": 0,
            "warning": 1,
            "error": 2,
        }
        min_level = level_map[level]

        filtered_results = [r for r in results if level_map[r.level.value] >= min_level]

        if not filtered_results:
            console.print("[green]Configuration is valid![/green]")
            return

        # Display results
        table = Table(title="Configuration Validation Results")
        table.add_column("Level", style="cyan")
        table.add_column("Key", style="green")
        table.add_column("Message", style="yellow")
        table.add_column("Value")

        for result in filtered_results:
            level_style = {
                "info": "blue",
                "warning": "yellow",
                "error": "red",
            }[result.level.value]

            table.add_row(
                f"[{level_style}]{result.level.value.upper()}[/{level_style}]",
                result.key,
                result.message,
                str(result.value),
            )

        console.print(table)

        # Exit with error if there are errors
        if any(r.level == ValidationLevel.ERROR for r in filtered_results):
            sys.exit(1)
    except Exception as e:
        console.print(f"[red]Error validating configuration: {e}[/red]")
        logger.error(f"Failed to validate configuration: {e}", exc_info=True)
        sys.exit(1)


@config_group.command(name="init")
@click.option(
    "--file",
    "-f",
    default="config.yaml",
    help="Path to the configuration file to create",
)
@click.option(
    "--format",
    type=click.Choice(["json", "yaml", "toml"]),
    default="yaml",
    help="Configuration file format",
)
@click.option(
    "--force",
    is_flag=True,
    help="Overwrite existing file",
)
def init_config(file: str, format: str, force: bool):
    """Initialize a new configuration file with default values."""
    try:
        # Check if file exists
        if os.path.exists(file) and not force:
            console.print(
                f"[yellow]File {file} already exists. Use --force to overwrite.[/yellow]"
            )
            return

        # Create a new configuration manager
        manager = ConfigManager()

        # Load defaults
        manager.load_defaults()

        # Save to file
        format_enum = ConfigFormat(format)
        manager.save(file, format=format_enum)

        console.print(f"[green]Configuration initialized and saved to {file}[/green]")
    except Exception as e:
        console.print(f"[red]Error initializing configuration: {e}[/red]")
        logger.error(f"Failed to initialize configuration: {e}", exc_info=True)
        sys.exit(1)


@config_group.command(name="load")
@click.option(
    "--file",
    "-f",
    help="Path to the configuration file to load",
)
@click.option(
    "--env-prefix",
    default="IMMUTA_",
    help="Prefix for environment variables",
)
@click.option(
    "--secrets-provider",
    type=click.Choice(["azure", "aws", "hashicorp"]),
    help="Secrets provider to use",
)
@click.option(
    "--vault-url",
    help="URL for Azure Key Vault or HashiCorp Vault",
)
@click.option(
    "--secret-name",
    help="Secret name for AWS Secrets Manager",
)
@click.option(
    "--region",
    help="Region for AWS Secrets Manager",
)
def load_config_command(
    file: Optional[str],
    env_prefix: str,
    secrets_provider: Optional[str],
    vault_url: Optional[str],
    secret_name: Optional[str],
    region: Optional[str],
):
    """Load configuration from various sources."""
    try:
        # Prepare kwargs for secrets provider
        kwargs = {}
        if secrets_provider == "azure":
            if not vault_url:
                console.print("[red]Vault URL is required for Azure Key Vault[/red]")
                sys.exit(1)
            kwargs["vault_url"] = vault_url
        elif secrets_provider == "aws":
            if region:
                kwargs["region"] = region
            if secret_name:
                kwargs["secret_name"] = secret_name
        elif secrets_provider == "hashicorp":
            if not vault_url:
                console.print("[red]Vault URL is required for HashiCorp Vault[/red]")
                sys.exit(1)
            kwargs["vault_url"] = vault_url

        # Load configuration
        load_config(
            config_file=file,
            env_prefix=env_prefix,
            secrets_provider=secrets_provider,
            **kwargs,
        )

        console.print("[green]Configuration loaded successfully[/green]")
    except Exception as e:
        console.print(f"[red]Error loading configuration: {e}[/red]")
        logger.error(f"Failed to load configuration: {e}", exc_info=True)
        sys.exit(1)


@config_group.group(name="profile")
def profile_group():
    """Configuration profile management commands."""
    pass


@profile_group.command(name="list")
@click.option(
    "--type",
    type=click.Choice(["environment", "role", "all"]),
    default="all",
    help="Type of profiles to list",
)
@click.option(
    "--enabled-only",
    is_flag=True,
    help="Show only enabled profiles",
)
def list_profiles(type: str, enabled_only: bool):
    """List configuration profiles."""
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Get profiles
        if type == "environment":
            profiles = manager.get_profiles_by_type(ProfileType.ENVIRONMENT)
        elif type == "role":
            profiles = manager.get_profiles_by_type(ProfileType.ROLE)
        else:
            profiles = manager.get_profiles()

        # Filter by enabled status if requested
        if enabled_only:
            profiles = [p for p in profiles if p.enabled]

        # Display profiles
        table = Table(title="Configuration Profiles")
        table.add_column("Name", style="cyan")
        table.add_column("Type", style="green")
        table.add_column("Enabled", style="yellow")
        table.add_column("Description")

        for profile in profiles:
            table.add_row(
                profile.name,
                profile.type.value,
                "[green]Yes[/green]" if profile.enabled else "[red]No[/red]",
                profile.description or "",
            )

        console.print(table)
    except Exception as e:
        console.print(f"[red]Error listing profiles: {e}[/red]")
        logger.error(f"Failed to list profiles: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="view")
@click.argument("name")
@click.option(
    "--format",
    "-f",
    type=click.Choice(["json", "yaml", "toml"]),
    default="yaml",
    help="Output format",
)
def view_profile(name: str, format: str):
    """View a configuration profile.

    NAME is the name of the profile to view
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Get profile
        profile = manager.get_profile(name)
        if not profile:
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        # Convert to dictionary
        profile_dict = profile.to_dict()

        # Format the profile
        if format == "json":
            formatted = json.dumps(profile_dict, indent=2)
            syntax = Syntax(formatted, "json", theme="monokai", line_numbers=True)
        elif format == "yaml":
            formatted = yaml.dump(profile_dict, default_flow_style=False)
            syntax = Syntax(formatted, "yaml", theme="monokai", line_numbers=True)
        elif format == "toml":
            formatted = toml.dumps(profile_dict)
            syntax = Syntax(formatted, "toml", theme="monokai", line_numbers=True)

        console.print(Panel(syntax, title=f"Profile: {name}"))
    except Exception as e:
        console.print(f"[red]Error viewing profile: {e}[/red]")
        logger.error(f"Failed to view profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="create")
@click.argument("name")
@click.option(
    "--type",
    type=click.Choice(["environment", "role"]),
    required=True,
    help="Type of profile",
)
@click.option(
    "--description",
    help="Profile description",
)
@click.option(
    "--enabled/--disabled",
    default=True,
    help="Whether the profile is enabled",
)
@click.option(
    "--file",
    "-f",
    help="Load profile configuration from a file",
)
@click.option(
    "--save",
    is_flag=True,
    help="Save the configuration after creating the profile",
)
def create_profile(
    name: str,
    type: str,
    description: Optional[str],
    enabled: bool,
    file: Optional[str],
    save: bool,
):
    """Create a new configuration profile.

    NAME is the name of the profile to create
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Check if profile already exists
        if manager.get_profile(name):
            console.print(f"[red]Profile '{name}' already exists[/red]")
            sys.exit(1)

        # Load configuration from file if provided
        config = {}
        if file:
            if not os.path.exists(file):
                console.print(f"[red]File '{file}' not found[/red]")
                sys.exit(1)

            try:
                if file.endswith(".json"):
                    with open(file, "r") as f:
                        config = json.load(f)
                elif file.endswith(".yaml") or file.endswith(".yml"):
                    with open(file, "r") as f:
                        config = yaml.safe_load(f)
                elif file.endswith(".toml"):
                    with open(file, "r") as f:
                        config = toml.load(f)
                else:
                    console.print(f"[red]Unsupported file format: {file}[/red]")
                    sys.exit(1)
            except Exception as e:
                console.print(f"[red]Error loading file: {e}[/red]")
                sys.exit(1)

        # Create profile
        profile = ConfigProfile(
            name=name,
            type=ProfileType(type),
            description=description,
            enabled=enabled,
            values=config,
        )

        # Add profile
        manager.add_profile(profile)

        console.print(f"[green]Profile '{name}' created successfully[/green]")

        # Save if requested
        if save:
            # Get the default config file path from the manager
            config_file = get_config_manager().config_file or "config.yaml"
            save_config(config_file)
            console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error creating profile: {e}[/red]")
        logger.error(f"Failed to create profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="update")
@click.argument("name")
@click.option(
    "--description",
    help="Profile description",
)
@click.option(
    "--enabled/--disabled",
    help="Whether the profile is enabled",
)
@click.option(
    "--file",
    "-f",
    help="Load profile configuration from a file",
)
@click.option(
    "--save",
    is_flag=True,
    help="Save the configuration after updating the profile",
)
def update_profile(
    name: str,
    description: Optional[str],
    enabled: Optional[bool],
    file: Optional[str],
    save: bool,
):
    """Update an existing configuration profile.

    NAME is the name of the profile to update
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Get profile
        profile = manager.get_profile(name)
        if not profile:
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        # Update description if provided
        if description is not None:
            profile.description = description

        # Update enabled status if provided
        if enabled is not None:
            if enabled:
                manager.enable_profile(name)
            else:
                manager.disable_profile(name)

        # Load configuration from file if provided
        if file:
            if not os.path.exists(file):
                console.print(f"[red]File '{file}' not found[/red]")
                sys.exit(1)

            try:
                if file.endswith(".json"):
                    with open(file, "r") as f:
                        profile.values = json.load(f)
                elif file.endswith(".yaml") or file.endswith(".yml"):
                    with open(file, "r") as f:
                        profile.values = yaml.safe_load(f)
                elif file.endswith(".toml"):
                    with open(file, "r") as f:
                        profile.values = toml.load(f)
                else:
                    console.print(f"[red]Unsupported file format: {file}[/red]")
                    sys.exit(1)
            except Exception as e:
                console.print(f"[red]Error loading file: {e}[/red]")
                sys.exit(1)

        console.print(f"[green]Profile '{name}' updated successfully[/green]")

        # Save if requested
        if save:
            # Get the default config file path from the manager
            config_file = get_config_manager().config_file or "config.yaml"
            save_config(config_file)
            console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error updating profile: {e}[/red]")
        logger.error(f"Failed to update profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="delete")
@click.argument("name")
@click.option(
    "--save",
    is_flag=True,
    help="Save the configuration after deleting the profile",
)
@click.option(
    "--force",
    is_flag=True,
    help="Force deletion without confirmation",
)
def delete_profile(name: str, save: bool, force: bool):
    """Delete a configuration profile.

    NAME is the name of the profile to delete
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Get profile
        profile = manager.get_profile(name)
        if not profile:
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        # Confirm deletion
        if not force:
            confirm = click.confirm(
                f"Are you sure you want to delete profile '{name}'?"
            )
            if not confirm:
                console.print("[yellow]Operation cancelled[/yellow]")
                return

        # Remove profile
        manager.remove_profile(name)

        console.print(f"[green]Profile '{name}' deleted successfully[/green]")

        # Save if requested
        if save:
            # Get the default config file path from the manager
            config_file = get_config_manager().config_file or "config.yaml"
            save_config(config_file)
            console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error deleting profile: {e}[/red]")
        logger.error(f"Failed to delete profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="enable")
@click.argument("name")
@click.option(
    "--save",
    is_flag=True,
    help="Save the configuration after enabling the profile",
)
def enable_profile(name: str, save: bool):
    """Enable a configuration profile.

    NAME is the name of the profile to enable
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Enable profile
        if not manager.enable_profile(name):
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        console.print(f"[green]Profile '{name}' enabled successfully[/green]")

        # Save if requested
        if save:
            # Get the default config file path from the manager
            config_file = get_config_manager().config_file or "config.yaml"
            save_config(config_file)
            console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error enabling profile: {e}[/red]")
        logger.error(f"Failed to enable profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="disable")
@click.argument("name")
@click.option(
    "--save",
    is_flag=True,
    help="Save the configuration after disabling the profile",
)
def disable_profile(name: str, save: bool):
    """Disable a configuration profile.

    NAME is the name of the profile to disable
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Disable profile
        if not manager.disable_profile(name):
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        console.print(f"[green]Profile '{name}' disabled successfully[/green]")

        # Save if requested
        if save:
            # Get the default config file path from the manager
            config_file = get_config_manager().config_file or "config.yaml"
            save_config(config_file)
            console.print(f"[green]Configuration saved to {config_file}[/green]")
    except Exception as e:
        console.print(f"[red]Error disabling profile: {e}[/red]")
        logger.error(f"Failed to disable profile: {e}", exc_info=True)
        sys.exit(1)


@profile_group.command(name="apply")
@click.argument("name")
def apply_profile(name: str):
    """Apply a configuration profile.

    NAME is the name of the profile to apply
    """
    try:
        # Get profile manager
        manager = get_config_manager().profile_manager

        # Apply profile
        if not manager.apply_profile(name):
            console.print(f"[red]Profile '{name}' not found[/red]")
            sys.exit(1)

        console.print(f"[green]Profile '{name}' applied successfully[/green]")
    except Exception as e:
        console.print(f"[red]Error applying profile: {e}[/red]")
        logger.error(f"Failed to apply profile: {e}", exc_info=True)
        sys.exit(1)
