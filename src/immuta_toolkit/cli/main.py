"""Main CLI entry point for the Immuta SRE Toolkit."""

import os
import sys
import argparse
import json
from typing import Optional, Dict, Any, List

import click
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.syntax import Syntax

from immuta_toolkit.config import load_config, get_config_value, get_config_manager
from immuta_toolkit.config.profiles import ProfileManager

from immuta_toolkit.cli.backup_commands import backup_group, restore_group
from immuta_toolkit.cli.user_commands import user_group
from immuta_toolkit.cli.data_source_commands import data_source_group
from immuta_toolkit.cli.api_commands import api_group
from immuta_toolkit.cli.batch_commands import batch_group
from immuta_toolkit.cli.config_commands import config_group
from immuta_toolkit.cli.interactive import interactive
from immuta_toolkit.cli.wizard_commands import wizard_group
from immuta_toolkit.cli.project_commands import project_group
from immuta_toolkit.cli.policy_commands import policy_group
from immuta_toolkit.cli.commands import common_options, format_output, OutputFormat
from immuta_toolkit.cli.help_formatter import (
    RichGroup,
    RichCommand,
    add_command_examples,
)
from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.utils.logging import get_logger, set_log_level

logger = get_logger(__name__)
console = Console()


@click.group(cls=RichGroup)
@click.version_option()
@click.option(
    "--local",
    is_flag=True,
    help="Run in local mode (no API calls)",
)
@click.option(
    "--web-fallback/--no-web-fallback",
    default=True,
    help="Use web automation as fallback when API operations fail",
)
@click.option(
    "--headless/--no-headless",
    default=True,
    help="Run browser in headless mode for web automation",
)
@click.option(
    "--config",
    "-c",
    help="Path to configuration file",
)
@click.option(
    "--env-prefix",
    default="IMMUTA_",
    help="Prefix for environment variables",
)
@click.option(
    "--secrets-provider",
    type=click.Choice(["azure", "aws", "hashicorp"]),
    help="Secrets provider to use",
)
@click.option(
    "--vault-url",
    help="URL for Azure Key Vault or HashiCorp Vault",
)
@click.option(
    "--secret-name",
    help="Secret name for AWS Secrets Manager",
)
@click.option(
    "--region",
    help="Region for AWS Secrets Manager",
)
@click.option(
    "--profile",
    help="Configuration profile to use",
)
@click.option(
    "--log-level",
    type=click.Choice(
        ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"], case_sensitive=False
    ),
    default="INFO",
    help="Set logging level",
)
@click.option(
    "--output",
    "-o",
    type=click.Choice([f.value for f in OutputFormat]),
    default=OutputFormat.TABLE.value,
    help="Default output format for commands",
)
@click.option(
    "--no-color",
    is_flag=True,
    help="Disable colored output",
)
@click.option(
    "--verbose",
    "-v",
    is_flag=True,
    help="Enable verbose output",
)
@click.pass_context
def cli(
    ctx,
    local,
    web_fallback,
    headless,
    config,
    env_prefix,
    secrets_provider,
    vault_url,
    secret_name,
    region,
    profile,
    log_level,
    output,
    no_color,
    verbose,
):
    """Immuta SRE Toolkit CLI.

    A comprehensive command-line interface for managing Immuta instances.
    Provides commands for user management, data source management, policy management,
    backup and restore, and more.
    """
    # Set up logging
    set_log_level(log_level.upper())

    # Configure console
    if no_color:
        console.no_color = True

    # Store global options in context
    ctx.ensure_object(dict)
    ctx.obj["output_format"] = output
    ctx.obj["verbose"] = verbose

    # Show welcome message if verbose
    if verbose:
        console.print(
            Panel.fit(
                "[bold green]Immuta SRE Toolkit CLI[/bold green]\n\n"
                "A comprehensive command-line interface for managing Immuta instances.",
                title="Welcome",
                subtitle="v1.0.0",
            )
        )

    # Load configuration from various sources
    try:
        # Prepare kwargs for secrets provider
        kwargs = {}
        if secrets_provider == "azure":
            if vault_url:
                kwargs["vault_url"] = vault_url
        elif secrets_provider == "aws":
            if region:
                kwargs["region"] = region
            if secret_name:
                kwargs["secret_name"] = secret_name
        elif secrets_provider == "hashicorp":
            if vault_url:
                kwargs["vault_url"] = vault_url

        # Load configuration
        load_config(
            config_file=config,
            env_prefix=env_prefix,
            secrets_provider=secrets_provider,
            **kwargs,
        )

        # Apply profile if provided
        if profile:
            config_manager = get_config_manager()
            profile_manager = config_manager.profile_manager
            if profile_manager:
                if not profile_manager.apply_profile(profile):
                    logger.warning(f"Profile '{profile}' not found")
                    if verbose:
                        console.print(
                            f"[yellow]Warning: Profile '{profile}' not found[/yellow]"
                        )
            else:
                logger.warning("Profile manager not available")
                if verbose:
                    console.print(
                        "[yellow]Warning: Profile manager not available[/yellow]"
                    )

        if verbose:
            console.print("[green]Configuration loaded successfully[/green]")
        logger.info("Configuration loaded successfully")
    except Exception as e:
        logger.warning(f"Failed to load configuration: {e}")
        if verbose:
            console.print(
                f"[yellow]Warning: Failed to load configuration: {e}[/yellow]"
            )

    # Get credentials from configuration or environment variables
    api_key = get_config_value("api.key") or os.getenv("IMMUTA_API_KEY")
    base_url = get_config_value("api.base_url") or os.getenv("IMMUTA_BASE_URL")
    username = get_config_value("api.username") or os.getenv("IMMUTA_USERNAME")
    password = get_config_value("api.password") or os.getenv("IMMUTA_PASSWORD")

    # Override with local mode if specified
    if local:
        api_key = "mock_key"
        base_url = "https://example.com"
        if verbose:
            console.print("[yellow]Running in local mode (no API calls)[/yellow]")

    # Initialize client
    try:
        client = ImmutaHybridClient(
            api_key=api_key,
            base_url=base_url,
            username=username,
            password=password,
            use_web_fallback=web_fallback and not local,
            headless=headless,
            is_local=local,
        )

        # Store client in context
        ctx.obj["client"] = client

        # Store connection info in context
        ctx.obj["connection_info"] = {
            "base_url": base_url,
            "api_key": api_key,
            "username": username,
            "web_fallback": web_fallback and not local,
            "headless": headless,
            "local_mode": local,
        }

        if verbose:
            console.print("[green]Client initialized successfully[/green]")
        logger.info("Client initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize client: {e}")
        console.print(f"[red]Error: Failed to initialize client: {e}[/red]")
        sys.exit(1)


# Add command groups
cli.add_command(backup_group)
cli.add_command(restore_group)
cli.add_command(user_group)
cli.add_command(data_source_group)
cli.add_command(project_group)
cli.add_command(policy_group)
cli.add_command(api_group)
cli.add_command(batch_group)
cli.add_command(config_group)
cli.add_command(interactive)
cli.add_command(wizard_group)

# Add command aliases
user_group.aliases = ["users"]
data_source_group.aliases = ["datasources", "ds"]
project_group.aliases = ["projects"]
policy_group.aliases = ["policies"]
backup_group.aliases = ["backups"]
restore_group.aliases = ["restores"]
config_group.aliases = ["configs", "configuration"]
interactive.aliases = ["shell", "repl"]
wizard_group.aliases = ["wizards", "guide"]


def main():
    """Main entry point for the CLI."""
    ctx = None
    try:
        # Create context and run CLI
        ctx = click.Context(cli)
        ctx.ensure_object(dict)
        cli(ctx)
    except click.Abort:
        # Handle keyboard interrupt (Ctrl+C)
        console.print("\n[yellow]Operation aborted by user[/yellow]")
        sys.exit(130)  # Standard exit code for SIGINT
    except click.UsageError as e:
        # Handle usage errors (invalid commands, options, etc.)
        console.print(f"[red]Error: {e}[/red]")
        console.print("\nRun [bold]immuta-toolkit --help[/bold] for usage information.")
        sys.exit(2)
    except Exception as e:
        # Get error context
        context = {
            "config_file": os.environ.get("IMMUTA_CONFIG_FILE"),
            "base_url": os.environ.get("IMMUTA_BASE_URL"),
            "timeout": int(os.environ.get("IMMUTA_API_TIMEOUT", "30")),
            "retry_count": 0,
            "headless": os.environ.get("IMMUTA_HEADLESS", "true").lower() == "true",
        }

        # Handle error
        from immuta_toolkit.utils.error_handler import handle_error

        if not handle_error(e, context):
            console.print(f"[red]Error: {e}[/red]")
            logger.error(f"Unhandled exception: {e}", exc_info=True)

            # Show traceback in verbose mode
            if ctx and ctx.obj and ctx.obj.get("verbose"):
                import traceback

                console.print("\n[bold red]Traceback:[/bold red]")
                console.print(Syntax(traceback.format_exc(), "python", theme="monokai"))

        sys.exit(1)
    finally:
        # Close client if it exists
        if ctx and ctx.obj and "client" in ctx.obj:
            client = ctx.obj["client"]
            if hasattr(client, "close"):
                try:
                    client.close()
                except Exception as e:
                    logger.warning(f"Failed to close client: {e}")

        # Show goodbye message
        if ctx and ctx.obj and ctx.obj.get("verbose"):
            console.print("[green]Goodbye![/green]")


if __name__ == "__main__":
    main()
