"""Automation commands for the Immuta SRE Toolkit CLI."""

import json
import asyncio
from typing import Dict, Any, List, Optional

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn

from immuta_toolkit.cli.commands import common_options, format_output, OutputFormat
from immuta_toolkit.cli.help_formatter import RichGroup, RichCommand
from immuta_toolkit.operations.engine import OperationPlan, ExecutionMode
from immuta_toolkit.hybrid_client import FallbackStrategy
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.group(cls=RichGroup, name="automation")
def automation_group():
    """Enhanced automation commands for Immuta operations.

    This group provides commands for executing complex automation workflows,
    managing operation plans, monitoring execution metrics, and configuring
    the hybrid client behavior.
    """
    pass


@automation_group.command(cls=RichCommand, name="execute-plan")
@click.argument("plan_file", type=click.Path(exists=True))
@click.option(
    "--mode",
    type=click.Choice([mode.value for mode in ExecutionMode]),
    default=ExecutionMode.SEQUENTIAL.value,
    help="Execution mode for operations",
)
@click.option(
    "--max-concurrent",
    type=int,
    default=5,
    help="Maximum number of concurrent operations (for parallel mode)",
)
@click.option(
    "--fail-fast",
    is_flag=True,
    help="Stop execution on first failure",
)
@click.option(
    "--retry-failed",
    is_flag=True,
    help="Retry failed operations",
)
@click.option(
    "--no-validation",
    is_flag=True,
    help="Disable validation",
)
@click.option(
    "--no-reporting",
    is_flag=True,
    help="Disable reporting",
)
@click.option(
    "--output-report",
    type=click.Path(),
    help="Path to save execution report",
)
@common_options
@click.pass_context
def execute_plan(
    ctx,
    plan_file,
    mode,
    max_concurrent,
    fail_fast,
    retry_failed,
    no_validation,
    no_reporting,
    output_report,
    output,
    verbose,
):
    """Execute an operation plan from a JSON file.

    The plan file should contain a JSON object with the following structure:

    {
        "operations": [
            {
                "operation": "get_user",
                "params": {"user_id": 123},
                "metadata": {"description": "Get user info"}
            }
        ],
        "metadata": {
            "title": "User Management Plan",
            "description": "Manage users in Immuta"
        }
    }
    """
    client = ctx.obj["client"]

    try:
        # Load plan from file
        with open(plan_file, 'r') as f:
            plan_data = json.load(f)

        # Create operation plan
        plan = OperationPlan(
            operations=plan_data.get("operations", []),
            mode=ExecutionMode(mode),
            max_concurrent=max_concurrent,
            fail_fast=fail_fast,
            retry_failed=retry_failed,
            validation_enabled=not no_validation,
            reporting_enabled=not no_reporting,
            metadata=plan_data.get("metadata", {}),
        )

        if verbose:
            console.print(f"[green]Loaded plan with {len(plan.operations)} operations[/green]")

        # Execute plan with progress tracking
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console,
        ) as progress:
            task = progress.add_task("Executing operations...", total=len(plan.operations))

            def progress_callback(completed: int, total: int, message: str):
                progress.update(task, completed=completed, description=message)

            # Set progress callback on operation engine
            if hasattr(client, 'operation_engine'):
                client.operation_engine.progress_callback = progress_callback

            # Execute the plan
            context = client.operation_engine.execute_plan(plan)

        # Display results
        successful = sum(1 for r in context.results if r.status.value == "success")
        failed = len(context.results) - successful

        console.print(f"\n[green]Execution completed![/green]")
        console.print(f"Total operations: {len(context.results)}")
        console.print(f"Successful: {successful}")
        console.print(f"Failed: {failed}")

        if context.failed_operations:
            console.print(f"Failed operations: {len(context.failed_operations)}")

        # Save report if requested
        if output_report and context.report:
            with open(output_report, 'w') as f:
                json.dump(context.report.to_dict(), f, indent=2)
            console.print(f"[green]Report saved to: {output_report}[/green]")

        # Format output
        if output == OutputFormat.JSON.value:
            result = {
                "summary": {
                    "total": len(context.results),
                    "successful": successful,
                    "failed": failed,
                    "failed_operations": len(context.failed_operations),
                },
                "results": [r.to_dict() for r in context.results],
            }
            format_output(result, OutputFormat.JSON, console)
        else:
            # Create table for results
            table = Table(title="Operation Results")
            table.add_column("Operation", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Time (s)", style="yellow")
            table.add_column("Method", style="blue")
            table.add_column("Attempts", style="magenta")

            for result in context.results:
                status_color = "green" if result.status.value == "success" else "red"
                method = "API" if result.api_used else "Web" if result.web_used else "Unknown"

                table.add_row(
                    result.operation_name or "Unknown",
                    f"[{status_color}]{result.status.value}[/{status_color}]",
                    f"{result.execution_time:.2f}",
                    method,
                    str(result.attempts),
                )

            console.print(table)

    except Exception as e:
        logger.error(f"Failed to execute plan: {e}")
        console.print(f"[red]Error: {e}[/red]")
        raise click.ClickException(str(e))


@automation_group.command(cls=RichCommand, name="metrics")
@click.option(
    "--operation",
    help="Show metrics for specific operation",
)
@click.option(
    "--export",
    type=click.Path(),
    help="Export metrics to file",
)
@click.option(
    "--format",
    "export_format",
    type=click.Choice(["json", "csv"]),
    default="json",
    help="Export format",
)
@click.option(
    "--clear",
    is_flag=True,
    help="Clear collected metrics",
)
@common_options
@click.pass_context
def metrics(ctx, operation, export, export_format, clear, output, verbose):
    """Display or export operation metrics.

    Shows performance metrics for operations executed by the hybrid client,
    including success rates, execution times, and fallback usage.
    """
    client = ctx.obj["client"]

    try:
        if clear:
            if hasattr(client, 'metrics_collector') and client.metrics_collector:
                client.metrics_collector.clear_metrics()
                console.print("[green]Metrics cleared successfully[/green]")
            else:
                console.print("[yellow]No metrics collector available[/yellow]")
            return

        # Get metrics
        if hasattr(client, 'metrics_collector') and client.metrics_collector:
            if operation:
                metrics_data = client.metrics_collector.get_operation_stats(operation)
            else:
                metrics_data = client.metrics_collector.get_metrics()
        else:
            console.print("[yellow]No metrics collector available[/yellow]")
            return

        # Export if requested
        if export:
            if hasattr(client, 'metrics_collector') and client.metrics_collector:
                exported_data = client.metrics_collector.export_metrics(export_format)
                with open(export, 'w') as f:
                    f.write(exported_data)
                console.print(f"[green]Metrics exported to: {export}[/green]")
            return

        # Display metrics
        if output == OutputFormat.JSON.value:
            format_output(metrics_data, OutputFormat.JSON, console)
        else:
            if operation:
                # Show specific operation metrics
                if "error" in metrics_data:
                    console.print(f"[red]{metrics_data['error']}[/red]")
                else:
                    panel = Panel.fit(
                        f"Operation: {metrics_data['operation_name']}\n"
                        f"Total Executions: {metrics_data['total_executions']}\n"
                        f"Success Rate: {metrics_data['success_rate']:.2%}\n"
                        f"Average Time: {metrics_data['average_time']:.2f}s\n"
                        f"Min Time: {metrics_data['min_time']:.2f}s\n"
                        f"Max Time: {metrics_data['max_time']:.2f}s\n"
                        f"Fallback Usage: {metrics_data['fallback_usage']}",
                        title=f"Metrics for {operation}",
                    )
                    console.print(panel)
            else:
                # Show summary metrics
                summary = metrics_data.get("summary", {})
                panel = Panel.fit(
                    f"Total Operations: {summary.get('total_operations', 0)}\n"
                    f"Success Rate: {summary.get('success_rate', 0):.2%}\n"
                    f"Average Time: {summary.get('average_time', 0):.2f}s\n"
                    f"API Operations: {summary.get('api_operations', 0)}\n"
                    f"Web Operations: {summary.get('web_operations', 0)}\n"
                    f"Fallback Rate: {summary.get('fallback_rate', 0):.2%}\n"
                    f"Uptime: {summary.get('uptime', 0):.2f}s",
                    title="Operation Metrics Summary",
                )
                console.print(panel)

    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        console.print(f"[red]Error: {e}[/red]")
        raise click.ClickException(str(e))


@automation_group.command(cls=RichCommand, name="status")
@common_options
@click.pass_context
def status(ctx, output, verbose):
    """Show the status of the hybrid client.

    Displays information about the client configuration, available services,
    and current operational status.
    """
    client = ctx.obj["client"]

    try:
        # Get client status
        status_info = client.get_client_status()

        if output == OutputFormat.JSON.value:
            format_output(status_info, OutputFormat.JSON, console)
        else:
            # Create status panel
            status_text = []
            status_text.append(f"API Client: {'✓' if status_info['api_client_available'] else '✗'}")
            status_text.append(f"Web Client: {'✓' if status_info['web_client_available'] else '✗'}")
            status_text.append(f"Fallback Strategy: {status_info['fallback_strategy']}")
            status_text.append(f"Validation: {'Enabled' if status_info['validation_enabled'] else 'Disabled'}")
            status_text.append(f"Reporting: {'Enabled' if status_info['reporting_enabled'] else 'Disabled'}")
            status_text.append(f"Metrics: {'Enabled' if status_info['metrics_enabled'] else 'Disabled'}")
            status_text.append(f"Local Mode: {'Yes' if status_info['is_local'] else 'No'}")
            status_text.append(f"Max Retries: {status_info['max_retries']}")
            status_text.append(f"Retry Delay: {status_info['retry_delay']}s")

            if "api_circuit_breaker" in status_info:
                cb_state = status_info["api_circuit_breaker"]
                status_text.append(f"Circuit Breaker: {cb_state}")

            panel = Panel.fit(
                "\n".join(status_text),
                title="Hybrid Client Status",
            )
            console.print(panel)

    except Exception as e:
        logger.error(f"Failed to get status: {e}")
        console.print(f"[red]Error: {e}[/red]")
        raise click.ClickException(str(e))


@automation_group.command(cls=RichCommand, name="configure")
@click.option(
    "--fallback-strategy",
    type=click.Choice([strategy.value for strategy in FallbackStrategy]),
    help="Set fallback strategy",
)
@click.option(
    "--enable-validation/--disable-validation",
    default=None,
    help="Enable or disable validation",
)
@click.option(
    "--enable-reporting/--disable-reporting",
    default=None,
    help="Enable or disable reporting",
)
@click.option(
    "--enable-metrics/--disable-metrics",
    default=None,
    help="Enable or disable metrics collection",
)
@common_options
@click.pass_context
def configure(
    ctx,
    fallback_strategy,
    enable_validation,
    enable_reporting,
    enable_metrics,
    output,
    verbose,
):
    """Configure the hybrid client settings.

    Allows runtime configuration of the hybrid client behavior,
    including fallback strategy and feature toggles.
    """
    client = ctx.obj["client"]

    try:
        changes = []

        # Update fallback strategy
        if fallback_strategy:
            client.set_fallback_strategy(FallbackStrategy(fallback_strategy))
            changes.append(f"Fallback strategy set to: {fallback_strategy}")

        # Update validation setting
        if enable_validation is not None:
            client.enable_validation(enable_validation)
            changes.append(f"Validation {'enabled' if enable_validation else 'disabled'}")

        # Update reporting setting
        if enable_reporting is not None:
            client.enable_reporting(enable_reporting)
            changes.append(f"Reporting {'enabled' if enable_reporting else 'disabled'}")

        # Update metrics setting
        if enable_metrics is not None:
            client.enable_metrics(enable_metrics)
            changes.append(f"Metrics {'enabled' if enable_metrics else 'disabled'}")

        if changes:
            console.print("[green]Configuration updated:[/green]")
            for change in changes:
                console.print(f"  • {change}")
        else:
            console.print("[yellow]No configuration changes specified[/yellow]")
            console.print("Use --help to see available options")

    except Exception as e:
        logger.error(f"Failed to configure client: {e}")
        console.print(f"[red]Error: {e}[/red]")
        raise click.ClickException(str(e))
