"""Command structure for the Immuta SRE Toolkit CLI."""

import os
import sys
import json
import yaml
from typing import Dict, List, Optional, Any, Callable, Union
from enum import Enum

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.syntax import Syntax
from rich.markdown import Markdown

from immuta_toolkit.utils.logging import get_logger
from immuta_toolkit.hybrid_client import ImmutaHybridClient

logger = get_logger(__name__)
console = Console()


class OutputFormat(str, Enum):
    """Output format for CLI commands."""

    TABLE = "table"
    JSON = "json"
    YAML = "yaml"
    CSV = "csv"


def common_options(function):
    """Add common options to a command."""
    function = click.option(
        "--output",
        "-o",
        type=click.Choice([f.value for f in OutputFormat]),
        default=OutputFormat.TABLE.value,
        help="Output format",
    )(function)
    function = click.option(
        "--no-color",
        is_flag=True,
        help="Disable colored output",
    )(function)
    function = click.option(
        "--verbose",
        "-v",
        is_flag=True,
        help="Enable verbose output",
    )(function)
    return function


def format_output(data: Any, format: str, title: Optional[str] = None) -> None:
    """Format and print output based on the specified format.

    Args:
        data: Data to format and print.
        format: Output format (table, json, yaml, csv).
        title: Optional title for the output.
    """
    if format == OutputFormat.JSON.value:
        # JSON output
        json_str = json.dumps(data, indent=2)
        console.print(Syntax(json_str, "json", theme="monokai"))
    elif format == OutputFormat.YAML.value:
        # YAML output
        yaml_str = yaml.dump(data, default_flow_style=False)
        console.print(Syntax(yaml_str, "yaml", theme="monokai"))
    elif format == OutputFormat.CSV.value:
        # CSV output
        if isinstance(data, list) and data and isinstance(data[0], dict):
            # Get headers from first item
            headers = list(data[0].keys())
            
            # Print headers
            console.print(",".join(headers))
            
            # Print rows
            for item in data:
                row = [str(item.get(h, "")) for h in headers]
                console.print(",".join(row))
        else:
            console.print("[yellow]Warning: Data is not suitable for CSV format[/yellow]")
            console.print(data)
    else:
        # Default to table output
        if isinstance(data, list) and data:
            # Create table for list of items
            table = Table(title=title)
            
            # Add columns based on first item
            if data and isinstance(data[0], dict):
                for key in data[0].keys():
                    table.add_column(key.capitalize())
                
                # Add rows
                for item in data:
                    row = [str(item.get(key, "")) for key in data[0].keys()]
                    table.add_row(*row)
                
                console.print(table)
            else:
                # Simple list
                table = Table(title=title)
                table.add_column("Value")
                
                for item in data:
                    table.add_row(str(item))
                
                console.print(table)
        elif isinstance(data, dict):
            # Create table for dictionary
            table = Table(title=title)
            table.add_column("Key")
            table.add_column("Value")
            
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, indent=2)
                table.add_row(key, str(value))
            
            console.print(table)
        else:
            # Just print the data
            console.print(data)


def with_progress(message: str) -> Callable:
    """Decorator to add a progress indicator to a function.

    Args:
        message: Message to display during progress.

    Returns:
        Decorated function.
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
            ) as progress:
                task = progress.add_task(f"[cyan]{message}[/cyan]", total=None)
                
                try:
                    result = func(*args, **kwargs)
                    progress.update(task, completed=True)
                    return result
                except Exception as e:
                    progress.update(task, completed=True)
                    raise e
        
        return wrapper
    
    return decorator


def handle_error(func):
    """Decorator to handle errors in CLI commands."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            logger.error(f"Error in command: {e}", exc_info=True)
            sys.exit(1)
    
    return wrapper


def confirm_action(message: str = "Are you sure you want to proceed?") -> bool:
    """Confirm an action with the user.

    Args:
        message: Confirmation message.

    Returns:
        True if confirmed, False otherwise.
    """
    return click.confirm(message)


def create_command_group(name: str, help_text: str) -> click.Group:
    """Create a command group.

    Args:
        name: Command group name.
        help_text: Help text for the command group.

    Returns:
        Command group.
    """
    @click.group(name=name)
    def group():
        """Command group."""
        pass
    
    group.help = help_text
    return group


def add_aliases(command, aliases: List[str]) -> None:
    """Add aliases to a command.

    Args:
        command: Command to add aliases to.
        aliases: List of aliases.
    """
    for alias in aliases:
        command.aliases.append(alias)


def get_client_from_context(ctx) -> ImmutaHybridClient:
    """Get the Immuta client from the Click context.

    Args:
        ctx: Click context.

    Returns:
        Immuta client.

    Raises:
        ValueError: If the client is not found in the context.
    """
    if not ctx.obj or "client" not in ctx.obj:
        raise ValueError("Client not found in context")
    
    return ctx.obj["client"]
