"""SQLite storage provider for Immuta automation."""

import os
import json
import sqlite3
from typing import Dict, List, Optional, Any, Union

from immuta_toolkit.storage.base import StorageProvider
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class SQLiteStorageProvider(StorageProvider):
    """SQLite storage provider.
    
    This class provides a storage provider that stores data in a SQLite database.
    
    Attributes:
        name: Storage provider name.
        db_path: Path to the SQLite database file.
        table_name: Name of the table to store data in.
        create_table: Whether to create the table if it doesn't exist.
    """

    def __init__(
        self,
        db_path: str,
        table_name: str = "storage",
        create_table: bool = True,
        name: str = "sqlite",
    ):
        """Initialize the SQLite storage provider.
        
        Args:
            db_path: Path to the SQLite database file.
            table_name: Name of the table to store data in.
            create_table: Whether to create the table if it doesn't exist.
            name: Storage provider name.
        """
        super().__init__(name)
        self.db_path = os.path.abspath(db_path)
        self.table_name = table_name
        self.create_table = create_table
        
        # Create directory if it doesn't exist
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
        
        # Create table if it doesn't exist
        if create_table:
            self._create_table()
        
        logger.info(f"Initialized SQLite storage provider with database: {self.db_path}")
    
    def _create_table(self) -> None:
        """Create the storage table if it doesn't exist."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    f"""
                    CREATE TABLE IF NOT EXISTS {self.table_name} (
                        key TEXT PRIMARY KEY,
                        data TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """
                )
                conn.commit()
            
            logger.debug(f"Created table: {self.table_name}")
        except Exception as e:
            logger.error(f"Failed to create table: {e}")
    
    def store(self, key: str, data: Any) -> bool:
        """Store data in the database.
        
        Args:
            key: Key to store the data under.
            data: Data to store.
            
        Returns:
            True if the data was stored successfully, False otherwise.
        """
        try:
            # Convert data to JSON string
            data_json = json.dumps(data)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Check if key exists
                cursor.execute(
                    f"SELECT COUNT(*) FROM {self.table_name} WHERE key = ?",
                    (key,),
                )
                count = cursor.fetchone()[0]
                
                if count > 0:
                    # Update existing record
                    cursor.execute(
                        f"""
                        UPDATE {self.table_name}
                        SET data = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE key = ?
                        """,
                        (data_json, key),
                    )
                else:
                    # Insert new record
                    cursor.execute(
                        f"""
                        INSERT INTO {self.table_name} (key, data)
                        VALUES (?, ?)
                        """,
                        (key, data_json),
                    )
                
                conn.commit()
            
            logger.debug(f"Stored data for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to store data for key {key}: {e}")
            return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve data from the database.
        
        Args:
            key: Key to retrieve the data for.
            
        Returns:
            Retrieved data if found, None otherwise.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    f"SELECT data FROM {self.table_name} WHERE key = ?",
                    (key,),
                )
                result = cursor.fetchone()
            
            if not result:
                logger.debug(f"No data found for key: {key}")
                return None
            
            # Parse JSON string
            data = json.loads(result[0])
            
            logger.debug(f"Retrieved data for key: {key}")
            return data
        except Exception as e:
            logger.error(f"Failed to retrieve data for key {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """Delete data from the database.
        
        Args:
            key: Key to delete the data for.
            
        Returns:
            True if the data was deleted successfully, False otherwise.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    f"DELETE FROM {self.table_name} WHERE key = ?",
                    (key,),
                )
                conn.commit()
            
            logger.debug(f"Deleted data for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete data for key {key}: {e}")
            return False
    
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """List keys from the database.
        
        Args:
            prefix: Prefix to filter keys by.
            
        Returns:
            List of keys.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if prefix:
                    cursor.execute(
                        f"SELECT key FROM {self.table_name} WHERE key LIKE ?",
                        (f"{prefix}%",),
                    )
                else:
                    cursor.execute(f"SELECT key FROM {self.table_name}")
                
                results = cursor.fetchall()
            
            # Extract keys from results
            keys = [result[0] for result in results]
            
            return keys
        except Exception as e:
            logger.error(f"Failed to list keys: {e}")
            return []
    
    def exists(self, key: str) -> bool:
        """Check if a key exists in the database.
        
        Args:
            key: Key to check.
            
        Returns:
            True if the key exists, False otherwise.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    f"SELECT COUNT(*) FROM {self.table_name} WHERE key = ?",
                    (key,),
                )
                count = cursor.fetchone()[0]
            
            return count > 0
        except Exception as e:
            logger.error(f"Failed to check if key {key} exists: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all data from the database.
        
        Returns:
            True if all data was cleared successfully, False otherwise.
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"DELETE FROM {self.table_name}")
                conn.commit()
            
            logger.debug("Cleared all data")
            return True
        except Exception as e:
            logger.error(f"Failed to clear data: {e}")
            return False
