"""File storage provider for Immuta automation."""

import os
import json
import shutil
from typing import Dict, List, Optional, Any, Union

from immuta_toolkit.storage.base import StorageProvider
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class FileStorageProvider(StorageProvider):
    """File storage provider.
    
    This class provides a storage provider that stores data in files.
    
    Attributes:
        name: Storage provider name.
        base_dir: Base directory for storing files.
        create_dirs: Whether to create directories if they don't exist.
    """

    def __init__(self, base_dir: str, create_dirs: bool = True, name: str = "file"):
        """Initialize the file storage provider.
        
        Args:
            base_dir: Base directory for storing files.
            create_dirs: Whether to create directories if they don't exist.
            name: Storage provider name.
        """
        super().__init__(name)
        self.base_dir = os.path.abspath(base_dir)
        self.create_dirs = create_dirs
        
        if create_dirs and not os.path.exists(self.base_dir):
            os.makedirs(self.base_dir)
            logger.info(f"Created directory: {self.base_dir}")
        
        logger.info(f"Initialized file storage provider with base directory: {self.base_dir}")
    
    def _get_file_path(self, key: str) -> str:
        """Get the file path for a key.
        
        Args:
            key: Key to get the file path for.
            
        Returns:
            File path.
        """
        # Replace any characters that are not allowed in file names
        safe_key = key.replace("/", "_").replace("\\", "_")
        return os.path.join(self.base_dir, f"{safe_key}.json")
    
    def store(self, key: str, data: Any) -> bool:
        """Store data in a file.
        
        Args:
            key: Key to store the data under.
            data: Data to store.
            
        Returns:
            True if the data was stored successfully, False otherwise.
        """
        try:
            file_path = self._get_file_path(key)
            
            # Create directory if it doesn't exist
            dir_path = os.path.dirname(file_path)
            if self.create_dirs and not os.path.exists(dir_path):
                os.makedirs(dir_path)
            
            # Write data to file
            with open(file_path, "w") as f:
                json.dump(data, f, indent=2)
            
            logger.debug(f"Stored data for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to store data for key {key}: {e}")
            return False
    
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve data from a file.
        
        Args:
            key: Key to retrieve the data for.
            
        Returns:
            Retrieved data if found, None otherwise.
        """
        try:
            file_path = self._get_file_path(key)
            
            if not os.path.exists(file_path):
                logger.debug(f"No data found for key: {key}")
                return None
            
            # Read data from file
            with open(file_path, "r") as f:
                data = json.load(f)
            
            logger.debug(f"Retrieved data for key: {key}")
            return data
        except Exception as e:
            logger.error(f"Failed to retrieve data for key {key}: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """Delete data from a file.
        
        Args:
            key: Key to delete the data for.
            
        Returns:
            True if the data was deleted successfully, False otherwise.
        """
        try:
            file_path = self._get_file_path(key)
            
            if not os.path.exists(file_path):
                logger.debug(f"No data found for key: {key}")
                return False
            
            # Delete file
            os.remove(file_path)
            
            logger.debug(f"Deleted data for key: {key}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete data for key {key}: {e}")
            return False
    
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """List keys.
        
        Args:
            prefix: Prefix to filter keys by.
            
        Returns:
            List of keys.
        """
        try:
            if not os.path.exists(self.base_dir):
                return []
            
            # Get all JSON files in the base directory
            files = [f for f in os.listdir(self.base_dir) if f.endswith(".json")]
            
            # Extract keys from file names
            keys = [os.path.splitext(f)[0] for f in files]
            
            # Filter by prefix if provided
            if prefix:
                keys = [k for k in keys if k.startswith(prefix)]
            
            return keys
        except Exception as e:
            logger.error(f"Failed to list keys: {e}")
            return []
    
    def exists(self, key: str) -> bool:
        """Check if a key exists.
        
        Args:
            key: Key to check.
            
        Returns:
            True if the key exists, False otherwise.
        """
        try:
            file_path = self._get_file_path(key)
            return os.path.exists(file_path)
        except Exception as e:
            logger.error(f"Failed to check if key {key} exists: {e}")
            return False
    
    def clear(self) -> bool:
        """Clear all data.
        
        Returns:
            True if all data was cleared successfully, False otherwise.
        """
        try:
            if not os.path.exists(self.base_dir):
                return True
            
            # Delete all files in the base directory
            for file_name in os.listdir(self.base_dir):
                file_path = os.path.join(self.base_dir, file_name)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            
            logger.debug("Cleared all data")
            return True
        except Exception as e:
            logger.error(f"Failed to clear data: {e}")
            return False
