"""Base storage provider for Immuta automation."""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union

from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class StorageProvider(ABC):
    """Base class for storage providers.
    
    This class provides a common interface for all storage providers,
    including methods for storing and retrieving data.
    
    Attributes:
        name: Storage provider name.
    """

    def __init__(self, name: str = "base"):
        """Initialize the storage provider.
        
        Args:
            name: Storage provider name.
        """
        self.name = name
        logger.info(f"Initialized {name} storage provider")
    
    @abstractmethod
    def store(self, key: str, data: Any) -> bool:
        """Store data.
        
        Args:
            key: Key to store the data under.
            data: Data to store.
            
        Returns:
            True if the data was stored successfully, False otherwise.
        """
        pass
    
    @abstractmethod
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve data.
        
        Args:
            key: Key to retrieve the data for.
            
        Returns:
            Retrieved data if found, None otherwise.
        """
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """Delete data.
        
        Args:
            key: Key to delete the data for.
            
        Returns:
            True if the data was deleted successfully, False otherwise.
        """
        pass
    
    @abstractmethod
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """List keys.
        
        Args:
            prefix: Prefix to filter keys by.
            
        Returns:
            List of keys.
        """
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """Check if a key exists.
        
        Args:
            key: Key to check.
            
        Returns:
            True if the key exists, False otherwise.
        """
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """Clear all data.
        
        Returns:
            True if all data was cleared successfully, False otherwise.
        """
        pass
    
    def __enter__(self):
        """Enter context manager."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        pass
