"""JSON exporter for Immuta automation reports."""

import json
import os
from typing import Dict, Any, Optional

from immuta_toolkit.reporting.report import Report
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class JsonExporter:
    """JSON exporter for reports.

    This class provides methods for exporting reports to JSON files.
    """

    def export(self, report: Report, output_path: str, pretty: bool = True) -> str:
        """Export a report to a JSON file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.
            pretty: Whether to format the JSON with indentation.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .json extension if not present
        if not output_path.endswith(".json"):
            output_path += ".json"

        # Convert report to dictionary
        try:
            # Use model_dump for newer Pydantic versions
            if hasattr(report, "model_dump"):
                report_dict = report.model_dump()
            else:
                report_dict = report.dict()

            # Write the report to the file
            with open(output_path, "w") as f:
                if pretty:
                    json.dump(report_dict, f, indent=2)
                else:
                    json.dump(report_dict, f)
        except Exception as e:
            logger.error(f"Failed to export report: {e}")
            raise IOError(f"Failed to export report: {e}")

        logger.info(f"Exported report to {output_path}")
        return output_path

    def export_summary(
        self, report: Report, output_path: str, pretty: bool = True
    ) -> str:
        """Export a report summary to a JSON file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.
            pretty: Whether to format the JSON with indentation.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .json extension if not present
        if not output_path.endswith(".json"):
            output_path += ".json"

        try:
            # Get report summary
            summary = report.get_summary()

            # Write the summary to the file
            with open(output_path, "w") as f:
                if pretty:
                    json.dump(summary, f, indent=2)
                else:
                    json.dump(summary, f)
        except Exception as e:
            logger.error(f"Failed to export report summary: {e}")
            raise IOError(f"Failed to export report summary: {e}")

        logger.info(f"Exported report summary to {output_path}")
        return output_path
