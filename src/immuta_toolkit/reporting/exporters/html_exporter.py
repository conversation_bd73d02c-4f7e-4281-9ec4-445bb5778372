"""HTML exporter for Immuta automation reports."""

import os
import json
import pkg_resources
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

import jinja2

from immuta_toolkit.reporting.report import Report, ReportEntry
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class HtmlExporter:
    """HTML exporter for reports.

    This class provides methods for exporting reports to HTML files using Jinja2 templates.
    """

    def __init__(self):
        """Initialize the HTML exporter."""
        # Get the templates directory
        templates_dir = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), "templates", "html"
        )

        # Set up Jinja2 environment
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(templates_dir),
            autoescape=jinja2.select_autoescape(["html", "xml"]),
        )

        # Load CSS and JavaScript
        self.css = self._load_resource("css/report.css")
        self.charts_js = self._load_resource("js/charts.js")
        self.dashboard_js = self._load_resource("js/dashboard.js")

        logger.info(f"Initialized HTML exporter with templates from {templates_dir}")

    def export(self, report: Report, output_path: str) -> str:
        """Export a report to an HTML file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .html extension if not present
        if not output_path.endswith(".html"):
            output_path += ".html"

        # Get report summary
        summary = report.get_summary()

        # Prepare template context
        context = self._prepare_template_context(report, summary)

        # Generate HTML content
        html = self._render_template("base.html", context)

        # Write the report to the file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)

        logger.info(f"Exported report to {output_path}")
        return output_path

    def export_summary(self, report: Report, output_path: str) -> str:
        """Export a report summary to an HTML file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .html extension if not present
        if not output_path.endswith(".html"):
            output_path += ".html"

        # Get report summary
        summary = report.get_summary()

        # Prepare template context (with only summary tab)
        context = self._prepare_template_context(report, summary)
        context["summary_only"] = True

        # Generate HTML content
        html = self._render_template("base.html", context)

        # Write the summary to the file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(html)

        logger.info(f"Exported report summary to {output_path}")
        return output_path

    def _load_resource(self, resource_path: str) -> str:
        """Load a resource file.

        Args:
            resource_path: Path to the resource file.

        Returns:
            Content of the resource file.
        """
        try:
            # First try to load from the package resources
            full_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)), "templates", resource_path
            )

            with open(full_path, "r", encoding="utf-8") as f:
                return f.read()
        except Exception as e:
            logger.warning(f"Failed to load resource {resource_path}: {e}")
            return ""

    def _render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render a template.

        Args:
            template_name: Name of the template to render.
            context: Template context.

        Returns:
            Rendered template.
        """
        try:
            template = self.env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {e}")
            return f"<html><body><h1>Error rendering template</h1><p>{str(e)}</p></body></html>"

    def _prepare_template_context(
        self, report: Report, summary: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare the template context.

        Args:
            report: Report to prepare context for.
            summary: Report summary.

        Returns:
            Template context.
        """
        # Format entries for display
        formatted_entries = []
        for entry in report.entries:
            # Format timestamp
            timestamp = datetime.fromtimestamp(entry.timestamp)
            timestamp_formatted = timestamp.strftime("%Y-%m-%d %H:%M:%S")

            # Create formatted entry
            formatted_entry = {
                "operation_name": entry.operation_name,
                "operation_type": getattr(entry, "operation_type", None),
                "status": entry.status,
                "timestamp": entry.timestamp,
                "timestamp_formatted": timestamp_formatted,
                "execution_time": f"{entry.execution_time:.3f}",
                "api_used": entry.api_used,
                "web_used": entry.web_used,
                "attempts": entry.attempts,
                "error": entry.error,
                "error_type": getattr(entry, "error_type", None),
                "error_details": getattr(entry, "error_details", None),
                "performance_metrics": getattr(entry, "performance_metrics", None),
                "tags": getattr(entry, "tags", []),
                "details": getattr(entry, "details", None),
            }

            formatted_entries.append(formatted_entry)

        # Calculate operation type metrics if available
        operation_type_metrics = {}
        operation_types = summary.get("operation_types", [])

        if operation_types:
            for op_type in operation_types:
                entries = [
                    e
                    for e in report.entries
                    if getattr(e, "operation_type", None) == op_type
                ]
                if entries:
                    # Calculate metrics for this operation type
                    success_count = sum(1 for e in entries if e.status == "success")
                    total_count = len(entries)
                    success_percentage = (
                        (success_count / total_count) * 100 if total_count > 0 else 0
                    )

                    api_count = sum(1 for e in entries if e.api_used)
                    api_percentage = (
                        (api_count / total_count) * 100 if total_count > 0 else 0
                    )

                    web_count = sum(1 for e in entries if e.web_used)
                    web_percentage = (
                        (web_count / total_count) * 100 if total_count > 0 else 0
                    )

                    total_execution_time = sum(e.execution_time for e in entries)
                    avg_execution_time = (
                        total_execution_time / total_count if total_count > 0 else 0
                    )

                    # Add metrics to dictionary
                    operation_type_metrics[op_type] = {
                        "success_count": success_count,
                        "total_count": total_count,
                        "success_percentage": success_percentage,
                        "api_usage_percentage": api_percentage,
                        "web_usage_percentage": web_percentage,
                        "total_execution_time": total_execution_time,
                        "average_execution_time": avg_execution_time,
                    }

                    # Add CPU and memory usage if available
                    cpu_entries = [
                        e
                        for e in entries
                        if hasattr(e, "performance_metrics")
                        and e.performance_metrics
                        and e.performance_metrics.cpu_usage is not None
                    ]

                    if cpu_entries:
                        cpu_values = [
                            e.performance_metrics.cpu_usage for e in cpu_entries
                        ]
                        operation_type_metrics[op_type]["cpu_usage"] = {
                            "average": sum(cpu_values) / len(cpu_values),
                            "min": min(cpu_values),
                            "max": max(cpu_values),
                        }

                    memory_entries = [
                        e
                        for e in entries
                        if hasattr(e, "performance_metrics")
                        and e.performance_metrics
                        and e.performance_metrics.memory_usage is not None
                    ]

                    if memory_entries:
                        memory_values = [
                            e.performance_metrics.memory_usage for e in memory_entries
                        ]
                        operation_type_metrics[op_type]["memory_usage"] = {
                            "average": sum(memory_values) / len(memory_values),
                            "min": min(memory_values),
                            "max": max(memory_values),
                        }

        # Check if we have CPU and memory metrics
        has_cpu_metrics = any(
            hasattr(e, "performance_metrics")
            and e.performance_metrics
            and e.performance_metrics.cpu_usage is not None
            for e in report.entries
        )

        has_memory_metrics = any(
            hasattr(e, "performance_metrics")
            and e.performance_metrics
            and e.performance_metrics.memory_usage is not None
            for e in report.entries
        )

        # Create context
        context = {
            # Report metadata
            "id": report.id,
            "title": report.title,
            "description": report.description,
            "type": report.type,
            "report_type": report.type,
            "created_at": datetime.fromtimestamp(report.created_at).isoformat(),
            "updated_at": datetime.fromtimestamp(report.updated_at).isoformat(),
            "generated_at": datetime.now().isoformat(),
            # Summary data
            "total_operations": summary["total_operations"],
            "successful_operations": summary["successful_operations"],
            "failed_operations": summary["failed_operations"],
            "success_percentage": f"{summary['success_percentage']:.1f}",
            "total_execution_time": f"{summary['total_execution_time']:.3f}",
            "average_execution_time": f"{summary['average_execution_time']:.3f}",
            "api_usage_percentage": f"{summary['api_usage_percentage']:.1f}",
            "web_usage_percentage": f"{summary['web_usage_percentage']:.1f}",
            # Additional data
            "operation_types": summary.get("operation_types", []),
            "error_types": summary.get("error_types", {}),
            "performance": summary.get("performance", {}),
            # Entries
            "entries": formatted_entries,
            # Operation type metrics
            "operation_type_metrics": operation_type_metrics,
            "has_cpu_metrics": has_cpu_metrics,
            "has_memory_metrics": has_memory_metrics,
            # Resources
            "css": self.css,
            "charts_js": self.charts_js,
            "dashboard_js": self.dashboard_js,
            # JSON data for JavaScript
            "report_json": json.dumps(
                {
                    "id": report.id,
                    "title": report.title,
                    "type": report.type,
                    "successful_operations": summary["successful_operations"],
                    "failed_operations": summary["failed_operations"],
                    "partial_success_operations": 0,  # Not tracked yet
                    "skipped_operations": 0,  # Not tracked yet
                    "success_percentage": summary["success_percentage"],
                    "operation_type_metrics": operation_type_metrics,
                    "entries": [
                        {
                            "timestamp": e.timestamp,
                            "execution_time": float(e.execution_time),
                            "performance_metrics": (
                                e.performance_metrics.model_dump()
                                if hasattr(e, "performance_metrics")
                                and e.performance_metrics
                                else None
                            ),
                        }
                        for e in report.entries
                    ],
                }
            ),
        }

        return context
