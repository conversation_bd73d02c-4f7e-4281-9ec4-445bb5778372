"""CSV exporter for Immuta automation reports."""

import csv
import os
from datetime import datetime
from typing import Dict, Any, List, Optional

from immuta_toolkit.reporting.report import Report, ReportEntry
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class CsvExporter:
    """CSV exporter for reports.

    This class provides methods for exporting reports to CSV files.
    """

    def export(self, report: Report, output_path: str) -> str:
        """Export a report to a CSV file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .csv extension if not present
        if not output_path.endswith(".csv"):
            output_path += ".csv"

        # Write the report to the file
        with open(output_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow(
                [
                    "Operation",
                    "Operation Type",
                    "Status",
                    "Timestamp",
                    "Execution Time (s)",
                    "API Used",
                    "Web Used",
                    "Attempts",
                    "Error",
                    "Error Type",
                    "Tags",
                    "CPU Usage (%)",
                    "Memory Usage (MB)",
                    "API Calls",
                ]
            )

            # Write entries
            for entry in report.entries:
                timestamp = datetime.fromtimestamp(entry.timestamp).isoformat()

                # Get performance metrics if available
                cpu_usage = None
                memory_usage = None
                api_calls = None

                if hasattr(entry, "performance_metrics") and entry.performance_metrics:
                    cpu_usage = entry.performance_metrics.cpu_usage
                    memory_usage = entry.performance_metrics.memory_usage
                    api_calls = entry.performance_metrics.api_calls

                # Format tags
                tags = (
                    ", ".join(entry.tags)
                    if hasattr(entry, "tags") and entry.tags
                    else ""
                )

                writer.writerow(
                    [
                        entry.operation_name,
                        entry.operation_type or "",
                        entry.status,
                        timestamp,
                        f"{entry.execution_time:.3f}",
                        "Yes" if entry.api_used else "No",
                        "Yes" if entry.web_used else "No",
                        entry.attempts,
                        entry.error or "",
                        entry.error_type or "" if hasattr(entry, "error_type") else "",
                        tags,
                        f"{cpu_usage:.1f}" if cpu_usage is not None else "",
                        f"{memory_usage:.1f}" if memory_usage is not None else "",
                        api_calls or "",
                    ]
                )

        logger.info(f"Exported report to {output_path}")
        return output_path

    def export_summary(self, report: Report, output_path: str) -> str:
        """Export a report summary to a CSV file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.

        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .csv extension if not present
        if not output_path.endswith(".csv"):
            output_path += ".csv"

        # Get report summary
        summary = report.get_summary()

        # Write the summary to the file
        with open(output_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header and values
            writer.writerow(["Metric", "Value"])
            writer.writerow(["Report ID", summary["id"]])
            writer.writerow(["Report Type", summary["type"]])
            writer.writerow(["Report Title", summary["title"]])
            writer.writerow(["Created At", summary["created_at"]])
            writer.writerow(["Updated At", summary["updated_at"]])
            writer.writerow(["Total Operations", summary["total_operations"]])
            writer.writerow(["Successful Operations", summary["successful_operations"]])
            writer.writerow(["Failed Operations", summary["failed_operations"]])
            writer.writerow(
                ["Success Percentage", f"{summary['success_percentage']:.1f}%"]
            )
            writer.writerow(
                [
                    "Total Execution Time",
                    f"{summary['total_execution_time']:.3f} seconds",
                ]
            )
            writer.writerow(
                [
                    "Average Execution Time",
                    f"{summary['average_execution_time']:.3f} seconds",
                ]
            )
            writer.writerow(
                ["API Usage Percentage", f"{summary['api_usage_percentage']:.1f}%"]
            )
            writer.writerow(
                ["Web Usage Percentage", f"{summary['web_usage_percentage']:.1f}%"]
            )

            # Add operation types if available
            if "operation_types" in summary and summary["operation_types"]:
                writer.writerow(["", ""])
                writer.writerow(["Operation Types", ""])
                for op_type in summary["operation_types"]:
                    writer.writerow(["", op_type])

            # Add error types if available
            if "error_types" in summary and summary["error_types"]:
                writer.writerow(["", ""])
                writer.writerow(["Error Types", "Count"])
                for error_type, count in summary["error_types"].items():
                    writer.writerow([error_type, count])

            # Add performance summary if available
            if "performance" in summary:
                perf = summary["performance"]
                writer.writerow(["", ""])
                writer.writerow(["Performance Metrics", ""])

                # Execution time
                if "execution_time" in perf:
                    writer.writerow(["Execution Time", ""])
                    writer.writerow(
                        ["  Total", f"{perf['execution_time']['total']:.3f} seconds"]
                    )
                    writer.writerow(
                        [
                            "  Average",
                            f"{perf['execution_time']['average']:.3f} seconds",
                        ]
                    )
                    writer.writerow(
                        ["  Min", f"{perf['execution_time']['min']:.3f} seconds"]
                    )
                    writer.writerow(
                        ["  Max", f"{perf['execution_time']['max']:.3f} seconds"]
                    )

                # CPU usage
                if "cpu_usage" in perf:
                    writer.writerow(["CPU Usage", ""])
                    writer.writerow(
                        ["  Average", f"{perf['cpu_usage']['average']:.1f}%"]
                    )
                    writer.writerow(["  Min", f"{perf['cpu_usage']['min']:.1f}%"])
                    writer.writerow(["  Max", f"{perf['cpu_usage']['max']:.1f}%"])

                # Memory usage
                if "memory_usage" in perf:
                    writer.writerow(["Memory Usage", ""])
                    writer.writerow(
                        ["  Average", f"{perf['memory_usage']['average']:.1f} MB"]
                    )
                    writer.writerow(["  Min", f"{perf['memory_usage']['min']:.1f} MB"])
                    writer.writerow(["  Max", f"{perf['memory_usage']['max']:.1f} MB"])

                # API calls
                if "api_calls" in perf:
                    writer.writerow(["API Calls", ""])
                    writer.writerow(["  Total", perf["api_calls"]["total"]])
                    writer.writerow(
                        ["  Average", f"{perf['api_calls']['average']:.1f}"]
                    )
                    writer.writerow(["  Min", perf["api_calls"]["min"]])
                    writer.writerow(["  Max", perf["api_calls"]["max"]])

        logger.info(f"Exported report summary to {output_path}")
        return output_path

    def export_entries(self, entries: List[ReportEntry], output_path: str) -> str:
        """Export report entries to a CSV file.

        Args:
            entries: Report entries to export.
            output_path: Path to export the entries to.

        Returns:
            Path to the exported entries.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .csv extension if not present
        if not output_path.endswith(".csv"):
            output_path += ".csv"

        # Write the entries to the file
        with open(output_path, "w", newline="") as f:
            writer = csv.writer(f)

            # Write header
            writer.writerow(
                [
                    "Operation",
                    "Operation Type",
                    "Status",
                    "Timestamp",
                    "Execution Time (s)",
                    "API Used",
                    "Web Used",
                    "Attempts",
                    "Error",
                    "Error Type",
                    "Tags",
                    "CPU Usage (%)",
                    "Memory Usage (MB)",
                    "API Calls",
                ]
            )

            # Write entries
            for entry in entries:
                timestamp = datetime.fromtimestamp(entry.timestamp).isoformat()

                # Get performance metrics if available
                cpu_usage = None
                memory_usage = None
                api_calls = None

                if hasattr(entry, "performance_metrics") and entry.performance_metrics:
                    cpu_usage = entry.performance_metrics.cpu_usage
                    memory_usage = entry.performance_metrics.memory_usage
                    api_calls = entry.performance_metrics.api_calls

                # Format tags
                tags = (
                    ", ".join(entry.tags)
                    if hasattr(entry, "tags") and entry.tags
                    else ""
                )

                writer.writerow(
                    [
                        entry.operation_name,
                        (
                            entry.operation_type or ""
                            if hasattr(entry, "operation_type")
                            else ""
                        ),
                        entry.status,
                        timestamp,
                        f"{entry.execution_time:.3f}",
                        "Yes" if entry.api_used else "No",
                        "Yes" if entry.web_used else "No",
                        entry.attempts,
                        entry.error or "",
                        entry.error_type or "" if hasattr(entry, "error_type") else "",
                        tags,
                        f"{cpu_usage:.1f}" if cpu_usage is not None else "",
                        f"{memory_usage:.1f}" if memory_usage is not None else "",
                        api_calls or "",
                    ]
                )

        logger.info(f"Exported {len(entries)} entries to {output_path}")
        return output_path
