"""PDF exporter for reports."""

import os
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import (
    SimpleDocTemplate,
    Paragraph,
    Spacer,
    Table,
    TableStyle,
    PageBreak,
)

from immuta_toolkit.reporting.report import Report, ReportEntry
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class PdfExporter:
    """PDF exporter for reports.

    This class provides methods for exporting reports to PDF files.
    """

    def __init__(self):
        """Initialize the PDF exporter."""
        self.styles = getSampleStyleSheet()

        # Add custom styles
        self.styles.add(
            ParagraphStyle(
                name="Title",
                parent=self.styles["Heading1"],
                fontSize=16,
                spaceAfter=12,
            )
        )
        self.styles.add(
            ParagraphStyle(
                name="Heading2",
                parent=self.styles["Heading2"],
                fontSize=14,
                spaceAfter=10,
            )
        )
        self.styles.add(
            ParagraphStyle(
                name="Heading3",
                parent=self.styles["Heading3"],
                fontSize=12,
                spaceAfter=8,
            )
        )
        self.styles.add(
            ParagraphStyle(
                name="Normal",
                parent=self.styles["Normal"],
                fontSize=10,
                spaceAfter=6,
            )
        )
        self.styles.add(
            ParagraphStyle(
                name="Small",
                parent=self.styles["Normal"],
                fontSize=8,
                spaceAfter=4,
            )
        )

    def export(self, report: Report, output_path: str) -> str:
        """Export a report to a PDF file.

        Args:
            report: Report to export.
            output_path: Path to export the report to.

        Returns:
            Path to the exported report.

        Raises:
            IOError: If the report cannot be exported.
        """
        logger.info(f"Exporting report {report.id} to PDF: {output_path}")

        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .pdf extension if not present
        if not output_path.endswith(".pdf"):
            output_path += ".pdf"

        try:
            # Create the PDF document
            doc = SimpleDocTemplate(output_path, pagesize=letter)

            # Create the content
            content = []

            # Add title
            content.append(Paragraph(report.title, self.styles["Title"]))

            # Add description if available
            if report.description:
                content.append(Paragraph(report.description, self.styles["Normal"]))

            # Add spacer
            content.append(Spacer(1, 12))

            # Add summary
            content.extend(self._create_summary_section(report))

            # Add spacer
            content.append(Spacer(1, 12))

            # Add entries
            content.extend(self._create_entries_section(report))

            # Build the PDF
            doc.build(content)

            logger.info(f"Report exported to {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to export report to PDF: {e}")
            raise IOError(f"Failed to export report to PDF: {e}")

    def export_summary(self, report: Report, output_path: str) -> str:
        """Export a report summary to a PDF file.

        Args:
            report: Report to export.
            output_path: Path to export the report summary to.

        Returns:
            Path to the exported report summary.

        Raises:
            IOError: If the report summary cannot be exported.
        """
        logger.info(f"Exporting report summary {report.id} to PDF: {output_path}")

        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

        # Add .pdf extension if not present
        if not output_path.endswith(".pdf"):
            output_path += ".pdf"

        try:
            # Create the PDF document
            doc = SimpleDocTemplate(output_path, pagesize=letter)

            # Create the content
            content = []

            # Add title
            content.append(Paragraph(f"{report.title} - Summary", self.styles["Title"]))

            # Add description if available
            if report.description:
                content.append(Paragraph(report.description, self.styles["Normal"]))

            # Add spacer
            content.append(Spacer(1, 12))

            # Add summary
            content.extend(self._create_summary_section(report))

            # Build the PDF
            doc.build(content)

            logger.info(f"Report summary exported to {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Failed to export report summary to PDF: {e}")
            raise IOError(f"Failed to export report summary to PDF: {e}")

    def _create_summary_section(self, report: Report) -> List:
        """Create the summary section of the report.

        Args:
            report: Report to create the summary section for.

        Returns:
            List of PDF elements for the summary section.
        """
        elements = []

        # Add summary heading
        elements.append(Paragraph("Summary", self.styles["Heading2"]))

        # Get report summary
        summary = report.get_summary()

        # Create summary table data
        data = [
            ["Report ID", summary["id"]],
            ["Report Type", summary["type"]],
            ["Created At", summary["created_at"]],
            ["Updated At", summary["updated_at"]],
            ["Total Operations", str(summary["total_operations"])],
            [
                "Successful Operations",
                f"{summary['successful_operations']} ({summary['success_percentage']:.1f}%)",
            ],
            ["Failed Operations", str(summary["failed_operations"])],
            ["Total Execution Time", f"{summary['total_execution_time']:.3f} seconds"],
            [
                "Average Execution Time",
                f"{summary['average_execution_time']:.3f} seconds",
            ],
            ["API Usage", f"{summary['api_usage_percentage']:.1f}%"],
            ["Web Automation Usage", f"{summary['web_usage_percentage']:.1f}%"],
        ]

        # Create summary table
        table = Table(data, colWidths=[200, 300])
        table.setStyle(
            TableStyle(
                [
                    ("BACKGROUND", (0, 0), (0, -1), colors.lightgrey),
                    ("TEXTCOLOR", (0, 0), (0, -1), colors.black),
                    ("ALIGN", (0, 0), (0, -1), "LEFT"),
                    ("ALIGN", (1, 0), (1, -1), "LEFT"),
                    ("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
                    ("FONTNAME", (1, 0), (1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 10),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                    ("GRID", (0, 0), (-1, -1), 1, colors.black),
                ]
            )
        )

        elements.append(table)
        elements.append(Spacer(1, 12))

        # Add operation types if available
        if "operation_types" in summary and summary["operation_types"]:
            elements.append(Paragraph("Operation Types", self.styles["Heading3"]))
            for op_type in summary["operation_types"]:
                elements.append(Paragraph(f"• {op_type}", self.styles["Normal"]))
            elements.append(Spacer(1, 12))

        # Add error types if available
        if "error_types" in summary and summary["error_types"]:
            elements.append(Paragraph("Error Types", self.styles["Heading3"]))

            # Create error types table data
            error_data = [["Error Type", "Count"]]
            for error_type, count in summary["error_types"].items():
                error_data.append([error_type, str(count)])

            # Create error types table
            error_table = Table(error_data, colWidths=[350, 150])
            error_table.setStyle(
                TableStyle(
                    [
                        ("BACKGROUND", (0, 0), (-1, 0), colors.lightgrey),
                        ("TEXTCOLOR", (0, 0), (-1, 0), colors.black),
                        ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                        ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                        ("FONTSIZE", (0, 0), (-1, -1), 10),
                        ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                        ("GRID", (0, 0), (-1, -1), 1, colors.black),
                    ]
                )
            )

            elements.append(error_table)
            elements.append(Spacer(1, 12))

        # Add performance summary if available
        if "performance" in summary:
            elements.append(Paragraph("Performance Metrics", self.styles["Heading3"]))
            perf = summary["performance"]

            # Create performance table data
            perf_data = []

            # Add execution time
            if "execution_time" in perf:
                perf_data.append(["Execution Time", ""])
                perf_data.append(
                    ["  Total", f"{perf['execution_time']['total']:.3f} seconds"]
                )
                perf_data.append(
                    ["  Average", f"{perf['execution_time']['average']:.3f} seconds"]
                )
                perf_data.append(
                    ["  Min", f"{perf['execution_time']['min']:.3f} seconds"]
                )
                perf_data.append(
                    ["  Max", f"{perf['execution_time']['max']:.3f} seconds"]
                )

            # Add CPU usage
            if "cpu_usage" in perf:
                perf_data.append(["CPU Usage", ""])
                perf_data.append(["  Average", f"{perf['cpu_usage']['average']:.1f}%"])
                perf_data.append(["  Min", f"{perf['cpu_usage']['min']:.1f}%"])
                perf_data.append(["  Max", f"{perf['cpu_usage']['max']:.1f}%"])

            # Add memory usage
            if "memory_usage" in perf:
                perf_data.append(["Memory Usage", ""])
                perf_data.append(
                    ["  Average", f"{perf['memory_usage']['average']:.1f} MB"]
                )
                perf_data.append(["  Min", f"{perf['memory_usage']['min']:.1f} MB"])
                perf_data.append(["  Max", f"{perf['memory_usage']['max']:.1f} MB"])

            # Add API calls
            if "api_calls" in perf:
                perf_data.append(["API Calls", ""])
                perf_data.append(["  Total", str(perf["api_calls"]["total"])])
                perf_data.append(["  Average", f"{perf['api_calls']['average']:.1f}"])
                perf_data.append(["  Min", str(perf["api_calls"]["min"])])
                perf_data.append(["  Max", str(perf["api_calls"]["max"])])

            # Create performance table
            if perf_data:
                perf_table = Table(perf_data, colWidths=[200, 300])
                perf_table.setStyle(
                    TableStyle(
                        [
                            ("ALIGN", (0, 0), (0, -1), "LEFT"),
                            ("ALIGN", (1, 0), (1, -1), "LEFT"),
                            ("FONTNAME", (0, 0), (0, -1), "Helvetica"),
                            ("FONTSIZE", (0, 0), (-1, -1), 10),
                            ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                            ("GRID", (0, 0), (-1, -1), 1, colors.black),
                            # Highlight section headers
                            ("BACKGROUND", (0, 0), (-1, 0), colors.lightgrey),
                            (
                                ("BACKGROUND", (0, 5), (-1, 5), colors.lightgrey)
                                if len(perf_data) > 5
                                else None
                            ),
                            (
                                ("BACKGROUND", (0, 9), (-1, 9), colors.lightgrey)
                                if len(perf_data) > 9
                                else None
                            ),
                            (
                                ("BACKGROUND", (0, 13), (-1, 13), colors.lightgrey)
                                if len(perf_data) > 13
                                else None
                            ),
                        ]
                    )
                )

                elements.append(perf_table)
                elements.append(Spacer(1, 12))

        return elements

    def _create_entries_section(self, report: Report) -> List:
        """Create the entries section of the report.

        Args:
            report: Report to create the entries section for.

        Returns:
            List of PDF elements for the entries section.
        """
        elements = []

        # Add entries heading
        elements.append(Paragraph("Operations", self.styles["Heading2"]))

        # Check if there are any entries
        if not report.entries:
            elements.append(Paragraph("No operations found.", self.styles["Normal"]))
            return elements

        # Group entries by operation type if available
        entries_by_type = {}
        for entry in report.entries:
            op_type = getattr(entry, "operation_type", "Other") or "Other"
            if op_type not in entries_by_type:
                entries_by_type[op_type] = []
            entries_by_type[op_type].append(entry)

        # If no operation types, use a single group
        if len(entries_by_type) == 1 and "Other" in entries_by_type:
            # Create entries table
            elements.extend(self._create_entries_table(report.entries))
        else:
            # Create a section for each operation type
            for i, (op_type, entries) in enumerate(entries_by_type.items()):
                # Add page break if not the first section
                if i > 0:
                    elements.append(PageBreak())

                # Add operation type heading
                elements.append(
                    Paragraph(f"{op_type} Operations", self.styles["Heading3"])
                )

                # Add entries table
                elements.extend(self._create_entries_table(entries))

                # Add spacer
                elements.append(Spacer(1, 12))

        return elements

    def _create_entries_table(self, entries: List[ReportEntry]) -> List:
        """Create a table for report entries.

        Args:
            entries: Report entries to create a table for.

        Returns:
            List of PDF elements for the entries table.
        """
        elements = []

        # Create table data
        data = [
            ["Operation", "Status", "Timestamp", "Execution Time", "API/Web", "Error"]
        ]

        # Add entries
        for entry in entries:
            # Format timestamp
            timestamp = datetime.fromtimestamp(entry.timestamp).strftime(
                "%Y-%m-%d %H:%M:%S"
            )

            # Format API/Web usage
            api_web = ""
            if entry.api_used and entry.web_used:
                api_web = "API + Web"
            elif entry.api_used:
                api_web = "API"
            elif entry.web_used:
                api_web = "Web"

            # Format error
            error = entry.error or ""
            if len(error) > 50:
                error = error[:47] + "..."

            # Add row
            data.append(
                [
                    entry.operation_name,
                    entry.status,
                    timestamp,
                    f"{entry.execution_time:.3f}s",
                    api_web,
                    error,
                ]
            )

        # Create table
        table = Table(data, colWidths=[120, 70, 120, 70, 70, 150])
        table.setStyle(
            TableStyle(
                [
                    # Header style
                    ("BACKGROUND", (0, 0), (-1, 0), colors.lightgrey),
                    ("TEXTCOLOR", (0, 0), (-1, 0), colors.black),
                    ("ALIGN", (0, 0), (-1, 0), "CENTER"),
                    ("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
                    # Data style
                    ("FONTNAME", (0, 1), (-1, -1), "Helvetica"),
                    ("FONTSIZE", (0, 0), (-1, -1), 8),
                    ("BOTTOMPADDING", (0, 0), (-1, -1), 6),
                    # Grid
                    ("GRID", (0, 0), (-1, -1), 1, colors.black),
                    # Color code status
                    (
                        "TEXTCOLOR",
                        (1, 1),
                        (1, -1),
                        colors.green,
                        lambda x, y: data[y][1] == "success",
                    ),
                    (
                        "TEXTCOLOR",
                        (1, 1),
                        (1, -1),
                        colors.red,
                        lambda x, y: data[y][1] == "failure",
                    ),
                    (
                        "TEXTCOLOR",
                        (1, 1),
                        (1, -1),
                        colors.orange,
                        lambda x, y: data[y][1] == "partial_success",
                    ),
                ]
            )
        )

        elements.append(table)

        return elements
