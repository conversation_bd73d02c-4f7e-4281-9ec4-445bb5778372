"""Report manager for Immuta automation."""

import os
import json
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Callable
from pathlib import Path

from immuta_toolkit.reporting.report import Report, ReportType, ReportEntry
from immuta_toolkit.reporting.formatter import ReportFormatter, JsonReportFormatter
from immuta_toolkit.reporting.exporters.json_exporter import JsonExporter
from immuta_toolkit.reporting.exporters.csv_exporter import CsvExporter
from immuta_toolkit.reporting.exporters.html_exporter import HtmlExporter
from immuta_toolkit.reporting.exporters.pdf_exporter import PdfExporter
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ReportManager:
    """Report manager for Immuta automation.

    This class provides methods for creating, storing, retrieving, and
    exporting reports.

    Attributes:
        storage_dir: Directory to store reports.
        reports: Dictionary of reports, keyed by report ID.
        active_report_id: ID of the currently active report.
    """

    def __init__(self, storage_dir: Optional[str] = None):
        """Initialize the report manager.

        Args:
            storage_dir: Directory to store reports. If None, will use
                the default directory.
        """
        if storage_dir:
            self.storage_dir = storage_dir
        else:
            # Use default directory in user's home directory
            self.storage_dir = os.path.join(
                os.path.expanduser("~"), ".immuta-sre-toolkit", "reports"
            )

        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_dir, exist_ok=True)

        # Initialize reports dictionary
        self.reports: Dict[str, Report] = {}

        # Initialize active report ID
        self.active_report_id: Optional[str] = None

        logger.info(
            f"Initialized report manager with storage directory: {self.storage_dir}"
        )

    def create_report(
        self,
        title: str,
        report_type: Union[ReportType, str],
        description: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        set_active: bool = True,
    ) -> Report:
        """Create a new report.

        Args:
            title: Report title.
            report_type: Report type.
            description: Report description.
            metadata: Report metadata.
            set_active: Whether to set the new report as the active report.

        Returns:
            The created report.
        """
        # Convert string report type to enum if needed
        if isinstance(report_type, str):
            report_type = ReportType(report_type)

        # Generate a unique ID for the report
        report_id = str(uuid.uuid4())

        # Create the report
        report = Report(
            id=report_id,
            type=report_type,
            title=title,
            description=description,
            metadata=metadata or {},
        )

        # Add the report to the dictionary
        self.reports[report_id] = report

        # Set as active report if requested
        if set_active:
            self.active_report_id = report_id

        logger.info(f"Created report: {report_id} - {title}")
        return report

    def get_report(self, report_id: str) -> Optional[Report]:
        """Get a report by ID.

        Args:
            report_id: Report ID.

        Returns:
            The report if found, None otherwise.
        """
        return self.reports.get(report_id)

    def get_active_report(self) -> Optional[Report]:
        """Get the active report.

        Returns:
            The active report if set, None otherwise.
        """
        if not self.active_report_id:
            return None
        return self.get_report(self.active_report_id)

    def set_active_report(self, report_id: str) -> bool:
        """Set the active report.

        Args:
            report_id: Report ID.

        Returns:
            True if the report was found and set as active, False otherwise.
        """
        if report_id in self.reports:
            self.active_report_id = report_id
            logger.info(f"Set active report: {report_id}")
            return True
        return False

    def add_entry(
        self,
        entry: ReportEntry,
        report_id: Optional[str] = None,
    ) -> bool:
        """Add an entry to a report.

        Args:
            entry: Report entry to add.
            report_id: Report ID. If None, will use the active report.

        Returns:
            True if the entry was added, False otherwise.
        """
        # Get the report
        report_id = report_id or self.active_report_id
        if not report_id:
            logger.warning("No report ID provided and no active report set")
            return False

        report = self.get_report(report_id)
        if not report:
            logger.warning(f"Report not found: {report_id}")
            return False

        # Add the entry
        report.add_entry(entry)
        logger.debug(f"Added entry to report {report_id}: {entry.operation_name}")
        return True

    def save_report(self, report_id: Optional[str] = None) -> Optional[str]:
        """Save a report to storage.

        Args:
            report_id: Report ID. If None, will use the active report.

        Returns:
            Path to the saved report file if successful, None otherwise.
        """
        # Get the report
        report_id = report_id or self.active_report_id
        if not report_id:
            logger.warning("No report ID provided and no active report set")
            return None

        report = self.get_report(report_id)
        if not report:
            logger.warning(f"Report not found: {report_id}")
            return None

        # Create the file path
        file_path = os.path.join(self.storage_dir, f"{report_id}.json")

        # Save the report
        formatter = JsonReportFormatter()
        try:
            formatter.export_report(report, file_path)
            logger.info(f"Saved report to {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to save report: {e}")
            return None

    def load_report(self, report_id: str) -> Optional[Report]:
        """Load a report from storage.

        Args:
            report_id: Report ID.

        Returns:
            The loaded report if successful, None otherwise.
        """
        # Create the file path
        file_path = os.path.join(self.storage_dir, f"{report_id}.json")

        # Check if the file exists
        if not os.path.exists(file_path):
            logger.warning(f"Report file not found: {file_path}")
            return None

        # Load the report
        try:
            with open(file_path, "r") as f:
                data = json.load(f)

            # Create the report
            report = Report(**data)

            # Add the report to the dictionary
            self.reports[report_id] = report

            logger.info(f"Loaded report from {file_path}")
            return report
        except Exception as e:
            logger.error(f"Failed to load report: {e}")
            return None

    def list_reports(self) -> List[Dict[str, Any]]:
        """List all reports in storage.

        Returns:
            List of report summaries.
        """
        reports = []

        # Get all JSON files in the storage directory
        for file_name in os.listdir(self.storage_dir):
            if file_name.endswith(".json"):
                report_id = file_name[:-5]  # Remove .json extension

                # Try to load the report
                report = self.load_report(report_id)
                if report:
                    # Add report summary to the list
                    reports.append(
                        {
                            "id": report.id,
                            "title": report.title,
                            "type": report.type,
                            "created_at": datetime.fromtimestamp(
                                report.created_at
                            ).isoformat(),
                            "updated_at": datetime.fromtimestamp(
                                report.updated_at
                            ).isoformat(),
                            "entries_count": len(report.entries),
                        }
                    )

        return reports

    def export_report(
        self,
        format: str,
        output_path: str,
        report_id: Optional[str] = None,
        summary_only: bool = False,
    ) -> Optional[str]:
        """Export a report to a file.

        Args:
            format: Export format (json, csv, html, pdf).
            output_path: Path to export the report to.
            report_id: Report ID. If None, will use the active report.
            summary_only: Whether to export only the report summary.

        Returns:
            Path to the exported report if successful, None otherwise.
        """
        # Get the report
        report_id = report_id or self.active_report_id
        if not report_id:
            logger.warning("No report ID provided and no active report set")
            return None

        report = self.get_report(report_id)
        if not report:
            logger.warning(f"Report not found: {report_id}")
            return None

        # Export the report
        try:
            format = format.lower()

            if format == "json":
                exporter = JsonExporter()
                if summary_only:
                    return exporter.export_summary(report, output_path)
                else:
                    return exporter.export(report, output_path)
            elif format == "csv":
                exporter = CsvExporter()
                if summary_only:
                    return exporter.export_summary(report, output_path)
                else:
                    return exporter.export(report, output_path)
            elif format == "html":
                exporter = HtmlExporter()
                if summary_only:
                    return exporter.export_summary(report, output_path)
                else:
                    return exporter.export(report, output_path)
            elif format == "pdf":
                exporter = PdfExporter()
                if summary_only:
                    return exporter.export_summary(report, output_path)
                else:
                    return exporter.export(report, output_path)
            else:
                logger.warning(f"Unsupported export format: {format}")
                return None
        except Exception as e:
            logger.error(f"Failed to export report: {e}")
            return None
