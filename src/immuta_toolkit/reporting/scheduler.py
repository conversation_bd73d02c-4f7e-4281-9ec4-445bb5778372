"""Report scheduler for Immuta automation."""

import os
import time
import json
import threading
import schedule
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable, Union
from enum import Enum

from immuta_toolkit.reporting.report import Report, ReportType
from immuta_toolkit.reporting.manager import ReportManager
from immuta_toolkit.reporting.delivery import EmailDelivery
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ScheduleInterval(str, Enum):
    """Schedule interval."""

    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


class ReportSchedule:
    """Report schedule.

    This class represents a scheduled report.
    """

    def __init__(
        self,
        id: str,
        name: str,
        report_type: Union[ReportType, str],
        interval: Union[ScheduleInterval, str],
        enabled: bool = True,
        recipients: Optional[List[str]] = None,
        cc: Optional[List[str]] = None,
        bcc: Optional[List[str]] = None,
        subject: Optional[str] = None,
        body: Optional[str] = None,
        format: str = "html",
        custom_schedule: Optional[str] = None,
        last_run: Optional[float] = None,
        next_run: Optional[float] = None,
        parameters: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the report schedule.

        Args:
            id: Schedule ID.
            name: Schedule name.
            report_type: Report type.
            interval: Schedule interval.
            enabled: Whether the schedule is enabled.
            recipients: List of recipient email addresses.
            cc: List of CC email addresses.
            bcc: List of BCC email addresses.
            subject: Email subject.
            body: Email body.
            format: Report format (html, pdf).
            custom_schedule: Custom schedule string (e.g., "0 9 * * 1-5").
            last_run: Timestamp of the last run.
            next_run: Timestamp of the next run.
            parameters: Additional parameters for the report.
        """
        self.id = id
        self.name = name
        self.report_type = (
            report_type
            if isinstance(report_type, ReportType)
            else ReportType(report_type)
        )
        self.interval = (
            interval
            if isinstance(interval, ScheduleInterval)
            else ScheduleInterval(interval)
        )
        self.enabled = enabled
        self.recipients = recipients or []
        self.cc = cc or []
        self.bcc = bcc or []
        self.subject = subject
        self.body = body
        self.format = format
        self.custom_schedule = custom_schedule
        self.last_run = last_run
        self.next_run = next_run
        self.parameters = parameters or {}

    def to_dict(self) -> Dict[str, Any]:
        """Convert the schedule to a dictionary.

        Returns:
            Dictionary representation of the schedule.
        """
        return {
            "id": self.id,
            "name": self.name,
            "report_type": self.report_type,
            "interval": self.interval,
            "enabled": self.enabled,
            "recipients": self.recipients,
            "cc": self.cc,
            "bcc": self.bcc,
            "subject": self.subject,
            "body": self.body,
            "format": self.format,
            "custom_schedule": self.custom_schedule,
            "last_run": self.last_run,
            "next_run": self.next_run,
            "parameters": self.parameters,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ReportSchedule":
        """Create a schedule from a dictionary.

        Args:
            data: Dictionary representation of the schedule.

        Returns:
            Report schedule.
        """
        return cls(
            id=data["id"],
            name=data["name"],
            report_type=data["report_type"],
            interval=data["interval"],
            enabled=data["enabled"],
            recipients=data["recipients"],
            cc=data.get("cc"),
            bcc=data.get("bcc"),
            subject=data.get("subject"),
            body=data.get("body"),
            format=data.get("format", "html"),
            custom_schedule=data.get("custom_schedule"),
            last_run=data.get("last_run"),
            next_run=data.get("next_run"),
            parameters=data.get("parameters", {}),
        )


class ReportScheduler:
    """Report scheduler for Immuta automation.

    This class provides methods for scheduling reports.
    """

    def __init__(
        self,
        report_manager: ReportManager,
        email_delivery: Optional[EmailDelivery] = None,
        storage_dir: Optional[str] = None,
    ):
        """Initialize the report scheduler.

        Args:
            report_manager: Report manager.
            email_delivery: Email delivery.
            storage_dir: Directory to store schedules.
        """
        self.report_manager = report_manager
        self.email_delivery = email_delivery

        if storage_dir:
            self.storage_dir = storage_dir
        else:
            # Use default directory in user's home directory
            self.storage_dir = os.path.join(
                os.path.expanduser("~"), ".immuta-sre-toolkit", "schedules"
            )

        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_dir, exist_ok=True)

        # Initialize schedules dictionary
        self.schedules: Dict[str, ReportSchedule] = {}

        # Initialize scheduler
        self.scheduler = schedule

        # Initialize scheduler thread
        self.scheduler_thread = None
        self.running = False

        # Load schedules
        self._load_schedules()

        logger.info(
            f"Initialized report scheduler with storage directory: {self.storage_dir}"
        )

    def add_schedule(self, schedule: ReportSchedule) -> bool:
        """Add a schedule.

        Args:
            schedule: Report schedule.

        Returns:
            True if the schedule was added, False otherwise.
        """
        # Add the schedule to the dictionary
        self.schedules[schedule.id] = schedule

        # Save the schedule
        self._save_schedule(schedule)

        # Schedule the report if enabled
        if schedule.enabled:
            self._schedule_report(schedule)

        logger.info(f"Added schedule: {schedule.id} - {schedule.name}")
        return True

    def update_schedule(self, schedule: ReportSchedule) -> bool:
        """Update a schedule.

        Args:
            schedule: Report schedule.

        Returns:
            True if the schedule was updated, False otherwise.
        """
        # Check if the schedule exists
        if schedule.id not in self.schedules:
            logger.warning(f"Schedule not found: {schedule.id}")
            return False

        # Remove the old schedule
        self._unschedule_report(schedule.id)

        # Update the schedule
        self.schedules[schedule.id] = schedule

        # Save the schedule
        self._save_schedule(schedule)

        # Schedule the report if enabled
        if schedule.enabled:
            self._schedule_report(schedule)

        logger.info(f"Updated schedule: {schedule.id} - {schedule.name}")
        return True

    def delete_schedule(self, schedule_id: str) -> bool:
        """Delete a schedule.

        Args:
            schedule_id: Schedule ID.

        Returns:
            True if the schedule was deleted, False otherwise.
        """
        # Check if the schedule exists
        if schedule_id not in self.schedules:
            logger.warning(f"Schedule not found: {schedule_id}")
            return False

        # Remove the schedule
        self._unschedule_report(schedule_id)
        del self.schedules[schedule_id]

        # Delete the schedule file
        schedule_path = os.path.join(self.storage_dir, f"{schedule_id}.json")
        if os.path.exists(schedule_path):
            os.remove(schedule_path)

        logger.info(f"Deleted schedule: {schedule_id}")
        return True

    def get_schedule(self, schedule_id: str) -> Optional[ReportSchedule]:
        """Get a schedule by ID.

        Args:
            schedule_id: Schedule ID.

        Returns:
            The schedule if found, None otherwise.
        """
        return self.schedules.get(schedule_id)

    def list_schedules(self) -> List[ReportSchedule]:
        """List all schedules.

        Returns:
            List of schedules.
        """
        return list(self.schedules.values())

    def start(self) -> None:
        """Start the scheduler."""
        if self.running:
            logger.warning("Scheduler is already running")
            return

        # Schedule all enabled reports
        for schedule in self.schedules.values():
            if schedule.enabled:
                self._schedule_report(schedule)

        # Start the scheduler thread
        self.running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler)
        self.scheduler_thread.daemon = True
        self.scheduler_thread.start()

        logger.info("Started report scheduler")

    def stop(self) -> None:
        """Stop the scheduler."""
        if not self.running:
            logger.warning("Scheduler is not running")
            return

        # Stop the scheduler thread
        self.running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=1)
            self.scheduler_thread = None

        # Clear all jobs
        self.scheduler.clear()

        logger.info("Stopped report scheduler")

    def _run_scheduler(self) -> None:
        """Run the scheduler."""
        while self.running:
            self.scheduler.run_pending()
            time.sleep(1)

    def _load_schedules(self) -> None:
        """Load schedules from storage."""
        # Get all JSON files in the storage directory
        for file_name in os.listdir(self.storage_dir):
            if file_name.endswith(".json"):
                schedule_id = file_name[:-5]  # Remove .json extension

                # Load the schedule
                schedule_path = os.path.join(self.storage_dir, file_name)
                try:
                    with open(schedule_path, "r") as f:
                        data = json.load(f)

                    # Create the schedule
                    schedule = ReportSchedule.from_dict(data)

                    # Add the schedule to the dictionary
                    self.schedules[schedule_id] = schedule

                    logger.info(f"Loaded schedule: {schedule_id} - {schedule.name}")
                except Exception as e:
                    logger.error(f"Failed to load schedule {schedule_id}: {e}")

    def _save_schedule(self, schedule: ReportSchedule) -> None:
        """Save a schedule to storage.

        Args:
            schedule: Report schedule.
        """
        # Create the file path
        schedule_path = os.path.join(self.storage_dir, f"{schedule.id}.json")

        # Save the schedule
        try:
            with open(schedule_path, "w") as f:
                json.dump(schedule.to_dict(), f, indent=2)

            logger.info(f"Saved schedule to {schedule_path}")
        except Exception as e:
            logger.error(f"Failed to save schedule: {e}")

    def _schedule_report(self, schedule: ReportSchedule) -> None:
        """Schedule a report.

        Args:
            schedule: Report schedule.
        """
        # Create the job function
        job_func = lambda: self._run_report(schedule.id)

        # Schedule the job based on the interval
        if schedule.interval == ScheduleInterval.HOURLY:
            self.scheduler.every().hour.do(job_func)
        elif schedule.interval == ScheduleInterval.DAILY:
            self.scheduler.every().day.at("00:00").do(job_func)
        elif schedule.interval == ScheduleInterval.WEEKLY:
            self.scheduler.every().week.do(job_func)
        elif schedule.interval == ScheduleInterval.MONTHLY:
            self.scheduler.every().month.do(job_func)
        elif schedule.interval == ScheduleInterval.CUSTOM and schedule.custom_schedule:
            # Parse custom schedule (not implemented yet)
            logger.warning(
                f"Custom schedule not fully implemented: {schedule.custom_schedule}"
            )
        else:
            logger.warning(f"Invalid schedule interval: {schedule.interval}")

        logger.info(
            f"Scheduled report: {schedule.id} - {schedule.name} ({schedule.interval})"
        )

    def _unschedule_report(self, schedule_id: str) -> None:
        """Unschedule a report.

        Args:
            schedule_id: Schedule ID.
        """
        # Find and remove the job
        for job in self.scheduler.get_jobs():
            if job.job_func.args and job.job_func.args[0] == schedule_id:
                self.scheduler.cancel_job(job)
                logger.info(f"Unscheduled report: {schedule_id}")
                break

    def _run_report(self, schedule_id: str) -> None:
        """Run a scheduled report.

        Args:
            schedule_id: Schedule ID.
        """
        # Get the schedule
        schedule = self.get_schedule(schedule_id)
        if not schedule:
            logger.warning(f"Schedule not found: {schedule_id}")
            return

        logger.info(f"Running scheduled report: {schedule.id} - {schedule.name}")

        try:
            # Create the report
            report = self.report_manager.create_report(
                title=f"{schedule.name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                report_type=schedule.report_type,
                description=f"Scheduled report: {schedule.name}",
                metadata={
                    "schedule_id": schedule.id,
                    "parameters": schedule.parameters,
                },
            )

            # TODO: Generate report content based on report type and parameters
            # This would involve calling specific report generation functions
            # based on the report type and parameters

            # Save the report
            report_path = self.report_manager.save_report(report.id)

            # Update schedule with last run time
            schedule.last_run = time.time()

            # Calculate next run time based on interval
            if schedule.interval == ScheduleInterval.HOURLY:
                schedule.next_run = schedule.last_run + 3600  # 1 hour
            elif schedule.interval == ScheduleInterval.DAILY:
                schedule.next_run = schedule.last_run + 86400  # 1 day
            elif schedule.interval == ScheduleInterval.WEEKLY:
                schedule.next_run = schedule.last_run + 604800  # 1 week
            elif schedule.interval == ScheduleInterval.MONTHLY:
                # Approximate 30 days
                schedule.next_run = schedule.last_run + 2592000  # 30 days

            # Save the updated schedule
            self._save_schedule(schedule)

            # Send the report via email if recipients are specified
            if schedule.recipients and self.email_delivery:
                self.email_delivery.send_report(
                    report=report,
                    recipients=schedule.recipients,
                    subject=schedule.subject,
                    body=schedule.body,
                    format=schedule.format,
                    cc=schedule.cc,
                    bcc=schedule.bcc,
                )

            logger.info(f"Completed scheduled report: {schedule.id} - {schedule.name}")
        except Exception as e:
            logger.error(f"Failed to run scheduled report {schedule.id}: {e}")

    def run_now(self, schedule_id: str) -> bool:
        """Run a scheduled report immediately.

        Args:
            schedule_id: Schedule ID.

        Returns:
            True if the report was run, False otherwise.
        """
        # Check if the schedule exists
        if schedule_id not in self.schedules:
            logger.warning(f"Schedule not found: {schedule_id}")
            return False

        # Run the report
        self._run_report(schedule_id)
        return True
