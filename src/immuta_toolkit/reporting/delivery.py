"""Report delivery for Immuta automation."""

import os
import time
import smtplib
import ssl
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from immuta_toolkit.reporting.report import Report
from immuta_toolkit.reporting.exporters.html_exporter import HtmlExporter
from immuta_toolkit.reporting.exporters.pdf_exporter import PdfExporter
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class EmailDelivery:
    """Email delivery for reports.

    This class provides methods for delivering reports via email.
    """

    def __init__(
        self,
        smtp_server: str,
        smtp_port: int,
        username: Optional[str] = None,
        password: Optional[str] = None,
        use_tls: bool = True,
        default_sender: Optional[str] = None,
    ):
        """Initialize the email delivery.

        Args:
            smtp_server: SMTP server address.
            smtp_port: SMTP server port.
            username: SMTP username. If None, will not authenticate.
            password: SMTP password. If None, will not authenticate.
            use_tls: Whether to use TLS.
            default_sender: Default sender email address.
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.use_tls = use_tls
        self.default_sender = default_sender

        logger.info(
            f"Initialized email delivery with SMTP server {smtp_server}:{smtp_port}"
        )

    def send_report(
        self,
        report: Report,
        recipients: List[str],
        subject: Optional[str] = None,
        body: Optional[str] = None,
        sender: Optional[str] = None,
        format: str = "html",
        include_attachment: bool = True,
        cc: Optional[List[str]] = None,
        bcc: Optional[List[str]] = None,
    ) -> bool:
        """Send a report via email.

        Args:
            report: Report to send.
            recipients: List of recipient email addresses.
            subject: Email subject. If None, will use the report title.
            body: Email body. If None, will use a default message.
            sender: Sender email address. If None, will use the default sender.
            format: Report format (html, pdf).
            include_attachment: Whether to include the report as an attachment.
            cc: List of CC email addresses.
            bcc: List of BCC email addresses.

        Returns:
            True if the email was sent successfully, False otherwise.
        """
        try:
            # Set default values
            sender = sender or self.default_sender
            if not sender:
                raise ValueError("Sender email address is required")

            subject = subject or f"Immuta Report: {report.title}"

            if body is None:
                body = f"""
                <html>
                <body>
                    <h1>{report.title}</h1>
                    <p>Please find attached the Immuta report.</p>
                    <p>Report ID: {report.id}</p>
                    <p>Report Type: {report.type}</p>
                    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </body>
                </html>
                """

            # Create message
            msg = MIMEMultipart()
            msg["From"] = sender
            msg["To"] = ", ".join(recipients)
            msg["Subject"] = subject

            if cc:
                msg["Cc"] = ", ".join(cc)

            # Add body
            msg.attach(MIMEText(body, "html"))

            # Add attachment if requested
            if include_attachment:
                attachment_path = self._create_attachment(report, format)
                if attachment_path:
                    with open(attachment_path, "rb") as f:
                        attachment = MIMEApplication(f.read(), _subtype=format)
                        attachment.add_header(
                            "Content-Disposition",
                            f"attachment; filename={os.path.basename(attachment_path)}",
                        )
                        msg.attach(attachment)

            # Send email
            all_recipients = recipients.copy()
            if cc:
                all_recipients.extend(cc)
            if bcc:
                all_recipients.extend(bcc)

            self._send_email(sender, all_recipients, msg)

            logger.info(f"Sent report {report.id} to {len(all_recipients)} recipients")
            return True
        except Exception as e:
            logger.error(f"Failed to send report {report.id}: {e}")
            return False

    def _create_attachment(self, report: Report, format: str) -> Optional[str]:
        """Create a report attachment.

        Args:
            report: Report to create an attachment for.
            format: Report format (html, pdf).

        Returns:
            Path to the attachment if successful, None otherwise.
        """
        try:
            # Create a temporary file
            temp_dir = os.path.join(
                os.path.expanduser("~"), ".immuta-sre-toolkit", "temp"
            )
            os.makedirs(temp_dir, exist_ok=True)

            filename = f"{report.id}_{report.type}_{int(time.time())}"
            output_path = os.path.join(temp_dir, filename)

            # Export the report
            if format.lower() == "html":
                exporter = HtmlExporter()
                return exporter.export(report, output_path)
            elif format.lower() == "pdf":
                exporter = PdfExporter()
                return exporter.export(report, output_path)
            else:
                logger.warning(f"Unsupported format for email attachment: {format}")
                return None
        except Exception as e:
            logger.error(f"Failed to create attachment: {e}")
            return None

    def _send_email(
        self, sender: str, recipients: List[str], msg: MIMEMultipart
    ) -> None:
        """Send an email.

        Args:
            sender: Sender email address.
            recipients: List of recipient email addresses.
            msg: Email message.

        Raises:
            Exception: If the email could not be sent.
        """
        # Create SMTP connection
        if self.use_tls:
            context = ssl.create_default_context()
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls(context=context)
        else:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)

        try:
            # Login if credentials are provided
            if self.username and self.password:
                server.login(self.username, self.password)

            # Send email
            server.sendmail(sender, recipients, msg.as_string())

        finally:
            server.quit()
