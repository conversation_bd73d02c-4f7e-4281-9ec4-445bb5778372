/**
 * Charts and graphs for Immuta SRE Toolkit reports.
 * This module uses Chart.js to create interactive charts and graphs.
 */

// Create a pie chart for operation status
function createStatusPieChart(elementId, successCount, failureCount, partialSuccessCount = 0, skippedCount = 0) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Define the data
    const data = {
        labels: ['Success', 'Failure', 'Partial Success', 'Skipped'],
        datasets: [{
            data: [successCount, failureCount, partialSuccessCount, skippedCount],
            backgroundColor: [
                'rgba(75, 192, 192, 0.8)',  // Success - Green
                'rgba(255, 99, 132, 0.8)',  // Failure - Red
                'rgba(255, 159, 64, 0.8)',  // Partial Success - Orange
                'rgba(201, 203, 207, 0.8)'  // Skipped - Gray
            ],
            borderColor: [
                'rgba(75, 192, 192, 1)',
                'rgba(255, 99, 132, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(201, 203, 207, 1)'
            ],
            borderWidth: 1
        }]
    };
    
    // Create the chart
    new Chart(ctx, {
        type: 'pie',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                title: {
                    display: true,
                    text: 'Operation Status'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
}

// Create a bar chart for execution time by operation type
function createExecutionTimeBarChart(elementId, labels, data) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Create the chart
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Average Execution Time (s)',
                data: data,
                backgroundColor: 'rgba(54, 162, 235, 0.8)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Seconds'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Operation Type'
                    }
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Average Execution Time by Operation Type'
                }
            }
        }
    });
}

// Create a line chart for performance metrics over time
function createPerformanceLineChart(elementId, timestamps, executionTimes, cpuUsages = null, memoryUsages = null) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Format timestamps
    const labels = timestamps.map(ts => {
        const date = new Date(ts * 1000);
        return date.toLocaleTimeString();
    });
    
    // Define datasets
    const datasets = [
        {
            label: 'Execution Time (s)',
            data: executionTimes,
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            yAxisID: 'y',
            tension: 0.1
        }
    ];
    
    // Add CPU usage if available
    if (cpuUsages) {
        datasets.push({
            label: 'CPU Usage (%)',
            data: cpuUsages,
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            yAxisID: 'y1',
            tension: 0.1
        });
    }
    
    // Add memory usage if available
    if (memoryUsages) {
        datasets.push({
            label: 'Memory Usage (MB)',
            data: memoryUsages,
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            yAxisID: 'y2',
            tension: 0.1
        });
    }
    
    // Create the chart
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: datasets
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            stacked: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Execution Time (s)'
                    }
                },
                y1: cpuUsages ? {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'CPU Usage (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    }
                } : null,
                y2: memoryUsages ? {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Memory Usage (MB)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    }
                } : null
            },
            plugins: {
                title: {
                    display: true,
                    text: 'Performance Metrics Over Time'
                }
            }
        }
    });
}

// Create a radar chart for API vs Web usage by operation type
function createApiWebRadarChart(elementId, labels, apiData, webData) {
    const ctx = document.getElementById(elementId).getContext('2d');
    
    // Create the chart
    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'API Usage',
                    data: apiData,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(54, 162, 235, 1)'
                },
                {
                    label: 'Web Usage',
                    data: webData,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: 'rgba(255, 99, 132, 1)'
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'API vs Web Usage by Operation Type'
                }
            },
            scales: {
                r: {
                    angleLines: {
                        display: true
                    },
                    suggestedMin: 0,
                    suggestedMax: 100
                }
            }
        }
    });
}
