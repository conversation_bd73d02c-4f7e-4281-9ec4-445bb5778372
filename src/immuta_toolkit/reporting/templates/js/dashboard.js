/**
 * Dashboard functionality for Immuta SRE Toolkit reports.
 */

// Initialize the dashboard when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tabs
    initTabs();
    
    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined') {
        initCharts();
    }
    
    // Initialize filters
    initFilters();
    
    // Initialize search
    initSearch();
});

// Initialize tabs
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and tab contents
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab and corresponding tab content
            tab.classList.add('active');
            const tabId = tab.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
    
    // Activate first tab by default
    if (tabs.length > 0) {
        tabs[0].click();
    }
}

// Initialize charts
function initCharts() {
    // Get report data from the data attribute
    const reportDataElement = document.getElementById('report-data');
    if (!reportDataElement) return;
    
    const reportData = JSON.parse(reportDataElement.getAttribute('data-report'));
    
    // Create status pie chart
    if (document.getElementById('status-chart')) {
        createStatusPieChart(
            'status-chart',
            reportData.successful_operations,
            reportData.failed_operations,
            reportData.partial_success_operations || 0,
            reportData.skipped_operations || 0
        );
    }
    
    // Create execution time chart by operation type
    if (document.getElementById('execution-time-chart') && reportData.operation_type_metrics) {
        const labels = [];
        const data = [];
        
        for (const [type, metrics] of Object.entries(reportData.operation_type_metrics)) {
            labels.push(type);
            data.push(metrics.average_execution_time);
        }
        
        createExecutionTimeBarChart('execution-time-chart', labels, data);
    }
    
    // Create performance metrics chart
    if (document.getElementById('performance-chart') && reportData.entries) {
        const timestamps = [];
        const executionTimes = [];
        const cpuUsages = [];
        const memoryUsages = [];
        
        // Sort entries by timestamp
        const sortedEntries = [...reportData.entries].sort((a, b) => a.timestamp - b.timestamp);
        
        // Extract data from entries
        sortedEntries.forEach(entry => {
            timestamps.push(entry.timestamp);
            executionTimes.push(entry.execution_time);
            
            if (entry.performance_metrics) {
                if (entry.performance_metrics.cpu_usage !== undefined) {
                    cpuUsages.push(entry.performance_metrics.cpu_usage);
                }
                
                if (entry.performance_metrics.memory_usage !== undefined) {
                    memoryUsages.push(entry.performance_metrics.memory_usage);
                }
            }
        });
        
        // Only include CPU and memory usage if we have data for all entries
        const hasCpuData = cpuUsages.length === timestamps.length;
        const hasMemoryData = memoryUsages.length === timestamps.length;
        
        createPerformanceLineChart(
            'performance-chart',
            timestamps,
            executionTimes,
            hasCpuData ? cpuUsages : null,
            hasMemoryData ? memoryUsages : null
        );
    }
    
    // Create API vs Web usage radar chart
    if (document.getElementById('api-web-chart') && reportData.operation_type_metrics) {
        const labels = [];
        const apiData = [];
        const webData = [];
        
        for (const [type, metrics] of Object.entries(reportData.operation_type_metrics)) {
            labels.push(type);
            apiData.push(metrics.api_usage_percentage);
            webData.push(metrics.web_usage_percentage);
        }
        
        createApiWebRadarChart('api-web-chart', labels, apiData, webData);
    }
}

// Initialize filters
function initFilters() {
    const statusFilter = document.getElementById('status-filter');
    const typeFilter = document.getElementById('type-filter');
    const rows = document.querySelectorAll('.entry-row');
    
    // Status filter
    if (statusFilter) {
        statusFilter.addEventListener('change', filterEntries);
    }
    
    // Type filter
    if (typeFilter) {
        typeFilter.addEventListener('change', filterEntries);
    }
    
    // Filter entries based on selected filters
    function filterEntries() {
        const selectedStatus = statusFilter ? statusFilter.value : 'all';
        const selectedType = typeFilter ? typeFilter.value : 'all';
        
        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            const type = row.getAttribute('data-type');
            
            const statusMatch = selectedStatus === 'all' || status === selectedStatus;
            const typeMatch = selectedType === 'all' || type === selectedType;
            
            if (statusMatch && typeMatch) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
}

// Initialize search
function initSearch() {
    const searchInput = document.getElementById('search-input');
    const rows = document.querySelectorAll('.entry-row');
    
    if (searchInput) {
        searchInput.addEventListener('input', () => {
            const searchTerm = searchInput.value.toLowerCase();
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
}

// Toggle details for an entry
function toggleDetails(entryId) {
    const detailsElement = document.getElementById(`details-${entryId}`);
    
    if (detailsElement) {
        detailsElement.classList.toggle('active');
        
        const button = document.querySelector(`[data-entry="${entryId}"]`);
        if (button) {
            if (detailsElement.classList.contains('active')) {
                button.textContent = 'Hide Details';
            } else {
                button.textContent = 'Show Details';
            }
        }
    }
}
