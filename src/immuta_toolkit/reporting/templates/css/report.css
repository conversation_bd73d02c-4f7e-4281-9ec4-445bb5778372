/**
 * Styles for Immuta SRE Toolkit reports.
 */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #34495e;
    --text-color: #333;
    --border-color: #ddd;
    --hover-color: #f5f5f5;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header h1 {
    margin: 0;
    font-size: 24px;
}

.header p {
    margin: 10px 0 0;
    opacity: 0.8;
}

.card {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: var(--secondary-color);
    color: white;
    padding: 15px 20px;
    font-weight: bold;
    font-size: 18px;
}

.card-body {
    padding: 20px;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.summary-item {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    text-align: center;
}

.summary-item h3 {
    margin-top: 0;
    color: var(--secondary-color);
    font-size: 16px;
}

.summary-item p {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
}

.summary-item.success {
    border-top: 3px solid var(--success-color);
}

.summary-item.failure {
    border-top: 3px solid var(--danger-color);
}

.summary-item.warning {
    border-top: 3px solid var(--warning-color);
}

.summary-item.info {
    border-top: 3px solid var(--secondary-color);
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

.table-container {
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

table th,
table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

table th {
    background-color: var(--light-color);
    font-weight: bold;
    color: var(--primary-color);
}

table tr:hover {
    background-color: var(--hover-color);
}

.status-success {
    color: var(--success-color);
    font-weight: bold;
}

.status-failure {
    color: var(--danger-color);
    font-weight: bold;
}

.status-partial {
    color: var(--warning-color);
    font-weight: bold;
}

.badge {
    display: inline-block;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
    color: white;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 10px;
    margin-right: 5px;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab {
    padding: 10px 15px;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom: none;
    margin-bottom: -1px;
    background-color: transparent;
}

.tab.active {
    border-color: var(--border-color);
    border-bottom-color: white;
    background-color: white;
    font-weight: bold;
    color: var(--secondary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.footer {
    text-align: center;
    padding: 20px;
    margin-top: 20px;
    color: #777;
    font-size: 14px;
}

@media (max-width: 768px) {
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .header h1 {
        font-size: 20px;
    }
    
    .card-header {
        font-size: 16px;
    }
    
    .summary-item p {
        font-size: 20px;
    }
}
