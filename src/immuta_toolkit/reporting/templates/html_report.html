<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        header {
            background-color: #f5f5f5;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        h1 {
            margin-top: 0;
            color: #2c3e50;
        }
        .summary {
            background-color: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .summary h2 {
            margin-top: 0;
            color: #2980b9;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
        }
        .summary-item {
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .summary-item h3 {
            margin-top: 0;
            font-size: 16px;
            color: #7f8c8d;
        }
        .summary-item p {
            margin-bottom: 0;
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .entries {
            margin-bottom: 20px;
        }
        .entries h2 {
            color: #2980b9;
        }
        .entry {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        .entry h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .entry-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
        }
        .entry-item {
            margin-bottom: 5px;
        }
        .entry-label {
            font-weight: bold;
            color: #7f8c8d;
        }
        .status-success {
            color: #27ae60;
        }
        .status-failure {
            color: #e74c3c;
        }
        .status-partial {
            color: #f39c12;
        }
        .status-skipped {
            color: #95a5a6;
        }
        .error {
            background-color: #fde8e8;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            color: #e74c3c;
            white-space: pre-wrap;
        }
        footer {
            margin-top: 30px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>{{ title }}</h1>
            <p>{{ description }}</p>
            <p>
                <span class="entry-label">Report Type:</span> {{ type }}<br>
                <span class="entry-label">Created:</span> {{ created_at }}<br>
                <span class="entry-label">Updated:</span> {{ updated_at }}
            </p>
        </header>

        <div class="summary">
            <h2>Summary</h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <h3>Total Operations</h3>
                    <p>{{ summary.total_operations }}</p>
                </div>
                <div class="summary-item">
                    <h3>Successful Operations</h3>
                    <p class="status-success">{{ summary.successful_operations }} ({{ summary.success_percentage }}%)</p>
                </div>
                <div class="summary-item">
                    <h3>Failed Operations</h3>
                    <p class="status-failure">{{ summary.failed_operations }}</p>
                </div>
                <div class="summary-item">
                    <h3>Total Execution Time</h3>
                    <p>{{ summary.total_execution_time }} seconds</p>
                </div>
                <div class="summary-item">
                    <h3>Average Execution Time</h3>
                    <p>{{ summary.average_execution_time }} seconds</p>
                </div>
                <div class="summary-item">
                    <h3>API Usage</h3>
                    <p>{{ summary.api_usage_percentage }}%</p>
                </div>
                <div class="summary-item">
                    <h3>Web Automation Usage</h3>
                    <p>{{ summary.web_usage_percentage }}%</p>
                </div>
            </div>
        </div>

        <div class="entries">
            <h2>Operations</h2>
            {% for entry in entries %}
            <div class="entry">
                <h3>{{ entry.operation_name }}</h3>
                <div class="entry-grid">
                    <div class="entry-item">
                        <span class="entry-label">Status:</span>
                        <span class="status-{{ entry.status }}">{{ entry.status }}</span>
                    </div>
                    <div class="entry-item">
                        <span class="entry-label">Timestamp:</span> {{ entry.timestamp }}
                    </div>
                    <div class="entry-item">
                        <span class="entry-label">Execution Time:</span> {{ entry.execution_time }} seconds
                    </div>
                    <div class="entry-item">
                        <span class="entry-label">API Used:</span> {{ "Yes" if entry.api_used else "No" }}
                    </div>
                    <div class="entry-item">
                        <span class="entry-label">Web Used:</span> {{ "Yes" if entry.web_used else "No" }}
                    </div>
                    <div class="entry-item">
                        <span class="entry-label">Attempts:</span> {{ entry.attempts }}
                    </div>
                </div>
                {% if entry.error %}
                <div class="error">{{ entry.error }}</div>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <footer>
            <p>Generated by Immuta SRE Toolkit</p>
        </footer>
    </div>
</body>
</html>
