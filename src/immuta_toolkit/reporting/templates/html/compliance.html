<div class="card">
    <div class="card-header">
        <i class="fas fa-check-circle"></i> Compliance Summary
    </div>
    <div class="card-body">
        <div class="summary-grid">
            <div class="summary-item success">
                <h3>Compliance Score</h3>
                <p>{{ compliance_score if compliance_score is defined else 'N/A' }}%</p>
            </div>
            <div class="summary-item success">
                <h3>Compliant Policies</h3>
                <p>{{ compliant_policies if compliant_policies is defined else 'N/A' }}</p>
            </div>
            <div class="summary-item failure">
                <h3>Non-Compliant Policies</h3>
                <p>{{ non_compliant_policies if non_compliant_policies is defined else 'N/A' }}</p>
            </div>
            <div class="summary-item info">
                <h3>Total Policies</h3>
                <p>{{ total_policies if total_policies is defined else 'N/A' }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-shield-alt"></i> Policy Compliance
    </div>
    <div class="card-body">
        {% if policy_compliance %}
        <table>
            <thead>
                <tr>
                    <th>Policy</th>
                    <th>Status</th>
                    <th>Last Checked</th>
                    <th>Details</th>
                </tr>
            </thead>
            <tbody>
                {% for policy in policy_compliance %}
                <tr>
                    <td>{{ policy.name }}</td>
                    <td class="status-{{ 'success' if policy.compliant else 'failure' }}">
                        {{ 'Compliant' if policy.compliant else 'Non-Compliant' }}
                    </td>
                    <td>{{ policy.last_checked }}</td>
                    <td>
                        <button class="btn-details" data-policy="{{ loop.index }}" onclick="togglePolicyDetails('{{ loop.index }}')">Show Details</button>
                    </td>
                </tr>
                <tr id="policy-details-{{ loop.index }}" style="display: none;">
                    <td colspan="4">
                        <div class="policy-details">
                            <h4>Policy Details</h4>
                            <p><strong>Description:</strong> {{ policy.description }}</p>
                            
                            {% if policy.requirements %}
                            <h5>Requirements</h5>
                            <ul>
                                {% for req in policy.requirements %}
                                <li class="{{ 'status-success' if req.met else 'status-failure' }}">
                                    {{ req.description }}
                                    {% if not req.met and req.reason %}
                                    <p><em>Reason: {{ req.reason }}</em></p>
                                    {% endif %}
                                </li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                            
                            {% if policy.recommendations %}
                            <h5>Recommendations</h5>
                            <ul>
                                {% for rec in policy.recommendations %}
                                <li>{{ rec }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No policy compliance data available.</p>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-history"></i> Compliance History
    </div>
    <div class="card-body">
        {% if compliance_history %}
        <div class="chart-container">
            <canvas id="compliance-history-chart"></canvas>
        </div>
        
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const ctx = document.getElementById('compliance-history-chart').getContext('2d');
                
                const labels = {{ compliance_history.dates|tojson }};
                const scores = {{ compliance_history.scores|tojson }};
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Compliance Score',
                            data: scores,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: 'Score (%)'
                                }
                            },
                            x: {
                                title: {
                                    display: true,
                                    text: 'Date'
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: 'Compliance Score History'
                            }
                        }
                    }
                });
            });
        </script>
        {% else %}
        <p>No compliance history data available.</p>
        {% endif %}
    </div>
</div>

<script>
    function togglePolicyDetails(policyId) {
        const detailsElement = document.getElementById(`policy-details-${policyId}`);
        
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'table-row' : 'none';
            
            const button = document.querySelector(`[data-policy="${policyId}"]`);
            if (button) {
                button.textContent = detailsElement.style.display === 'none' ? 'Show Details' : 'Hide Details';
            }
        }
    }
</script>
