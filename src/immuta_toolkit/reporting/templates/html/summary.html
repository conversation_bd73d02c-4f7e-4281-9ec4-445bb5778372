<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-pie"></i> Summary
    </div>
    <div class="card-body">
        <div class="summary-grid">
            <div class="summary-item success">
                <h3>Success Rate</h3>
                <p>{{ success_percentage }}%</p>
            </div>
            <div class="summary-item success">
                <h3>Successful Operations</h3>
                <p>{{ successful_operations }}</p>
            </div>
            <div class="summary-item failure">
                <h3>Failed Operations</h3>
                <p>{{ failed_operations }}</p>
            </div>
            <div class="summary-item info">
                <h3>Total Operations</h3>
                <p>{{ total_operations }}</p>
            </div>
            <div class="summary-item info">
                <h3>Total Execution Time</h3>
                <p>{{ total_execution_time }} s</p>
            </div>
            <div class="summary-item info">
                <h3>Average Execution Time</h3>
                <p>{{ average_execution_time }} s</p>
            </div>
            <div class="summary-item info">
                <h3>API Usage</h3>
                <p>{{ api_usage_percentage }}%</p>
            </div>
            <div class="summary-item info">
                <h3>Web Usage</h3>
                <p>{{ web_usage_percentage }}%</p>
            </div>
        </div>
        
        {% if operation_types %}
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tags"></i> Operation Types
            </div>
            <div class="card-body">
                <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                    {% for op_type in operation_types %}
                    <span class="badge badge-primary">{{ op_type }}</span>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if error_types %}
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-triangle"></i> Error Types
            </div>
            <div class="card-body">
                <table>
                    <thead>
                        <tr>
                            <th>Error Type</th>
                            <th>Count</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for error_type, count in error_types.items() %}
                        <tr>
                            <td>{{ error_type }}</td>
                            <td>{{ count }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-info-circle"></i> Report Details
    </div>
    <div class="card-body">
        <table>
            <tr>
                <th>Report ID</th>
                <td>{{ id }}</td>
            </tr>
            <tr>
                <th>Report Type</th>
                <td>{{ type }}</td>
            </tr>
            <tr>
                <th>Created At</th>
                <td>{{ created_at }}</td>
            </tr>
            <tr>
                <th>Updated At</th>
                <td>{{ updated_at }}</td>
            </tr>
        </table>
    </div>
</div>
