<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-pie"></i> Operation Status
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="status-chart"></canvas>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-bar"></i> Execution Time by Operation Type
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="execution-time-chart"></canvas>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-line"></i> Performance Metrics Over Time
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="performance-chart"></canvas>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-radar"></i> API vs Web Usage by Operation Type
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="api-web-chart"></canvas>
        </div>
    </div>
</div>
