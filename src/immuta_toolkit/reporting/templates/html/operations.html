<div class="card">
    <div class="card-header">
        <i class="fas fa-tasks"></i> Operations
    </div>
    <div class="card-body">
        <div style="margin-bottom: 20px;">
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <div>
                    <label for="status-filter">Status:</label>
                    <select id="status-filter">
                        <option value="all">All</option>
                        <option value="success">Success</option>
                        <option value="failure">Failure</option>
                        <option value="partial_success">Partial Success</option>
                        <option value="skipped">Skipped</option>
                    </select>
                </div>
                
                {% if operation_types %}
                <div>
                    <label for="type-filter">Type:</label>
                    <select id="type-filter">
                        <option value="all">All</option>
                        {% for op_type in operation_types %}
                        <option value="{{ op_type }}">{{ op_type }}</option>
                        {% endfor %}
                    </select>
                </div>
                {% endif %}
                
                <div>
                    <label for="search-input">Search:</label>
                    <input type="text" id="search-input" placeholder="Search operations...">
                </div>
            </div>
        </div>
        
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Operation</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Timestamp</th>
                        <th>Execution Time</th>
                        <th>API/Web</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in entries %}
                    <tr class="entry-row" data-status="{{ entry.status }}" data-type="{{ entry.operation_type }}">
                        <td>{{ entry.operation_name }}</td>
                        <td>{{ entry.operation_type or 'N/A' }}</td>
                        <td class="status-{{ entry.status }}">{{ entry.status }}</td>
                        <td>{{ entry.timestamp_formatted }}</td>
                        <td>{{ entry.execution_time }} s</td>
                        <td>
                            {% if entry.api_used and entry.web_used %}
                            API + Web
                            {% elif entry.api_used %}
                            API
                            {% elif entry.web_used %}
                            Web
                            {% else %}
                            N/A
                            {% endif %}
                        </td>
                        <td>
                            <button class="btn-details" data-entry="{{ loop.index }}" onclick="toggleDetails('{{ loop.index }}')">Show Details</button>
                        </td>
                    </tr>
                    <tr id="details-{{ loop.index }}" style="display: none;">
                        <td colspan="7">
                            <div class="entry-details">
                                <h4>Details</h4>
                                
                                {% if entry.error %}
                                <div class="error-details">
                                    <h5>Error</h5>
                                    <p>{{ entry.error }}</p>
                                    
                                    {% if entry.error_type %}
                                    <p><strong>Type:</strong> {{ entry.error_type }}</p>
                                    {% endif %}
                                    
                                    {% if entry.error_details %}
                                    <pre>{{ entry.error_details }}</pre>
                                    {% endif %}
                                </div>
                                {% endif %}
                                
                                {% if entry.performance_metrics %}
                                <div class="performance-details">
                                    <h5>Performance Metrics</h5>
                                    <table>
                                        <tr>
                                            <th>Execution Time</th>
                                            <td>{{ entry.execution_time }} s</td>
                                        </tr>
                                        
                                        {% if entry.performance_metrics.cpu_usage is not none %}
                                        <tr>
                                            <th>CPU Usage</th>
                                            <td>{{ entry.performance_metrics.cpu_usage }}%</td>
                                        </tr>
                                        {% endif %}
                                        
                                        {% if entry.performance_metrics.memory_usage is not none %}
                                        <tr>
                                            <th>Memory Usage</th>
                                            <td>{{ entry.performance_metrics.memory_usage }} MB</td>
                                        </tr>
                                        {% endif %}
                                        
                                        {% if entry.performance_metrics.api_calls is not none %}
                                        <tr>
                                            <th>API Calls</th>
                                            <td>{{ entry.performance_metrics.api_calls }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                                {% endif %}
                                
                                {% if entry.tags %}
                                <div class="tags">
                                    <h5>Tags</h5>
                                    <div>
                                        {% for tag in entry.tags %}
                                        <span class="badge badge-secondary">{{ tag }}</span>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}
                                
                                {% if entry.details %}
                                <div class="additional-details">
                                    <h5>Additional Details</h5>
                                    <pre>{{ entry.details }}</pre>
                                </div>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
