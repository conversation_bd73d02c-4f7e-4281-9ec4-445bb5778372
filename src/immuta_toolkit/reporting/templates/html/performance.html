<div class="card">
    <div class="card-header">
        <i class="fas fa-tachometer-alt"></i> Performance Summary
    </div>
    <div class="card-body">
        {% if performance %}
        <div class="summary-grid">
            <div class="summary-item info">
                <h3>Avg Execution Time</h3>
                <p>{{ performance.execution_time.average }} s</p>
            </div>
            <div class="summary-item info">
                <h3>Min Execution Time</h3>
                <p>{{ performance.execution_time.min }} s</p>
            </div>
            <div class="summary-item info">
                <h3>Max Execution Time</h3>
                <p>{{ performance.execution_time.max }} s</p>
            </div>
            <div class="summary-item info">
                <h3>Total Execution Time</h3>
                <p>{{ performance.execution_time.total }} s</p>
            </div>
            
            {% if performance.cpu_usage %}
            <div class="summary-item info">
                <h3>Avg CPU Usage</h3>
                <p>{{ performance.cpu_usage.average }}%</p>
            </div>
            <div class="summary-item info">
                <h3>Min CPU Usage</h3>
                <p>{{ performance.cpu_usage.min }}%</p>
            </div>
            <div class="summary-item info">
                <h3>Max CPU Usage</h3>
                <p>{{ performance.cpu_usage.max }}%</p>
            </div>
            {% endif %}
            
            {% if performance.memory_usage %}
            <div class="summary-item info">
                <h3>Avg Memory Usage</h3>
                <p>{{ performance.memory_usage.average }} MB</p>
            </div>
            <div class="summary-item info">
                <h3>Min Memory Usage</h3>
                <p>{{ performance.memory_usage.min }} MB</p>
            </div>
            <div class="summary-item info">
                <h3>Max Memory Usage</h3>
                <p>{{ performance.memory_usage.max }} MB</p>
            </div>
            {% endif %}
            
            {% if performance.api_calls %}
            <div class="summary-item info">
                <h3>Total API Calls</h3>
                <p>{{ performance.api_calls.total }}</p>
            </div>
            <div class="summary-item info">
                <h3>Avg API Calls</h3>
                <p>{{ performance.api_calls.average }}</p>
            </div>
            {% endif %}
        </div>
        {% else %}
        <p>No performance metrics available.</p>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-line"></i> Performance Trends
    </div>
    <div class="card-body">
        <div class="chart-container">
            <canvas id="performance-chart"></canvas>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-list"></i> Performance by Operation Type
    </div>
    <div class="card-body">
        {% if operation_type_metrics %}
        <table>
            <thead>
                <tr>
                    <th>Operation Type</th>
                    <th>Avg Execution Time</th>
                    <th>Success Rate</th>
                    <th>API Usage</th>
                    <th>Web Usage</th>
                    {% if has_cpu_metrics %}
                    <th>Avg CPU Usage</th>
                    {% endif %}
                    {% if has_memory_metrics %}
                    <th>Avg Memory Usage</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for type, metrics in operation_type_metrics.items() %}
                <tr>
                    <td>{{ type }}</td>
                    <td>{{ metrics.average_execution_time }} s</td>
                    <td>{{ metrics.success_percentage }}%</td>
                    <td>{{ metrics.api_usage_percentage }}%</td>
                    <td>{{ metrics.web_usage_percentage }}%</td>
                    {% if has_cpu_metrics and metrics.cpu_usage %}
                    <td>{{ metrics.cpu_usage.average }}%</td>
                    {% endif %}
                    {% if has_memory_metrics and metrics.memory_usage %}
                    <td>{{ metrics.memory_usage.average }} MB</td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No operation type metrics available.</p>
        {% endif %}
    </div>
</div>
