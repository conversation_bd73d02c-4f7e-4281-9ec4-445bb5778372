<div class="card">
    <div class="card-header">
        <i class="fas fa-shield-alt"></i> Security Summary
    </div>
    <div class="card-body">
        <div class="summary-grid">
            <div class="summary-item success">
                <h3>Security Score</h3>
                <p>{{ security_score if security_score is defined else 'N/A' }}%</p>
            </div>
            <div class="summary-item failure">
                <h3>Critical Issues</h3>
                <p>{{ critical_issues if critical_issues is defined else 'N/A' }}</p>
            </div>
            <div class="summary-item warning">
                <h3>Warnings</h3>
                <p>{{ warning_issues if warning_issues is defined else 'N/A' }}</p>
            </div>
            <div class="summary-item info">
                <h3>Total Checks</h3>
                <p>{{ total_checks if total_checks is defined else 'N/A' }}</p>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-exclamation-triangle"></i> Security Issues
    </div>
    <div class="card-body">
        {% if security_issues %}
        <div style="margin-bottom: 20px;">
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <div>
                    <label for="severity-filter">Severity:</label>
                    <select id="severity-filter" onchange="filterSecurityIssues()">
                        <option value="all">All</option>
                        <option value="critical">Critical</option>
                        <option value="high">High</option>
                        <option value="medium">Medium</option>
                        <option value="low">Low</option>
                    </select>
                </div>
                
                <div>
                    <label for="category-filter">Category:</label>
                    <select id="category-filter" onchange="filterSecurityIssues()">
                        <option value="all">All</option>
                        {% for category in security_categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div>
                    <label for="security-search">Search:</label>
                    <input type="text" id="security-search" placeholder="Search issues..." oninput="filterSecurityIssues()">
                </div>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>Issue</th>
                    <th>Severity</th>
                    <th>Category</th>
                    <th>Status</th>
                    <th>Details</th>
                </tr>
            </thead>
            <tbody>
                {% for issue in security_issues %}
                <tr class="security-issue" data-severity="{{ issue.severity }}" data-category="{{ issue.category }}">
                    <td>{{ issue.title }}</td>
                    <td>
                        <span class="badge badge-{{ 'danger' if issue.severity == 'critical' else 'warning' if issue.severity == 'high' else 'secondary' if issue.severity == 'medium' else 'primary' }}">
                            {{ issue.severity }}
                        </span>
                    </td>
                    <td>{{ issue.category }}</td>
                    <td class="status-{{ 'success' if issue.status == 'resolved' else 'failure' }}">
                        {{ issue.status }}
                    </td>
                    <td>
                        <button class="btn-details" data-issue="{{ loop.index }}" onclick="toggleIssueDetails('{{ loop.index }}')">Show Details</button>
                    </td>
                </tr>
                <tr id="issue-details-{{ loop.index }}" style="display: none;">
                    <td colspan="5">
                        <div class="issue-details">
                            <h4>Issue Details</h4>
                            <p><strong>Description:</strong> {{ issue.description }}</p>
                            
                            {% if issue.affected_components %}
                            <h5>Affected Components</h5>
                            <ul>
                                {% for component in issue.affected_components %}
                                <li>{{ component }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                            
                            {% if issue.remediation %}
                            <h5>Remediation</h5>
                            <p>{{ issue.remediation }}</p>
                            {% endif %}
                            
                            {% if issue.references %}
                            <h5>References</h5>
                            <ul>
                                {% for ref in issue.references %}
                                <li><a href="{{ ref.url }}" target="_blank">{{ ref.title }}</a></li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>No security issues found.</p>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="fas fa-chart-pie"></i> Security Issues by Category
    </div>
    <div class="card-body">
        {% if security_issues %}
        <div class="chart-container">
            <canvas id="security-category-chart"></canvas>
        </div>
        
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const ctx = document.getElementById('security-category-chart').getContext('2d');
                
                const categories = {{ security_category_counts.categories|tojson }};
                const counts = {{ security_category_counts.counts|tojson }};
                
                new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: categories,
                        datasets: [{
                            data: counts,
                            backgroundColor: [
                                'rgba(255, 99, 132, 0.8)',
                                'rgba(54, 162, 235, 0.8)',
                                'rgba(255, 206, 86, 0.8)',
                                'rgba(75, 192, 192, 0.8)',
                                'rgba(153, 102, 255, 0.8)',
                                'rgba(255, 159, 64, 0.8)',
                                'rgba(199, 199, 199, 0.8)',
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            title: {
                                display: true,
                                text: 'Security Issues by Category'
                            }
                        }
                    }
                });
            });
        </script>
        {% else %}
        <p>No security issues data available.</p>
        {% endif %}
    </div>
</div>

<script>
    function toggleIssueDetails(issueId) {
        const detailsElement = document.getElementById(`issue-details-${issueId}`);
        
        if (detailsElement) {
            detailsElement.style.display = detailsElement.style.display === 'none' ? 'table-row' : 'none';
            
            const button = document.querySelector(`[data-issue="${issueId}"]`);
            if (button) {
                button.textContent = detailsElement.style.display === 'none' ? 'Show Details' : 'Hide Details';
            }
        }
    }
    
    function filterSecurityIssues() {
        const severityFilter = document.getElementById('severity-filter').value;
        const categoryFilter = document.getElementById('category-filter').value;
        const searchTerm = document.getElementById('security-search').value.toLowerCase();
        
        const issues = document.querySelectorAll('.security-issue');
        
        issues.forEach(issue => {
            const severity = issue.getAttribute('data-severity');
            const category = issue.getAttribute('data-category');
            const text = issue.textContent.toLowerCase();
            
            const severityMatch = severityFilter === 'all' || severity === severityFilter;
            const categoryMatch = categoryFilter === 'all' || category === categoryFilter;
            const searchMatch = text.includes(searchTerm);
            
            if (severityMatch && categoryMatch && searchMatch) {
                issue.style.display = '';
                const detailsId = issue.querySelector('.btn-details').getAttribute('data-issue');
                const detailsRow = document.getElementById(`issue-details-${detailsId}`);
                if (detailsRow && detailsRow.style.display !== 'none') {
                    detailsRow.style.display = 'table-row';
                }
            } else {
                issue.style.display = 'none';
                const detailsId = issue.querySelector('.btn-details').getAttribute('data-issue');
                const detailsRow = document.getElementById(`issue-details-${detailsId}`);
                if (detailsRow) {
                    detailsRow.style.display = 'none';
                }
            }
        });
    }
</script>
