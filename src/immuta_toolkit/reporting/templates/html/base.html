<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        {{ css }}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        {{ charts_js }}
    </script>
    <script>
        {{ dashboard_js }}
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
            {% if description %}
            <p>{{ description }}</p>
            {% endif %}
            <p>Generated: {{ generated_at }}</p>
        </div>
        
        <div id="report-data" data-report="{{ report_json }}" style="display: none;"></div>
        
        <div class="tabs">
            <button class="tab active" data-tab="summary-tab">Summary</button>
            <button class="tab" data-tab="operations-tab">Operations</button>
            <button class="tab" data-tab="charts-tab">Charts</button>
            {% if report_type == 'performance' %}
            <button class="tab" data-tab="performance-tab">Performance</button>
            {% endif %}
            {% if report_type == 'compliance' %}
            <button class="tab" data-tab="compliance-tab">Compliance</button>
            {% endif %}
            {% if report_type == 'security' %}
            <button class="tab" data-tab="security-tab">Security</button>
            {% endif %}
        </div>
        
        <div id="summary-tab" class="tab-content active">
            {% include 'summary.html' %}
        </div>
        
        <div id="operations-tab" class="tab-content">
            {% include 'operations.html' %}
        </div>
        
        <div id="charts-tab" class="tab-content">
            {% include 'charts.html' %}
        </div>
        
        {% if report_type == 'performance' %}
        <div id="performance-tab" class="tab-content">
            {% include 'performance.html' %}
        </div>
        {% endif %}
        
        {% if report_type == 'compliance' %}
        <div id="compliance-tab" class="tab-content">
            {% include 'compliance.html' %}
        </div>
        {% endif %}
        
        {% if report_type == 'security' %}
        <div id="security-tab" class="tab-content">
            {% include 'security.html' %}
        </div>
        {% endif %}
        
        <div class="footer">
            <p>Generated by Immuta SRE Toolkit</p>
        </div>
    </div>
</body>
</html>
