"""Report formatters for Immuta automation."""

import json
import csv
import os
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, TextIO, Union

from immuta_toolkit.reporting.report import Report, ReportEntry
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


class ReportFormatter(ABC):
    """Base class for report formatters.
    
    This class provides a common interface for all report formatters,
    including formatting and export methods.
    """

    @abstractmethod
    def format_report(self, report: Report) -> str:
        """Format a report as a string.
        
        Args:
            report: Report to format.
            
        Returns:
            Formatted report as a string.
        """
        pass
    
    @abstractmethod
    def export_report(self, report: Report, output_path: str) -> str:
        """Export a report to a file.
        
        Args:
            report: Report to export.
            output_path: Path to export the report to.
            
        Returns:
            Path to the exported report.
        """
        pass


class JsonReportFormatter(ReportFormatter):
    """JSON report formatter."""

    def format_report(self, report: Report) -> str:
        """Format a report as a JSON string.
        
        Args:
            report: Report to format.
            
        Returns:
            Formatted report as a JSON string.
        """
        return json.dumps(report.dict(), indent=2)
    
    def export_report(self, report: Report, output_path: str) -> str:
        """Export a report to a JSON file.
        
        Args:
            report: Report to export.
            output_path: Path to export the report to.
            
        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        # Add .json extension if not present
        if not output_path.endswith(".json"):
            output_path += ".json"
        
        # Write the report to the file
        with open(output_path, "w") as f:
            f.write(self.format_report(report))
        
        logger.info(f"Exported report to {output_path}")
        return output_path


class CsvReportFormatter(ReportFormatter):
    """CSV report formatter."""

    def format_report(self, report: Report) -> str:
        """Format a report as a CSV string.
        
        Args:
            report: Report to format.
            
        Returns:
            Formatted report as a CSV string.
        """
        # Create a string buffer to write CSV data
        import io
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            "Operation",
            "Status",
            "Timestamp",
            "Execution Time (s)",
            "API Used",
            "Web Used",
            "Attempts",
            "Error",
        ])
        
        # Write entries
        for entry in report.entries:
            timestamp = datetime.fromtimestamp(entry.timestamp).isoformat()
            writer.writerow([
                entry.operation_name,
                entry.status,
                timestamp,
                f"{entry.execution_time:.3f}",
                "Yes" if entry.api_used else "No",
                "Yes" if entry.web_used else "No",
                entry.attempts,
                entry.error or "",
            ])
        
        return output.getvalue()
    
    def export_report(self, report: Report, output_path: str) -> str:
        """Export a report to a CSV file.
        
        Args:
            report: Report to export.
            output_path: Path to export the report to.
            
        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        # Add .csv extension if not present
        if not output_path.endswith(".csv"):
            output_path += ".csv"
        
        # Write the report to the file
        with open(output_path, "w", newline="") as f:
            f.write(self.format_report(report))
        
        logger.info(f"Exported report to {output_path}")
        return output_path


class TextReportFormatter(ReportFormatter):
    """Text report formatter."""

    def format_report(self, report: Report) -> str:
        """Format a report as a plain text string.
        
        Args:
            report: Report to format.
            
        Returns:
            Formatted report as a plain text string.
        """
        lines = []
        
        # Add report header
        lines.append(f"Report: {report.title}")
        lines.append(f"Type: {report.type}")
        if report.description:
            lines.append(f"Description: {report.description}")
        lines.append(f"Created: {datetime.fromtimestamp(report.created_at).isoformat()}")
        lines.append(f"Updated: {datetime.fromtimestamp(report.updated_at).isoformat()}")
        lines.append("")
        
        # Add summary
        summary = report.get_summary()
        lines.append("Summary:")
        lines.append(f"  Total Operations: {summary['total_operations']}")
        lines.append(f"  Successful Operations: {summary['successful_operations']} ({summary['success_percentage']:.1f}%)")
        lines.append(f"  Failed Operations: {summary['failed_operations']}")
        lines.append(f"  Total Execution Time: {summary['total_execution_time']:.3f} seconds")
        lines.append(f"  Average Execution Time: {summary['average_execution_time']:.3f} seconds")
        lines.append(f"  API Usage: {summary['api_usage_percentage']:.1f}%")
        lines.append(f"  Web Automation Usage: {summary['web_usage_percentage']:.1f}%")
        lines.append("")
        
        # Add entries
        lines.append("Operations:")
        for i, entry in enumerate(report.entries, 1):
            timestamp = datetime.fromtimestamp(entry.timestamp).isoformat()
            lines.append(f"  {i}. {entry.operation_name}")
            lines.append(f"     Status: {entry.status}")
            lines.append(f"     Timestamp: {timestamp}")
            lines.append(f"     Execution Time: {entry.execution_time:.3f} seconds")
            lines.append(f"     API Used: {'Yes' if entry.api_used else 'No'}")
            lines.append(f"     Web Used: {'Yes' if entry.web_used else 'No'}")
            lines.append(f"     Attempts: {entry.attempts}")
            if entry.error:
                lines.append(f"     Error: {entry.error}")
            lines.append("")
        
        return "\n".join(lines)
    
    def export_report(self, report: Report, output_path: str) -> str:
        """Export a report to a text file.
        
        Args:
            report: Report to export.
            output_path: Path to export the report to.
            
        Returns:
            Path to the exported report.
        """
        # Ensure the directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        # Add .txt extension if not present
        if not output_path.endswith(".txt"):
            output_path += ".txt"
        
        # Write the report to the file
        with open(output_path, "w") as f:
            f.write(self.format_report(report))
        
        logger.info(f"Exported report to {output_path}")
        return output_path
