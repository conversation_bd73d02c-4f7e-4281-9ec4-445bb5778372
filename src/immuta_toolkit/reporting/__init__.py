"""Reporting module for Immuta automation."""

from immuta_toolkit.reporting.formatter import ReportFormatter, JsonReportFormatter
from immuta_toolkit.reporting.report import (
    Report,
    ReportType,
    ReportEntry,
    PerformanceMetrics,
)
from immuta_toolkit.reporting.manager import ReportManager
from immuta_toolkit.reporting.delivery import EmailDelivery
from immuta_toolkit.reporting.scheduler import (
    ReportScheduler,
    ReportSchedule,
    ScheduleInterval,
)

__all__ = [
    # Core reporting classes
    "ReportFormatter",
    "JsonReportFormatter",
    "Report",
    "ReportType",
    "ReportEntry",
    "PerformanceMetrics",
    # Report management
    "ReportManager",
    # Report delivery
    "EmailDelivery",
    # Report scheduling
    "ReportScheduler",
    "ReportSchedule",
    "ScheduleInterval",
]
