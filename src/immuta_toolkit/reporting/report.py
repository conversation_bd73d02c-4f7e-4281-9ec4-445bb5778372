"""Report models for Immuta automation."""

import time
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field

from immuta_toolkit.operations.base import OperationResult


class ReportType(str, Enum):
    """Report type."""

    OPERATION = "operation"
    BATCH = "batch"
    AUDIT = "audit"
    SUMMARY = "summary"
    COMPLIANCE = "compliance"
    PERFORMANCE = "performance"
    SECURITY = "security"
    COMPARISON = "comparison"
    TREND = "trend"
    CUSTOM = "custom"


class PerformanceMetrics(BaseModel):
    """Performance metrics for an operation."""

    execution_time: float = Field(
        ..., description="Operation execution time in seconds"
    )
    cpu_usage: Optional[float] = Field(None, description="CPU usage percentage")
    memory_usage: Optional[float] = Field(None, description="Memory usage in MB")
    api_calls: Optional[int] = Field(None, description="Number of API calls made")
    api_call_times: Optional[Dict[str, float]] = Field(
        None, description="API call times by endpoint"
    )
    web_actions: Optional[int] = Field(
        None, description="Number of web actions performed"
    )
    web_action_times: Optional[Dict[str, float]] = Field(
        None, description="Web action times by action type"
    )
    network_requests: Optional[int] = Field(
        None, description="Number of network requests made"
    )
    network_bytes_sent: Optional[int] = Field(
        None, description="Number of bytes sent over the network"
    )
    network_bytes_received: Optional[int] = Field(
        None, description="Number of bytes received over the network"
    )


class ReportEntry(BaseModel):
    """Report entry."""

    operation_name: str = Field(..., description="Operation name")
    operation_type: Optional[str] = Field(None, description="Operation type")
    status: str = Field(..., description="Operation status")
    timestamp: float = Field(..., description="Operation timestamp")
    execution_time: float = Field(
        ..., description="Operation execution time in seconds"
    )
    api_used: bool = Field(
        ..., description="Whether the API was used for the operation"
    )
    web_used: bool = Field(..., description="Whether web automation was used")
    attempts: int = Field(1, description="Number of attempts made")
    error: Optional[str] = Field(None, description="Error message if operation failed")
    error_type: Optional[str] = Field(
        None, description="Type of error if operation failed"
    )
    error_details: Optional[Dict[str, Any]] = Field(
        None, description="Detailed error information"
    )
    performance_metrics: Optional[PerformanceMetrics] = Field(
        None, description="Performance metrics"
    )
    tags: List[str] = Field(
        default_factory=list, description="Tags for categorizing the entry"
    )
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")

    @classmethod
    def from_operation_result(
        cls,
        operation_name: str,
        result: OperationResult,
        operation_type: Optional[str] = None,
        tags: Optional[List[str]] = None,
        performance_metrics: Optional[Dict[str, Any]] = None,
    ) -> "ReportEntry":
        """Create a report entry from an operation result.

        Args:
            operation_name: Operation name.
            result: Operation result.
            operation_type: Type of operation.
            tags: Tags for categorizing the entry.
            performance_metrics: Performance metrics for the operation.

        Returns:
            Report entry.
        """
        # Extract error details if available
        error_type = None
        error_details = None
        if result.error and hasattr(result, "error_info"):
            error_type = getattr(result.error_info, "type", None)
            error_details = getattr(result.error_info, "details", None)

        # Create performance metrics if provided
        metrics = None
        if performance_metrics:
            metrics_dict = {"execution_time": result.execution_time}
            metrics_dict.update(performance_metrics)
            metrics = PerformanceMetrics(**metrics_dict)

        return cls(
            operation_name=operation_name,
            operation_type=operation_type,
            status=result.status,
            timestamp=result.timestamp,
            execution_time=result.execution_time,
            api_used=result.api_used,
            web_used=result.web_used,
            attempts=result.attempts,
            error=result.error,
            error_type=error_type,
            error_details=error_details,
            performance_metrics=metrics,
            tags=tags or [],
            details={"data": result.data} if result.data else None,
        )


class Report(BaseModel):
    """Report model."""

    id: str = Field(..., description="Report ID")
    type: ReportType = Field(..., description="Report type")
    title: str = Field(..., description="Report title")
    description: Optional[str] = Field(None, description="Report description")
    created_at: float = Field(
        default_factory=time.time, description="Report creation timestamp"
    )
    updated_at: float = Field(
        default_factory=time.time, description="Report update timestamp"
    )
    entries: List[ReportEntry] = Field(
        default_factory=list, description="Report entries"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Report metadata"
    )

    def add_entry(self, entry: ReportEntry) -> None:
        """Add an entry to the report.

        Args:
            entry: Report entry to add.
        """
        self.entries.append(entry)
        self.updated_at = time.time()

    def add_operation_result(
        self, operation_name: str, result: OperationResult
    ) -> None:
        """Add an operation result to the report.

        Args:
            operation_name: Operation name.
            result: Operation result.
        """
        entry = ReportEntry.from_operation_result(operation_name, result)
        self.add_entry(entry)

    def get_success_count(self) -> int:
        """Get the number of successful operations.

        Returns:
            Number of successful operations.
        """
        return sum(1 for entry in self.entries if entry.status == "success")

    def get_failure_count(self) -> int:
        """Get the number of failed operations.

        Returns:
            Number of failed operations.
        """
        return sum(1 for entry in self.entries if entry.status == "failure")

    def get_total_execution_time(self) -> float:
        """Get the total execution time of all operations.

        Returns:
            Total execution time in seconds.
        """
        return sum(entry.execution_time for entry in self.entries)

    def get_average_execution_time(self) -> float:
        """Get the average execution time of all operations.

        Returns:
            Average execution time in seconds.
        """
        if not self.entries:
            return 0.0
        return self.get_total_execution_time() / len(self.entries)

    def get_api_usage_percentage(self) -> float:
        """Get the percentage of operations that used the API.

        Returns:
            Percentage of operations that used the API.
        """
        if not self.entries:
            return 0.0
        return (
            sum(1 for entry in self.entries if entry.api_used)
            / len(self.entries)
            * 100.0
        )

    def get_web_usage_percentage(self) -> float:
        """Get the percentage of operations that used web automation.

        Returns:
            Percentage of operations that used web automation.
        """
        if not self.entries:
            return 0.0
        return (
            sum(1 for entry in self.entries if entry.web_used)
            / len(self.entries)
            * 100.0
        )

    def get_success_percentage(self) -> float:
        """Get the percentage of successful operations.

        Returns:
            Percentage of successful operations.
        """
        if not self.entries:
            return 0.0
        return self.get_success_count() / len(self.entries) * 100.0

    def get_operation_types(self) -> List[str]:
        """Get the unique operation types in the report.

        Returns:
            List of unique operation types.
        """
        return list(
            set(entry.operation_type for entry in self.entries if entry.operation_type)
        )

    def get_entries_by_operation_type(self, operation_type: str) -> List[ReportEntry]:
        """Get entries by operation type.

        Args:
            operation_type: Operation type to filter by.

        Returns:
            List of entries with the specified operation type.
        """
        return [
            entry for entry in self.entries if entry.operation_type == operation_type
        ]

    def get_entries_by_tag(self, tag: str) -> List[ReportEntry]:
        """Get entries by tag.

        Args:
            tag: Tag to filter by.

        Returns:
            List of entries with the specified tag.
        """
        return [entry for entry in self.entries if tag in entry.tags]

    def get_entries_by_status(self, status: str) -> List[ReportEntry]:
        """Get entries by status.

        Args:
            status: Status to filter by.

        Returns:
            List of entries with the specified status.
        """
        return [entry for entry in self.entries if entry.status == status]

    def get_error_types(self) -> Dict[str, int]:
        """Get the count of each error type in the report.

        Returns:
            Dictionary mapping error types to their counts.
        """
        error_types = {}
        for entry in self.entries:
            if entry.error_type:
                error_types[entry.error_type] = error_types.get(entry.error_type, 0) + 1
        return error_types

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a summary of performance metrics.

        Returns:
            Dictionary with performance summary.
        """
        metrics = {
            "execution_time": {
                "total": self.get_total_execution_time(),
                "average": self.get_average_execution_time(),
                "min": min((entry.execution_time for entry in self.entries), default=0),
                "max": max((entry.execution_time for entry in self.entries), default=0),
            }
        }

        # Add CPU usage if available
        cpu_entries = [
            entry
            for entry in self.entries
            if entry.performance_metrics
            and entry.performance_metrics.cpu_usage is not None
        ]
        if cpu_entries:
            cpu_values = [entry.performance_metrics.cpu_usage for entry in cpu_entries]
            metrics["cpu_usage"] = {
                "average": sum(cpu_values) / len(cpu_values),
                "min": min(cpu_values),
                "max": max(cpu_values),
            }

        # Add memory usage if available
        memory_entries = [
            entry
            for entry in self.entries
            if entry.performance_metrics
            and entry.performance_metrics.memory_usage is not None
        ]
        if memory_entries:
            memory_values = [
                entry.performance_metrics.memory_usage for entry in memory_entries
            ]
            metrics["memory_usage"] = {
                "average": sum(memory_values) / len(memory_values),
                "min": min(memory_values),
                "max": max(memory_values),
            }

        # Add API calls if available
        api_entries = [
            entry
            for entry in self.entries
            if entry.performance_metrics
            and entry.performance_metrics.api_calls is not None
        ]
        if api_entries:
            api_values = [entry.performance_metrics.api_calls for entry in api_entries]
            metrics["api_calls"] = {
                "total": sum(api_values),
                "average": sum(api_values) / len(api_values),
                "min": min(api_values),
                "max": max(api_values),
            }

        return metrics

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the report.

        Returns:
            Report summary.
        """
        summary = {
            "id": self.id,
            "type": self.type,
            "title": self.title,
            "created_at": datetime.fromtimestamp(self.created_at).isoformat(),
            "updated_at": datetime.fromtimestamp(self.updated_at).isoformat(),
            "total_operations": len(self.entries),
            "successful_operations": self.get_success_count(),
            "failed_operations": self.get_failure_count(),
            "success_percentage": self.get_success_percentage(),
            "total_execution_time": self.get_total_execution_time(),
            "average_execution_time": self.get_average_execution_time(),
            "api_usage_percentage": self.get_api_usage_percentage(),
            "web_usage_percentage": self.get_web_usage_percentage(),
        }

        # Add operation types if available
        operation_types = self.get_operation_types()
        if operation_types:
            summary["operation_types"] = operation_types

        # Add error types if available
        error_types = self.get_error_types()
        if error_types:
            summary["error_types"] = error_types

        # Add performance summary if available
        performance_entries = [
            entry for entry in self.entries if entry.performance_metrics
        ]
        if performance_entries:
            summary["performance"] = self.get_performance_summary()

        return summary
