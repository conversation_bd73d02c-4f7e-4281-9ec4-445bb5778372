# Immuta SRE Toolkit Configuration

This directory contains configuration files for the Immuta SRE Toolkit. These files are used to configure the toolkit for different environments and use cases.

## Configuration Structure

- **default.yaml**: Default configuration values that apply to all environments.
- **environments/**: Environment-specific configuration files.
  - **dev.yaml**: Configuration for development environments.
  - **test.yaml**: Configuration for test environments.
  - **prod.yaml**: Configuration for production environments.
- **schemas/**: JSON schemas for validating configuration files.
- **examples/**: Example configuration files for different use cases.

## Configuration Format

Configuration files are in YAML format and follow a specific structure. Here's an example:

```yaml
# API Configuration
api:
  base_url: "https://immuta.example.com"
  api_key: "${IMMUTA_API_KEY}"
  timeout: 30
  rate_limit: 10

# Web Automation Configuration
web:
  username: "${IMMUTA_USERNAME}"
  password: "${IMMUTA_PASSWORD}"
  headless: true
  use_fallback: true
  version: "2024.02"

# Logging Configuration
logging:
  level: "INFO"
  file: "/app/logs/immuta-toolkit.log"
  format: "json"

# Storage Configuration
storage:
  connection_string: "${BLOB_CONNECTION_STRING}"
  container_name: "immuta-backups"

# Notification Configuration
notifications:
  enabled: true
  email:
    enabled: false
    recipients: ["<EMAIL>"]
    smtp_server: "smtp.example.com"
    smtp_port: 25
    smtp_user: "${SMTP_USER}"
    smtp_password: "${SMTP_PASSWORD}"
    from_address: "<EMAIL>"
  slack:
    enabled: false
    webhook: "${SLACK_WEBHOOK}"
  teams:
    enabled: false
    webhook: "${TEAMS_WEBHOOK}"
```

## Environment Variables

Configuration values can reference environment variables using the `${VARIABLE_NAME}` syntax. These variables will be replaced with the actual environment variable values at runtime.

## Using Configuration Files

To use a specific configuration file, you can use the `--config` option with the CLI:

```bash
immuta --config configs/environments/dev.yaml user list
```

Or in code:

```python
from immuta_toolkit.config import Config

config = Config.from_file("configs/environments/dev.yaml")
```

## Creating Custom Configuration Files

You can create custom configuration files for your specific use cases. Simply create a new YAML file with the appropriate structure and place it in the `configs/` directory or a subdirectory.

## Validating Configuration Files

You can validate configuration files using the CLI:

```bash
immuta config validate --config configs/environments/dev.yaml
```

Or in code:

```python
from immuta_toolkit.config import Config

config = Config.from_file("configs/environments/dev.yaml")
config.validate()
```
