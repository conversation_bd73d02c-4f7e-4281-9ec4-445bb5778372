# Production environment configuration for Immuta SRE Toolkit

environment: prod

api:
  base_url: https://prod-immuta-instance.com
  api_key: ""
  timeout: 30
  rate_limit: 5  # Lower rate limit for production
  rate_limit_window: 1
  max_retries: 5  # More retries for production

web:
  base_url: https://prod-immuta-instance.com
  username: ""
  password: ""
  headless: true  # Always use headless mode in production
  browser_type: chromium
  timeout: 60000  # Longer timeout for production

secrets:
  provider: azure  # Use Azure Key Vault in production
  azure_key_vault_url: ""
  azure_client_id: ""
  azure_tenant_id: ""

storage:
  provider: sqlite  # Use SQLite in production for better reliability
  file_path: data
  sqlite_path: data/immuta.db

logging:
  level: WARNING  # Use WARNING level in production
  file_path: logs/immuta.log
  max_size: 52428800  # 50 MB
  backup_count: 10  # More backups in production

reporting:
  output_dir: reports
  default_format: json

operations:
  use_web_fallback: true
  max_retries: 5  # More retries in production
  retry_delay: 2.0  # Longer retry delay in production
