# Test environment configuration for Immuta SRE Toolkit

environment: test

api:
  base_url: https://test-immuta-instance.com
  api_key: ""
  timeout: 30
  rate_limit: 10
  rate_limit_window: 1
  max_retries: 3

web:
  base_url: https://test-immuta-instance.com
  username: ""
  password: ""
  headless: true  # Use headless mode for testing
  browser_type: chromium
  timeout: 30000

secrets:
  provider: env
  azure_key_vault_url: ""
  azure_client_id: ""
  azure_tenant_id: ""

storage:
  provider: file
  file_path: data
  sqlite_path: data/immuta.db

logging:
  level: INFO  # Use INFO level for testing
  file_path: logs/immuta.log
  max_size: 10485760
  backup_count: 5

reporting:
  output_dir: reports
  default_format: json

operations:
  use_web_fallback: true
  max_retries: 3
  retry_delay: 1.0
