# Default configuration for Immuta SRE Toolkit

environment: dev

api:
  base_url: https://your-immuta-instance.com
  api_key: ""
  timeout: 30
  rate_limit: 10
  rate_limit_window: 1
  max_retries: 3

web:
  base_url: https://your-immuta-instance.com
  username: ""
  password: ""
  headless: true
  browser_type: chromium
  timeout: 30000

secrets:
  provider: env  # Options: env, azure
  azure_key_vault_url: ""
  azure_client_id: ""
  azure_tenant_id: ""

storage:
  provider: file  # Options: file, sqlite
  file_path: data
  sqlite_path: data/immuta.db

logging:
  level: INFO
  file_path: logs/immuta.log
  max_size: 10485760  # 10 MB
  backup_count: 5

reporting:
  output_dir: reports
  default_format: json  # Options: json, csv, text, html

operations:
  use_web_fallback: true
  max_retries: 3
  retry_delay: 1.0
