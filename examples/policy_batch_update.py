#!/usr/bin/env python3
"""Example script for batch updating policies in Immuta."""

import os
import sys
import argparse
import csv
from typing import List, Dict, Any

from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.models.policy import (
    PolicyModel,
    PolicyType,
    ActionType,
    MaskingAction,
    ColumnRule,
)
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Batch update policies in Immuta")
    parser.add_argument(
        "--api-key",
        help="Immuta API key",
        default=os.getenv("IMMUTA_API_KEY"),
    )
    parser.add_argument(
        "--base-url",
        help="Immuta base URL",
        default=os.getenv("IMMUTA_BASE_URL"),
    )
    parser.add_argument(
        "--csv-file",
        required=True,
        help="CSV file with policy updates",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Perform a dry run without making changes",
    )
    return parser.parse_args()


def read_policy_updates(csv_file: str) -> List[Dict[str, Any]]:
    """Read policy updates from a CSV file.
    
    Args:
        csv_file: Path to CSV file.
        
    Returns:
        List of policy updates.
    """
    updates = []
    with open(csv_file, "r") as f:
        reader = csv.DictReader(f)
        for row in reader:
            updates.append(row)
    return updates


def update_policies(
    client: ImmutaHybridClient,
    updates: List[Dict[str, Any]],
    dry_run: bool = False,
) -> bool:
    """Update policies.
    
    Args:
        client: Immuta client.
        updates: List of policy updates.
        dry_run: Whether to perform a dry run without making changes.
        
    Returns:
        True if successful, False otherwise.
    """
    success = True
    
    for update in updates:
        data_source_name = update.get("data_source_name")
        policy_name = update.get("policy_name")
        column_name = update.get("column_name")
        action_type = update.get("action_type")
        
        if not all([data_source_name, policy_name, column_name, action_type]):
            logger.error("Missing required fields in update")
            success = False
            continue
        
        # Get the data source
        data_source = client.get_data_source(data_source_name)
        if data_source is None:
            logger.error(f"Data source {data_source_name} not found")
            success = False
            continue
        
        # Get existing policies for the data source
        policies = client.list_policies(data_source_id=data_source.id)
        
        # Find the policy to update
        policy = next((p for p in policies if p.name == policy_name), None)
        
        if policy is None:
            # Create a new policy
            logger.info(f"Creating new policy {policy_name} for data source {data_source_name}")
            
            # Create a masking policy
            if action_type.lower() == "mask":
                policy = PolicyModel(
                    name=policy_name,
                    policy_type=PolicyType.MASKING,
                    data_source_id=data_source.id,
                    actions=[
                        MaskingAction(
                            action_type=ActionType.MASK,
                            rule=ColumnRule(
                                column_names=[column_name],
                            ),
                        ),
                    ],
                )
            else:
                logger.error(f"Unsupported action type: {action_type}")
                success = False
                continue
            
            if not dry_run:
                created_policy = client.create_policy(policy)
                if created_policy:
                    logger.info(f"Created policy {policy_name}")
                else:
                    logger.error(f"Failed to create policy {policy_name}")
                    success = False
            else:
                logger.info(f"Dry run: Would create policy {policy_name}")
        else:
            # Update the existing policy
            logger.info(f"Updating policy {policy_name} for data source {data_source_name}")
            
            # Add the column to the existing policy
            if action_type.lower() == "mask":
                for action in policy.actions:
                    if action.action_type == ActionType.MASK and isinstance(action.rule, ColumnRule):
                        if column_name not in action.rule.column_names:
                            action.rule.column_names.append(column_name)
            else:
                logger.error(f"Unsupported action type: {action_type}")
                success = False
                continue
            
            if not dry_run:
                updated_policy = client.update_policy(policy.id, policy)
                if updated_policy:
                    logger.info(f"Updated policy {policy_name}")
                else:
                    logger.error(f"Failed to update policy {policy_name}")
                    success = False
            else:
                logger.info(f"Dry run: Would update policy {policy_name}")
    
    return success


def main():
    """Main entry point."""
    args = parse_args()
    
    # Initialize client
    client = ImmutaHybridClient(
        api_key=args.api_key,
        base_url=args.base_url,
    )
    
    try:
        # Read policy updates
        updates = read_policy_updates(args.csv_file)
        
        # Update policies
        success = update_policies(
            client=client,
            updates=updates,
            dry_run=args.dry_run,
        )
        
        if success:
            logger.info("Operation completed successfully")
            return 0
        else:
            logger.error("Operation failed")
            return 1
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    finally:
        client.close()


if __name__ == "__main__":
    sys.exit(main())
