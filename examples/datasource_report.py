#!/usr/bin/env python3
"""Example script for generating a data source report in Immuta."""

import os
import sys
import argparse
from datetime import datetime
from typing import List, Dict, Any, Optional

from immuta_toolkit.hybrid_client import ImmutaHybridClient
from immuta_toolkit.models.data_source import DataSourceModel
from immuta_toolkit.reporting.formatter import JsonReportFormatter, CsvReportFormatter, TextReportFormatter
from immuta_toolkit.reporting.report import Report, ReportType, ReportEntry
from immuta_toolkit.utils.logging import get_logger

logger = get_logger(__name__)


def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments.
    """
    parser = argparse.ArgumentParser(description="Generate a data source report in Immuta")
    parser.add_argument(
        "--api-key",
        help="Immuta API key",
        default=os.getenv("IMMUTA_API_KEY"),
    )
    parser.add_argument(
        "--base-url",
        help="Immuta base URL",
        default=os.getenv("IMMUTA_BASE_URL"),
    )
    parser.add_argument(
        "--output-format",
        choices=["json", "csv", "text"],
        default="json",
        help="Output format for the report",
    )
    parser.add_argument(
        "--output-file",
        help="Output file for the report",
        default="data_source_report",
    )
    parser.add_argument(
        "--tag",
        help="Filter data sources by tag",
    )
    return parser.parse_args()


def generate_data_source_report(
    client: ImmutaHybridClient,
    tag: Optional[str] = None,
) -> Report:
    """Generate a data source report.
    
    Args:
        client: Immuta client.
        tag: Filter data sources by tag.
        
    Returns:
        Report object.
    """
    # Get data sources
    data_sources = client.list_data_sources(tag=tag)
    
    # Create report
    report = Report(
        title="Immuta Data Source Report",
        type=ReportType.DATA_SOURCE,
        description=f"Report of all data sources{' with tag ' + tag if tag else ''}",
        created_at=datetime.now().timestamp(),
        updated_at=datetime.now().timestamp(),
        entries=[],
    )
    
    # Add entries for each data source
    for ds in data_sources:
        # Get policies for the data source
        policies = client.list_policies(data_source_id=ds.id)
        
        # Get projects for the data source
        projects = client.list_projects_for_data_source(ds.id)
        
        # Create entry
        entry = ReportEntry(
            operation_name=f"DataSource: {ds.name}",
            status="success",
            timestamp=datetime.now().timestamp(),
            execution_time=0.0,
            api_used=True,
            web_used=False,
            attempts=1,
            data={
                "id": ds.id,
                "name": ds.name,
                "description": ds.description,
                "type": ds.type,
                "created_at": ds.created_at,
                "updated_at": ds.updated_at,
                "tags": [tag.name for tag in ds.tags] if ds.tags else [],
                "columns": [
                    {
                        "name": col.name,
                        "type": col.type,
                        "attributes": col.attributes.dict() if col.attributes else {},
                    }
                    for col in ds.columns
                ] if ds.columns else [],
                "policies": [
                    {
                        "id": policy.id,
                        "name": policy.name,
                        "type": policy.policy_type,
                    }
                    for policy in policies
                ],
                "projects": [
                    {
                        "id": project.id,
                        "name": project.name,
                    }
                    for project in projects
                ],
            },
        )
        
        report.entries.append(entry)
    
    return report


def main():
    """Main entry point."""
    args = parse_args()
    
    # Initialize client
    client = ImmutaHybridClient(
        api_key=args.api_key,
        base_url=args.base_url,
    )
    
    try:
        # Generate report
        report = generate_data_source_report(
            client=client,
            tag=args.tag,
        )
        
        # Format and export report
        if args.output_format == "json":
            formatter = JsonReportFormatter()
        elif args.output_format == "csv":
            formatter = CsvReportFormatter()
        else:
            formatter = TextReportFormatter()
        
        output_path = formatter.export_report(report, args.output_file)
        
        logger.info(f"Report exported to {output_path}")
        return 0
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    finally:
        client.close()


if __name__ == "__main__":
    sys.exit(main())
