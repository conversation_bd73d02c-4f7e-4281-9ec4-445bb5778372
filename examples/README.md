# Immuta SRE Toolkit Examples

This directory contains example scripts and configurations for the Immuta SRE Toolkit. These examples demonstrate how to use the toolkit for common tasks and can be used as a starting point for your own scripts.

## Examples

### User Management

- [Add Users to Group](add_users_to_group.py): Example script for adding users to a group in Immuta.

### Data Source Management

- [Data Source Report](datasource_report.py): Example script for generating a report of data sources in Immuta.

### Policy Management

- [Policy Batch Update](policy_batch_update.py): Example script for batch updating policies in Immuta.

## Usage

To run these examples, you'll need to have the Immuta SRE Toolkit installed and configured. You can run the examples using the following command:

```bash
python examples/example_script.py
```

Or with PDM:

```bash
pdm run python examples/example_script.py
```

## Creating Your Own Examples

You can use these examples as a starting point for your own scripts. Simply copy an example and modify it to suit your needs.

## Contributing

If you have an example that you'd like to share, please feel free to contribute it to this directory. Make sure to include a brief description of what the example does and how to use it.
