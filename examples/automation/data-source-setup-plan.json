{"metadata": {"title": "Data Source Setup Automation Plan", "description": "Automated setup and configuration of data sources in Immuta", "version": "1.0", "author": "Immuta SRE Team", "created": "2024-01-01", "tags": ["data-sources", "setup", "automation", "configuration"]}, "operations": [{"operation": "list_data_sources", "params": {"limit": 50, "offset": 0}, "metadata": {"description": "List existing data sources", "category": "read", "priority": "high"}}, {"operation": "create_data_source", "params": {"data_source": {"name": "Production PostgreSQL", "type": "PostgreSQL", "connection_string": "***************************************/analytics", "schema": "public", "tags": ["production", "analytics", "postgresql"], "metadata": {"environment": "production", "owner": "data-team", "criticality": "high"}}, "backup": true, "dry_run": false}, "metadata": {"description": "Create production PostgreSQL data source", "category": "write", "priority": "high"}}, {"operation": "create_data_source", "params": {"data_source": {"name": "Staging S3 Bucket", "type": "S3", "connection_string": "s3://staging-data-bucket/", "tags": ["staging", "s3", "data-lake"], "metadata": {"environment": "staging", "owner": "data-team", "criticality": "medium"}}, "backup": true, "dry_run": false}, "metadata": {"description": "Create staging S3 data source", "category": "write", "priority": "medium"}}, {"operation": "apply_tags_to_data_source", "params": {"data_source_id": "Production PostgreSQL", "tags": ["PII", "sensitive", "GDPR"]}, "metadata": {"description": "Apply compliance tags to production database", "category": "write", "priority": "high"}}, {"operation": "create_data_source_policy", "params": {"policy": {"name": "Production Data Access Policy", "type": "data", "data_source_id": "Production PostgreSQL", "rules": [{"type": "masking", "conditions": [{"column": "email", "operator": "contains", "value": "@"}], "actions": [{"type": "mask", "method": "email_mask"}]}]}, "backup": true}, "metadata": {"description": "Create data masking policy for production database", "category": "write", "priority": "high"}}, {"operation": "validate_data_source_connection", "params": {"data_source_id": "Production PostgreSQL"}, "metadata": {"description": "Validate connection to production database", "category": "validation", "priority": "high"}}], "execution_config": {"mode": "sequential", "max_concurrent": 2, "fail_fast": true, "retry_failed": true, "validation_enabled": true, "reporting_enabled": true}}