{"metadata": {"title": "User Management Automation Plan", "description": "Comprehensive user management operations for Immuta", "version": "1.0", "author": "Immuta SRE Team", "created": "2024-01-01", "tags": ["users", "management", "automation"]}, "operations": [{"operation": "list_users", "params": {"limit": 100, "offset": 0}, "metadata": {"description": "List all users in the system", "category": "read", "priority": "high"}}, {"operation": "get_user", "params": {"user_id": "<EMAIL>"}, "metadata": {"description": "Get admin user details", "category": "read", "priority": "medium"}}, {"operation": "create_user", "params": {"user": {"email": "<EMAIL>", "name": "New User", "attributes": {"role": "DataScientist"}, "groups": ["DataScientists", "Analysts"]}, "backup": true, "dry_run": false}, "metadata": {"description": "Create a new data scientist user", "category": "write", "priority": "high"}}, {"operation": "update_user", "params": {"user_id": "<EMAIL>", "updates": {"name": "Updated User Name", "groups": ["DataScientists", "Analysts", "PowerUsers"]}, "backup": true}, "metadata": {"description": "Update user information and add to PowerUsers group", "category": "write", "priority": "medium"}}, {"operation": "list_user_groups", "params": {"user_id": "<EMAIL>"}, "metadata": {"description": "List groups for the new user", "category": "read", "priority": "low"}}], "execution_config": {"mode": "sequential", "max_concurrent": 3, "fail_fast": true, "retry_failed": true, "validation_enabled": true, "reporting_enabled": true}}