# Changelog

All notable changes to the Immuta SRE Toolkit will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Prometheus metrics for monitoring API requests, operations, cache, and backups
- Healthcheck endpoint for health monitoring and readiness/liveness probes
- Kubernetes deployment manifests
- Automated backup script with retention management
- GitHub Actions workflow for CI/CD
- Development container configuration
- VSCode configuration for consistent development experience

### Changed
- Updated Docker configuration with healthcheck
- Enhanced backup and restore functionality
- Improved caching mechanism with TTL support
- Updated documentation with comprehensive software lifecycle guide

### Fixed
- Fixed issue with tag cache invalidation
- Improved error handling in backup service

## [1.0.0] - 2023-06-15

### Added
- Initial release of the Immuta SRE Toolkit
- User management functionality
- Policy management functionality
- Data source management with tagging support
- Project management with members, data sources, and purposes
- Backup and restore capabilities
- Secrets management with environment variables and Azure Key Vault
- Caching for performance optimization
- Batch operations for managing multiple resources
- Asynchronous operations for improved performance
- Docker support for easy deployment
- Comprehensive documentation

## How to Update the Changelog

When making changes to the codebase, add an entry to the "Unreleased" section of this file. When releasing a new version, rename the "Unreleased" section to the version number and add a new "Unreleased" section at the top.

### Types of Changes
- `Added` for new features.
- `Changed` for changes in existing functionality.
- `Deprecated` for soon-to-be removed features.
- `Removed` for now removed features.
- `Fixed` for any bug fixes.
- `Security` in case of vulnerabilities.

### Example Entry

```
## [1.1.0] - 2023-07-01

### Added
- New feature X
- Support for Y

### Changed
- Improved performance of Z

### Fixed
- Bug in feature A
```
