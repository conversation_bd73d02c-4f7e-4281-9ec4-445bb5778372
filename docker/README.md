# Docker Support for Immuta SRE Toolkit

This directory contains Docker-related files for the Immuta SRE Toolkit.

## Overview

The Immuta SRE Toolkit provides Docker support for:

1. **Production Environment**: A lightweight container for running the toolkit in production
2. **Development Environment**: A fully-featured container for development work
3. **API Server**: A container for running the API server
4. **Testing Environment**: A container for running tests

## Docker Images

The toolkit provides the following Docker images:

- **immuta-sre-toolkit:latest**: Production image for running the toolkit
- **immuta-sre-toolkit-dev:latest**: Development image with all dependencies

## Getting Started

### Prerequisites

- Docker 20.10.0 or higher
- Docker Compose 2.0.0 or higher

### Quick Start

1. **Build the Docker images**:

   ```bash
   make build       # Build production image
   make build-dev   # Build development image
   ```

2. **Run the toolkit**:

   ```bash
   make run         # Run production container
   make run-dev     # Run development container
   make run-api     # Run API server
   ```

3. **Access the development environment**:

   ```bash
   make shell       # Open a shell in the development container
   ```

4. **Run tests**:

   ```bash
   make test        # Run all tests
   make test-web    # Run web automation tests
   ```

## Docker Compose Services

The `docker-compose.yml` file defines the following services:

### immuta-toolkit

The main CLI service for running commands.

```bash
docker-compose up -d immuta-toolkit
```

### api

The API server for running the REST API.

```bash
docker-compose up -d api
```

### dev

The development environment with all dependencies.

```bash
docker-compose up -d dev
```

### test

The testing environment for running tests.

```bash
docker-compose up -d test
```

## Environment Variables

The Docker containers use the following environment variables:

| Variable                 | Description                          | Default        |
| ------------------------ | ------------------------------------ | -------------- |
| `IMMUTA_API_KEY`         | Immuta API key                       | -              |
| `IMMUTA_BASE_URL`        | Immuta base URL                      | -              |
| `IMMUTA_API_TIMEOUT`     | API timeout in seconds               | 30             |
| `IMMUTA_API_RATE_LIMIT`  | API rate limit                       | 10             |
| `IMMUTA_LOG_LEVEL`       | Logging level                        | INFO           |
| `BLOB_CONNECTION_STRING` | Azure Blob Storage connection string | -              |
| `BLOB_CONTAINER_NAME`    | Azure Blob Storage container name    | immuta-backups |
| `AZURE_KEY_VAULT_URL`    | Azure Key Vault URL                  | -              |
| `AZURE_CLIENT_ID`        | Azure Client ID                      | -              |
| `AZURE_CLIENT_SECRET`    | Azure Client Secret                  | -              |
| `AZURE_TENANT_ID`        | Azure Tenant ID                      | -              |

## Volumes

The Docker containers use the following volumes:

| Volume                   | Description             | Mode       |
| ------------------------ | ----------------------- | ---------- |
| `./data:/app/data`       | Data directory          | Read/Write |
| `./logs:/app/logs`       | Logs directory          | Read/Write |
| `./reports:/app/reports` | Reports directory       | Read/Write |
| `./configs:/app/configs` | Configuration directory | Read-Only  |

## Resource Limits

The Docker containers have the following resource limits:

| Service        | CPU Limit | Memory Limit | CPU Reservation | Memory Reservation |
| -------------- | --------- | ------------ | --------------- | ------------------ |
| immuta-toolkit | 1.0       | 1G           | 0.25            | 512M               |
| api            | 1.0       | 1G           | 0.25            | 512M               |
| dev            | 2.0       | 2G           | 0.5             | 1G                 |
| test           | 2.0       | 2G           | -               | -                  |

## Security

The Docker containers are configured with the following security options:

- Non-root user: The containers run as a non-root user (`immuta`) for better security
- No new privileges: The containers are configured with `no-new-privileges:true` to prevent privilege escalation

## Makefile Commands

The Makefile provides the following commands for Docker operations:

| Command                | Description                                            |
| ---------------------- | ------------------------------------------------------ |
| `make build`           | Build the production Docker image                      |
| `make build-dev`       | Build the development Docker image                     |
| `make run`             | Run the production container                           |
| `make run-dev`         | Run the development container                          |
| `make run-api`         | Run the API server container                           |
| `make shell`           | Open a shell in the development container              |
| `make test`            | Run tests in the development container                 |
| `make test-web`        | Run web automation tests                               |
| `make test-docker`     | Run tests in a dedicated test container                |
| `make test-web-docker` | Run web automation tests in a dedicated test container |
| `make docker-push`     | Build and push Docker images to registry               |
| `make docker-pull`     | Pull Docker images from registry                       |
| `make clean`           | Remove containers                                      |
| `make clean-all`       | Remove containers and images                           |

## Customization

You can customize the Docker configuration by:

1. **Modifying the Dockerfile**: Edit `docker/Dockerfile` for production or `Dockerfile.dev` for development
2. **Modifying docker-compose.yml**: Edit the Docker Compose configuration
3. **Setting environment variables**: Create a `.env` file with your environment variables

## Troubleshooting

### Common Issues

1. **Permission issues with volumes**:
   - Ensure the host directories exist and have the correct permissions
   - Run `mkdir -p data logs reports configs` to create the required directories

2. **Container fails to start**:
   - Check the logs with `docker logs immuta-toolkit`
   - Ensure all required environment variables are set

3. **Tests fail in Docker**:
   - Ensure the container has access to the required resources
   - Check if the tests require specific environment variables

### Debugging

To debug issues in the Docker containers:

1. **Access the container**:
   ```bash
   docker exec -it immuta-toolkit bash
   ```

2. **Check the logs**:
   ```bash
   docker logs immuta-toolkit
   ```

3. **Run with verbose logging**:
   ```bash
   IMMUTA_LOG_LEVEL=DEBUG docker-compose up immuta-toolkit
   ```

## Best Practices

1. **Use the Makefile**: The Makefile provides convenient commands for common operations
2. **Keep containers updated**: Rebuild containers when dependencies change
3. **Use environment variables**: Store sensitive information in environment variables
4. **Use volumes for persistent data**: Store data in volumes for persistence
5. **Use resource limits**: Set resource limits to prevent container resource exhaustion
