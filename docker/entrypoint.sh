#!/bin/bash
# Entrypoint script for the Immuta SRE Toolkit Docker container

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Print header
echo -e "${GREEN}=========================================${NC}"
echo -e "${GREEN}    Immuta SRE Toolkit Docker Container  ${NC}"
echo -e "${GREEN}=========================================${NC}"
echo ""

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if PDM is installed
if ! command_exists pdm; then
    echo -e "${YELLOW}PDM is not installed. Installing PDM...${NC}"
    pip install --no-cache-dir pdm
    echo -e "${GREEN}PDM installed successfully!${NC}"
fi

# Install dependencies if they're not already installed
if [ ! -d "__pypackages__" ]; then
    echo -e "${YELLOW}Installing dependencies...${NC}"
    pdm install --prod --no-lock --no-editable
    echo -e "${GREEN}Dependencies installed successfully!${NC}"
fi

# Create directories for data, logs, and reports
mkdir -p /app/data /app/logs /app/reports
echo -e "${GREEN}Created data, logs, and reports directories.${NC}"

# Set up Playwright if needed
if [ "$1" = "web" ] || [ "$IMMUTA_USE_WEB_FALLBACK" = "true" ]; then
    if ! command_exists playwright; then
        echo -e "${YELLOW}Installing Playwright...${NC}"
        pdm run playwright install --with-deps chromium
        echo -e "${GREEN}Playwright installed successfully!${NC}"
    fi
fi

# Check for API server command
if [ "$1" = "api" ] && [ "$2" = "start" ]; then
    echo -e "${YELLOW}Starting API server...${NC}"
    # Start the API server
    exec pdm run python -m immuta_toolkit.service.app "$@"
# Check for test command
elif [ "$1" = "pytest" ] || [ "$1" = "test" ]; then
    echo -e "${YELLOW}Running tests...${NC}"
    shift
    # Run tests
    exec pdm run pytest "$@"
# Check for shell command
elif [ "$1" = "shell" ] || [ "$1" = "bash" ]; then
    echo -e "${YELLOW}Starting shell...${NC}"
    # Start a shell
    exec bash
else
    echo -e "${YELLOW}Running CLI command: $@${NC}"
    # Run the CLI command
    exec pdm run python -m immuta_toolkit.cli "$@"
fi
