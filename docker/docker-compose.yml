version: "3.8"

services:
  # CLI service for running commands
  immuta-toolkit:
    build:
      context: .
      dockerfile: docker/Dockerfile
    image: immuta-sre-toolkit:latest
    container_name: immuta-toolkit
    volumes:
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw
      - ./reports:/app/reports:rw
      - ./configs:/app/configs:ro
    env_file:
      - .env
    environment:
      - IMMUTA_API_KEY=${IMMUTA_API_KEY}
      - IMMUTA_BASE_URL=${IMMUTA_BASE_URL}
      - IMMUTA_API_TIMEOUT=${IMMUTA_API_TIMEOUT:-30}
      - IMMUTA_API_RATE_LIMIT=${IMMUTA_API_RATE_LIMIT:-10}
      - IMMUTA_LOG_LEVEL=${IMMUTA_LOG_LEVEL:-INFO}
      - IMMUTA_LOG_FILE=/app/logs/immuta-toolkit.log
      - BLOB_CONNECTION_STRING=${BLOB_CONNECTION_STRING}
      - BLOB_CONTAINER_NAME=${BLOB_CONTAINER_NAME:-immuta-backups}
      - TAG_CACHE_TTL_MINUTES=${TAG_CACHE_TTL_MINUTES:-5}
      - NOTIFICATION_ENABLED=${NOTIFICATION_ENABLED:-true}
      - AZURE_KEY_VAULT_URL=${AZURE_KEY_VAULT_URL}
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
    command: ["--help"]
    healthcheck:
      test:
        [
          "CMD",
          "pdm",
          "run",
          "python",
          "-m",
          "immuta_toolkit.cli.main",
          "config",
          "validate",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    # Set resource limits
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.25"
          memory: 512M

  # API service for running the API server
  api:
    build:
      context: .
      dockerfile: docker/Dockerfile
    image: immuta-sre-toolkit:latest
    container_name: immuta-api
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw
      - ./reports:/app/reports:rw
      - ./configs:/app/configs:ro
    env_file:
      - .env
    environment:
      - IMMUTA_API_KEY=${IMMUTA_API_KEY}
      - IMMUTA_BASE_URL=${IMMUTA_BASE_URL}
      - IMMUTA_API_TIMEOUT=${IMMUTA_API_TIMEOUT:-30}
      - IMMUTA_API_RATE_LIMIT=${IMMUTA_API_RATE_LIMIT:-10}
      - IMMUTA_LOG_LEVEL=${IMMUTA_LOG_LEVEL:-INFO}
      - IMMUTA_LOG_FILE=/app/logs/immuta-api.log
      - BLOB_CONNECTION_STRING=${BLOB_CONNECTION_STRING}
      - BLOB_CONTAINER_NAME=${BLOB_CONTAINER_NAME:-immuta-backups}
      - TAG_CACHE_TTL_MINUTES=${TAG_CACHE_TTL_MINUTES:-5}
      - NOTIFICATION_ENABLED=${NOTIFICATION_ENABLED:-true}
      - AZURE_KEY_VAULT_URL=${AZURE_KEY_VAULT_URL}
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
    command: ["api", "start", "--host", "0.0.0.0", "--port", "8000"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    # Set resource limits
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1G
        reservations:
          cpus: "0.25"
          memory: 512M

  # Development environment with all dependencies
  dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: immuta-sre-toolkit-dev:latest
    container_name: immuta-toolkit-dev
    volumes:
      - .:/app:rw
    ports:
      - "8000:8000" # For API development
    env_file:
      - .env
    environment:
      - IMMUTA_API_KEY=${IMMUTA_API_KEY}
      - IMMUTA_BASE_URL=${IMMUTA_BASE_URL}
      - IMMUTA_API_TIMEOUT=${IMMUTA_API_TIMEOUT:-30}
      - IMMUTA_API_RATE_LIMIT=${IMMUTA_API_RATE_LIMIT:-10}
      - IMMUTA_LOG_LEVEL=${IMMUTA_LOG_LEVEL:-DEBUG}
      - IMMUTA_LOG_FILE=/app/logs/immuta-dev.log
      - BLOB_CONNECTION_STRING=${BLOB_CONNECTION_STRING}
      - BLOB_CONTAINER_NAME=${BLOB_CONTAINER_NAME:-immuta-backups}
      - TAG_CACHE_TTL_MINUTES=${TAG_CACHE_TTL_MINUTES:-5}
      - NOTIFICATION_ENABLED=${NOTIFICATION_ENABLED:-true}
      - AZURE_KEY_VAULT_URL=${AZURE_KEY_VAULT_URL}
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID}
    command: ["bash"]
    # Keep the container running
    tty: true
    stdin_open: true
    # Set resource limits for development
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G
        reservations:
          cpus: "0.5"
          memory: 1G

  # Test environment for running tests
  test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: immuta-sre-toolkit-dev:latest
    container_name: immuta-toolkit-test
    volumes:
      - .:/app:rw
    env_file:
      - .env
    environment:
      - IMMUTA_API_KEY=${IMMUTA_API_KEY}
      - IMMUTA_BASE_URL=${IMMUTA_BASE_URL}
      - IMMUTA_API_TIMEOUT=${IMMUTA_API_TIMEOUT:-30}
      - IMMUTA_API_RATE_LIMIT=${IMMUTA_API_RATE_LIMIT:-10}
      - IMMUTA_LOG_LEVEL=${IMMUTA_LOG_LEVEL:-DEBUG}
      - IMMUTA_LOG_FILE=/app/logs/immuta-test.log
      - PYTHONPATH=/app
      - PYTEST_ADDOPTS="-v"
    command: ["pdm", "run", "pytest"]
    # Set resource limits for testing
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 2G
