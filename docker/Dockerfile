FROM python:3.13-slim

# Add labels for GitHub Container Registry
LABEL org.opencontainers.image.source=https://github.com/davidlu1001/immuta-sre-toolkit
LABEL org.opencontainers.image.description="Immuta SRE Toolkit"
LABEL org.opencontainers.image.licenses=MIT
LABEL org.opencontainers.image.vendor="Immuta SRE Team"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONFAULTHANDLER=1 \
    PYTHONPATH=/app \
    PDM_USE_VENV=false \
    PDM_IGNORE_SAVED_PYTHON=true \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    libffi-dev \
    libssl-dev \
    make \
    wget \
    gnupg \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PDM
RUN pip install --upgrade pip && \
    pip install pdm

# Set up working directory
WORKDIR /app

# Copy PDM files
COPY pyproject.toml pdm.lock README.md /app/

# Install dependencies
RUN pdm install --prod --no-lock --no-editable

# Install Playwright dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libglib2.0-0 \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxcb1 \
    libxkbcommon0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Playwright browsers
RUN pdm run playwright install --with-deps chromium

# Copy source code
COPY src/ /app/src/
COPY scripts/ /app/scripts/
COPY configs/ /app/configs/

# Create directories for data, logs, and reports
RUN mkdir -p /app/data /app/logs /app/reports

# Make scripts executable
RUN chmod +x /app/scripts/*

# Add PDM binaries to PATH
ENV PATH="/app/__pypackages__/3.13/bin:$PATH"

# Copy entrypoint script
COPY docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Create a non-root user
RUN useradd -m -u 1000 immuta
RUN chown -R immuta:immuta /app
USER immuta

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Set default command
CMD ["--help"]
