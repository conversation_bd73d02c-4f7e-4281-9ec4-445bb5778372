FROM python:3.13-slim

# Add labels for GitHub Container Registry
LABEL org.opencontainers.image.source=https://github.com/davidlu1001/immuta-sre-toolkit
LABEL org.opencontainers.image.description="Immuta SRE Toolkit Development Environment"
LABEL org.opencontainers.image.licenses=MIT
LABEL org.opencontainers.image.vendor="Immuta SRE Team"
LABEL org.opencontainers.image.version="0.2.0-dev"
LABEL org.opencontainers.image.created="2024-07-01T00:00:00Z"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONFAULTHANDLER=1 \
    PYTHONPATH=/app \
    PDM_USE_VENV=false \
    PDM_IGNORE_SAVED_PYTHON=1 \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    vim \
    nano \
    less \
    procps \
    libffi-dev \
    libssl-dev \
    make \
    wget \
    gnupg \
    # Playwright dependencies
    libglib2.0-0 \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxcb1 \
    libxkbcommon0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PDM
RUN pip install --upgrade pip && \
    pip install pdm

# Set working directory
WORKDIR /app

# Copy PDM files
COPY pyproject.toml pdm.lock README.md ./

# Install all dependencies including dev dependencies
RUN pdm install --dev --no-lock

# Install Playwright browsers
RUN pdm run playwright install --with-deps chromium

# Create directories for data, logs, and reports
RUN mkdir -p /app/data /app/logs /app/reports

# Add PDM binaries to PATH
ENV PATH="/app/__pypackages__/3.13/bin:$PATH"

# Copy entrypoint script
COPY docker/entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Create a non-root user for development
RUN useradd -m -u 1000 immuta
RUN chown -R immuta:immuta /app

# Switch to non-root user for better security
USER immuta

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Keep container running
CMD ["bash"]
