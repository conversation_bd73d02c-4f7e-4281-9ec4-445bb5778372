FROM python:3.13-slim

# Add labels for GitHub Container Registry
LABEL org.opencontainers.image.source=https://github.com/davidlu1001/immuta-sre-toolkit
LABEL org.opencontainers.image.description="Immuta SRE Toolkit"
LABEL org.opencontainers.image.licenses=MIT
LABEL org.opencontainers.image.vendor="Immuta SRE Team"
LABEL org.opencontainers.image.version="0.2.0"
LABEL org.opencontainers.image.created="2024-05-13T00:00:00Z"

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PDM_USE_VENV=false \
    PDM_IGNORE_SAVED_PYTHON=1

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    curl \
    git \
    # Playwright dependencies
    libglib2.0-0 \
    libnss3 \
    libnspr4 \
    libatk1.0-0 \
    libatk-bridge2.0-0 \
    libcups2 \
    libdrm2 \
    libdbus-1-3 \
    libxcb1 \
    libxkbcommon0 \
    libx11-6 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libgbm1 \
    libpango-1.0-0 \
    libcairo2 \
    libasound2 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PDM
RUN pip install --no-cache-dir pdm

# Set working directory
WORKDIR /app

# Copy PDM files
COPY pyproject.toml pdm.lock README.md ./

# Install dependencies
RUN pdm install --no-lock --no-editable

# Copy source code
COPY src/ /app/src/
COPY scripts/ /app/scripts/
COPY configs/ /app/configs/

# Install Playwright browsers
RUN pdm run playwright install --with-deps chromium

# Create directories for data, logs, and reports
RUN mkdir -p /app/data /app/logs /app/reports

# Create non-root user
RUN useradd -m -u 1000 immuta
RUN chown -R immuta:immuta /app
USER immuta

# Expose port for metrics and healthcheck
EXPOSE 8000

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Set entrypoint
ENTRYPOINT ["pdm", "run", "python", "-m", "immuta_toolkit.cli.main"]

# Default command (show help)
CMD ["--help"]

# To run in API mode, override the command:
# docker run --rm immuta-sre-toolkit api start --host 0.0.0.0 --port 8000
