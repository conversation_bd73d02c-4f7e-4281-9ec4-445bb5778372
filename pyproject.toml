[project]
name = "immuta-sre-toolkit"
version = "0.1.0"
description = "Default template for PDM package"
authors = [
    {name = "davidlu1001", email = "<EMAIL>"},
    {name = "ac-david", email = "<EMAIL>"},
]
keywords = ["immuta", "data-governance", "sre", "devops", "automation"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Systems Administration",
]
dependencies = [
    "requests>=2.32.3",
    "pydantic>=2.0.0",
    "click>=8.2.0",
    "rich>=14.0.0",
    "tenacity>=8.1.0",
    "pyyaml>=6.0.2",
    "azure-storage-blob>=12.25.1",
    "azure-monitor-query>=1.0.3",
    "azure-identity>=1.23.0",
    "azure-keyvault-secrets>=4.9.0",
    "snowflake-connector-python>=3.15.0",
    "pandas>=1.5.3",
    "openpyxl>=3.1.0",
    "ratelimit>=2.2.1",
    "faker>=13.15.1",
    "pytest>=8.3.5",
    "email-validator>=2.2.0",
    "prometheus-client>=0.16.0",
    "black>=23.3.0",
    "ruff>=0.1.5",
    "mypy>=1.3.0",
    "isort>=5.12.0",
    "pre-commit>=3.3.2",
    "cryptography>=44.0.0",
    "bandit>=1.8.3",
    "playwright>=1.40.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.27.0",
    "structlog>=24.1.0",
    "toml>=0.10.2",
    "hvac>=2.3.0",
    "python-jose>=3.4.0",
    "passlib>=1.7.4",
    "bcrypt>=4.3.0",
    "jsonschema>=4.23.0",
    "jinja2>=3.1.6",
    "reportlab>=4.4.1",
    "schedule>=1.2.2",
    "httpx>=0.28.1",
    "python-multipart>=0.0.20",
]
requires-python = ">=3.10,<3.14"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "black>=23.3.0",
    "isort>=5.12.0",
    "mypy>=1.3.0",
    "ruff>=0.1.5",
    "pre-commit>=3.3.1",
    "pytest-mock>=3.10.0",
    "responses>=0.23.1",
    "types-requests>=**********",
    "types-PyYAML>=********",
    "pyinstaller>=6.5.0",
    "bandit>=1.8.3",
    "pytest-playwright>=0.4.0",
]

[project.scripts]
immuta = "immuta_toolkit.cli.main:main"

[tool.black]
line-length = 88
target-version = ["py310", "py311", "py312", "py313"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.ruff]
line-length = 88
target-version = "py310"

[tool.ruff.lint]
select = ["E", "F", "B", "I", "N", "UP", "ANN", "S", "A", "C4", "T20", "PT", "RET", "SIM"]
ignore = ["ANN401"]  # Removed ANN101 and ANN102 as they're no longer valid

[tool.ruff.lint.isort]
known-first-party = ["immuta_toolkit"]

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101", "ANN", "PT009"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
python_classes = "Test*"

[tool.pdm]
distribution = false
[tool.pdm.build]
includes = ["src/immuta_toolkit"]
[tool.pdm.options]
lock = ["--no-cross-platform"]
[tool.pdm.python]
use-venv = true
