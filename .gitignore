# Python
.py[cod]
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
src/immuta_sre_toolkit.egg-info/

# Testing
.mypy_cache/
.pytest_cache/
.ruff_cache/
.coverage
htmlcov/
.tox/

# PDM
.pdm-python
__pypackages__/

# Environment variables
.env
.venv
env/
venv/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
