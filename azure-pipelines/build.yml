trigger:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - src/**
      - tests/**
      - pyproject.toml
      - pdm.lock

pr:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - src/**
      - tests/**
      - pyproject.toml
      - pdm.lock

pool:
  vmImage: 'ubuntu-latest'

variables:
  pythonVersion: '3.10'
  pdmVersion: '2.10.0'

stages:
  - stage: Build
    displayName: 'Build and Test'
    jobs:
      - job: Lint
        displayName: 'Lint'
        steps:
          - template: templates/setup-python.yml
            parameters:
              pythonVersion: $(pythonVersion)
              pdmVersion: $(pdmVersion)
          
          - script: |
              pdm run ruff check .
            displayName: 'Run Ruff'
          
          - script: |
              pdm run black --check .
            displayName: 'Run Black'
          
          - script: |
              pdm run mypy src
            displayName: 'Run MyPy'
          
          - script: |
              pdm run bandit -r src
            displayName: 'Run Bandit'
      
      - job: Test
        displayName: 'Run Tests'
        steps:
          - template: templates/setup-python.yml
            parameters:
              pythonVersion: $(pythonVersion)
              pdmVersion: $(pdmVersion)
          
          - script: |
              pdm run pytest tests/unit --cov=immuta_toolkit --cov-report=xml
            displayName: 'Run Unit Tests'
          
          - task: PublishCodeCoverageResults@1
            inputs:
              codeCoverageTool: Cobertura
              summaryFileLocation: '$(System.DefaultWorkingDirectory)/coverage.xml'
              reportDirectory: '$(System.DefaultWorkingDirectory)/htmlcov'
            displayName: 'Publish Code Coverage'
          
          - task: PublishTestResults@2
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: 'junit/test-results.xml'
              mergeTestResults: true
              testRunTitle: 'Unit Tests'
            displayName: 'Publish Test Results'
      
      - job: Build
        displayName: 'Build Package'
        steps:
          - template: templates/setup-python.yml
            parameters:
              pythonVersion: $(pythonVersion)
              pdmVersion: $(pdmVersion)
          
          - script: |
              pdm build
            displayName: 'Build Package'
          
          - task: PublishPipelineArtifact@1
            inputs:
              targetPath: 'dist'
              artifact: 'dist'
              publishLocation: 'pipeline'
            displayName: 'Publish Package'
      
      - job: Docker
        displayName: 'Build Docker Image'
        steps:
          - task: Docker@2
            inputs:
              containerRegistry: 'DockerHub'
              repository: 'immuta-sre-toolkit'
              command: 'buildAndPush'
              Dockerfile: 'docker/Dockerfile'
              tags: |
                latest
                $(Build.BuildNumber)
            displayName: 'Build and Push Docker Image'
  
  - stage: Deploy
    displayName: 'Deploy'
    dependsOn: Build
    condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    jobs:
      - job: DeployPyPI
        displayName: 'Deploy to PyPI'
        steps:
          - task: DownloadPipelineArtifact@2
            inputs:
              artifactName: 'dist'
              targetPath: '$(Pipeline.Workspace)/dist'
            displayName: 'Download Package'
          
          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
              addToPath: true
            displayName: 'Use Python $(pythonVersion)'
          
          - script: |
              pip install --upgrade pip
              pip install twine
            displayName: 'Install Twine'
          
          - task: TwineAuthenticate@1
            inputs:
              pythonUploadServiceConnection: 'PyPI'
            displayName: 'Authenticate with PyPI'
          
          - script: |
              python -m twine upload -r "pypi" --config-file $(PYPIRC_PATH) $(Pipeline.Workspace)/dist/*
            displayName: 'Upload to PyPI'
