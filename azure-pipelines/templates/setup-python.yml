parameters:
  - name: pythonVersion
    type: string
    default: '3.10'
  - name: pdmVersion
    type: string
    default: '2.10.0'

steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: ${{ parameters.pythonVersion }}
      addToPath: true
    displayName: 'Use Python ${{ parameters.pythonVersion }}'
  
  - script: |
      python -m pip install --upgrade pip
      pip install pdm==${{ parameters.pdmVersion }}
    displayName: 'Install PDM'
  
  - script: |
      pdm install
    displayName: 'Install Dependencies'
  
  - script: |
      pdm run playwright install --with-deps chromium
    displayName: 'Install Playwright'
