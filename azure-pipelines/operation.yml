parameters:
  - name: operation
    type: string
    default: 'backup'
    values:
      - 'backup'
      - 'restore'
      - 'validate'
      - 'apply'
      - 'report'
  - name: environment
    type: string
    default: 'dev'
    values:
      - 'dev'
      - 'test'
      - 'prod'
  - name: configFile
    type: string
    default: ''
  - name: outputDir
    type: string
    default: '$(Build.ArtifactStagingDirectory)/immuta'

trigger: none

pr: none

pool:
  vmImage: 'ubuntu-latest'

variables:
  - group: immuta-${{ parameters.environment }}

resources:
  containers:
    - container: immuta_toolkit
      image: immuta-sre-toolkit:latest
      endpoint: DockerHub

jobs:
  - job: ImmutaOperation
    displayName: 'Immuta ${{ parameters.operation }}'
    container: immuta_toolkit
    steps:
      - ${{ if eq(parameters.operation, 'backup') }}:
        - script: |
            immuta backup create --output-dir ${{ parameters.outputDir }}
          displayName: 'Create Immuta Backup'
          env:
            IMMUTA_API_KEY: $(IMMUTA_API_KEY)
            IMMUTA_BASE_URL: $(IMMUTA_BASE_URL)
        
        - task: PublishPipelineArtifact@1
          inputs:
            targetPath: '${{ parameters.outputDir }}'
            artifact: 'immuta-backup'
            publishLocation: 'pipeline'
          displayName: 'Publish Backup'
      
      - ${{ if eq(parameters.operation, 'restore') }}:
        - task: DownloadPipelineArtifact@2
          inputs:
            artifactName: 'immuta-backup'
            targetPath: '${{ parameters.outputDir }}'
          displayName: 'Download Backup'
        
        - script: |
            immuta backup restore --input-dir ${{ parameters.outputDir }}
          displayName: 'Restore Immuta Backup'
          env:
            IMMUTA_API_KEY: $(IMMUTA_API_KEY)
            IMMUTA_BASE_URL: $(IMMUTA_BASE_URL)
      
      - ${{ if eq(parameters.operation, 'validate') }}:
        - script: |
            immuta ${{ parameters.configFile }} validate --config-file ${{ parameters.configFile }}
          displayName: 'Validate Immuta Configuration'
          env:
            IMMUTA_API_KEY: $(IMMUTA_API_KEY)
            IMMUTA_BASE_URL: $(IMMUTA_BASE_URL)
      
      - ${{ if eq(parameters.operation, 'apply') }}:
        - script: |
            # Create backup first
            immuta backup create --output-dir ${{ parameters.outputDir }}/backup
            
            # Apply configuration
            immuta ${{ parameters.configFile }} apply --config-file ${{ parameters.configFile }}
          displayName: 'Apply Immuta Configuration'
          env:
            IMMUTA_API_KEY: $(IMMUTA_API_KEY)
            IMMUTA_BASE_URL: $(IMMUTA_BASE_URL)
        
        - task: PublishPipelineArtifact@1
          inputs:
            targetPath: '${{ parameters.outputDir }}/backup'
            artifact: 'immuta-backup-before-apply'
            publishLocation: 'pipeline'
          displayName: 'Publish Backup Before Apply'
      
      - ${{ if eq(parameters.operation, 'report') }}:
        - script: |
            # Generate reports
            immuta data-sources report --output-format html --output-file ${{ parameters.outputDir }}/data-sources.html
            immuta policies report --output-format html --output-file ${{ parameters.outputDir }}/policies.html
            immuta users report --output-format html --output-file ${{ parameters.outputDir }}/users.html
            immuta projects report --output-format html --output-file ${{ parameters.outputDir }}/projects.html
          displayName: 'Generate Immuta Reports'
          env:
            IMMUTA_API_KEY: $(IMMUTA_API_KEY)
            IMMUTA_BASE_URL: $(IMMUTA_BASE_URL)
        
        - task: PublishPipelineArtifact@1
          inputs:
            targetPath: '${{ parameters.outputDir }}'
            artifact: 'immuta-reports'
            publishLocation: 'pipeline'
          displayName: 'Publish Reports'
